{"name": "fabriqon-web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "https": "next dev --turbopack --experimental-https", "legacydev": "nodemon", "legacyhttps": "next dev  --experimental-https", "build": "next build --no-lint --debug && tsc --project tsconfig.server.json", "build:local": "cross-env NODE_ENV=test pnpm build", "build:analyze": "cross-env ANALYZE=true pnpm build:local", "start": "cross-env NODE_ENV=production node dist/server.js", "sort:json": "node json-sort.js src/messages", "lint": "next lint", "prepare": "husky"}, "--do-not-update": "auth0", "dependencies": {"@auth0/nextjs-auth0": "3.6.0", "@casl/ability": "6.7.3", "@dnd-kit/core": "6.3.1", "@dnd-kit/sortable": "10.0.0", "@dnd-kit/utilities": "3.2.2", "@fullcalendar/core": "6.1.17", "@fullcalendar/interaction": "6.1.17", "@fullcalendar/react": "6.1.17", "@fullcalendar/resource": "6.1.17", "@fullcalendar/resource-timeline": "6.1.17", "@hookform/resolvers": "5.0.1", "@radix-ui/react-accordion": "1.2.11", "@radix-ui/react-alert-dialog": "1.1.14", "@radix-ui/react-aspect-ratio": "1.1.7", "@radix-ui/react-avatar": "1.1.10", "@radix-ui/react-checkbox": "1.3.2", "@radix-ui/react-collapsible": "1.1.11", "@radix-ui/react-context-menu": "2.2.15", "@radix-ui/react-dialog": "1.1.14", "@radix-ui/react-dropdown-menu": "2.1.15", "@radix-ui/react-hover-card": "1.1.14", "@radix-ui/react-label": "2.1.7", "@radix-ui/react-menubar": "1.1.15", "@radix-ui/react-navigation-menu": "1.2.13", "@radix-ui/react-popover": "1.1.14", "@radix-ui/react-progress": "1.1.7", "@radix-ui/react-radio-group": "1.3.7", "@radix-ui/react-scroll-area": "1.2.9", "@radix-ui/react-select": "2.2.5", "@radix-ui/react-separator": "1.1.7", "@radix-ui/react-slider": "1.3.5", "@radix-ui/react-slot": "1.2.3", "@radix-ui/react-switch": "1.2.5", "@radix-ui/react-tabs": "1.1.12", "@radix-ui/react-toast": "1.2.14", "@radix-ui/react-toggle": "1.1.9", "@radix-ui/react-toggle-group": "1.1.10", "@radix-ui/react-tooltip": "1.2.7", "@tinymce/tinymce-react": "^6.2.1", "axios": "1.9.0", "class-variance-authority": "0.7.1", "clipboard-copy": "4.0.1", "clsx": "2.1.1", "cmdk": "1.1.1", "date-fns": "4.1.0", "dompurify": "3.2.6", "firebase": "11.9.0", "form-data": "4.0.3", "formidable": "3.5.4", "joi": "17.13.3", "jotai": "2.12.5", "jsonwebtoken": "9.0.2", "lodash": "4.17.21", "lucide-react": "0.513.0", "next": "15.3.3", "next-intl": "4.1.0", "next-themes": "0.4.6", "qrcode.react": "4.2.0", "react": "19.1.0", "react-day-picker": "9.7.0", "react-dom": "19.1.0", "react-grid-layout": "1.5.1", "react-hook-form": "7.57.0", "react-intersection-observer": "9.16.0", "react-resizable-panels": "3.0.2", "react-signature-canvas": "1.0.7", "react-textarea-autosize": "8.5.9", "react-window": "1.8.11", "sonner": "2.0.5", "swr": "2.3.3", "tailwindcss-animate": "1.0.7", "vaul": "1.1.2"}, "devDependencies": {"@next/bundle-analyzer": "15.3.3", "@tailwindcss/postcss": "4.1.8", "@types/formidable": "3.4.5", "@types/jsonwebtoken": "9.0.9", "@types/lodash": "4.17.17", "@types/node": "22.15.30", "@types/react": "19.1.6", "@types/react-dom": "19.1.6", "@types/react-grid-layout": "1.3.5", "@types/react-signature-canvas": "1.0.7", "@types/react-window": "1.8.8", "@typescript-eslint/eslint-plugin": "8.33.1", "@typescript-eslint/parser": "8.33.1", "cross-env": "7.0.3", "eslint": "9.28.0", "eslint-config-next": "15.3.3", "eslint-config-prettier": "10.1.5", "eslint-plugin-import": "2.31.0", "eslint-plugin-perfectionist": "4.14.0", "eslint-plugin-prettier": "5.4.1", "eslint-plugin-react": "7.37.5", "eslint-plugin-security": "3.0.1", "eslint-plugin-tailwindcss": "3.18.0", "husky": "9.1.7", "lint-staged": "16.1.0", "nodemon": "3.1.10", "postcss": "8.5.4", "prettier": "3.5.3", "tailwind-merge": "3.3.0", "tailwindcss": "4.1.8", "ts-node": "10.9.2", "typescript": "5.8.3"}}