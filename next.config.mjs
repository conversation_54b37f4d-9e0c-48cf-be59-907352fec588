/** @type {import('next').NextConfig} */
const nextConfig = {
  compress: true,
  i18n: {
    defaultLocale: 'ro',
    locales: ['ro', 'en', 'hu'],
  },
  modularizeImports: {
    'date-fns': {
      transform: 'date-fns/{{member}}',
    },
    lodash: {
      transform: 'lodash/{{member}}',
    },
  },
  poweredByHeader: false,
  reactStrictMode: true,
  rewrites: async () => [
    {
      destination: '/sales/orders',
      source: '/sales'
    },
    {
      destination: '/inventory/items',
      source: '/inventory'
    },
    {
      destination: '/sales/quotes',
      source: '/sales/quotes'
    },
    {
      destination: '/settings/general',
      source: '/settings'
    },
    {
      destination: '/manufacturing/orders',
      source: '/manufacturing'
    }
  ],
  turbopack: {
    resolveAlias: {
      underscore: 'lodash',
    }
  },
  ...(process.env.NODE_ENV === 'production' ? {output: 'standalone'} : {}),
  devIndicators: false,
  experimental: {
    optimizePackageImports: ['@/components', '@/hooks', '@/assets'],
  }
};

let withAnalyzer = (config) => config;

if (process.env.NODE_ENV !== 'production') {
  withAnalyzer = (await import('@next/bundle-analyzer')).default({ enabled: process.env.ANALYZE === 'true' });
}

export default withAnalyzer(nextConfig);
