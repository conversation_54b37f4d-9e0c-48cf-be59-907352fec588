/* eslint-disable security/detect-non-literal-fs-filename */
const fs = require('fs');
const path = require('path');

const folderPath = process.argv[2];

if (!folderPath) {
  console.error('Please provide the folder path containing JSON files as an argument.');
  process.exit(1);
}

fs.readdirSync(folderPath).forEach((file) => {
  const filePath = path.join(folderPath, file);

  if (path.extname(filePath) === '.json') {
    const input = fs.readFileSync(filePath, 'utf8');
    const data = JSON.parse(input);
    const sortedData = sortKeys(data);
    const output = JSON.stringify(sortedData, null, 2) + '\n';

    fs.writeFileSync(filePath, output, 'utf8');

    console.log(`Sorted keys in ${filePath}`);
  }
});

function sortKeys(obj) {
  if (typeof obj !== 'object' || obj === null) {
    return obj;
  }

  if (Array.isArray(obj)) {
    return obj.map(sortKeys);
  }

  const sortedKeys = Object.keys(obj).sort(naturalSort);

  const sortedObj = {};
  sortedKeys.forEach((key) => {
    sortedObj[key] = sortKeys(obj[key]);
  });

  return sortedObj;
}

function naturalSort(a, b) {
  return a.localeCompare(b, undefined, {numeric: true, sensitivity: 'base'});
}
