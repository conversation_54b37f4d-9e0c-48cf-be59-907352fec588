import {createServer} from 'http';
import {createServer as createSecureServer} from 'https';
import next from 'next';
import {parse} from 'url';

const port = parseInt(process.env.PORT || '3000', 10);
const prod = process.env.NODE_ENV === 'production';
const app = next({dev: !prod});
const handle = app.getRequestHandler();

const SSL_CERT = process.env.SSL_CERT;
const SSL_KEY = process.env.SSL_KEY;

if (prod && (!SSL_CERT || !SSL_KEY)) {
  console.log('Missing HTTPS certificates...');
}

app.prepare().then(() => {
  (prod && SSL_CERT && SSL_KEY
    ? createSecureServer(
        {
          cert: SSL_CERT,
          key: SSL_KEY,
        },
        (req, res) => {
          const parsedUrl = parse(req.url, true);
          handle(req, res, parsedUrl);
        },
      )
    : createServer((req, res) => {
        const parsedUrl = parse(req.url, true);
        handle(req, res, parsedUrl);
      })
  ).listen(port);

  console.log(`> Server listening at http://localhost:${port} as ${process.env.NODE_ENV}`);
});
