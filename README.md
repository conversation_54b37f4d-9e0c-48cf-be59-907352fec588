# ![](https://avatars.slack-edge.com/2022-08-02/3881080085621_31b021c6d88ece020a13_88.png) fabriqon

[![pipeline status](https://gitlab.com/ezbizproject/ezbiz-web/badges/develop/pipeline.svg)](https://gitlab.com/ezbizproject/ezbiz-web/-/commits/develop) [![coverage report](https://gitlab.com/ezbizproject/ezbiz-web/badges/develop/coverage.svg)](https://gitlab.com/ezbizproject/ezbiz-web/-/commits/develop) [![Latest Release](https://gitlab.com/ezbizproject/ezbiz-web/-/badges/release.svg)](https://gitlab.com/ezbizproject/ezbiz-web/-/releases)

## Getting Started

First, install dependencies

```bash
pnpm install
```

set the environment variables (.env):

```bash
APP_NAME='fabriqon' # used for page title
NEXT_PUBLIC_BASE_PATH='http://localhost:3000' # used for client API requests

# Auth0
AUTH0_SECRET='use [openssl rand -hex 32] to generate a 32 bytes value'
AUTH0_BASE_URL='http://localhost:3000'
AUTH0_ISSUER_BASE_URL='https://ezbizdev.eu.auth0.com'
AUTH0_CLIENT_ID='grab it from Auth0 Application Dashboard'
AUTH0_CLIENT_SECRET='grab it from Auth0 Application Dashboard'
AUTH0_SCOPE='openid profile email offline_access'
AUTH0_AUDIENCE='http://localhost:8080'
```

run the development server:

```bash
pnpm dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the [pages](https://nextjs.org/docs/basic-features/pages) by modifying `pages/` files. The page auto-updates as you edit the file.

[API routes](https://nextjs.org/docs/api-routes/introduction) can be accessed on [http://localhost:3000/api](http://localhost:3000/api). The endpoints can be edited in `pages/api`.

The `pages/api` directory is mapped to `/api/*`. Files in this directory are treated as [API routes](https://nextjs.org/docs/api-routes/introduction) instead of [React pages](https://nextjs.org/docs/basic-features/pages).

## Other commands:

```bash
pnpm lint
pnpm tsc
pnpm build
pnpm analyze
pnpm start
docker-compose up --build
```

## Useful links:

- [Next.js Documentation](https://nextjs.org/docs)
- [Auth0 Dashboard](https://manage.auth0.com/dashboard/eu/ezbizdev/)
- [Figma Design](https://www.figma.com/file/NObrwHjnYA9Ir8yeXFrivO/Inventhor)
