export const fake_codes = [
  {
    address: {
      address1: 'Str. Libert<PERSON>, Nr. 20',
      address2: 'Corp A, Ap. 5',
      city: 'Timișoara',
      country: 'RO',
      name: 'Company HQ',
      state: '<PERSON><PERSON><PERSON>',
      types: ['BILLING', 'SHIPPING'],
      zip: '300100',
    },
    identificationNumber: 'RO654321',
    name: 'All fields filled',
    taxIdentificationNumber: '57283910',
  },
  {
    address: {
      address1: 'Str. Unirii, Nr. 7',
      address2: 'Birou 10',
      city: 'Brașov',
      country: 'RO',
      name: 'Regional Office',
      state: '<PERSON><PERSON><PERSON><PERSON>',
      types: ['BILLING', 'SHIPPING'],
      zip: '500200',
    },
    identificationNumber: 'RO987654',
    name: 'All fields filled 2',
    taxIdentificationNumber: '67234198',
  },
  {
    address: {
      address1: 'Str. <PERSON><PERSON>azu, Nr. 12',
      address2: 'Et. 3, Birou 15',
      city: 'Constanța',
      country: 'RO',
      name: 'Regional Office',
      state: '<PERSON>stan<PERSON><PERSON>',
      types: ['BILLING', 'SHIPPING'],
      zip: '900300',
    },
    identificationNumber: 'RO741258',
    name: 'All fields filled 3',
    taxIdentificationNumber: '83476521',
  },
  {
    address: {
      address1: 'Str. Mărășești, Nr. 5',
      address2: 'Ap. 6',
      city: 'Sibiu',
      country: 'RO',
      name: 'Regional Office',
      state: 'Sibiu',
      types: ['BILLING', 'SHIPPING'],
      zip: '550350',
    },
    identificationNumber: 'RO369852',
    name: 'All fields filled 4',
    taxIdentificationNumber: '45678912',
  },
  {
    address: {
      address1: 'Str. Carol I, Nr. 9',
      address2: 'Bl. C, Sc. 1',
      city: 'Oradea',
      country: 'RO',
      name: 'Regional Office',
      state: 'Bihor',
      types: ['BILLING', 'SHIPPING'],
      zip: '410250',
    },
    identificationNumber: 'RO852147',
    name: 'All fields filled 5',
    taxIdentificationNumber: '96325874',
  },
  {
    address: {
      address1: 'Str. Eminescu, Nr. 3',
      address2: 'Corp B, Et. 2',
      city: 'Iași',
      country: 'RO',
      name: 'Regional Office',
      state: 'Iași',
      types: ['BILLING', 'SHIPPING'],
      zip: '700500',
    },
    identificationNumber: 'RO951753',
    name: 'All fields filled 6',
    taxIdentificationNumber: '78541236',
  },
  {
    address: {
      address1: 'Bd. Revoluției, Nr. 14',
      address2: 'Et. 4, Camera 2',
      city: 'Bacău',
      country: 'RO',
      name: 'Regional Office',
      state: 'Bacău',
      types: ['BILLING', 'SHIPPING'],
      zip: '600450',
    },
    identificationNumber: 'RO753159',
    name: 'All fields filled 7',
    taxIdentificationNumber: '14523698',
  },
  {
    address: {
      address1: 'Str. Horia, Nr. 21',
      address2: 'Birou 5',
      city: 'Arad',
      country: 'RO',
      name: 'Regional Office',
      state: 'Arad',
      types: ['BILLING', 'SHIPPING'],
      zip: '310220',
    },
    identificationNumber: 'RO258369',
    name: 'All fields filled 8',
    taxIdentificationNumber: '36987452',
  },
  {
    address: {
      address1: 'Str. Tudor Vladimirescu, Nr. 8',
      address2: 'Et. 1, Ap. 2',
      city: 'Craiova',
      country: 'RO',
      name: 'Regional Office',
      state: 'Dolj',
      types: ['BILLING', 'SHIPPING'],
      zip: '200400',
    },
    identificationNumber: 'RO987321',
    name: 'All fields filled 9',
    taxIdentificationNumber: '85296314',
  },
  {
    address: {
      address1: 'Str. Decebal, Nr. 15',
      address2: 'Sc. B, Et. 3',
      city: 'Galați',
      country: 'RO',
      name: 'Regional Office',
      state: 'Galați',
      types: ['BILLING', 'SHIPPING'],
      zip: '800250',
    },
    identificationNumber: 'RO654987',
    name: 'All fields filled 10',
    taxIdentificationNumber: '98745623',
  },
  {
    address: {
      address1: 'Str. sd, Nr. aa',
      address2: 'Sc. sB, Et. 3',
      city: 'ff',
      country: 'DE',
      name: 'd Office',
      state: 'asd',
      types: ['BILLING', 'SHIPPING'],
      zip: '1111',
    },
    identificationNumber: 'RO654987',
    name: 'External',
    taxIdentificationNumber: '123123',
  },
];
