import {FC, ReactNode} from 'react';

import {ManufacturingTaskStatusReason} from 'types/global';
import {IconProps} from 'types/icon';

export enum NotificationSection {
  CUSTOMERS = 'CUSTOMERS',
  MANUFACTURING = 'MANUFACTURING',
  MANUFACTURING_TASK = 'MANUFACTURING_TASK',
  PURCHASES = 'PURCHASES',
  SALES = 'SALES',
  SERVICING = 'SERVICING',
}

export enum NotificationType {
  COMMENT = 'COMMENT',
  ISSUE = 'ISSUE',
  UPDATE = 'UPDATE',
}

export type CommentNotification = Notification & {
  details: {
    name: string;
    noteId: string;
  };
};

export type LiveNotification = Notification & {
  [key: string]: null | string;
};

export type ManufacturingTaskNotification = Notification & {
  details: {
    orderId: string;
    orderNumber: string;
    statusReason: ManufacturingTaskStatusReason;
    workstationId: string;
    workstationName: string;
  };
};

export type Notification = {
  createTime: string;
  details: {};
  id: string;
  read: boolean | null;
  readBy: null | string[];
  resolvedAt: null | string;
  resolvedBy: null | string;
  section: NotificationSection;
  targetEntityId: string;
  triggeredBy: string;
  type: NotificationType;
};

export type NotificationMeta = Notification & {
  href?: string;
  icon?: ReactNode;
  message?: string;
  relatedId?: string;
  silent: boolean;
};

export type SidebarItem = {
  active?: string[];
  href: string;
  icon?: FC<IconProps>;
  subItems?: SidebarItem[];
  text: string;
};

export type size = 'lg' | 'md' | 'sm' | 'xl' | 'xs' | 'xxl';
