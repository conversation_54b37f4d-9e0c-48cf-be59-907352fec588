import {Address, BankAccount, Currency} from '@/types/global';

export type AccountInformation = {
  address: Address;
  bankAccounts: BankAccount[] | undefined;
  email: string;
  identificationNumber: string;
  includeAddressOnDocuments: boolean;
  includeBankAccountOnDocuments: boolean[];
  includeEmailOnDocuments: boolean;
  includeIdentificationNumberOnDocuments: boolean;
  includePhoneOnDocuments: boolean;
  includeSocialCapitalOnDocuments: boolean;
  includeTaxIdentificationNumberOnDocuments: boolean;
  phone: string;
  socialCapital: number;
  taxIdentificationNumber: string;
};

export type Category = {
  deleted: boolean;
  details: {
    name: string;
  };
  id: string;
};

export type UserSettings = {
  enabledMeasurementUnits: string[];
  general: {
    defaultCurrency: string;
    defaultDeliveryTimeForSalesOrders: number;
    defaultLanguage: string;
    defaultTimeZone: string;
    inventoryAccountingSettings: {
      method: string;
      unitDesignations: {
        defaultInventoryUnit: string;
      };
    };
  };
  manufacturing: {
    administrativeOverheadPerEmployeeHour?: Currency;
    manufacturingOverheadPerEmployeeHour?: Currency;
    workDayEndTime?: string;
    workDayStartTime?: string;
    workingDays?: number[];
  };
  taxRates: {
    defaultVAT: number;
  };
};
