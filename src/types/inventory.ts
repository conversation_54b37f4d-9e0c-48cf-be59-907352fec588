import {
  Currency,
  CustomFile,
  Id,
  SimplifiedConsumption,
  SimplifiedConsumptionMaterial,
  SimplifiedReception,
  SimplifiedReceptionItem,
  Unit,
} from '@/types/global';

export enum AdjustmentType {
  ADJUSTMENT = 'ADJUSTMENT',
  TYPE_CHANGE = 'TYPE_CHANGE',
}

export enum InventoryHistoryDocumentType {
  INVENTORY_MOVE_ADJUSTMENT = 'INVENTORY_MOVE_ADJUSTMENT',
  MATERIAL_ISSUE_NOTE = 'MATERIAL_ISSUE_NOTE',
  RECEPTION_RECEIPT = 'RECEPTION_RECEIPT',
}

export enum StockHistoryEntryType {
  INCOMING = 'INCOMING',
  OUTGOING = 'OUTGOING',
}

export type Adjustment = {
  createTime: string;
  deliveredBy: string;
  id: string;
  inventoryEntries: InventoryEntry[];
  number: string;
  reason: string;
  receivedBy: string;
  totalValue: Currency;
  type: AdjustmentType;
};

export type Consumption = SimplifiedConsumption & {
  createTime: string;
  id: string;
  manufacturingOrder: Id;
  materials: SimplifiedConsumptionMaterial[];
  number: string;
  ownerId: string;
};

export type InventoryEntry = {
  expiryDate: null | string;
  fromUnit?: Id | null; // used for move
  locationId: null | string;
  markup: number;
  materialGood: Id & {code: string; measurementUnit: Unit};
  originalFromQuantity?: number; // used for move
  originalQuantity?: number; // used for move
  price: Currency;
  purchaseDate: null | string;
  quantity: number;
  supplier?: Id | null; // used for move
  unit: Id;
};

export type InventoryItem = Id & {
  appliedVariantOptions?: InventoryItemAppliedVariantOption[] | null;
  category: Id;
  code: string;
  criticalOnHand: number;
  customizable: boolean;
  deletable: boolean;
  description: string;
  estimatedAdministrativeCost: Currency;
  estimatedEmployeeAndWorkstationCost?: Currency;
  estimatedMaterialCost: Currency;
  estimatedProductionCost?: Currency;
  estimatedProductionOverheadCost?: Currency;
  files: CustomFile[];
  inventoryCostPerItem: Currency;
  lastPurchase: null | {price: Currency; supplierId: string; supplierName: string};
  manufacturingOperations: InventoryItemManufacturingOperation[];
  margin: number;
  markup: null | number;
  material: MaterialDimension;
  measurementUnit: Unit;
  parentId?: null | string;
  produced: boolean;
  requiredMaterials: InventoryItemRequiredMaterial[] | null;
  sellPrice: Currency;
  stock: number;
  unitOfProduction: number;
  variantCount: number;
  variantOptions: InventoryItemVariantOption[] | null;
  vatRate: null | number;
};

export type InventoryItemAppliedVariantOption = {
  name: string;
  value: string;
};

export type InventoryItemHistory = {
  balance: number;
  committedTo: InventoryItemHistoryCommitted[];
  criticalOnHand: number;
  currentStock: number;
  incomingOrders: InventoryItemHistoryIncoming[];
  incomingStock: number;
  reservedStock: number;
  stockHistory: InventoryItemHistoryStock[];
};

export type InventoryItemHistoryCommitted = {
  customer: Id;
  manufacturingOrder: Id;
  quantity: number;
  salesOrder: Id;
};

export type InventoryItemHistoryIncoming = {
  expectedBy: string;
  manufacturingOrder: Id;
  price: Currency;
  purchaseOrder: Id;
  quantity: number;
  supplier: Id;
};

export type InventoryItemHistoryStock = {
  averageCostAfter: Currency;
  createTime: string;
  document?: Id & {type: InventoryHistoryDocumentType};
  id: string;
  inventoryAdjustmentOrder: Id;
  manufacturingOrder: Id;
  price: Currency;
  purchaseOrder: Id;
  quantity: number;
  salesOrder: Id;
  servicingOrder: Id;
  stockAfter: number;
};

export type InventoryItemManufacturingOperation = Id & {
  candidateEmployees: Id[];
  candidateWorkstations: Id[];
  costPerHour: Currency | null;
  durationInMinutes: number;
  materials: InventoryItemRequiredMaterial[] | null;
  operationTemplateId: null | string;
  parallelizable: boolean;
};

export type InventoryItemOption = Id & {
  available: boolean;
  category: Id;
  code: string;
  cost: Currency;
  lastOrderedFrom: Id | null;
  material: MaterialDimension;
  measurementUnit: Unit;
  produced: boolean;
};

export type InventoryItemRequiredMaterial = {
  category: Id;
  configurableWithOptions: boolean;
  optional: boolean;
  options: InventoryItemOption[];
  quantity: number;
  replaceableWithOptions: boolean;
  requiredDimensions: null | {length: number; width?: number};
  wastePercentage: number;
};

export type InventoryItemVariantOption = {
  name: string;
  values: string[];
};

export type InventoryMaterialGood = Id & {
  available: number;
  balance: number;
  category: Id;
  code: string;
  committed: number;
  cost: Currency;
  criticalOnHand: number;
  description: string;
  incoming: number;
  inventoryUnit: Id;
  lastOrderedFrom: Id | null;
  margin: number;
  materialGoodId: string;
  measurementUnit: Unit;
  name: string;
  produced: boolean;
  profit: Currency;
  quantity: number;
  sellingPrice: Currency;
  stockValue: Currency;
  unitOfProduction?: number;
};

export type InventoryReceiptItem = SimplifiedReceptionItem & {
  materialGood: Id;
};

export type InventoryReception = SimplifiedReception & {
  goods: InventoryReceiptItem[];
};

export type InventoryStats = {
  count?: number;
  potentialProfit?: Currency;
  potentialRevenue?: Currency;
  value?: Currency;
};

export type InventoryUnit = Id & {
  deleted: boolean;
};

export type LightInventoryItem = Id & {
  category: {id: string};
  code: string;
  measurementUnit: Unit;
};

export type MaterialDimension = null | {
  dimensions: {
    dimensionKey: string;
    name: string;
    value: number;
  }[];
  material: {
    density: number;
    key: string;
    name: string;
  };
  shape: {
    name: string;
    shape: string;
  };
};

export type MaterialGood = {
  available: number;
  balance: number;
  category: Id;
  code: string;
  committed: number;
  cost: Currency;
  criticalOnHand: number;
  incoming: number;
  lastOrderedFrom: Id | null;
  margin: number;
  materialGoodId: string;
  measurementUnit: Unit;
  name: string;
  produced: boolean;
  profit: Currency;
  quantity: number;
  sellingPrice: Currency;
  stockValue: Currency;
};
