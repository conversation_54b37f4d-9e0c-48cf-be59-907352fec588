export enum CardSize {
  LARGE = 'large',
  MEDIUM = 'medium',
  SMALL = 'small',
}

export type ChartProps = {
  segments: ChartSegment[];
  showTooltip?: boolean;
  title?: string;
};

export type ChartSegment = {
  color: string;
  label: string;
  value: number;
};

export interface DashboardCardConfig {
  defaultSize?: CardSize;
  id: string;
  sizes: Partial<Record<CardSize, SizeRepresentation>>;
  title: string;
}

export type ReactGridLayoutItem = SizeRepresentation & {
  i: string;
  size: CardSize;
  type: string;
  x: number;
  y: number;
};

export type SizeRepresentation = {
  h: number;
  w: number;
};

export type StoredLayoutItem = {
  i: string; // Unique ID for the widget instance
  size: CardSize;
  type: string; // Card type (matches the card ID in CARD_COMPONENTS_MAP)
  x: number;
  y: number;
};

/*
| Size Category | w   | h   | Width (px) | Height (px) | Inner Width | Inner Height |
┌───────────────┬─────┬─────┬────────────┬─────────────┬──────────────┬──────────────┐
│ Small         │ 2   │ 2   │ 280        │ 170         │ 248          │ 138          │
│ Small         │ 2   │ 3   │ 280        │ 255         │ 248          │ 223          │
│ Small         │ 3   │ 2   │ 420        │ 170         │ 388          │ 138          │
│ Small         │ 3   │ 3   │ 420        │ 255         │ 388          │ 223          │
│ Small         │ 4   │ 2   │ 560        │ 170         │ 528          │ 138          │
│ Small         │ 4   │ 3   │ 560        │ 255         │ 528          │ 223          │
│ Medium        │ 6   │ 3   │ 840        │ 255         │ 808          │ 223          │
│ Medium        │ 6   │ 4   │ 840        │ 340         │ 808          │ 308          │
│ Large         │ 6   │ 6   │ 840        │ 510         │ 808          │ 478          │
│ Large         │ 8   │ 6   │ 1120       │ 510         │ 1088         │ 478          │
│ Large         │ 12  │ 6   │ 1680       │ 510         │ 1648         │ 478          │
└───────────────┴─────┴─────┴────────────┴─────────────┴──────────────┴──────────────┘
 */
