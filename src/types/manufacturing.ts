import {MaterialDimension} from '@/types/inventory';
import {
  Currency,
  CustomFile,
  GoodsAccompanyingNote,
  Id,
  ManufacturingStatus,
  ManufacturingTaskStatus,
  ManufacturingTaskStatusReason,
  ServicingStatus,
  ServicingTaskStatus,
  SimplifiedConsumption,
  SimplifiedConsumptionMaterial,
  SimplifiedOrder,
  Unit,
} from 'types/global';

export enum EmployeeActiveTimeOffType {
  BUSINESS = 'BUSINESS',
  PTO = 'PTO',
  UNDEFINED = 'UNDEFINED',
}

export enum MyTaskType {
  MANUFACTURING_TASK = 'MANUFACTURING_TASK',
  SERVICE = 'SERVICE',
}

export type Consumption = SimplifiedConsumption & {
  materials: ConsumptionMaterial[];
};

export type ConsumptionMaterial = SimplifiedConsumptionMaterial & {
  code: string;
  measurementUnit: Unit;
  name: string;
};

export type Employee = Id & {
  activeTimeoffs: EmployeeActiveTimeOff[];
  hourlyRate: Currency;
  isDevicePaired: boolean;
  manufacturingOperationTemplates: (Id & {
    preferential: boolean;
  })[];
  monthlyGrossSalary: Currency;
  position: string;
};

export type EmployeeActiveTimeOff = {
  customer: Id | null;
  endTime: string;
  id: string;
  servicingOrder: Id | null;
  startTime: string;
  type: EmployeeActiveTimeOffType | null;
};

export type EmployeePairing = {
  pairingLink: string;
  token: string;
};

export type ManufacturingOrder = SimplifiedOrder & {
  allMaterialsAvailable: boolean;
  category: Id;
  customer: Id | null;
  customProduct?: boolean;
  deliveryDeadline: string;
  estimatedCompletionDate: string;
  files: CustomFile[];
  manufacturedProducts: {
    date: string;
    quantity: number;
  }[];
  manufacturingOperations: ManufacturingOrderOperationStep[];
  manufacturingTasks: ManufacturingOrderTask[];
  materialIssueNote: (Id & {number: string}) | null;
  materials: ManufacturingOrderMaterial[];
  measurementUnit: Unit;
  notes: null | string;
  parent: Id | null;
  product: Id & {
    code: string;
    employeeAndWorkstationCosts?: Currency;
    manufacturingOverheadCosts?: Currency;
    materialCosts?: Currency;
    measurementUnit: string;
  };
  productionDeadline: null | string;
  productionTime: number;
  quantity: number;
  ranking: number;
  saleOrderId: null | string;
  status: ManufacturingStatus;
  totalCost: Currency;
};

export type ManufacturingOrderMaterial = {
  allAvailable: boolean | null;
  cost: Currency;
  materialGoods: ManufacturingOrderMaterialGood[];
  onStock: number;
  required: number;
  requiredDimensions: null | {length: number; width?: number};
  requiredTotal: number;
  reservedTotal: number;
  subassemblyManufacturingOrder: null | {
    id: string;
    number: string;
    status: ManufacturingStatus;
  };
  totalRequiredDimensions: null | {length: number; width?: number};
  wastePercentage: number;
};

export type ManufacturingOrderMaterialGood = Id & {
  available: boolean;
  category: Id;
  code: string;
  cost: Currency;
  inventoryCostPerItem: Currency;
  lastOrderedFrom: Id | null;
  material: MaterialDimension;
  measurementUnit: Id;
  produced: boolean;
};

export type ManufacturingOrderOperationStep = OperationStep & {
  materials: ManufacturingOrderMaterial[];
};

export type ManufacturingOrderTask = Task & {
  materials: ManufacturingOrderMaterial[];
};

export type ManufacturingServiceMaterial = {
  cost: Currency;
  isNew?: boolean;
  materialGoods: (Id & {
    code: string;
    measurementUnit: string;
    produced: boolean;
  })[];
  measurementUnit: Unit;
  required: number;
  usedQuantity: null | number;
};

export type MyTask = Id & {
  durationInMinutes: number;
  number: string;
  parentId: null | string;
  startTime: string;
  status: ManufacturingStatus | ManufacturingTaskStatus | ServicingStatus | ServicingTaskStatus;
  statusReason: ManufacturingTaskStatusReason;
  target: Id;
  type: MyTaskType;
};

export type Operation = Id & {
  cost: Currency | null;
  costPerHour: Currency | null;
  employees: (Id & {
    preferential: boolean;
  })[];
  workstations: Workstation[];
};

export type OperationStep = {
  candidateEmployees: Id[];
  candidateWorkstations: Id[];
  costPerHour: Currency | null;
  durationInMinutes: number;
  id: string;
  name: string;
  parallelizable: boolean;
};

export type ServicingMaterial = {
  allAvailable: boolean | null;
  cost: Currency;
  inventoryCostPerItem: Currency;
  materialGoods: ServicingMaterialGood[];
  measurementUnit: Unit;
  onStock: number;
  produced: boolean;
  required: number;
  requiredDimensions: null | {length: number; width?: number};
  requiredTotal: number;
  reservedTotal: number;
  totalRequiredDimensions: null | {length: number; width?: number};
  wastePercentage: number;
};

export type ServicingMaterialGood = Id & {
  code: string;
  lastOrderedFrom: Id | null;
  material: MaterialDimension;
  measurementUnit: string;
  produced: boolean;
};

export type ServicingOrder = SimplifiedOrder & {
  assignedTo: Id | null;
  customer: Id | null;
  files: CustomFile[];
  goodsAccompanyingNotes: GoodsAccompanyingNote[];
  manufacturingOperations: OperationStep[];
  materialIssueNote: (Id & {number: string}) | null;
  materials: ManufacturingServiceMaterial[];
  measurementUnit: Unit;
  notes: null | string;
  productionDeadline: string;
  productionTime: number;
  quantity: number;
  ranking: number;
  saleOrderId: null | string;
  service: Id & {
    employeeAndWorkstationCosts?: Currency;
    manufacturingOverheadCosts?: Currency;
    materialCosts?: Currency;
    measurementUnit: string;
  };
  status: ServicingStatus;
  totalCost: Currency;
};

export type Task = Id & {
  actualDurationInMinutes: null | number;
  assignedEmployees: Id[];
  assignedWorkstations: Id[];
  costPerHour: Currency | null;
  durationInMinutes: number;
  endTime: string;
  estimatedEndTime: string;
  estimatedStartTime: string;
  manuallyAssigned: boolean;
  measurementUnit: Unit;
  number: string;
  numberOfAssignees: number;
  order: {id: string; number: string; ranking: number};
  orderEndTime: string;
  orderStartTime: string;
  product: Id;
  quantity: number;
  startTime: string;
  status: ManufacturingTaskStatus;
  statusReason: ManufacturingTaskStatusReason;
};

export type Workstation = Id & {
  costPerHour: Currency;
  manufacturingOperationTemplates: Id[];
};
