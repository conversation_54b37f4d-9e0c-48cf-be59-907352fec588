type DotNestedKeys<T> = {
  [K in keyof T & string]: T[K] extends Record<string, unknown>
    ?
      | `${K}`
      | `${K}.${DotNestedKeys<T[K]>}`
    : K
}[keyof T & string];

// // Import settings-specific messages
// import enSettings from '../../messages/settings/en.json';
// import huSettings from '../../messages/settings/hu.json';
// import roSettings from '../../messages/settings/ro.json';
//
// // Import hooks-specific messages
// import enHooks from '../../messages/hooks/en.json';
// import huHooks from '../../messages/hooks/hu.json';
// import roHooks from '../../messages/hooks/ro.json';
//
// // Import reports-specific messages
// import enReports from '../../messages/reports/en.json';
// import huReports from '../../messages/reports/hu.json';
// import roReports from '../../messages/reports/ro.json';
//
// // Import customers-specific messages
// import enCustomers from '../../messages/customers/en.json';
// import huCustomers from '../../messages/customers/hu.json';
// import roCustomers from '../../messages/customers/ro.json';
//
// // Import employees-specific messages
// import enEmployees from '../../messages/employees/en.json';
// import huEmployees from '../../messages/employees/hu.json';
// import roEmployees from '../../messages/employees/ro.json';
//
// // Import invoices-specific messages
// import enInvoices from '../../messages/invoices/en.json';
// import huInvoices from '../../messages/invoices/hu.json';
// import roInvoices from '../../messages/invoices/ro.json';
//
// // Import suppliers-specific messages
// import enSuppliers from '../../messages/suppliers/en.json';
// import huSuppliers from '../../messages/suppliers/hu.json';
// import roSuppliers from '../../messages/suppliers/ro.json';
//
// // Import inventory items-specific messages
// import enInventoryItems from '../../messages/inventory/items/en.json';
// import huInventoryItems from '../../messages/inventory/items/hu.json';
// import roInventoryItems from '../../messages/inventory/items/ro.json';
//
// // Import inventory adjustments-specific messages
// import enInventoryAdjustments from '../../messages/inventory/adjustments/en.json';
// import huInventoryAdjustments from '../../messages/inventory/adjustments/hu.json';
// import roInventoryAdjustments from '../../messages/inventory/adjustments/ro.json';
//
// // Import inventory consumptions-specific messages
// import enInventoryConsumptions from '../../messages/inventory/consumptions/en.json';
// import huInventoryConsumptions from '../../messages/inventory/consumptions/hu.json';
// import roInventoryConsumptions from '../../messages/inventory/consumptions/ro.json';
//
// // Import inventory receptions-specific messages
// import enInventoryReceptions from '../../messages/inventory/receptions/en.json';
// import huInventoryReceptions from '../../messages/inventory/receptions/hu.json';
// import roInventoryReceptions from '../../messages/inventory/receptions/ro.json';
//
// // Import manufacturing orders-specific messages
// import enManufacturingOrders from '../../messages/manufacturing/orders/en.json';
// import huManufacturingOrders from '../../messages/manufacturing/orders/hu.json';
// import roManufacturingOrders from '../../messages/manufacturing/orders/ro.json';
//
// // Import manufacturing services-specific messages
// import enManufacturingServices from '../../messages/manufacturing/services/en.json';
// import huManufacturingServices from '../../messages/manufacturing/services/hu.json';
// import roManufacturingServices from '../../messages/manufacturing/services/ro.json';
//
// // Import manufacturing planning-specific messages
// import enManufacturingPlanning from '../../messages/manufacturing/planning/en.json';
// import huManufacturingPlanning from '../../messages/manufacturing/planning/hu.json';
// import roManufacturingPlanning from '../../messages/manufacturing/planning/ro.json';
//
// // Import manufacturing tasks-specific messages
// import enManufacturingTasks from '../../messages/manufacturing/tasks/en.json';
// import huManufacturingTasks from '../../messages/manufacturing/tasks/hu.json';
// import roManufacturingTasks from '../../messages/manufacturing/tasks/ro.json';
//
// // Import sales-specific messages
// import enSales from '../../messages/sales/en.json';
// import huSales from '../../messages/sales/hu.json';
// import roSales from '../../messages/sales/ro.json';
//
// // Import purchases orders-specific messages
// import enPurchasesOrders from '../../messages/purchases/en.json';
// import huPurchasesOrders from '../../messages/purchases/hu.json';
// import roPurchasesOrders from '../../messages/purchases/ro.json';

// Import global messages
import enGlobal from '../../messages/en.json';
import huGlobal from '../../messages/hu.json';
import roGlobal from '../../messages/ro.json';

// // Define message types for settings-specific messages
// type EnSettingsMessages = typeof enSettings;
// type HuSettingsMessages = typeof huSettings;
// type RoSettingsMessages = typeof roSettings;
//
// // Define message types for reports-specific messages
// type EnReportsMessages = typeof enReports;
// type HuReportsMessages = typeof huReports;
// type RoReportsMessages = typeof roReports;
//
// // Define message types for customers-specific messages
// type EnCustomersMessages = typeof enCustomers;
// type HuCustomersMessages = typeof huCustomers;
// type RoCustomersMessages = typeof roCustomers;
//
// // Define message types for employees-specific messages
// type EnEmployeesMessages = typeof enEmployees;
// type HuEmployeesMessages = typeof huEmployees;
// type RoEmployeesMessages = typeof roEmployees;
//
// // Define message types for invoices-specific messages
// type EnInvoicesMessages = typeof enInvoices;
// type HuInvoicesMessages = typeof huInvoices;
// type RoInvoicesMessages = typeof roInvoices;
//
// // Define message types for suppliers-specific messages
// type EnSuppliersMessages = typeof enSuppliers;
// type HuSuppliersMessages = typeof huSuppliers;
// type RoSuppliersMessages = typeof roSuppliers;
//
// // Define message types for inventory items-specific messages
// type EnInventoryItemsMessages = typeof enInventoryItems;
// type HuInventoryItemsMessages = typeof huInventoryItems;
// type RoInventoryItemsMessages = typeof roInventoryItems;
//
// // Define message types for inventory adjustments-specific messages
// type EnInventoryAdjustmentsMessages = typeof enInventoryAdjustments;
// type HuInventoryAdjustmentsMessages = typeof huInventoryAdjustments;
// type RoInventoryAdjustmentsMessages = typeof roInventoryAdjustments;
//
// // Define message types for inventory consumptions-specific messages
// type EnInventoryConsumptionsMessages = typeof enInventoryConsumptions;
// type HuInventoryConsumptionsMessages = typeof huInventoryConsumptions;
// type RoInventoryConsumptionsMessages = typeof roInventoryConsumptions;
//
// // Define message types for inventory receptions-specific messages
// type EnInventoryReceptionsMessages = typeof enInventoryReceptions;
// type HuInventoryReceptionsMessages = typeof huInventoryReceptions;
// type RoInventoryReceptionsMessages = typeof roInventoryReceptions;
//
// // Define message types for manufacturing orders-specific messages
// type EnManufacturingOrdersMessages = typeof enManufacturingOrders;
// type HuManufacturingOrdersMessages = typeof huManufacturingOrders;
// type RoManufacturingOrdersMessages = typeof roManufacturingOrders;
//
// // Define message types for manufacturing services-specific messages
// type EnManufacturingServicesMessages = typeof enManufacturingServices;
// type HuManufacturingServicesMessages = typeof huManufacturingServices;
// type RoManufacturingServicesMessages = typeof roManufacturingServices;
//
// // Define message types for manufacturing planning-specific messages
// type EnManufacturingPlanningMessages = typeof enManufacturingPlanning;
// type HuManufacturingPlanningMessages = typeof huManufacturingPlanning;
// type RoManufacturingPlanningMessages = typeof roManufacturingPlanning;
//
// // Define message types for manufacturing tasks-specific messages
// type EnManufacturingTasksMessages = typeof enManufacturingTasks;
// type HuManufacturingTasksMessages = typeof huManufacturingTasks;
// type RoManufacturingTasksMessages = typeof roManufacturingTasks;
//
// // Define message types for sales-specific messages
// type EnSalesMessages = typeof enSales;
// type HuSalesMessages = typeof huSales;
// type RoSalesMessages = typeof roSales;
//
// // Define message types for purchases orders-specific messages
// type EnPurchasesOrdersMessages = typeof enPurchasesOrders;
// type HuPurchasesOrdersMessages = typeof huPurchasesOrders;
// type RoPurchasesOrdersMessages = typeof roPurchasesOrders;
// // Define message types for hooks messages
// type EnHooksMessages = typeof enHooks;
// type HuHooksMessages = typeof huHooks;
// type RoHooksMessages = typeof roHooks;

// Define message types for global messages
type EnGlobalMessages = typeof enGlobal;
type HuGlobalMessages = typeof huGlobal;
type RoGlobalMessages = typeof roGlobal;

// Combine message types to include all possible keys
type EnMessages =
  // EnSettingsMessages &
  // EnReportsMessages &
  // EnCustomersMessages &
  // EnEmployeesMessages &
  // EnInvoicesMessages &
  // EnSuppliersMessages &
  // EnInventoryItemsMessages &
  // EnInventoryAdjustmentsMessages &
  // EnInventoryConsumptionsMessages &
  // EnInventoryReceptionsMessages &
  // EnManufacturingOrdersMessages &
  // EnManufacturingServicesMessages &
  // EnManufacturingPlanningMessages &
  // EnManufacturingTasksMessages &
  // EnSalesMessages &
  // EnPurchasesOrdersMessages &
  EnGlobalMessages;
type HuMessages =
  // HuSettingsMessages &
  // HuReportsMessages &
  // HuCustomersMessages &
  // HuEmployeesMessages &
  // HuInvoicesMessages &
  // HuSuppliersMessages &
  // HuInventoryItemsMessages &
  // HuInventoryAdjustmentsMessages &
  // HuInventoryConsumptionsMessages &
  // HuInventoryReceptionsMessages &
  // HuManufacturingOrdersMessages &
  // HuManufacturingServicesMessages &
  // HuManufacturingPlanningMessages &
  // HuManufacturingTasksMessages &
  // HuSalesMessages &
  // HuPurchasesOrdersMessages &
  HuGlobalMessages;
type RoMessages =
  // RoSettingsMessages &
  // RoReportsMessages &
  // RoCustomersMessages &
  // RoEmployeesMessages &
  // RoInvoicesMessages &
  // RoSuppliersMessages &
  // RoInventoryItemsMessages &
  // RoInventoryAdjustmentsMessages &
  // RoInventoryConsumptionsMessages &
  // RoInventoryReceptionsMessages &
  // RoManufacturingOrdersMessages &
  // RoManufacturingServicesMessages &
  // RoManufacturingPlanningMessages &
  // RoManufacturingTasksMessages &
  // RoSalesMessages &
  // RoPurchasesOrdersMessages &
  RoGlobalMessages;

type EnKeys = DotNestedKeys<EnMessages>;
type HuKeys = DotNestedKeys<HuMessages>;
type RoKeys = DotNestedKeys<RoMessages>;

type CommonKeys = EnKeys & HuKeys & RoKeys;

declare module 'next-intl' {
  export function useTranslations(): <K extends CommonKeys>(
    key: K,
    ...args: any[]
  ) => string;
}
