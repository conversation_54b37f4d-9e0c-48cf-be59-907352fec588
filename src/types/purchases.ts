import {
  Currency,
  DocumentLocale,
  Id,
  PurchaseStatus,
  SimplifiedOrder,
  SimplifiedReception,
  SimplifiedReceptionItem,
  Unit,
} from 'types/global';

export enum ReceptionAdditionalCostAllocationStrategy {
  BY_QUANTITY = 'BY_QUANTITY',
  BY_VALUE = 'BY_VALUE',
  CUSTOM = 'CUSTOM',
}

export enum ReceptionAdditionalCostType {
  TRANSPORTATION = 'TRANSPORTATION',
}

export enum SupportingDocumentType {
  ACCOMPANYING_LETTER = 'ACCOMPANYING_LETTER',
  INVOICE = 'INVOICE',
  NONE = 'NONE',
}

export type PurchaseOrder = SimplifiedOrder & {
  deliveredAt: string;
  expectedDelivery: string;
  items: PurchaseOrderItem[];
  managedBy: Id;
  receptionReceipt: Id;
  renderingDetails?: PurchaseRenderingDetails;
  status: PurchaseStatus;
  supplier: Id;
  totalAmount: Currency;
};

export type PurchaseOrderItem = {
  expectedDelivery: string;
  materialGood: Id & {code: string};
  measurementUnit: Unit;
  price: Currency;
  quantity: number;
};

export type PurchaseRenderingDetails = {
  columnsToHide?: ('PRICE' | 'QUANTITY')[];
  language?: DocumentLocale;
  title?: string;
};

export type Reception = SimplifiedReception & {
  additionalCosts: ReceptionAdditionalCost[];
  goods: ReceptionItem[];
};

export type ReceptionAdditionalCost = {
  allocationStrategy: ReceptionAdditionalCostAllocationStrategy;
  code: string;
  description: string;
  price: Currency;
  quantity: number;
  type: ReceptionAdditionalCostType;
  vat: number;
};

export type ReceptionItem = SimplifiedReceptionItem & {
  inventoryUnit: Id;
  materialGood: Id & {code: string};
  measurementUnit: Id;
  totalValue: Currency;
};

export type Wishlist = Id & {
  lastOrderDeliveredIn: number;
  renderingDetails?: PurchaseRenderingDetails;
  wishlistItems: WishlistItem[];
};

export type WishlistItem = {
  expectedDelivery?: string;
  id: string;
  materialGood: Id & {code: string};
  measurementUnit: Unit;
  price?: Currency;
  quantity: number;
  supplier: Id;
  totalPrice?: Currency;
};

export type WishlistLastOrderedGoods = Id & {
  code: string;
  committed: number;
  criticalOnHand: number;
  incoming: number;
  measurementUnit: Id;
  price: Currency;
  quantity: number;
  stock: number;
};
