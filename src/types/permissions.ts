import {MongoAbility} from '@casl/ability';

export type Actions = 'financial' | 'read' | 'update';
export type Permission = {
  action: Actions;
  subject: Subjects;
};

export type PermissionsAbility = MongoAbility<[Actions, Subjects]>;

export type Subjects =
  | 'customers'
  | 'employees'
  | 'inventory'
  | 'invoices'
  | 'manufacturing'
  | 'purchases'
  | 'reports'
  | 'sales'
  | 'settings'
  | 'suppliers';
