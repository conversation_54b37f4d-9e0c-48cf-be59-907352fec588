import {
  Address,
  BankAccount,
  Contact,
  Currency,
  CustomFile,
  DocumentLocale,
  GoodsAccompanyingNote,
  Id,
  InvoiceStatus,
  ManufacturingStatus,
  SaleItemStatus,
  SaleStatus,
  ServicingStatus,
  SimplifiedOrder,
  Unit,
} from 'types/global';

export enum CustomerType {
  FOREIGN_LEGAL_ENTITY = 'FOREIGN_LEGAL_ENTITY',
  INDIVIDUAL = 'INDIVIDUAL',
  LOCAL_LEGAL_ENTITY = 'LOCAL_LEGAL_ENTITY',
}

export enum SaleType {
  ORDER = 'orders',
  QUOTE = 'quotes',
}

export enum SaleView {
  active = 'active',
  canceled = 'canceled',
  delivered = 'delivered',
}

export type Customer = Client & {type: CustomerType};

export type Invoice = {
  creationDate: string;
  customer: Customer;
  dueDate: string;
  id: string;
  items: InvoiceItem[];
  number: string;
  sentAt: string;
  status: InvoiceStatus;
  subTotal: Currency;
  supplier: Supplier;
  total: Currency;
  vat: Currency;
};

export type InvoiceItem = {
  name: string;
  productId: string;
  quantity: number;
  unitPrice: Currency;
};

export type InvoiceRenderingDetails = {
  columnsToHide?: 'PRICE'[];
  language?: DocumentLocale;
  title?: string;
};

export type SaleItem = {
  addedToWishlist: boolean;
  administrativeOverheadCosts: Currency;
  code?: null | string;
  customizable: boolean;
  customizationNote?: null;
  customizations: null | SaleItemCustomization[];
  discount: number;
  discountAmount: Currency;
  expectedBy?: null | string;
  laborCosts: Currency;
  lastPurchase?: null | {
    price: Currency;
    supplierId: string;
    supplierName: string;
  };
  manufacturingOrder?: null | {
    customProduct: boolean;
    id: string;
    number: string;
    orderedQuantity?: number;
    status: ManufacturingStatus;
  };
  materialCosts: Currency;
  measurementUnit: Unit;
  name: string;
  originalQuantity?: number;
  price: Currency;
  produced?: boolean | null;
  productId?: null | string;
  purchaseOrder?: Id | null;
  quantity?: number;
  reserved?: null | number;
  serviceId?: null | string;
  servicingOrderDetails?: null | {
    id: string;
    number: string;
    orderedQuantity?: number;
    status: ServicingStatus;
  };
  status: SaleItemStatus;
  taxAmount: Currency;
  totalAmount: Currency;
  variant?: null | string;
  vatRate: null | number;
};

export type SaleItemCustomization = {
  materialId: string;
  quantity: number;
};

export type SaleOrder = SimplifiedOrder & {
  availability: number;
  customer: Id;
  customerNotes: string;
  deliveryDeadline: string;
  discountAmount: Currency;
  files: CustomFile[];
  goodsAccompanyingNotes: GoodsAccompanyingNote[];
  invoice: Id & {number: string; sentAt: string};
  isQuotation: boolean;
  items: SaleItem[];
  notes: null | string;
  numberOfAvailableItems: number;
  numberOfItems: number;
  offerDate: string;
  offerExpiration?: string;
  proformaInvoice: Id & {number: string; sentAt: string};
  shippingAddress: Address;
  status: SaleStatus;
  submittedDate: string;
  subTotalAmount: Currency;
  taxAmount: Currency;
  totalAmount: Currency;
  versions: CustomFile[];
};

export type SalesRenderingDetails = {
  columnsToHide?: 'DISCOUNT'[];
  language?: DocumentLocale;
  title?: string;
};

export type Supplier = Client & {lastOrderDeliveredIn: null | number};

type Client = Id & {
  addresses: Address[];
  bankAccounts: BankAccount[];
  contacts: Contact[];
  files: CustomFile[];
  identificationNumber: string;
  lifetimeValue: Currency;
  notes: string;
  taxIdentificationNumber: string;
};
