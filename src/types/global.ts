import {FC, ReactNode} from 'react';

import {NextPage} from 'next';
import type {DateRange as ReactDatePickerRange} from 'react-day-picker';

import {documentLocales, locales} from '@/constants/config';
import {SupportingDocumentType} from 'types/purchases';

export enum ActivityType {
  CUSTOMER = 'customers',
  MANUFACTURING = 'manufacturing/orders',
  PURCHASE = 'purchases/orders',
  SALE = 'sales/orders',
  SERVICING = 'servicing/orders',
}

export enum AddressType {
  BILLING = 'BILLING',
  OTHER = 'OTHER',
  SHIPPING = 'SHIPPING',
}

export type {AppProps} from 'next/app';
export enum InvoiceStatus {
  DUE = 'DUE',
  OVERDUE = 'OVERDUE',
  PAID = 'PAID',
}

export enum ManufacturingStatus {
  CUSTOMIZATION_NEEDED = 'CUSTOMIZATION_NEEDED',
  DONE = 'DONE',
  MANUFACTURED = 'MANUFACTURED',
  MANUFACTURING = 'MANUFACTURING',
  SUBMITTED = 'SUBMITTED',
}

export enum ManufacturingTaskStatus {
  DONE = 'DONE',
  IN_PROGRESS = 'IN_PROGRESS',
  STOPPED = 'STOPPED',
  TODO = 'TODO',
}

export enum ManufacturingTaskStatusReason {
  BROKEN_EQUIPMENT = 'BROKEN_EQUIPMENT',
  EQUIPMENT_UNAVAILABLE = 'EQUIPMENT_UNAVAILABLE',
  MISSING_MATERIAL = 'MISSING_MATERIAL',
  PAUSED = 'PAUSED',
}

export enum PurchaseStatus {
  DELIVERED = 'DELIVERED',
  SUBMITTED = 'SUBMITTED',
}

export enum SaleItemStatus {
  NEED_SUPPLY = 'NEED_SUPPLY',
  READY = 'READY',
}

export enum SaleStatus {
  CANCELED = 'CANCELED',
  DELIVERED = 'DELIVERED',
  IN_QUOTATION = 'IN_QUOTATION',
  PROCESSING = 'PROCESSING',
  READY_TO_SHIP = 'READY_TO_SHIP',
  SHIPPING = 'SHIPPING',
  SUBMITTED = 'SUBMITTED',
}

export enum ServicingStatus {
  DONE = 'DONE',
  MANUFACTURED = 'MANUFACTURED',
  MANUFACTURING = 'MANUFACTURING',
  SUBMITTED = 'SUBMITTED',
}

export enum ServicingTaskStatus {
  DONE = 'DONE',
  IN_PROGRESS = 'IN_PROGRESS',
  STOPPED = 'STOPPED',
  TODO = 'TODO',
}

export enum ServicingTaskStatusReason {
  BROKEN_EQUIPMENT = 'BROKEN_EQUIPMENT',
  EQUIPMENT_UNAVAILABLE = 'EQUIPMENT_UNAVAILABLE',
  MISSING_MATERIAL = 'MISSING_MATERIAL',
  PAUSED = 'PAUSED',
}

export type AccompanyingNote = {
  createTime: string;
  customer: Id;
  delegate: Id;
  deliveryDate: string;
  from: Address | null;
  id: string;
  items: AccompanyingNoteItem[];
  notes: string;
  number: string;
  salesOrder: Id;
  servicingOrder: Id;
  to: Address | null;
  transportRegistrationNumber: string;
};

export type AccompanyingNoteItem = Id & {
  discount: number;
  quantity: number;
  unitPrice: Currency;
};

export type Activity = {
  addedBy: Id;
  createTime: string;
  id: string;
  note: string;
};

export type Address = {
  address1: string;
  address2: string;
  city: string;
  country: string;
  name: string;
  state: string;
  types: AddressType[];
  zip: string;
};

export type AllOrNone<T> = T | {[K in keyof T]?: never};

export type AtLeastOne<T, Keys extends keyof T = keyof T> = Pick<T, Exclude<keyof T, Keys>> &
  {
    [K in Keys]-?: Partial<Pick<T, Exclude<Keys, K>>> & Required<Pick<T, K>>;
  }[Keys];

export type AtMostOneOf<T> = NoneOf<T> | {[K in keyof T]: NoneOf<Omit<T, K>> & Pick<T, K>}[keyof T];

export type BankAccount = {
  bank: string;
  name: string;
  number: string;
  swiftNumber: string;
};

export type ConfigCountry = {
  code: string;
  name: string;
};

export type ConfigCurrency = {
  displayName: string;
  symbol: string;
};

export type Contact = {
  email: string;
  name: string;
  phoneNumber: string;
};

export type Currency = {
  amount: number;
  currency?: string;
};

export type CustomFile = Id & {
  downloadLink: string;
  previewLink: string;
};

export type DateRange = ReactDatePickerRange;

export type DeepPartial<T> = T extends object
  ? {
      [P in keyof T]?: DeepPartial<T[P]>;
    }
  : T;

export type DimensionShape = {
  dimensions: DimensionShapeDimension[];
  isRestrictedToSteels: boolean;
  name: string;
  type: string;
};

export type DimensionShapeDimension = {
  key: string;
  name: string;
  standardValues: number[];
};

export type DocumentLocale = (typeof documentLocales)[number];

export type ErrorMessage = {
  code: string;
  message: string;
  userMessage: string;
};

export type ExactlyOne<T> = AtLeastOne<T> & AtMostOneOf<T>;

export type ExchangeRates = {
  [key: string]: number;
};

export type GoodsAccompanyingNote = {
  id: string;
  items: AccompanyingNoteItem[];
  number: string;
};

export type Id = {id: string; name: string};

export type Locale = (typeof locales)[number];

export type Mention = {
  end: number;
  id: string;
  name: string;
  start: number;
};

export type NextPageWithLayout<T = {}> = NextPage<T> & {
  Layout?: FC<{children: ReactNode}>;
};

export type NoneOf<T> = {[K in keyof T]?: never};

export type Optional<T, K extends keyof T> = Omit<T, K> & Pick<Partial<T>, K>;

export type Require<T, K extends keyof T> = Omit<T, K> & Required<Pick<T, K>>;

export type Service = Id & {
  cost: Currency | null;
  measurementUnit: string;
  sellPrice: Currency | null;
  vatRate: number;
};

export type SimplifiedConsumption = {
  date: string;
  inventoryManager: string;
  worker: string;
};

export type SimplifiedConsumptionMaterial = {
  id: string;
  quantity: number;
};

export type SimplifiedItem = Id & {
  code: string;
  lastOrderedFrom: Id | null;
  measurementUnit: Unit;
  quantity: number;
  reserved: number;
};

export type SimplifiedOrder = {
  createTime: string;
  id: string;
  number: string;
  updateTime: string;
};

export type SimplifiedReception = {
  currency: string;
  files: CustomFile[];
  id: string;
  notes: string;
  number: string;
  purchaseOrder: Id;
  receivedBy: string;
  receptionDate: string;
  supplier?: Id | null;
  supportingDocument: {
    date: string;
    number: string;
    type: SupportingDocumentType;
  };
  transportedBy: string;
  transportedWith: string;
};

export type SimplifiedReceptionItem = {
  additionalCostCustomValue: Currency;
  calculatedAdditionalCost: Currency;
  inventoryUnit: Id;
  orderedQuantity: number;
  price: Currency;
  receivedQuantity: number;
  vat: number;
};

export type Unit = Id;

export type Value = {
  className: string;
  count: number;
  disabled: boolean;
  download: boolean;
  element: ReactNode;
  error: boolean;
  href: string;
  id: string;
  selected: boolean;
  value: string;
  warning: boolean;
};
