import {FC} from 'react';

import {ResourceApi} from '@fullcalendar/resource';
import {useTranslations} from 'next-intl';

import NoEnterIcon from '@/assets/NoEnterIcon';
import WarningIcon from '@/assets/WarningIcon';
import {Pill, WithLink} from 'oldcomponents';
import {EmployeeActiveTimeOff} from 'types/manufacturing';
import {classes} from 'utils/common';
import {formatDate} from 'utils/format';

type ResourceLabelType = {
  deadline?: string;
  isOrder?: boolean;
  percentage?: number;
  resource: ResourceApi;
  timeOffs?: EmployeeActiveTimeOff[];
};

const ResourceLabel: FC<ResourceLabelType> = ({deadline, isOrder, percentage, resource, timeOffs}) => {
  const t = useTranslations();

  return (
    <div
      className={classes(
        'flex h-10 items-center justify-between gap-1 text-sm font-medium',
        isOrder ? 'w-[163px]' : 'w-[185px]',
      )}
    >
      <div className='inline-flex gap-1'>
        <div
          className={classes(
            'overflow-hidden text-ellipsis',
            !!timeOffs || (isOrder && !deadline && 'max-w-[108px]'),
            isOrder && deadline && 'max-w-[86px]',
          )}
        >
          <WithLink
            className='hover:underline'
            href={isOrder ? `/manufacturing/orders/${resource.id}` : ''}
            target='_blank'
          >
            <span title={resource.title}>{resource.title}</span>
          </WithLink>
        </div>
        {!!timeOffs && (
          <div
            title={`${t('employee leaves')}:\n${timeOffs.map((leave) => `${formatDate(leave.startTime)} - ${formatDate(leave.endTime)}\n`)}`}
          >
            <NoEnterIcon className='size-5 shrink-0 text-alert-red' />
          </div>
        )}
        {deadline && (
          <div title={`${t('production deadline')}: ${formatDate(deadline)}`}>
            <WarningIcon className='h-6 w-5 shrink-0 fill-red-700 text-white' />
          </div>
        )}
      </div>
      {percentage !== undefined && <Pill className='min-w-[51px] bg-alert-light-gray'>{Math.round(percentage)}%</Pill>}
    </div>
  );
};

export default ResourceLabel;
