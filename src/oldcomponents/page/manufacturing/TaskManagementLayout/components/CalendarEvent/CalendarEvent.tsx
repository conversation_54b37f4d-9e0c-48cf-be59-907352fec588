import {FC, useEffect, useMemo, useRef} from 'react';

import {EventImpl} from '@fullcalendar/core/internal';
import {useAtomValue} from 'jotai';
import Link from 'next/link';
import {useTranslations} from 'next-intl';

import OptionsIcon from '@/assets/OptionsIcon';
import {defaultWorkingHoursPerDayAtom} from 'store/defaults';
import {Id, ManufacturingTaskStatus} from 'types/global';
import {getBgColor} from 'utils/colors';
import {classes, minutesToWorkHoursTimeString} from 'utils/common';

type CalendarEventType = {
  event: EventImpl;
  isDragging?: boolean;
  isEditable?: boolean;
  isOrder?: boolean;
  onClick?: (value: {id: string; name: string; numberOfAssignees: number; orderId: string}) => void;
};

const CalendarEvent: FC<CalendarEventType> = ({event, isDragging, isEditable, isOrder, onClick = () => {}}) => {
  const ref = useRef<HTMLDivElement | null>(null);
  const t = useTranslations();
  const workingHoursPerDay = useAtomValue(defaultWorkingHoursPerDayAtom);

  const isCustom = useMemo(() => event.id.startsWith('custom-'), [event]);

  useEffect(() => {
    const node = ref.current;
    if (node) {
      const parentElement = node.parentNode?.parentNode?.parentNode as HTMLElement;
      const parentRowElement = node.parentNode?.parentNode?.parentNode?.parentNode?.parentNode
        ?.parentNode as HTMLElement;

      const handleMouseEnter = () => {
        if (parentElement) parentElement.style.zIndex = '500';
        if (parentElement) parentRowElement.style.zIndex = '500';
      };

      const handleMouseLeave = () => {
        if (parentElement) parentElement.style.zIndex = '';
        if (parentRowElement) parentRowElement.style.zIndex = '';
      };

      node.addEventListener('mouseenter', handleMouseEnter);
      node.addEventListener('mouseleave', handleMouseLeave);

      return () => {
        node.removeEventListener('mouseenter', handleMouseEnter);
        node.removeEventListener('mouseleave', handleMouseLeave);
      };
    }
  }, []);

  return (
    <div
      className={classes(
        'group relative overflow-x-hidden rounded-lg border border-transparent hover:min-w-fit',
        isDragging && 'w-fit',
        getBgColor(event.extendedProps.order.id),
        !isOrder && 'hover:border-black',
        isOrder && 'cursor-default',
      )}
      onClick={() => {
        onClick({
          id: event.id,
          name: event.extendedProps.name,
          numberOfAssignees: event.extendedProps.numberOfAssignees,
          orderId: event.extendedProps.order.id,
        });
      }}
      ref={ref}
    >
      <div
        className={classes(
          'relative flex items-center justify-between py-2 pl-2 text-slate-900',
          (!isEditable || event.extendedProps.status === ManufacturingTaskStatus.DONE) && 'pr-2',
        )}
      >
        <div
          className={classes(
            'flex flex-col',
            event.extendedProps.status === ManufacturingTaskStatus.DONE && 'line-through',
          )}
        >
          <div
            className={classes(
              'whitespace-nowrap text-sm font-semibold',
              isDragging && 'line-clamp-none whitespace-nowrap',
            )}
          >
            {isOrder && !isCustom && (
              <>
                {event.extendedProps.assignedEmployees.map((employee: Id) => employee.name).join(', ')} &bull;{' '}
                {event.extendedProps.assignedWorkstations.map((workstation: Id) => workstation.name).join(', ')}
              </>
            )}
            {(!isOrder || isCustom) && (
              <>
                {event.extendedProps.orig.product?.id && (
                  <>
                    <Link
                      className='hover:underline'
                      href={`/inventory/products/${event.extendedProps.orig.product.id}`}
                      onClick={(event) => event.stopPropagation()}
                      target='_blank'
                    >
                      {event.extendedProps.orig.product.name}
                    </Link>{' '}
                    &bull; {event.extendedProps.quantity}{' '}
                    {t(`unit.name.${event.extendedProps.measurementUnit.name}` as any)}
                  </>
                )}
              </>
            )}
            {!isOrder && <> &bull; {t(event.extendedProps.statusReason || event.extendedProps.status)}</>}
          </div>
          <div className={classes('whitespace-nowrap text-xs', isDragging && 'line-clamp-none whitespace-nowrap')}>
            {!isOrder && (
              <>
                <Link
                  className='hover:underline'
                  href={`/manufacturing/orders/${event.extendedProps.order.id}`}
                  onClick={(event) => event.stopPropagation()}
                  target='_blank'
                >
                  {event.extendedProps.order.number}
                </Link>{' '}
                &bull; {event.title} &bull;{' '}
              </>
            )}
            {isOrder && event.extendedProps.customer && <>{event.extendedProps.customer.name} &bull; </>}
            {minutesToWorkHoursTimeString(event.extendedProps.orig.durationInMinutes, workingHoursPerDay)}
          </div>
        </div>
        {isEditable && event.extendedProps.status !== ManufacturingTaskStatus.DONE && (
          <OptionsIcon className='size-6' />
        )}
      </div>
    </div>
  );
};

export default CalendarEvent;
