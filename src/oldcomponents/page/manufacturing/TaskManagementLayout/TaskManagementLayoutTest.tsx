import {FC, useCallback, useEffect, useMemo, useRef, useState} from 'react';

import interactionPlugin from '@fullcalendar/interaction';
import FullCalendar from '@fullcalendar/react';
import resourceTimelinePlugin from '@fullcalendar/resource-timeline';
import {
  differenceInMinutes,
  endOfDay,
  isAfter,
  isBefore,
  isWithinInterval,
  parse,
  parseISO,
  startOfDay,
} from 'date-fns';
import {useAtom} from 'jotai';
import {difference, filter, find, sumBy} from 'lodash';
import {useRouter} from 'next/router';
import {useTranslations} from 'next-intl';

import CalendarIcon from '@/assets/CalendarIcon';
import ChevronLeftIcon from '@/assets/ChevronLeftIcon';
import AssignEmployeesModal from '@/components/pages/lists/manufacturing/planning/ManufacturingPlanningEmployees/AssignEmployeesModal';
import {PageHeaderTitle, PageTitle} from '@/components/ui/special/Page';
import useSettings from '@/hooks/useSettings';
import useTaskActions from '@/hooks/useTaskActions';
import useTasksTest from '@/hooks/useTasksTest';
import {Id} from '@/types/global';
import {LoadingBar, SecondaryButton} from 'oldcomponents';
import {tasksCurrentDateAtom} from 'store/ui';
import {EmployeeActiveTimeOff} from 'types/manufacturing';
import {classes} from 'utils/common';
import {formatDate, formatDay, formatTime} from 'utils/format';

import {CalendarEvent, ResourceLabel} from './components';

export enum TaskView {
  employees = 'employees',
  workstations = 'workstations',
}

type Props = {
  resources?: {
    [rest: string]: any;
    id: string;
    title: string;
  }[];
  view: TaskView;
};

const TaskManagementLayoutTest: FC<Props> = ({resources, view}) => {
  const ref = useRef<FullCalendar>(null);
  const [currentDate, setCurrentDate] = useAtom(tasksCurrentDateAtom);
  const {isLoading, tasks} = useTasksTest(currentDate);
  const t = useTranslations();
  const [calendarApi, setCalendarApi] = useState<any>();
  const {settings} = useSettings();
  const {updateTaskEmployees, updateTaskWorkstations} = useTaskActions();
  const {push} = useRouter();
  const [selectedTask, setSelectedTask] = useState<{
    id: string;
    name: string;
    numberOfAssignees: number;
    orderId: string;
  }>();

  useEffect(() => {
    setCalendarApi(ref.current?.getApi());
  }, []);

  useEffect(() => {
    if (!currentDate && calendarApi) setCurrentDate(calendarApi.getDate());
  }, [calendarApi, currentDate, setCurrentDate]);

  const hiddenDays = useMemo(() => {
    const weekDays = [1, 2, 3, 4, 5, 6, 7];

    const diff = difference(weekDays, settings?.manufacturing?.workingDays || []);

    if (diff.length === 7) return [];

    return diff.map((day) => (day === 7 ? 0 : day));
  }, [settings?.manufacturing?.workingDays]);

  const calculatePercentage = useCallback(
    (id: string) => {
      if (!settings?.manufacturing?.workDayEndTime || !settings?.manufacturing?.workDayStartTime) return undefined;

      const workDayStartTime = parse(settings?.manufacturing?.workDayStartTime || '', 'HH:mm:ss', currentDate);
      const workDayEndTime = parse(settings?.manufacturing?.workDayEndTime || '', 'HH:mm:ss', currentDate);

      const totalWorkMinutes = sumBy(
        filter(tasks, (task) =>
          (view === TaskView.employees ? task.assignedEmployees : task.assignedWorkstations)
            .map((resource) => resource.id)
            .includes(id),
        ),
        (task) => {
          let limitedEndTime = parseISO(task.endTime);
          let limitedStartTime = parseISO(task.startTime);

          if (isAfter(limitedEndTime, workDayEndTime)) {
            limitedEndTime = workDayEndTime;
          }
          if (isBefore(limitedStartTime, workDayStartTime)) {
            limitedStartTime = workDayStartTime;
          }

          return Math.abs(differenceInMinutes(limitedEndTime, limitedStartTime));
        },
      );
      if (totalWorkMinutes == undefined) return undefined;

      return (totalWorkMinutes / differenceInMinutes(workDayEndTime, workDayStartTime)) * 100;
    },
    [currentDate, settings?.manufacturing?.workDayEndTime, settings?.manufacturing?.workDayStartTime, tasks, view],
  );

  const employeeHasLeave = useCallback(
    (activeTimeoffs: EmployeeActiveTimeOff[]) =>
      activeTimeoffs?.length > 0 &&
      find(activeTimeoffs, (leave) =>
        isWithinInterval(currentDate, {
          end: endOfDay(new Date(leave.endTime)),
          start: startOfDay(new Date(leave.startTime)),
        }),
      ) !== undefined,
    [currentDate],
  );

  return (
    <>
      <PageTitle>{t('task management')}</PageTitle>
      <div className='flex flex-col'>
        <div className={classes('inline-flex items-center justify-between', isLoading ? 'mb-7' : 'mb-8')}>
          <div className='flex items-center gap-4'>
            <div className='inline-flex items-center gap-7'>
              <PageHeaderTitle>{t('task management')}</PageHeaderTitle>
              <div className='inline-flex items-center gap-4'>
                <button
                  className='hover:text-oldprimary'
                  onClick={() => {
                    calendarApi.prev();
                    setCurrentDate(calendarApi.getDate());
                  }}
                >
                  <ChevronLeftIcon className='size-6' />
                </button>
                <button
                  className='hover:text-oldprimary'
                  onClick={() => {
                    calendarApi.next();
                    setCurrentDate(calendarApi.getDate());
                  }}
                >
                  <ChevronLeftIcon className='size-6 rotate-180' />
                </button>
                {currentDate && (
                  <PageHeaderTitle className='capitalize'>
                    {`${formatDay(currentDate)}, ${formatDate(currentDate) || ''}`}
                  </PageHeaderTitle>
                )}
              </div>
            </div>
          </div>
          <div className='inline-flex items-center gap-6'>
            <SecondaryButton
              Icon={CalendarIcon}
              onClick={() => {
                calendarApi.today();
                setCurrentDate(calendarApi.getDate());
              }}
            >
              {t('today')}
            </SecondaryButton>
            <SecondaryButton
              onClick={() => {
                push(`/manufacturing/taskstest/${TaskView.employees}`);
              }}
            >
              {t('employees')}
            </SecondaryButton>
            <SecondaryButton
              onClick={() => {
                push(`/manufacturing/taskstest/${TaskView.workstations}`);
              }}
            >
              {t('workstations')}
            </SecondaryButton>
          </div>
        </div>
        {isLoading && (
          <LoadingBar barClassName='bg-alert-light-yellow' className='ml-[200px]' progressClassName='bg-oldprimary' />
        )}

        <FullCalendar
          businessHours={{
            daysOfWeek: settings?.manufacturing?.workingDays?.map((day) => (day === 7 ? 0 : day)) || [],
            endTime: settings?.manufacturing?.workDayEndTime,
            startTime: settings?.manufacturing?.workDayStartTime,
          }}
          contentHeight={(window?.innerHeight || 800) - 100}
          eventColor='transparent'
          eventContent={({event, isDragging}) => (
            <CalendarEvent
              event={event}
              isDragging={isDragging}
              isEditable={view === TaskView.employees}
              onClick={(value) => {
                if (view === TaskView.employees) setSelectedTask(value);
              }}
            />
          )}
          eventDrop={({event, newResource, oldResource}) => {
            let newTask = {
              ...event.extendedProps.orig,
              assignedEmployees: [
                ...filter(event.extendedProps.orig.assignedEmployees, (employee) => employee.id !== oldResource?.id),
                {id: newResource?.id},
              ],
              assignedWorkstations: [
                ...filter(
                  event.extendedProps.orig.assignedWorkstations,
                  (workstation) => workstation.id !== oldResource?.id,
                ),
                {id: newResource?.id},
              ],
            };

            if (view === TaskView.employees) {
              updateTaskEmployees(
                newTask.id,
                newTask.assignedEmployees?.map((employee: Id) => employee.id),
                newTask.order?.id,
                newTask.name,
              );
            } else {
              updateTaskWorkstations(
                newTask.id,
                newTask.assignedWorkstations?.map((workstation: Id) => workstation.id),
                newTask.order?.id,
                newTask.name,
              );
            }
          }}
          eventResourceEditable={view === TaskView.employees}
          events={tasks.map((task) => ({
            ...task,
            end: task.endTime,
            orig: task,
            resourceEditable: true,
            resourceIds: (() => {
              switch (view) {
                case TaskView.employees:
                  return task.assignedEmployees;
                case TaskView.workstations:
                  return task.assignedWorkstations;
                default:
                  return [];
              }
            })().map((resource) => resource.id),
            start: task.startTime,
            title: task.name,
          }))}
          firstDay={1}
          headerToolbar={{center: '', left: '', right: ''}}
          hiddenDays={hiddenDays}
          initialDate={currentDate}
          initialView={'resourceTimelineDay'}
          nowIndicator={true}
          plugins={[resourceTimelinePlugin, interactionPlugin]}
          ref={ref}
          resourceAreaHeaderClassNames='hidden'
          resourceAreaHeaderContent=''
          resourceAreaWidth={200}
          resourceLabelClassNames='border-gray-100!'
          resourceLabelContent={({resource}) => (
            <ResourceLabel
              percentage={calculatePercentage(resource.id)}
              resource={resource}
              timeOffs={
                employeeHasLeave(resource.extendedProps.activeTimeoffs)
                  ? resource.extendedProps.activeTimeoffs
                  : undefined
              }
            />
          )}
          resourceLaneClassNames='border-gray-100!'
          resourceOrder='title'
          resources={resources}
          resourcesInitiallyExpanded={false}
          schedulerLicenseKey='CC-Attribution-NonCommercial-NoDerivatives'
          slotDuration={{minutes: 30}}
          slotLabelClassNames='font-normal text-sm'
          slotLabelContent={({date}) => formatTime(date)}
          slotMaxTime={settings?.manufacturing?.workDayEndTime || '24:00:00'}
          slotMinTime={settings?.manufacturing?.workDayStartTime || '00:00:00'}
          slotMinWidth={60}
          viewClassNames='[&>table]:border-none!'
          // weekNumberClassNames='hidden!'
          // slotLaneClassNames='hidden!'
          // resourceGroupLaneClassNames='hidden!'
          // resourceGroupLabelClassNames='hidden!'
          // nowIndicatorClassNames='hidden!'
          // moreLinkClassNames='hidden!'
          // eventClassNames='hidden!'
          // dayHeaderClassNames='hidden!'
          // dayCellClassNames='hidden!'
          // allDayClassNames='hidden!'
          // eventAllow={({start}) => isFuture(start)}
          // eventMinWidth={60}
          // snapDuration={{seconds: 1}}
          // eventOverlap={false}
        />
      </div>
      {selectedTask && (
        <AssignEmployeesModal
          onClose={() => setSelectedTask(undefined)}
          orderId={selectedTask.orderId}
          task={selectedTask}
        />
      )}
    </>
  );
};

export default TaskManagementLayoutTest;
