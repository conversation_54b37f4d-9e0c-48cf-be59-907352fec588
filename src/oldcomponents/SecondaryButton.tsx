import {FC} from 'react';

import {classes} from 'utils/common';

import Button, {ButtonProps} from './Button';

const SecondaryButton: FC<ButtonProps> = ({
  children,
  className,
  confirmation,
  disabled,
  href,
  Icon,
  linkClassName,
  onClick = () => {},
}) => {
  return (
    <Button
      className={classes(
        'h-[40px] border border-button-secondary-hovered bg-white px-4',
        disabled ? 'text-black/40 ' : 'hover:border-black',
        className,
      )}
      confirmation={confirmation}
      disabled={disabled}
      href={href}
      Icon={Icon}
      linkClassName={linkClassName}
      onClick={onClick}
    >
      {children}
    </Button>
  );
};

export default SecondaryButton;
