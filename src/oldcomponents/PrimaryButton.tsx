import {FC} from 'react';

import {classes} from 'utils/common';

import Button, {ButtonProps} from './Button';

const PrimaryButton: FC<ButtonProps> = ({
  children,
  className,
  confirmation,
  disabled,
  href,
  Icon,
  linkClassName,
  onClick = () => {},
}) => {
  return (
    <Button
      className={classes(
        'h-[40px] border border-transparent px-4 hover:bg-button-primary-hovered',
        disabled ? 'bg-button-primary/40 text-black/40' : 'bg-button-primary',
        className,
      )}
      confirmation={confirmation}
      disabled={disabled}
      href={href}
      Icon={Icon}
      linkClassName={linkClassName}
      onClick={onClick}
    >
      {children}
    </Button>
  );
};

export default PrimaryButton;
