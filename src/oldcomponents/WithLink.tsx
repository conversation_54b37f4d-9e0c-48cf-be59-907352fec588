import {FC, ReactNode} from 'react';

import Link from 'next/link';

type Props = {
  children: ReactNode;
  className?: string;
  href?: string;
  target?: string;
};

const WithLink: FC<Props> = ({children, className, href, target}) => {
  if (href)
    return (
      <Link className={className} href={href} target={target}>
        {children}
      </Link>
    );

  return <>{children}</>;
};

export default WithLink;
