import {FC, ReactNode, useEffect, useRef} from 'react';

import useClickOutside from 'hooks/helpers/useClickOutside';
import useToggle from 'hooks/helpers/useToggle';
import {WithLink} from 'oldcomponents';
import {IconProps} from 'types/icon';
import {classes} from 'utils/common';

export type ButtonProps = {
  children: ReactNode;
  className?: string;
  confirmation?: string;
  disabled?: boolean;
  href?: string;
  Icon?: FC<IconProps>;
  linkClassName?: string;
  onClick?: () => void;
  target?: string;
};

const Button: FC<ButtonProps> = ({
  children,
  className,
  confirmation,
  disabled,
  href,
  Icon,
  linkClassName,
  onClick = () => {},
  target,
}) => {
  const ref = useRef<HTMLButtonElement>(null);
  const [clicked, toggleClicked] = useToggle();

  useClickOutside([ref], () => {
    if (clicked) toggleClicked();
  });

  useEffect(() => {
    if (clicked) {
      ref.current?.setAttribute('disabled', 'disabled');
      setTimeout(() => {
        ref.current?.removeAttribute('disabled');
      }, 500);
    }
  }, [clicked]);

  return (
    <WithLink className={linkClassName} href={href} target={target}>
      {!clicked && (
        <button
          className={classes('inline-flex items-center justify-center rounded-lg', className)}
          disabled={disabled}
          onClick={() => {
            if (confirmation) {
              toggleClicked();
            } else {
              onClick();
            }
          }}
        >
          {Icon && <Icon className='-ml-1 mr-2 size-6' />}
          {children}
        </button>
      )}
      {clicked && confirmation !== undefined && (
        <button
          className={classes(
            'inline-flex h-[40px] items-center justify-center rounded-lg border border-red-500 bg-white px-4 text-red-700 hover:bg-red-100',
          )}
          onClick={onClick}
          ref={ref}
        >
          {Icon && <Icon className='-ml-1 mr-2 size-6' />}
          {confirmation}
        </button>
      )}
    </WithLink>
  );
};

export default Button;
