import {FC} from 'react';

import {classes} from 'utils/common';

import Button, {ButtonProps} from './Button';

const IconButton: FC<Omit<ButtonProps, 'Icon'>> = ({
  children,
  className,
  confirmation,
  disabled,
  href,
  linkClassName,
  onClick = () => {},
  target,
}) => {
  return (
    <Button
      className={classes(disabled && 'text-gray-400', className)}
      confirmation={confirmation}
      disabled={disabled}
      href={href}
      linkClassName={linkClassName}
      onClick={onClick}
      target={target}
    >
      {children}
    </Button>
  );
};

export default IconButton;
