import {FC, ReactNode} from 'react';

import {classes} from 'utils/common';

type Props = {
  children: ReactNode;
  className?: string;
  forTable?: boolean;
  onClick?: () => void;
};

const Pill: FC<Props> = ({children, className, forTable, onClick}) => {
  return (
    <span
      className={classes(
        'inline-flex items-center justify-center whitespace-nowrap',
        forTable ? 'rounded-2xl border px-2 py-1' : 'rounded-full px-2.5 py-0.5 text-xs',
        className,
      )}
      onClick={onClick}
    >
      {children}
    </span>
  );
};

export default Pill;
