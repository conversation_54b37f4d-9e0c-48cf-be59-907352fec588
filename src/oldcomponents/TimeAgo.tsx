import {FC} from 'react';

import {format} from 'date-fns';
import {useTranslations} from 'next-intl';

import {formatDate, formatTimeAgo} from 'utils/format';

type Props = {
  className?: string;
  date: Date | string | undefined;
  hide?: ('day' | 'days' | 'hours' | 'minutes' | 'seconds' | 'week' | 'weeks')[];
  useShort?: boolean;
  useYesterday?: boolean;
};

const TimeAgo: FC<Props> = ({className, date, hide, useShort, useYesterday}) => {
  const t = useTranslations();

  const {key, value} = formatTimeAgo(date, {hide, useYesterday});

  return (
    <div className={className}>
      {key ? t(key as any, {value}) : useShort ? format(value, 'dd MMM') : formatDate(value as Date)}
    </div>
  );
};

export default TimeAgo;
