import {CSSProperties, DetailedHTMLProps, forwardRef, HTMLAttributes, ReactNode, useMemo} from 'react';

import {AtMostOneOf} from 'types/global';
import {classes} from 'utils/common';

export interface BadgeInterface extends DetailedHTMLProps<HTMLAttributes<HTMLDivElement>, HTMLDivElement> {
  anchorOrigin?: 'bottom-left' | 'bottom-right' | 'top-left' | 'top-right';
  children?: ReactNode;
  className?: string;
  contentClassName?: string;
  contentStyle?: CSSProperties;
  hidden?: boolean;
  overlap?: 'circular' | 'rectangular';
  pulse?: boolean;
}

type Props = AtMostOneOf<{
  content?: string;
  element?: ReactNode;
  showDot?: boolean;
}> &
  BadgeInterface;

const Badge = forwardRef<HTMLDivElement, Props>(
  (
    {
      anchorOrigin = 'top-right',
      children,
      className,
      content,
      contentClassName,
      contentStyle,
      element,
      hidden,
      overlap = 'rectangular',
      pulse,
      showDot,
      ...props
    },
    ref,
  ) => {
    const placement = useMemo(
      () => ({
        circular: {
          'bottom-left': 'bottom-[14%] left-[14%] translate-y-2/4 -translate-x-2/4',
          'bottom-right': 'bottom-[14%] right-[14%] translate-y-2/4 translate-x-2/4',
          'top-left': 'top-[14%] left-[14%] -translate-y-2/4 -translate-x-2/4',
          'top-right': 'top-[14%] right-[14%] -translate-y-2/4 translate-x-2/4',
        },
        rectangular: {
          'bottom-left': '-bottom-2 -left-2',
          'bottom-right': '-bottom-2 -right-2',
          'top-left': '-top-2 -left-2',
          'top-right': '-top-2 -right-2',
        },
      }),
      [],
    );

    return (
      <div {...props} className={classes('relative size-fit', className)} ref={ref}>
        {children}
        {!hidden && (showDot || content || element) && (
          <span
            className={classes(
              'absolute rounded-full',
              showDot && 'size-5',
              placement[overlap][anchorOrigin],
              contentClassName,
              pulse && 'animate-pulse',
              (content || showDot) && 'border-2 border-white bg-red-500 px-1 text-center text-xs font-bold text-white',
            )}
            style={contentStyle}
          >
            {!showDot && content && <>{content}</>}
            {!showDot && element && <>{element}</>}
          </span>
        )}
      </div>
    );
  },
);

export default Badge;
