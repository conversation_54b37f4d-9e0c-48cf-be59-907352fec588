import {FC, ReactNode, useEffect} from 'react';

import {useAtomValue} from 'jotai';
import {useRouter} from 'next/router';

import {localeAtom} from 'store/locale';

type Props = {
  children: ReactNode;
};

const LanguageValidator: FC<Props> = ({children}) => {
  const {asPath, locale: routeLocale, push} = useRouter();
  const locale = useAtomValue(localeAtom);

  useEffect(() => {
    if (locale && locale !== routeLocale) {
      push(asPath, undefined, {locale});
    }
  }, [asPath, locale, push, routeLocale]);

  if (!locale) return null;

  return <>{children}</>;
};

export default LanguageValidator;
