import {FC, useEffect} from 'react';

import {useSetAtom} from 'jotai';

import useAccount from '@/hooks/useAccount';
import useCountries from '@/hooks/useCountries';
import useCurrencies from '@/hooks/useCurrencies';
import useExchangeRates from '@/hooks/useExchangeRates';
import useSettings from '@/hooks/useSettings';
import {
  setCompanyNameAtom,
  setCountriesAtom,
  setCurrenciesAtom,
  setDefaultsAtom,
  setExchangeRatesAtom,
} from '@/store/defaults';

const DefaultsProvider: FC = () => {
  const setDefaults = useSetAtom(setDefaultsAtom);
  const setCurrencies = useSetAtom(setCurrenciesAtom);
  const setCountries = useSetAtom(setCountriesAtom);
  const setCompanyName = useSetAtom(setCompanyNameAtom);
  const setExchangeRates = useSetAtom(setExchangeRatesAtom);
  const {isLoading: settingsIsLoading, settings} = useSettings();
  const {countries, isLoading: countryIsLoading} = useCountries();
  const {currencies, isLoading: currencyIsLoading} = useCurrencies();
  const {account, isLoading: accountIsLoading} = useAccount();
  const {exchangeRates, isLoading: exchangeRatesIsLoading} = useExchangeRates();

  useEffect(() => {
    if (!settingsIsLoading && settings) setDefaults(settings);
  }, [settingsIsLoading, setDefaults, settings]);

  useEffect(() => {
    if (!currencyIsLoading && currencies) setCurrencies(currencies);
  }, [currencyIsLoading, currencies, setCurrencies]);

  useEffect(() => {
    if (!countryIsLoading && countries) setCountries(countries);
  }, [countryIsLoading, setCountries, countries]);

  useEffect(() => {
    if (!accountIsLoading && account?.name) setCompanyName(account.name);
  }, [accountIsLoading, setCompanyName, account]);

  useEffect(() => {
    if (!exchangeRatesIsLoading && exchangeRates) setExchangeRates(exchangeRates);
  }, [exchangeRates, exchangeRatesIsLoading, setExchangeRates]);

  return null;
};

export default DefaultsProvider;
