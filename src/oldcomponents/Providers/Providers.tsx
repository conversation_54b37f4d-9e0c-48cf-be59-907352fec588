import {FC, ReactNode} from 'react';

import {UserProvider} from '@auth0/nextjs-auth0/client';
import {Provider as Jo<PERSON>Provider} from 'jotai';
import {useRouter} from 'next/router';
import {AbstractIntlMessages, NextIntlClientProvider} from 'next-intl';

import {TooltipProvider} from '@/components/ui/Tooltip';

import {DefaultsProvider, LanguageValidator} from './components';

type Props = {
  children: ReactNode;
  messages: AbstractIntlMessages;
};

const Providers: FC<Props> = ({children, messages}) => {
  const router = useRouter();

  return (
    <UserProvider>
      <NextIntlClientProvider locale={router.locale} messages={messages} timeZone='Europe/Bucharest'>
        <JotaiProvider>
          <DefaultsProvider />
          <LanguageValidator>
            <TooltipProvider>{children}</TooltipProvider>
          </LanguageValidator>
        </JotaiProvider>
      </NextIntlClientProvider>
    </UserProvider>
  );
};

export default Providers;
