@import 'tailwindcss';

@config '../../tailwind.config.ts';

/*
  The default border color has changed to `currentcolor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentcolor);
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    /* --background-color: hsl(0, 0%, 100%); */
    --foreground: 222.2 84% 4.9%;
    /* --foreground-color: hsl(222.2, 84%, 4.9%); */

    --menu: 240 12.68% 41.76%;
    /* --menu-color: hsl(240, 12.68%, 41.76%); */
    --menu-foreground: 240 17.07% 24.12%;
    /* --menu-foreground-color: hsl(240, 17.07%, 24.12%); */

    --button-primary: 42.81 94.68% 63.14%;
    /* --button-primary-color: hsl(42.81, 94.68%, 63.14%); */

    --border: 214.3 50% 91.4%;
    /* --border-color: hsl(214.3, 50%, 91.4%); */
    --border-foreground: 240 12.68% 51.76%;
    /* --border-foreground-color: hsl(240, 12.68%, 51.76%); */

    --accent: 210 40% 96.1%;
    /* --accent-color: hsl(210, 40%, 96.1%); */
    --accent-foreground: 222.2 47.4% 11.2%;
    /* --accent-foreground-color: hsl(222.2, 47.4%, 11.2%); */

    --error-red-light: 5 85.71% 97.25%;
    /* --error-red-light-color: hsl(5, 85.71%, 97.25%); */
    --error-red: 0 84.24% 60.2%;
    /* --error-red-color: hsl(0, 84.24%, 60.2%); */
    --error-red-dark: 4.31 80.1% 37.45%;
    /* --error-red-dark-color: hsl(4.31, 80.1%, 37.45%); */

    --success-green-light: 150 50% 95%;
    /* --success-green-light-color: hsl(150, 50%, 95%); */
    --success-green: 150 50% 45%;
    /* --success-green-color: hsl(150, 50%, 45%); */
    --success-green-dark: 150 50% 25%;
    /* --success-green-dark-color: hsl(150, 50%, 25%); */

    --warning-yellow-light: 45 100% 95%;
    /* --warning-yellow-light-color: hsl(45, 100%, 95%); */
    --warning-yellow: 45 100% 45%;
    /* --warning-yellow-color: hsl(45, 100%, 45%); */
    --warning-yellow-dark: 45 100% 25%;
    /* --warning-yellow-dark-color: hsl(45, 100%, 25%); */

    --secondary-gray-light: 210 16.7% 95.3%;
    /* --secondary-gray-light-color: hsl(210, 16.7%, 95.3%); */
    --secondary-gray: 210 16.7% 60%;
    /* --secondary-gray-color: hsl(210, 16.7%, 60%); */
    --secondary-gray-dark: 210 16.7% 20%;
    /* --secondary-gray-dark-color: hsl(210, 16.7%, 20%); */

    --info-blue-light: 240 100% 95%;
    /* --info-blue-light-color: hsl(240, 100%, 95%); */
    --info-blue: 240 100% 45%;
    /* --info-blue-color: hsl(240, 100%, 45%); */
    --info-blue-dark: 240 100% 25%;
    /* --info-blue-dark-color: hsl(240, 100%, 25%); */

    --primary: 222.2 47.4% 11.2%;
    /* --primary-color: hsl(222.2, 47.4%, 11.2%); */
    --primary-foreground: 210 40% 98%;
    /* --primary-foreground-color: hsl(210, 40%, 98%); */

    --muted: 0 0% 70%;
    /* --muted-color: hsl(0, 0%, 70%); */
    --muted-foreground: 215.4 16.3% 46.9%;
    /* --muted-foreground-color: hsl(215.4, 16.3%, 46.9%); */

    --destructive: 0 84.2% 60.2%;
    /* --destructive-color: hsl(0, 84.2%, 60.2%); */
    --destructive-foreground: 210 40% 98%;
    /* --destructive-foreground-color: hsl(210, 40%, 98%); */

    --input: 220 14.29% 95.88%;
    /* --input-color: hsl(220, 14.29%, 95.88%); */

    --dot-orange: 29 93% 55%;
    /* --dot-orange-color: hsl(29, 93%, 55%); */
    --dot-purple: 306 61% 51%;
    /* --dot-purple-color: hsl(306, 61%, 51%); */
    --dot-yellow: 43 95% 63%;
    /* --dot-yellow-color: hsl(43, 95%, 63%); */
    --dot-blue: 212 93% 55%;
    /* --dot-blue-color: hsl(212, 93%, 55%); */
    --dot-green: 150 50% 45%;
    /* --dot-green-color: hsl(150, 50%, 45%); */
    --dot-gray: 210 16.7% 60%;
    /* --dot-gray-color: hsl(210, 16.7%, 60%); */

    --radius: 0.5rem;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

.fc {
  .fc-datagrid-cell-frame, .fc-timeline-lane-frame {
    @apply min-h-[56px];
  }

  .fc-toolbar.fc-header-toolbar {
    @apply mb-0;
  }

  .fc-datagrid-cell-cushion {
    @apply flex items-center p-0 h-full;

    .fc-datagrid-cell-main {
      @apply w-full ;
    }
  }

  .fc-datagrid-expander, .fc-datagrid-cell-cushion {
    .fc-icon {
      @apply w-auto;
    }
  }
}

:root {
  --toastify-color-progress-light: #FAC748 !important;
}

.loadingBar {
  transform-origin: 0% 50%;
  animation: progress 1s infinite linear;
}

.arrow {
  @apply fill-background;
  filter: drop-shadow(0 0 3px gray);
  clip-path: inset(0 -10px -10px -10px);
}

input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input[type="number"] {
  -moz-appearance: textfield;
}

@keyframes progress {
  0% {
    transform: translateX(0) scaleX(0);
  }
  40% {
    transform: translateX(0) scaleX(0.4);
  }
  100% {
    transform: translateX(100%) scaleX(0.5);
  }
}

.react-grid-item > .react-resizable-handle::after {
  display: none;
}

.react-grid-item.react-grid-placeholder {
  @apply bg-muted!;
  border: 1px dashed black;
}

.tox-tinymce {
  border-radius: 0 !important;
}

.tox-tbtn {
  background-color: white !important;
}

.tox-editor-header,  .tox-toolbar__primary {
  @apply bg-input!;
}
