import {getSession, withPageAuthRequired} from '@auth0/nextjs-auth0';
import {GetServerSideProps} from 'next';

import PurchasesInvoicesDetails from '@/components/pages/details/purchases/invoice/PurchasesInvoicesDetails';
import {NextPageWithLayout} from '@/types/global';
import {hasRequiredPermissions} from '@/utils/permissions';
import {getReturnTo} from 'utils/common';

type Props = {
  id: string;
};

const PurchaseOrderInvoiceDetailsPage: NextPageWithLayout<Props> = ({id}) => {
  return <PurchasesInvoicesDetails purchaseId={id} />;
};

export default PurchaseOrderInvoiceDetailsPage;

export const getServerSideProps: GetServerSideProps = async (context) => {
  const {params} = context;
  const {purchaseId} = params || {};

  return withPageAuthRequired({
    ...getReturnTo(context),
    getServerSideProps: async ({locale, req, res}) => {
      const session = await getSession(req, res);

      if (!hasRequiredPermissions(session?.user?.permissions, 'update', 'purchases')) {
        return {
          redirect: {
            destination: '/purchases',
            permanent: false,
          },
        };
      }
      return {
        props: {
          id: purchaseId,
          messages: (await import(`../../../../messages/${locale}.json`)).default,
        },
      };
    },
  })(context);
};
