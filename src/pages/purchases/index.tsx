import {getSession, withPageAuthRequired} from '@auth0/nextjs-auth0';
import {GetServerSideProps} from 'next';

import PurchasesList from '@/components/pages/lists/purchases/PurchasesList';
import {hasRequiredPermissions} from '@/utils/permissions';
import {NextPageWithLayout} from 'types/global';
import {getReturnTo} from 'utils/common';

const PurchaseOrdersPage: NextPageWithLayout = () => {
  return <PurchasesList />;
};

export default PurchaseOrdersPage;

export const getServerSideProps: GetServerSideProps = async (context) => {
  return withPageAuthRequired({
    ...getReturnTo(context),
    getServerSideProps: async ({locale, req, res}) => {
      const session = await getSession(req, res);

      if (!hasRequiredPermissions(session?.user?.permissions, 'read', 'purchases')) {
        return {
          redirect: {
            destination: '/',
            permanent: false,
          },
        };
      }
      return {
        props: {
          messages: (await import(`../../messages/${locale}.json`)).default,
        },
      };
    },
  })(context);
};
