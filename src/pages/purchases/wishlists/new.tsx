import {getSession, withPageAuthRequired} from '@auth0/nextjs-auth0';
import {GetServerSideProps} from 'next';

import WishlistsDetails from '@/components/pages/details/purchases/wishlists/WishlistsDetails';
import {NextPageWithLayout} from '@/types/global';
import {hasRequiredPermissions} from '@/utils/permissions';
import {getReturnTo} from 'utils/common';

const PurchaseWishlistDetailsPage: NextPageWithLayout = () => {
  return <WishlistsDetails />;
};

export default PurchaseWishlistDetailsPage;

export const getServerSideProps: GetServerSideProps = async (context) => {
  return withPageAuthRequired({
    ...getReturnTo(context),
    getServerSideProps: async ({locale, req, res}) => {
      const session = await getSession(req, res);

      if (
        !hasRequiredPermissions(session?.user?.permissions, 'update', 'purchases') ||
        !hasRequiredPermissions(session?.user?.permissions, 'financial', 'purchases')
      ) {
        return {
          redirect: {
            destination: '/purchases',
            permanent: false,
          },
        };
      }
      return {
        props: {
          messages: (await import(`../../../messages/${locale}.json`)).default,
        },
      };
    },
  })(context);
};
