import {getSession, withPageAuthRequired} from '@auth0/nextjs-auth0';
import {GetServerSideProps} from 'next';

import WishlistInvoicesCreate from '@/components/pages/details/purchases/invoice/WishlistInvoicesCreate';
import {NextPageWithLayout} from '@/types/global';
import {hasRequiredPermissions} from '@/utils/permissions';
import {getReturnTo} from 'utils/common';

type Props = {
  id: string;
};

const PurchaseWishlistInvoiceDetailsPage: NextPageWithLayout<Props> = ({id}) => {
  return <WishlistInvoicesCreate wishlistId={id} />;
};

export default PurchaseWishlistInvoiceDetailsPage;

export const getServerSideProps: GetServerSideProps = async (context) => {
  const {params} = context;
  const {wishlistId} = params || {};

  return withPageAuthRequired({
    ...getReturnTo(context),
    getServerSideProps: async ({locale, req, res}) => {
      const session = await getSession(req, res);

      if (
        !hasRequiredPermissions(session?.user?.permissions, 'update', 'purchases') ||
        !hasRequiredPermissions(session?.user?.permissions, 'financial', 'purchases')
      ) {
        return {
          redirect: {
            destination: '/purchases',
            permanent: false,
          },
        };
      }
      return {
        props: {
          id: wishlistId,
          messages: (await import(`../../../../messages/${locale}.json`)).default,
        },
      };
    },
  })(context);
};
