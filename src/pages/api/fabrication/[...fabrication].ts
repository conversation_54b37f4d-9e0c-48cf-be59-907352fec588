import {NextApiRequest, NextApiResponse} from 'next';

import {Config} from '@/constants/config';
import {backendAxios as axios} from '@/utils/axios';

const FabricationProxyAPI = async (req: NextApiRequest, res: NextApiResponse) =>
  axios
    .request({
      baseURL: Config.APIPath,
      data: req.body,
      headers: {
        ...req.headers,
      },
      method: req.method,
      url: req.url?.replace('/api/', ''),
    })
    .then((response) => {
      res.status(response.status).json(response.data);
    })
    .catch(({response}) => {
      const responseBody = {data: response?.data, statusCode: response?.status};
      res.status(response?.status || 404).json(responseBody);
    });

export default FabricationProxyAPI;
