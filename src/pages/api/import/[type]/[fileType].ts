import {getAccessToken, withApiAuthRequired} from '@auth0/nextjs-auth0';
import FormData from 'form-data';
import {IncomingForm} from 'formidable';
import fs from 'fs';
import {NextApiRequest, NextApiResponse} from 'next';

import {Config} from '@/constants/config';
import {backendAxios as axios} from '@/utils/axios';

export const config = {
  api: {
    bodyParser: false,
  },
};

const typeMapping: {[key: string]: string} = {
  receptions: 'inventory/receptions',
};

const ImportFileAPI = async (req: NextApiRequest, res: NextApiResponse) => {
  const {fileType, type} = req.query;

  const filename = decodeURIComponent(req.headers['x-original-filename'] as string);
  if (!filename) return res.status(400).json({error: 'Missing filename'});

  let token;
  try {
    const {accessToken} = await getAccessToken(req, res);
    token = accessToken;
  } catch (e) {
    console.log('AUTH: error - ', e);
    return res.status(401).json({});
  }

  const form = new IncomingForm();

  form.on('file', (name, file) => {
    const formData = new FormData();

    // eslint-disable-next-line security/detect-non-literal-fs-filename
    formData.append('file', fs.createReadStream(file.filepath), {
      contentType: file.mimetype || 'application/octet-stream',
      filename: file.originalFilename || filename,
    });

    axios
      .post(`${Config.APIPath}/${typeMapping[type as string] || type}/import/${fileType}/process`, formData, {
        headers: {
          ...formData.getHeaders(),
          Authorization: `Bearer ${token}`,
          'Content-Type': 'multipart/form-data',
        },
      })
      .then((response) => {
        Object.entries(response.headers).forEach(([key, value]) => {
          res.setHeader(key, value);
        });

        res.status(response.status).json(response.data);
      })
      .catch(({response}) => {
        const responseBody = {data: response?.data, statusCode: response?.status};
        res.status(response?.status || 404).json(responseBody);
      });
  });

  form.parse(req);
};

export default withApiAuthRequired(ImportFileAPI);
