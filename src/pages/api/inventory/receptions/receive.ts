import {getAccessToken, withApiAuthRequired} from '@auth0/nextjs-auth0';
import FormData from 'form-data';
import {IncomingForm} from 'formidable';
import fs from 'fs';
import {NextApiRequest, NextApiResponse} from 'next';

import {Config} from '@/constants/config';
import {backendAxios as axios} from '@/utils/axios';

export const config = {
  api: {
    bodyParser: false,
  },
};

const ReceptionReceiveWithUploadAPI = async (req: NextApiRequest, res: NextApiResponse) => {
  let token;
  try {
    const {accessToken} = await getAccessToken(req, res);
    token = accessToken;
  } catch (e) {
    console.log('AUTH: error - ', e);
    return res.status(401).json({});
  }

  const form = new IncomingForm();

  form.parse(req, async (err, fields, files) => {
    if (err) {
      console.error('Form parsing error:', err);
      return res.status(500).json({error: 'Form parsing failed'});
    }

    const formData = new FormData();

    formData.append('goodsReceived', fields.goodsReceived?.toString(), {
      contentType: 'application/json',
    });

    Object.values(files).forEach((file) => {
      if (file && file[0])
        // eslint-disable-next-line security/detect-non-literal-fs-filename
        formData.append('files', fs.createReadStream(file[0].filepath), {
          contentType: file[0].mimetype || 'application/octet-stream',
          filename: file[0].originalFilename || file[0].newFilename || 'unknown',
        });
    });

    try {
      const response = await axios.post(`${Config.APIPath}/inventory/receptions/receive`, formData, {
        headers: {
          ...formData.getHeaders(),
          Authorization: `Bearer ${token}`,
          'Content-Type': 'multipart/form-data',
        },
      });

      res.status(response.status).json(response.data);
    } catch (error: any) {
      const responseBody = {data: error?.response?.data, statusCode: error?.response?.status};
      res.status(error?.response?.status || 404).json(responseBody);
    }
  });
};

export default withApiAuthRequired(ReceptionReceiveWithUploadAPI);
