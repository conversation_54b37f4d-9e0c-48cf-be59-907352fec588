import {getAccessToken, withApiAuthRequired} from '@auth0/nextjs-auth0';
import {NextApiRequest, NextApiResponse} from 'next';

import {Config} from '@/constants/config';
import {backendAxios} from '@/utils/axios';

import {fake_codes} from '../../../fake_codes';

const BINARY_TYPES = ['application/pdf', 'application/pdf+zip', 'application/zip'];
const isBinaryType = (mediaType: string | undefined) => {
  if (!mediaType) return false;
  return (
    BINARY_TYPES.includes(mediaType) ||
    mediaType.includes('zip') ||
    mediaType.includes('+zip') ||
    mediaType.includes('octet-stream')
  );
};

const ProxyAPI = async (req: NextApiRequest, res: NextApiResponse) => {
  let token;

  try {
    const {accessToken} = await getAccessToken(req, res);
    token = accessToken;
  } catch (e) {
    console.error('AUTH: error - ', e);
    return res.status(401).json({error: 'Unauthorized: Unable to retrieve access token.'});
  }

  const {proxy, ...queries} = req.query;

  if (!proxy || !Array.isArray(proxy)) {
    return res.status(400).json({error: 'Bad Request: Missing or invalid proxy parameter.'});
  }

  const queryParams = Object.entries(queries)
    .map(([key, value]) => {
      if (Array.isArray(value)) {
        return `${encodeURIComponent(key)}=${encodeURIComponent(value.join(','))}`;
      } else if (value !== undefined) {
        return `${encodeURIComponent(key)}=${encodeURIComponent(value)}`;
      }
      return '';
    })
    .filter(Boolean)
    .join('&');

  const url = `${proxy.join('/')}?${queryParams}`;

  if (process.env.FAKE_CODE && url.startsWith('customers/search')) {
    return res.status(200).json(fake_codes);
  }
  if (process.env.SLOW_MODE) {
    await new Promise((resolve) => setTimeout(resolve, 3000));
  }

  try {
    const response = await backendAxios.request({
      baseURL: Config.APIPath,
      data: req.body,
      headers: {
        ...req.headers,
        Accept: ((req.query.mediaType as string) ?? req.headers.accept ?? 'application/json').replace(' ', '+'),
        Authorization: `Bearer ${token}`,
      },
      method: req.method,
      responseEncoding: isBinaryType(req.query.mediaType as string) ? 'binary' : undefined,
      responseType: isBinaryType(req.query.mediaType as string) ? 'arraybuffer' : undefined,
      url,
    });
    Object.entries(response.headers).forEach(([key, value]) => {
      res.setHeader(key, value);
    });

    if (isBinaryType(req.query?.mediaType as string)) {
      if (!res.getHeader('content-type') && req.query?.mediaType) {
        res.setHeader('content-type', req.query.mediaType as string);
      }

      if (response.data instanceof Buffer) {
        res.status(response.status).send(response.data);
      } else if (response.data instanceof ArrayBuffer) {
        res.status(response.status).send(Buffer.from(response.data));
      } else if (typeof response.data === 'string') {
        res.status(response.status).send(Buffer.from(response.data, 'binary'));
      } else {
        res.status(response.status).send(response.data);
      }
    } else if (req.query?.mediaType !== 'application/json' && req.headers?.accept !== 'application/json') {
      res.status(response.status).send(response.data);
    } else {
      res.status(response.status).json(response.data);
    }
  } catch (err) {
    const error = err as {message: string; response?: {data: any; status: number}};

    const statusCode = error.response?.status || 500;

    console.error('Proxy API Error:', {
      error: error?.message,
      errorData: error?.response?.data,
      statusCode,
      url,
    });

    res.status(statusCode).json(error?.response?.data || {});
  }
};

export default withApiAuthRequired(ProxyAPI);
