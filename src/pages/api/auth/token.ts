import {getAccessToken, withApiAuthRequired} from '@auth0/nextjs-auth0';
import {NextApiRequest, NextApiResponse} from 'next';

const ProxyAPI = async (req: NextApiRequest, res: NextApiResponse) => {
  let token;

  try {
    const {accessToken} = await getAccessToken(req, res);
    token = accessToken;
  } catch (e) {
    console.error('AUTH: error - ', e);
    return res.status(401).json({error: 'Unauthorized: Unable to retrieve access token.'});
  }

  return res.status(200).json({token});
};

export default withApiAuthRequired(ProxyAPI);
