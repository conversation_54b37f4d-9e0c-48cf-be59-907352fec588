import {handleAuth, handleCallback, handleLogin} from '@auth0/nextjs-auth0';
import {NextApiRequest, NextApiResponse} from 'next';

import {Config} from '@/constants/config';
import {getPermissionsFromToken} from '@/utils/permissions';

export default handleAuth({
  callback: (req: NextApiRequest, res: NextApiResponse) =>
    handleCallback(req, res, {
      afterCallback: (_req, _res, session, _state) => {
        if (session && session.accessToken) {
          session.user.permissions = getPermissionsFromToken(session.accessToken);
        }
        return session;
      },
    }),
  login: handleLogin({
    authorizationParams: {audience: Config.Auth0Audience},
  }),
  onError: (_req, res, _err) => {
    res.writeHead(302, {
      Location: '/api/auth/logout',
    });
  },
});
