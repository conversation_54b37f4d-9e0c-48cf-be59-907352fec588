import {getAccessToken, withApiAuthRequired} from '@auth0/nextjs-auth0';
import {NextApiRequest, NextApiResponse} from 'next';

import {Config} from '@/constants/config';
import {backendAxios as axios} from '@/utils/axios';

const ProxyAPI = async (req: NextApiRequest, res: NextApiResponse) => {
  let token;
  try {
    const {accessToken} = await getAccessToken(req, res);
    token = accessToken;
  } catch (e) {
    console.log('AUTH: error - ', e);
    return res.status(401).json({});
  }

  return axios
    .get(`${Config.APIPath}/files/links/${req.query.type}/${req.query.fileId}`, {
      headers: {
        ...req.headers,
        Authorization: `Bearer ${token}`,
      },
      responseEncoding: 'binary',
    })
    .then((response) => {
      Object.entries(response.headers).forEach(([key, value]) => {
        res.setHeader(key, value);
      });

      try {
        const buffer = Buffer.from(response.data, 'binary');

        res.status(response.status).send(buffer);
      } catch (_e) {
        res.status(400).json({error: 'Failed to parse response'});
      }
    })
    .catch(({response}) => {
      const responseBody = {data: response?.data, statusCode: response?.status};
      res.status(response?.status || 404).json(responseBody);
    });
};

export default withApiAuthRequired(ProxyAPI);
