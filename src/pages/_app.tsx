import '@/styles/styles.css';
import 'utils/axios';
import {useMemo} from 'react';

import {Inter} from 'next/font/google';
import Head from 'next/head';

import {SidebarLayout} from '@/components/layout';
import {Toaster} from '@/components/ui/Toast';
import {Config} from '@/constants/config';
import {Providers} from 'oldcomponents';
import {AppProps, NextPageWithLayout} from 'types/global';
import {classes} from 'utils/common';

const inter = Inter({
  subsets: ['latin'],
  variable: '--font-sans',
});

type AppPropsWithLayout = AppProps & {
  Component: NextPageWithLayout;
};

function MyApp({Component, pageProps}: AppPropsWithLayout) {
  const Layout = useMemo(() => Component.Layout ?? SidebarLayout, [Component.Layout]);

  return (
    <>
      <Head>
        <meta content='width=device-width, initial-scale=1' name='viewport' />
        <title>{Config.AppName}</title>
      </Head>

      <Providers messages={pageProps.messages}>
        <div className={classes('min-h-screen bg-background font-sans antialiased', inter.variable)}>
          <Layout>
            <Component {...pageProps} />
          </Layout>
        </div>
      </Providers>
      <Toaster />
    </>
  );
}

export default MyApp;
