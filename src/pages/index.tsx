import {getSession, withPageAuthRequired} from '@auth0/nextjs-auth0';
import {GetServerSideProps} from 'next';

import {NoLayout} from '@/components/layout';
import {getReturnTo} from '@/utils/common';
import {hasRequiredPermissions} from '@/utils/permissions';
import {NextPageWithLayout} from 'types/global';

const HomePage: NextPageWithLayout = () => {
  return null;
};

export default HomePage;

HomePage.Layout = NoLayout;

export const getServerSideProps: GetServerSideProps = async (context) => {
  return withPageAuthRequired({
    ...getReturnTo(context),
    getServerSideProps: async ({req, res}) => {
      const session = await getSession(req, res);

      if (hasRequiredPermissions(session?.user?.permissions, 'read', 'sales')) {
        return {
          redirect: {
            destination: '/sales',
            permanent: false,
          },
        };
      } else if (hasRequiredPermissions(session?.user?.permissions, 'read', 'purchases')) {
        return {
          redirect: {
            destination: '/purchases',
            permanent: false,
          },
        };
      } else if (hasRequiredPermissions(session?.user?.permissions, 'read', 'manufacturing')) {
        return {
          redirect: {
            destination: '/manufacturing',
            permanent: false,
          },
        };
      } else if (hasRequiredPermissions(session?.user?.permissions, 'read', 'inventory')) {
        return {
          redirect: {
            destination: '/inventory',
            permanent: false,
          },
        };
      } else if (hasRequiredPermissions(session?.user?.permissions, 'read', 'customers')) {
        return {
          redirect: {
            destination: '/customers',
            permanent: false,
          },
        };
      } else if (hasRequiredPermissions(session?.user?.permissions, 'read', 'suppliers')) {
        return {
          redirect: {
            destination: '/suppliers',
            permanent: false,
          },
        };
      } else if (hasRequiredPermissions(session?.user?.permissions, 'read', 'employees')) {
        return {
          redirect: {
            destination: '/employees',
            permanent: false,
          },
        };
      } else if (hasRequiredPermissions(session?.user?.permissions, 'financial', 'invoices')) {
        return {
          redirect: {
            destination: '/invoices',
            permanent: false,
          },
        };
      } else if (hasRequiredPermissions(session?.user?.permissions, 'financial', 'reports')) {
        return {
          redirect: {
            destination: '/reports',
            permanent: false,
          },
        };
      } else if (hasRequiredPermissions(session?.user?.permissions, 'update', 'settings')) {
        return {
          redirect: {
            destination: '/settings',
            permanent: false,
          },
        };
      }

      return {
        redirect: {
          destination: '/api/auth/logout',
          permanent: false,
        },
      };
    },
  })(context);
};
