import {getSession, withPageAuthRequired} from '@auth0/nextjs-auth0';
import {GetServerSideProps} from 'next';

import CustomerDetails from '@/components/pages/details/customers/CustomerDetails';
import {NextPageWithLayout} from '@/types/global';
import {hasRequiredPermissions} from '@/utils/permissions';
import {getReturnTo} from 'utils/common';

type Props = {
  id: string;
};

const CustomerDetailsPage: NextPageWithLayout<Props> = ({id}) => {
  return <CustomerDetails id={id} />;
};

export default CustomerDetailsPage;

export const getServerSideProps: GetServerSideProps = async (context) => {
  const {params} = context;
  const {customerId} = params || {};

  return withPageAuthRequired({
    ...getReturnTo(context),
    getServerSideProps: async ({locale, req, res}) => {
      const session = await getSession(req, res);

      if (!hasRequiredPermissions(session?.user?.permissions, 'update', 'customers')) {
        return {
          redirect: {
            destination: '/customers',
            permanent: false,
          },
        };
      }
      return {
        props: {
          id: customerId,
          messages: (await import(`../../messages/${locale}.json`)).default,
        },
      };
    },
  })(context);
};
