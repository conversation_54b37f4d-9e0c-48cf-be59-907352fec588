import {getSession, withPageAuthRequired} from '@auth0/nextjs-auth0';
import {GetServerSideProps} from 'next';

import CustomersList from '@/components/pages/lists/customers/CustomersList';
import {NextPageWithLayout} from '@/types/global';
import {hasRequiredPermissions} from '@/utils/permissions';
import {getReturnTo} from 'utils/common';

const CustomersPage: NextPageWithLayout = () => {
  return <CustomersList />;
};

export default CustomersPage;

export const getServerSideProps: GetServerSideProps = async (context) => {
  return withPageAuthRequired({
    ...getReturnTo(context),
    getServerSideProps: async ({locale, req, res}) => {
      const session = await getSession(req, res);

      if (!hasRequiredPermissions(session?.user?.permissions, 'read', 'customers')) {
        return {
          redirect: {
            destination: '/',
            permanent: false,
          },
        };
      }
      return {
        props: {
          messages: (await import(`../../messages/${locale}.json`)).default,
        },
      };
    },
  })(context);
};
