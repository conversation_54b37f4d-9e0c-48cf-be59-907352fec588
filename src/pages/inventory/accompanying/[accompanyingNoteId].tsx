import {getSession, withPageAuthRequired} from '@auth0/nextjs-auth0';
import {GetServerSideProps} from 'next';

import AccompanyingNotesDetails from '@/components/pages/details/inventory/accompanying/AccompanyingNotesDetails';
import {hasRequiredPermissions} from '@/utils/permissions';
import {NextPageWithLayout} from 'types/global';
import {getReturnTo} from 'utils/common';

type Props = {
  accompanyingNoteId: string;
};

const AccompanyingNotesPreviewPage: NextPageWithLayout<Props> = ({accompanyingNoteId}) => {
  return <AccompanyingNotesDetails id={accompanyingNoteId} />;
};

export default AccompanyingNotesPreviewPage;

export const getServerSideProps: GetServerSideProps = async (context) => {
  const {params} = context;
  const {accompanyingNoteId} = params || {};
  return withPageAuthRequired({
    ...getReturnTo(context),
    getServerSideProps: async ({locale, req, res}) => {
      const session = await getSession(req, res);

      if (!hasRequiredPermissions(session?.user?.permissions, 'read', 'inventory')) {
        return {
          redirect: {
            destination: '/',
            permanent: false,
          },
        };
      }
      if (!hasRequiredPermissions(session?.user?.permissions, 'financial', 'inventory')) {
        return {
          redirect: {
            destination: '/inventory',
            permanent: false,
          },
        };
      }
      return {
        props: {
          accompanyingNoteId,
          messages: (await import(`../../../messages/${locale}.json`)).default,
        },
      };
    },
  })(context);
};
