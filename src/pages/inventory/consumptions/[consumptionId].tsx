import {getSession, withPageAuthRequired} from '@auth0/nextjs-auth0';
import {GetServerSideProps} from 'next';

import ConsumptionDetails from '@/components/pages/details/inventory/consumptions/ConsumptionDetails';
import {hasRequiredPermissions} from '@/utils/permissions';
import {NextPageWithLayout} from 'types/global';
import {getReturnTo} from 'utils/common';

type Props = {
  consumptionId: string;
};

const ConsumptionsDetailsPage: NextPageWithLayout<Props> = ({consumptionId}) => {
  return <ConsumptionDetails id={consumptionId} />;
};

export default ConsumptionsDetailsPage;

export const getServerSideProps: GetServerSideProps = async (context) => {
  const {params} = context;
  const {consumptionId} = params || {};
  return withPageAuthRequired({
    ...getReturnTo(context),
    getServerSideProps: async ({locale, req, res}) => {
      const session = await getSession(req, res);

      if (!hasRequiredPermissions(session?.user?.permissions, 'read', 'inventory')) {
        return {
          redirect: {
            destination: '/',
            permanent: false,
          },
        };
      }
      if (
        !hasRequiredPermissions(session?.user?.permissions, 'financial', 'inventory') ||
        !hasRequiredPermissions(session?.user?.permissions, 'read', 'manufacturing')
      ) {
        return {
          redirect: {
            destination: '/inventory',
            permanent: false,
          },
        };
      }
      return {
        props: {
          consumptionId,
          messages: (await import(`../../../messages/${locale}.json`)).default,
        },
      };
    },
  })(context);
};
