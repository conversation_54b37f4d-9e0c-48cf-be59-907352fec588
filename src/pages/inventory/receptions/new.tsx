import {getSession, withPageAuthRequired} from '@auth0/nextjs-auth0';
import {GetServerSideProps} from 'next';

import ReceptionCreate from '@/components/pages/details/inventory/receptions/ReceptionCreate';
import {hasRequiredPermissions} from '@/utils/permissions';
import {NextPageWithLayout} from 'types/global';
import {getReturnTo} from 'utils/common';

type Props = {};

const ReceptionCreatePage: NextPageWithLayout<Props> = () => {
  return <ReceptionCreate />;
};

export default ReceptionCreatePage;

export const getServerSideProps: GetServerSideProps = async (context) => {
  return withPageAuthRequired({
    ...getReturnTo(context),
    getServerSideProps: async ({locale, req, res}) => {
      const session = await getSession(req, res);

      if (!hasRequiredPermissions(session?.user?.permissions, 'read', 'inventory')) {
        return {
          redirect: {
            destination: '/',
            permanent: false,
          },
        };
      }
      if (!hasRequiredPermissions(session?.user?.permissions, 'financial', 'inventory')) {
        return {
          redirect: {
            destination: '/inventory',
            permanent: false,
          },
        };
      }
      return {
        props: {
          messages: (await import(`../../../messages/${locale}.json`)).default,
        },
      };
    },
  })(context);
};
