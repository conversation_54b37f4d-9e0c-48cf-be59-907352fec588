import {getSession, withPageAuthRequired} from '@auth0/nextjs-auth0';
import {GetServerSideProps} from 'next';

import MoveDetails from '@/components/pages/details/inventory/adjustments/MoveDetails';
import {NextPageWithLayout} from '@/types/global';
import {hasRequiredPermissions} from '@/utils/permissions';
import {getReturnTo} from 'utils/common';

type Props = {
  id: string;
};

const AdjustmentDetailsMovePage: NextPageWithLayout<Props> = ({id}) => {
  return <MoveDetails id={id} />;
};

export default AdjustmentDetailsMovePage;

export const getServerSideProps: GetServerSideProps = async (context) => {
  const {params} = context;
  const {adjustmentId} = params || {};

  return withPageAuthRequired({
    ...getReturnTo(context),
    getServerSideProps: async ({locale, req, res}) => {
      const session = await getSession(req, res);

      if (!hasRequiredPermissions(session?.user?.permissions, 'read', 'inventory')) {
        return {
          redirect: {
            destination: '/',
            permanent: false,
          },
        };
      }
      if (!hasRequiredPermissions(session?.user?.permissions, 'financial', 'inventory')) {
        return {
          redirect: {
            destination: '/inventory',
            permanent: false,
          },
        };
      }
      return {
        props: {
          id: adjustmentId,
          messages: (await import(`../../../../messages/${locale}.json`)).default,
        },
      };
    },
  })(context);
};
