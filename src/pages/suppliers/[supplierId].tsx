import {getSession, withPageAuthRequired} from '@auth0/nextjs-auth0';
import {GetServerSideProps} from 'next';

import SupplierDetails from '@/components/pages/details/suppliers/SupplierDetails';
import {NextPageWithLayout} from '@/types/global';
import {hasRequiredPermissions} from '@/utils/permissions';
import {getReturnTo} from 'utils/common';

type Props = {
  id: string;
};

const SupplierDetailsPage: NextPageWithLayout<Props> = ({id}) => {
  return <SupplierDetails id={id} />;
};

export default SupplierDetailsPage;

export const getServerSideProps: GetServerSideProps = async (context) => {
  const {params} = context;
  const {supplierId} = params || {};

  return withPageAuthRequired({
    ...getReturnTo(context),
    getServerSideProps: async ({locale, req, res}) => {
      const session = await getSession(req, res);

      if (!hasRequiredPermissions(session?.user?.permissions, 'update', 'suppliers')) {
        return {
          redirect: {
            destination: '/',
            permanent: false,
          },
        };
      }
      return {
        props: {
          id: supplierId,
          messages: (await import(`../../messages/${locale}.json`)).default,
        },
      };
    },
  })(context);
};
