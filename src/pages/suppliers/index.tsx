import {getSession, withPageAuthRequired} from '@auth0/nextjs-auth0';
import {GetServerSideProps} from 'next';

import SuppliersList from '@/components/pages/lists/suppliers/SuppliersList';
import {NextPageWithLayout} from '@/types/global';
import {hasRequiredPermissions} from '@/utils/permissions';
import {getReturnTo} from 'utils/common';

const SuppliersPage: NextPageWithLayout = () => {
  return <SuppliersList />;
};

export default SuppliersPage;

export const getServerSideProps: GetServerSideProps = async (context) => {
  return withPageAuthRequired({
    ...getReturnTo(context),
    getServerSideProps: async ({locale, req, res}) => {
      const session = await getSession(req, res);

      if (!hasRequiredPermissions(session?.user?.permissions, 'read', 'suppliers')) {
        return {
          redirect: {
            destination: '/',
            permanent: false,
          },
        };
      }
      return {
        props: {
          messages: (await import(`../../messages/${locale}.json`)).default,
        },
      };
    },
  })(context);
};
