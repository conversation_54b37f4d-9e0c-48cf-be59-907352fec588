import {getSession, withPageAuthRequired} from '@auth0/nextjs-auth0';
import {GetServerSideProps} from 'next';

import EmployeeDetails from '@/components/pages/details/employees/EmployeeDetails';
import {NextPageWithLayout} from '@/types/global';
import {hasRequiredPermissions} from '@/utils/permissions';
import {getReturnTo} from 'utils/common';

type Props = {
  id: string;
};

const EmployeeDetailsPage: NextPageWithLayout<Props> = ({id}) => {
  return <EmployeeDetails id={id} />;
};

export default EmployeeDetailsPage;

export const getServerSideProps: GetServerSideProps = async (context) => {
  const {params} = context;
  const {employeeId} = params || {};

  return withPageAuthRequired({
    ...getReturnTo(context),
    getServerSideProps: async ({locale, req, res}) => {
      const session = await getSession(req, res);

      if (!hasRequiredPermissions(session?.user?.permissions, 'update', 'employees')) {
        return {
          redirect: {
            destination: '/employees',
            permanent: false,
          },
        };
      }
      return {
        props: {
          id: employeeId,
          messages: (await import(`../../messages/${locale}.json`)).default,
        },
      };
    },
  })(context);
};
