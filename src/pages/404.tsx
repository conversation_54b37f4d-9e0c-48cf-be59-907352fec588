import {ArrowLeftIcon} from 'lucide-react';
import {GetStaticProps} from 'next';
import Link from 'next/link';
import {useTranslations} from 'next-intl';

import Logo from '@/assets/Logo';
import {NoLayout} from '@/components/layout';
import {Button} from '@/components/ui/Button';
import {useRouter} from '@/hooks/helpers/useRouter';
import {NextPageWithLayout} from 'types/global';

const Page404: NextPageWithLayout = () => {
  const t = useTranslations();
  const {back, replace} = useRouter();

  return (
    <div className='min-h-full bg-white pb-12 pt-24'>
      <main className='mx-auto flex w-full max-w-7xl grow flex-col justify-center px-4 sm:px-6 lg:px-8'>
        <div className='flex shrink-0 justify-center'>
          <Link href='/api/auth/logout'>
            <Logo className='size-24 text-blue-500' />
          </Link>
        </div>
        <div className='py-16'>
          <div className='text-center'>
            <h1 className='mt-2 text-4xl font-bold tracking-tight sm:text-5xl'>{t('page not found')}</h1>
            <p className='mt-2 text-base text-slate-900/75'>
              {t("sorry, we couldn't find the page you're looking for")}
            </p>
            <div className='mt-6 flex justify-center gap-4'>
              <Button onClick={() => back('/')} variant='text'>
                <ArrowLeftIcon />
                {t('go back')}
              </Button>
              <Button onClick={() => replace('/')} variant='text'>
                {t('go to home')}
              </Button>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default Page404;

Page404.Layout = NoLayout;

export const getStaticProps: GetStaticProps = async ({locale}) => {
  return {
    props: {
      messages: (await import(`../messages/${locale}.json`)).default,
    },
  };
};
