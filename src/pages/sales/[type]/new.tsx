import {getSession, withPageAuthRequired} from '@auth0/nextjs-auth0';
import {GetServerSideProps} from 'next';

import SalesDetails from '@/components/pages/details/sales/SalesDetails';
import {SaleType} from '@/types/sales';
import {hasRequiredPermissions} from '@/utils/permissions';
import {NextPageWithLayout} from 'types/global';
import {getReturnTo} from 'utils/common';

type Props = {
  type: SaleType;
};

const SaleOrderCreatePage: NextPageWithLayout<Props> = ({type}) => {
  return <SalesDetails saleType={type} />;
};

export default SaleOrderCreatePage;

export const getServerSideProps: GetServerSideProps = async (context) => {
  const {params} = context;
  const {type} = params || {};

  return withPageAuthRequired({
    ...getReturnTo(context),
    getServerSideProps: async ({locale, req, res}) => {
      const session = await getSession(req, res);

      if (!hasRequiredPermissions(session?.user?.permissions, 'update', 'sales')) {
        return {
          redirect: {
            destination: '/sales',
            permanent: false,
          },
        };
      }
      return {
        props: {
          messages: (await import(`../../../messages/${locale}.json`)).default,
          type,
        },
      };
    },
  })(context);
};
