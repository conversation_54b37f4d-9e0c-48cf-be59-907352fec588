import {getSession, withPageAuthRequired} from '@auth0/nextjs-auth0';
import {GetServerSideProps} from 'next';

import SaleOfferDetails from '@/components/pages/details/sales/offer/SaleOfferDetails';
import {NextPageWithLayout} from '@/types/global';
import {hasRequiredPermissions} from '@/utils/permissions';
import {getReturnTo} from 'utils/common';

type Props = {
  offerId: string;
  saleId: string;
};

const SaleOrderOfferDetailsPage: NextPageWithLayout<Props> = ({offerId, saleId}) => {
  return <SaleOfferDetails offerId={offerId} saleId={saleId} />;
};

export default SaleOrderOfferDetailsPage;

export const getServerSideProps: GetServerSideProps = async (context) => {
  const {params} = context;
  const {offerId, saleId} = params || {};

  return withPageAuthRequired({
    ...getReturnTo(context),
    getServerSideProps: async ({locale, req, res}) => {
      const session = await getSession(req, res);

      if (!hasRequiredPermissions(session?.user?.permissions, 'update', 'sales')) {
        return {
          redirect: {
            destination: '/sales',
            permanent: false,
          },
        };
      }
      return {
        props: {
          messages: (await import(`../../../../../messages/${locale}.json`)).default,
          offerId,
          saleId,
        },
      };
    },
  })(context);
};
