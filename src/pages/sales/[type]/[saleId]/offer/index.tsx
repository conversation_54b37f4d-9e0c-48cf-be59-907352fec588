import {getSession, withPageAuthRequired} from '@auth0/nextjs-auth0';
import {GetServerSideProps} from 'next';

import SaleOfferCreate from '@/components/pages/details/sales/offer/SaleOfferCreate';
import {NextPageWithLayout} from '@/types/global';
import {hasRequiredPermissions} from '@/utils/permissions';
import {getReturnTo} from 'utils/common';

type Props = {
  saleId: string;
};

const SaleOrderOfferCreatePage: NextPageWithLayout<Props> = ({saleId}) => {
  return <SaleOfferCreate saleId={saleId} />;
};

export default SaleOrderOfferCreatePage;

export const getServerSideProps: GetServerSideProps = async (context) => {
  const {params} = context;
  const {saleId} = params || {};

  return withPageAuthRequired({
    ...getReturnTo(context),
    getServerSideProps: async ({locale, req, res}) => {
      const session = await getSession(req, res);

      if (!hasRequiredPermissions(session?.user?.permissions, 'update', 'sales')) {
        return {
          redirect: {
            destination: '/sales',
            permanent: false,
          },
        };
      }
      return {
        props: {
          messages: (await import(`../../../../../messages/${locale}.json`)).default,
          saleId,
        },
      };
    },
  })(context);
};
