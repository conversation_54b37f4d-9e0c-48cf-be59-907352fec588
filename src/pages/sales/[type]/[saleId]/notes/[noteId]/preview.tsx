import {getSession, withPageAuthRequired} from '@auth0/nextjs-auth0';
import {GetServerSideProps} from 'next';

import SaleNotesDetails from '@/components/pages/details/sales/note/SaleNotesDetails';
import {NextPageWithLayout} from '@/types/global';
import {hasRequiredPermissions} from '@/utils/permissions';
import {getReturnTo} from 'utils/common';

type Props = {
  id: string;
  saleId: string;
};

const SaleOrderNoteEditPage: NextPageWithLayout<Props> = ({id, saleId}) => {
  return <SaleNotesDetails id={id} saleId={saleId} />;
};

export default SaleOrderNoteEditPage;

export const getServerSideProps: GetServerSideProps = async (context) => {
  const {params} = context;
  const {noteId, saleId} = params || {};

  return withPageAuthRequired({
    ...getReturnTo(context),
    getServerSideProps: async ({locale, req, res}) => {
      const session = await getSession(req, res);

      if (!hasRequiredPermissions(session?.user?.permissions, 'update', 'sales')) {
        return {
          redirect: {
            destination: '/sales',
            permanent: false,
          },
        };
      }
      return {
        props: {
          id: noteId,
          messages: (await import(`../../../../../../messages/${locale}.json`)).default,
          saleId,
        },
      };
    },
  })(context);
};
