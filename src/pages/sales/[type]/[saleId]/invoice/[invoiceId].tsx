import {getSession, withPageAuthRequired} from '@auth0/nextjs-auth0';
import {GetServerSideProps} from 'next';

import SaleInvoicesDetails from '@/components/pages/details/sales/invoice/SaleInvoicesDetails';
import {NextPageWithLayout} from '@/types/global';
import {hasRequiredPermissions} from '@/utils/permissions';
import {getReturnTo} from 'utils/common';

type Props = {
  invoiceId: string;
  saleId: string;
};

const SaleOrderInvoiceDetailsPage: NextPageWithLayout<Props> = ({invoiceId, saleId}) => {
  return <SaleInvoicesDetails invoiceId={invoiceId} saleId={saleId} />;
};

export default SaleOrderInvoiceDetailsPage;

export const getServerSideProps: GetServerSideProps = async (context) => {
  const {params} = context;
  const {invoiceId, saleId} = params || {};

  return withPageAuthRequired({
    ...getReturnTo(context),
    getServerSideProps: async ({locale, req, res}) => {
      const session = await getSession(req, res);

      if (!hasRequiredPermissions(session?.user?.permissions, 'update', 'sales')) {
        return {
          redirect: {
            destination: '/sales',
            permanent: false,
          },
        };
      }
      return {
        props: {
          invoiceId,
          messages: (await import(`../../../../../messages/${locale}.json`)).default,
          saleId,
        },
      };
    },
  })(context);
};
