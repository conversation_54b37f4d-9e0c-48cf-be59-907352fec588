import {getSession, withPageAuthRequired} from '@auth0/nextjs-auth0';
import {GetServerSideProps} from 'next';

import SalesDetails from '@/components/pages/details/sales/SalesDetails';
import {NextPageWithLayout} from '@/types/global';
import {SaleType} from '@/types/sales';
import {hasRequiredPermissions} from '@/utils/permissions';
import {getReturnTo} from 'utils/common';

type Props = {
  id: string;
  type: SaleType;
};

const SaleOrdersDetailsPage: NextPageWithLayout<Props> = ({id, type}) => {
  return <SalesDetails id={id} saleType={type} />;
};

export default SaleOrdersDetailsPage;

export const getServerSideProps: GetServerSideProps = async (context) => {
  const {params} = context;
  const {saleId, type} = params || {};

  return withPageAuthRequired({
    ...getReturnTo(context),
    getServerSideProps: async ({locale, req, res}) => {
      const session = await getSession(req, res);

      if (!hasRequiredPermissions(session?.user?.permissions, 'update', 'sales')) {
        return {
          redirect: {
            destination: '/sales',
            permanent: false,
          },
        };
      }
      return {
        props: {
          id: saleId,
          messages: (await import(`../../../../messages/${locale}.json`)).default,
          type,
        },
      };
    },
  })(context);
};
