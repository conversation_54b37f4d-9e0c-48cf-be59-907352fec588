import {getSession, withPageAuthRequired} from '@auth0/nextjs-auth0';
import {GetServerSideProps} from 'next';

import SalesList from '@/components/pages/lists/sales/SalesList';
import {SaleType} from '@/types/sales';
import {hasRequiredPermissions} from '@/utils/permissions';
import {NextPageWithLayout} from 'types/global';
import {getReturnTo} from 'utils/common';

type Props = {
  type: SaleType;
};

const SaleOrdersPage: NextPageWithLayout<Props> = ({type}) => {
  return <SalesList saleType={type} />;
};

export default SaleOrdersPage;

export const getServerSideProps: GetServerSideProps = async (context) => {
  const {params} = context;
  const {type} = params || {};

  return withPageAuthRequired({
    ...getReturnTo(context),
    getServerSideProps: async ({locale, req, res}) => {
      const session = await getSession(req, res);

      if (!hasRequiredPermissions(session?.user?.permissions, 'read', 'sales')) {
        return {
          redirect: {
            destination: '/',
            permanent: false,
          },
        };
      }
      return {
        props: {
          messages: (await import(`../../../messages/${locale}.json`)).default,
          type,
        },
      };
    },
  })(context);
};
