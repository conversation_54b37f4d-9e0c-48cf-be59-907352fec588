import {getSession, withPageAuthRequired} from '@auth0/nextjs-auth0';
import {GetServerSideProps} from 'next';

import InvoiceDetails from '@/components/pages/details/invoices/InvoiceDetails';
import {hasRequiredPermissions} from '@/utils/permissions';
import {NextPageWithLayout} from 'types/global';
import {getReturnTo} from 'utils/common';

type Props = {
  invoiceId: string;
};

const InvoicesDetailsPage: NextPageWithLayout<Props> = ({invoiceId}) => {
  return <InvoiceDetails id={invoiceId} />;
};

export default InvoicesDetailsPage;

export const getServerSideProps: GetServerSideProps = async (context) => {
  const {params} = context;
  const {invoiceId} = params || {};
  return withPageAuthRequired({
    ...getReturnTo(context),
    getServerSideProps: async ({locale, req, res}) => {
      const session = await getSession(req, res);

      if (!hasRequiredPermissions(session?.user?.permissions, 'financial', 'invoices')) {
        return {
          redirect: {
            destination: '/',
            permanent: false,
          },
        };
      }
      return {
        props: {
          invoiceId,
          messages: (await import(`../../messages/${locale}.json`)).default,
        },
      };
    },
  })(context);
};
