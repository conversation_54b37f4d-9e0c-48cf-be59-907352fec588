import {withPageAuthRequired} from '@auth0/nextjs-auth0';
import {GetServerSideProps} from 'next';

import Dashboard from '@/components/pages/dashboard/Dashboard';
import {NextPageWithLayout} from 'types/global';
import {getReturnTo} from 'utils/common';

const DashboardPage: NextPageWithLayout = () => {
  return <Dashboard />;
};

export default DashboardPage;

export const getServerSideProps: GetServerSideProps = async (context) => {
  return withPageAuthRequired({
    ...getReturnTo(context),
    getServerSideProps: async ({locale}) => {
      return {
        props: {
          messages: (await import(`../messages/${locale}.json`)).default,
        },
      };
    },
  })(context);
};
