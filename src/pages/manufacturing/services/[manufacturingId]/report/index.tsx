import {getSession, withPageAuthRequired} from '@auth0/nextjs-auth0';
import {GetServerSideProps} from 'next';

import ManufacturingServiceDetailsReport from '@/components/pages/details/manufacturing/services/report/ManufacturingServiceDetailsReport';
import {NextPageWithLayout} from '@/types/global';
import {getReturnTo} from '@/utils/common';
import {hasRequiredPermissions} from '@/utils/permissions';

type Props = {
  manufacturingId: string;
};

const ManufacturingServiceEstimateReportPage: NextPageWithLayout<Props> = ({manufacturingId}) => {
  return <ManufacturingServiceDetailsReport serviceId={manufacturingId} />;
};

export default ManufacturingServiceEstimateReportPage;

export const getServerSideProps: GetServerSideProps = async (context) => {
  const {params} = context;
  const {manufacturingId} = params || {};

  return withPageAuthRequired({
    ...getReturnTo(context),
    getServerSideProps: async ({locale, req, res}) => {
      const session = await getSession(req, res);

      if (!hasRequiredPermissions(session?.user?.permissions, 'update', 'manufacturing')) {
        return {
          redirect: {
            destination: '/manufacturing',
            permanent: false,
          },
        };
      }
      return {
        props: {
          manufacturingId,
          messages: (await import(`../../../../../messages/${locale}.json`)).default,
        },
      };
    },
  })(context);
};
