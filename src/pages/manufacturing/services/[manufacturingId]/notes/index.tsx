import {getSession, withPageAuthRequired} from '@auth0/nextjs-auth0';
import {GetServerSideProps} from 'next';

import ManufacturingServicesNoteCreate from '@/components/pages/details/manufacturing/services/note/ManufacturingServicesNoteCreateEdit/ManufacturingServicesNoteCreate';
import {NextPageWithLayout} from '@/types/global';
import {hasRequiredPermissions} from '@/utils/permissions';
import {getReturnTo} from 'utils/common';

type Props = {
  manufacturingId: string;
};

const ManufacturingServicesNotePreviewPage: NextPageWithLayout<Props> = ({manufacturingId}) => {
  return <ManufacturingServicesNoteCreate serviceId={manufacturingId} />;
};

export default ManufacturingServicesNotePreviewPage;

export const getServerSideProps: GetServerSideProps = async (context) => {
  const {params} = context;
  const {manufacturingId} = params || {};

  return withPageAuthRequired({
    ...getReturnTo(context),
    getServerSideProps: async ({locale, req, res}) => {
      const session = await getSession(req, res);

      if (!hasRequiredPermissions(session?.user?.permissions, 'update', 'sales')) {
        return {
          redirect: {
            destination: '/sales',
            permanent: false,
          },
        };
      }
      return {
        props: {
          manufacturingId,
          messages: (await import(`../../../../../messages/${locale}.json`)).default,
        },
      };
    },
  })(context);
};
