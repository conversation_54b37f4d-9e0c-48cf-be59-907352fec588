import {getSession, withPageAuthRequired} from '@auth0/nextjs-auth0';
import {GetServerSideProps} from 'next';

import ManufacturingServicesNotesDetails from '@/components/pages/details/manufacturing/services/note/ManufacturingServicesNotesDetails';
import {NextPageWithLayout} from '@/types/global';
import {hasRequiredPermissions} from '@/utils/permissions';
import {getReturnTo} from 'utils/common';

type Props = {
  id: string;
  manufacturingId: string;
};

const ManufacturingServicesNoteEditPage: NextPageWithLayout<Props> = ({id, manufacturingId}) => {
  return <ManufacturingServicesNotesDetails id={id} serviceId={manufacturingId} />;
};

export default ManufacturingServicesNoteEditPage;

export const getServerSideProps: GetServerSideProps = async (context) => {
  const {params} = context;
  const {manufacturingId, noteId} = params || {};

  return withPageAuthRequired({
    ...getReturnTo(context),
    getServerSideProps: async ({locale, req, res}) => {
      const session = await getSession(req, res);

      if (!hasRequiredPermissions(session?.user?.permissions, 'update', 'sales')) {
        return {
          redirect: {
            destination: '/sales',
            permanent: false,
          },
        };
      }
      return {
        props: {
          id: noteId,
          manufacturingId,
          messages: (await import(`../../../../../../messages/${locale}.json`)).default,
        },
      };
    },
  })(context);
};
