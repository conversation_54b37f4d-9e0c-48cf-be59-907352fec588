import {withPageAuthRequired} from '@auth0/nextjs-auth0';
import {GetServerSideProps} from 'next';

import useWorkstations from '@/hooks/useWorkstations';
import TaskManagementLayoutTest, {
  TaskView,
} from '@/oldcomponents/page/manufacturing/TaskManagementLayout/TaskManagementLayoutTest';
import {NextPageWithLayout} from '@/types/global';
import {getReturnTo} from 'utils/common';

const WorkstationsTasksPage: NextPageWithLayout = () => {
  const {workstations} = useWorkstations();

  return (
    <TaskManagementLayoutTest
      resources={workstations.map((workstation) => ({...workstation, title: workstation.name}))}
      view={TaskView.workstations}
    />
  );
};

export default WorkstationsTasksPage;

export const getServerSideProps: GetServerSideProps = async (context) => {
  return withPageAuthRequired({
    ...getReturnTo(context),
    getServerSideProps: async ({locale}) => {
      return {
        props: {
          messages: (await import(`../../../messages/${locale}.json`)).default,
        },
      };
    },
  })(context);
};
