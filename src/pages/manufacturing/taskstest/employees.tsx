import {withPageAuthRequired} from '@auth0/nextjs-auth0';
import {GetServerSideProps} from 'next';

import useEmployees from '@/hooks/useEmployees';
import TaskManagementLayoutTest, {
  TaskView,
} from '@/oldcomponents/page/manufacturing/TaskManagementLayout/TaskManagementLayoutTest';
import {NextPageWithLayout} from '@/types/global';
import {getReturnTo} from 'utils/common';

const EmployeesTasksPage: NextPageWithLayout = () => {
  const {employees} = useEmployees({workersOnly: true});

  return (
    <TaskManagementLayoutTest
      resources={employees.map((employee) => ({...employee, title: employee.name}))}
      view={TaskView.employees}
    />
  );
};

export default EmployeesTasksPage;

export const getServerSideProps: GetServerSideProps = async (context) => {
  return withPageAuthRequired({
    ...getReturnTo(context),
    getServerSideProps: async ({locale}) => {
      return {
        props: {
          messages: (await import(`../../../messages/${locale}.json`)).default,
        },
      };
    },
  })(context);
};
