import {getSession, withPageAuthRequired} from '@auth0/nextjs-auth0';
import {GetServerSideProps} from 'next';

import ManufacturingOrderDetails from '@/components/pages/details/manufacturing/orders/ManufacturingOrderDetails';
import {NextPageWithLayout} from '@/types/global';
import {hasRequiredPermissions} from '@/utils/permissions';
import {getReturnTo} from 'utils/common';

type Props = {
  id: string;
};

const ManufacturingOrderDetailsPage: NextPageWithLayout<Props> = ({id}) => {
  return <ManufacturingOrderDetails id={id} />;
};

export default ManufacturingOrderDetailsPage;

export const getServerSideProps: GetServerSideProps = async (context) => {
  const {params} = context;
  const {manufacturingId} = params || {};

  return withPageAuthRequired({
    ...getReturnTo(context),
    getServerSideProps: async ({locale, req, res}) => {
      const session = await getSession(req, res);

      if (!hasRequiredPermissions(session?.user?.permissions, 'update', 'manufacturing')) {
        return {
          redirect: {
            destination: '/manufacturing',
            permanent: false,
          },
        };
      }
      return {
        props: {
          id: manufacturingId,
          messages: (await import(`../../../../messages/${locale}.json`)).default,
        },
      };
    },
  })(context);
};
