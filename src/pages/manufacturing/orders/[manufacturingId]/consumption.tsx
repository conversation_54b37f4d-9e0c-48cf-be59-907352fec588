import {getSession, withPageAuthRequired} from '@auth0/nextjs-auth0';
import {GetServerSideProps} from 'next';

import ManufacturingConsumptionCreate from '@/components/pages/details/manufacturing/orders/consumption/ManufacturingConsumptionCreate';
import {NextPageWithLayout} from '@/types/global';
import {hasRequiredPermissions} from '@/utils/permissions';
import {getReturnTo} from 'utils/common';

type Props = {
  id: string;
};

const ManufacturingOrderConsumptionPage: NextPageWithLayout<Props> = ({id}) => {
  return <ManufacturingConsumptionCreate manufacturingId={id} />;
};

export default ManufacturingOrderConsumptionPage;

export const getServerSideProps: GetServerSideProps = async (context) => {
  const {params} = context;
  const {manufacturingId} = params || {};

  return withPageAuthRequired({
    ...getReturnTo(context),
    getServerSideProps: async ({locale, req, res}) => {
      const session = await getSession(req, res);

      if (!hasRequiredPermissions(session?.user?.permissions, 'update', 'manufacturing')) {
        return {
          redirect: {
            destination: '/manufacturing',
            permanent: false,
          },
        };
      }
      return {
        props: {
          id: manufacturingId,
          messages: (await import(`../../../../messages/${locale}.json`)).default,
        },
      };
    },
  })(context);
};
