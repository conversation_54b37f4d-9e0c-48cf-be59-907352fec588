import {getSession, withPageAuthRequired} from '@auth0/nextjs-auth0';
import {GetServerSideProps} from 'next';

import ManufacturingOrderProductionReportDetails from '@/components/pages/details/manufacturing/orders/production/ManufacturingOrderProductionReportDetails';
import {NextPageWithLayout} from '@/types/global';
import {hasRequiredPermissions} from '@/utils/permissions';
import {getReturnTo} from 'utils/common';

type Props = {
  manufacturingId: string;
};

const ManufacturingProductionReportPage: NextPageWithLayout<Props> = ({manufacturingId}) => {
  return <ManufacturingOrderProductionReportDetails manufacturingId={manufacturingId} />;
};

export default ManufacturingProductionReportPage;

export const getServerSideProps: GetServerSideProps = async (context) => {
  const {params} = context;
  const {manufacturingId} = params || {};

  return withPageAuthRequired({
    ...getReturnTo(context),
    getServerSideProps: async ({locale, req, res}) => {
      const session = await getSession(req, res);

      if (!hasRequiredPermissions(session?.user?.permissions, 'update', 'manufacturing')) {
        return {
          redirect: {
            destination: '/manufacturing',
            permanent: false,
          },
        };
      }
      return {
        props: {
          manufacturingId,
          messages: (await import(`../../../../messages/${locale}.json`)).default,
        },
      };
    },
  })(context);
};
