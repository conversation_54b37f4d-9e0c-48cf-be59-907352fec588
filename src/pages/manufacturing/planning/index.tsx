import {useEffect} from 'react';

import {getSession, withPageAuthRequired} from '@auth0/nextjs-auth0';
import {useAtomValue} from 'jotai';
import {GetServerSideProps} from 'next';

import {manufacturingPlanningTabAtom} from '@/components/pages/lists/manufacturing/planning/manufacturingPlanningStore';
import {useRouter} from '@/hooks/helpers/useRouter';
import {NextPageWithLayout} from '@/types/global';
import {getReturnTo} from '@/utils/common';
import {hasRequiredPermissions} from '@/utils/permissions';

const PlanningRedirectPage: NextPageWithLayout = () => {
  const planningTab = useAtomValue(manufacturingPlanningTabAtom);
  const {replace} = useRouter();

  useEffect(() => {
    if (planningTab) replace(`/manufacturing/planning/${planningTab}`);
  }, [planningTab, replace]);

  return null;
};

export default PlanningRedirectPage;

export const getServerSideProps: GetServerSideProps = async (context) => {
  return withPageAuthRequired({
    ...getReturnTo(context),
    getServerSideProps: async ({locale, req, res}) => {
      const session = await getSession(req, res);

      if (!hasRequiredPermissions(session?.user?.permissions, 'read', 'manufacturing')) {
        return {
          redirect: {
            destination: '/',
            permanent: false,
          },
        };
      }
      return {
        props: {
          messages: (await import(`../../../messages/${locale}.json`)).default,
        },
      };
    },
  })(context);
};
