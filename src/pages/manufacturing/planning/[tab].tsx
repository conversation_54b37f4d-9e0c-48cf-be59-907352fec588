import {getSession, withPageAuthRequired} from '@auth0/nextjs-auth0';
import {GetServerSideProps} from 'next';

import ManufacturingPlanning from '@/components/pages/lists/manufacturing/planning/ManufacturingPlanning';
import {ManufacturingPlanningTab} from '@/components/pages/lists/manufacturing/planning/manufacturingPlanningStore';
import {NextPageWithLayout} from '@/types/global';
import {hasRequiredPermissions} from '@/utils/permissions';
import {getReturnTo} from 'utils/common';

type Props = {
  tab: ManufacturingPlanningTab;
};

const ManufacturingPlanningPage: NextPageWithLayout<Props> = ({tab}) => {
  return <ManufacturingPlanning tab={tab} />;
};

export default ManufacturingPlanningPage;

export const getServerSideProps: GetServerSideProps = async (context) => {
  const {params} = context;
  const {tab} = params || {};

  return withPageAuthRequired({
    ...getReturnTo(context),
    getServerSideProps: async ({locale, req, res}) => {
      const session = await getSession(req, res);

      if (!hasRequiredPermissions(session?.user?.permissions, 'read', 'manufacturing')) {
        return {
          redirect: {
            destination: '/',
            permanent: false,
          },
        };
      }
      return {
        props: {
          messages: (await import(`../../../messages/${locale}.json`)).default,
          tab,
        },
      };
    },
  })(context);
};
