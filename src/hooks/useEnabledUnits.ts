import {useAtomValue} from 'jotai';
import {filter} from 'lodash';

import {Unit} from '@/types/global';
import {enabledUnitsAtom} from 'store/defaults';

import useUnits from './useUnits';

const useEnabledUnits = () => {
  const {error, isLoading, isValidating, units} = useUnits();
  const enabledUnitIds = useAtomValue(enabledUnitsAtom);

  const enabledUnits = filter(units, (unit) => enabledUnitIds.includes(unit.id));

  return {
    enabledUnits: (enabledUnits || []) as Unit[],
    error,
    isLoading,
    isValidating,
    units: (units || []) as Unit[],
  };
};

export default useEnabledUnits;
