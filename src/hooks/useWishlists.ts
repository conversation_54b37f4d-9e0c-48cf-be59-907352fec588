import axios from 'axios';
import {useTranslations} from 'next-intl';
import useSWR from 'swr';

import useWishlistActions from '@/hooks/useWishlistActions';
import {handleError} from '@/utils/axios';
import {Wishlist} from 'types/purchases';
import {getQueryString} from 'utils/common';

const useWishlists = (supplierId?: string) => {
  const t = useTranslations();
  const {prepareData} = useWishlistActions();

  const {data, error, isLoading, isValidating} = useSWR<Wishlist[]>(
    ['wishlists', supplierId],
    () =>
      axios
        .get(
          `/api/purchases/wishlist/suppliers/list?${getQueryString({
            supplier_id: supplierId,
          })}`,
        )
        .then((res) => res.data.map(prepareData))
        .catch((error) =>
          handleError(error, t('an error occurred while loading name', {name: t('suffixed.wishlists')})),
        ),
    {revalidateOnFocus: false},
  );

  return {
    error,
    isLoading,
    isValidating,
    wishlists: data || [],
  };
};

export default useWishlists;
