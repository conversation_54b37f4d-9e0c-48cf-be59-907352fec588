import {useCallback, useEffect, useState} from 'react';

import {joiResolver} from '@hookform/resolvers/joi';
import axios from 'axios';
import Joi from 'joi';
import {useTranslations} from 'next-intl';
import {useForm} from 'react-hook-form';
import useSWR from 'swr';

import {toast} from '@/components/ui/Toast';
import {useRouter} from '@/hooks/helpers/useRouter';
import useAccompanyingNotesActions from '@/hooks/useAccompanyingNotesActions';
import {AccompanyingNote, AtMostOneOf} from '@/types/global';
import {handleError} from '@/utils/axios';

import useLeaveConfirm from './helpers/useLeaveConfirm';

export const accompanyingNoteSchema = Joi.object({
  delegate: Joi.object({
    id: Joi.string().required(),
  }).unknown(),
  deliveryDate: Joi.string().required(),
  id: Joi.string().optional(),
  items: Joi.array()
    .items(
      Joi.object({
        id: Joi.string().required(),
        name: Joi.string().required(),
        quantity: Joi.number().positive().required(),
      }).unknown(),
    )
    .min(1)
    .required(),
  transportRegistrationNumber: Joi.string().required(),
}).unknown();

const useAccompanyingNote = (
  id?: string,
  {saleId, serviceId}: AtMostOneOf<{saleId?: string; serviceId?: string}> = {},
) => {
  const t = useTranslations();
  const {replace} = useRouter();
  const [created, setCreated] = useState(false);
  const {
    createAccompanyingNote: createAccompanyingNoteAction,
    createManufacturingServiceAccompanyingNote: createManufacturingServiceAccompanyingNoteAction,
    createSaleAccompanyingNote: createSaleAccompanyingNoteAction,
    prepareData,
    updateAccompanyingNote: updateAccompanyingNoteAction,
  } = useAccompanyingNotesActions();

  const {
    control,
    formState: {dirtyFields, errors, ...restUseFormState},
    handleSubmit,
    register,
    reset,
    resetField,
    setValue,
    watch,
    ...restUseForm
  } = useForm<AccompanyingNote>({
    defaultValues: {
      delegate: {
        id: '',
        name: '',
      },
      deliveryDate: new Date().toISOString(),
      items: [],
    },
    mode: 'onSubmit',
    resolver: joiResolver(accompanyingNoteSchema),
  });

  const isDirty = !!Object.keys(dirtyFields).length;

  useLeaveConfirm(created ? false : isDirty, async () => await saveAccompanyingNote());

  useEffect(() => {
    if (watch('id') && created) {
      if (saleId) {
        replace(`/sales/orders/${saleId}/notes/${watch('id')}`);
      } else if (serviceId) {
        replace(`/manufacturing/services/${serviceId}?tab=materials`);
      } else {
        replace(`/inventory/accompanying/${watch('id')}`);
      }
    }
  }, [id, created, replace, saleId, serviceId, watch]);

  const {data, isLoading, isValidating, mutate} = useSWR(
    id ? ['accompanyingNote', id] : null,
    () =>
      axios
        .get(`/api/goods-accompanying-notes/${id}/details`)
        .then((res) => prepareData(res.data))
        .then((values) => {
          reset(values);

          return values;
        })
        .catch((error) =>
          handleError(error, t('an error occurred while loading name', {name: t('suffixed.order.end')})),
        ),
    {revalidateOnFocus: false},
  );

  useEffect(() => {
    if (data) reset(data as AccompanyingNote);
  }, [data, reset]);

  const saveAccompanyingNote = useCallback(
    () =>
      handleSubmit(
        (values) => {
          return id
            ? updateAccompanyingNoteAction(id, values).then(() => mutate())
            : (saleId
                ? createSaleAccompanyingNoteAction(saleId, values)
                : serviceId
                  ? createManufacturingServiceAccompanyingNoteAction(serviceId!, values)
                  : createAccompanyingNoteAction(values)
              ).then((data) => {
                if (data?.id) {
                  setCreated(true);
                  setValue('id', data.id);
                }
              });
        },
        () => {
          toast.warning(t('please fill in all mandatory fields before saving'));
          return Promise.reject();
        },
      )(),
    [
      handleSubmit,
      id,
      updateAccompanyingNoteAction,
      saleId,
      createSaleAccompanyingNoteAction,
      serviceId,
      createManufacturingServiceAccompanyingNoteAction,
      createAccompanyingNoteAction,
      mutate,
      setValue,
      t,
    ],
  );

  return {
    accompanyingNote: watch() as AccompanyingNote,
    isDirty,
    isLoading: isLoading || (id && !watch('id')),
    isValidating,
    saveAccompanyingNote,
    useFormActions: {
      control,
      formState: {dirtyFields, errors, ...restUseFormState},
      handleSubmit,
      register,
      reset,
      resetField,
      setValue: ((name, value: any, options = {shouldDirty: true}) =>
        setValue(name, value, options)) as typeof setValue,
      watch,
      ...restUseForm,
    },
  };
};

export default useAccompanyingNote;
