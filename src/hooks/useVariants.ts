import axios from 'axios';
import {useTranslations} from 'next-intl';
import useSWR from 'swr';

import useItemActions from '@/hooks/useItemActions';
import {InventoryItem} from '@/types/inventory';
import {handleError} from '@/utils/axios';

const useVariants = (id: string) => {
  const t = useTranslations();
  const {prepareData} = useItemActions();

  const {data, error, isLoading, isValidating} = useSWR<InventoryItem[]>(
    id ? ['variants', id] : null,
    () =>
      axios
        .get(`/api/goods/${id}/variants/list`)
        .then((res) => res.data)
        .then((data) => data.map(prepareData))
        .catch((error) =>
          handleError(error, t('an error occurred while loading name', {name: t('suffixed.variants')})),
        ),
    {revalidateOnFocus: false},
  );

  return {
    error,
    isLoading,
    isValidating,
    variants: data || [],
  };
};

export default useVariants;
