import axios from 'axios';
import {useTranslations} from 'next-intl';
import useSWR from 'swr';

import useInventoryAdjustmentActions from '@/hooks/useInventoryAdjustmentActions';
import {handleError} from '@/utils/axios';
import {Adjustment} from 'types/inventory';
import {getQueryString} from 'utils/common';

const useInventoryAdjustments = ({
  sort = [],
}: {
  sort?: ('-createTime' | '-updateTime' | 'createTime' | 'updateTime')[];
} = {}) => {
  const t = useTranslations();
  const {prepareData} = useInventoryAdjustmentActions();

  const {data, error, isLoading, isValidating} = useSWR<Adjustment[]>(
    ['adjustments', sort],
    () =>
      axios
        .get(
          `/api/inventory/adjustments/list?${getQueryString({
            sort: (sort || []).join(','),
          })}`,
        )
        .then((res) => res.data.map(prepareData))
        .catch((error) =>
          handleError(error, t('an error occurred while loading name', {name: t('suffixed.adjustments')})),
        ),
    {revalidateOnFocus: false},
  );

  return {
    adjustments: data || [],
    error,
    isLoading,
    isValidating,
  };
};

export default useInventoryAdjustments;
