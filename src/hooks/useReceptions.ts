import axios from 'axios';
import {format} from 'date-fns';
import {useTranslations} from 'next-intl';
import useSWR from 'swr';

import useReceptionActions from '@/hooks/useReceptionActions';
import {handleError} from '@/utils/axios';
import {InventoryReception} from 'types/inventory';
import {getQueryString} from 'utils/common';

const useReceptions = ({end, search, start}: {end?: Date; search?: string; start?: Date} = {}) => {
  const t = useTranslations();
  const {prepareData} = useReceptionActions();

  const {data, error, isLoading, isValidating} = useSWR<InventoryReception[]>(
    ['receptions', search, start, end],
    () =>
      axios
        .get(
          `/api/inventory/receptions/list?${getQueryString({
            end: end ? format(end || 0, 'yyyy-MM-dd') : undefined,
            q: search,
            start: start ? format(start || 0, 'yyyy-MM-dd') : undefined,
          })}`,
        )
        .then((res) => res.data.map(prepareData))
        .catch((error) =>
          handleError(
            error,
            t('an error occurred while loading name', {
              name: t('suffixed.receptions'),
            }),
          ),
        ),
    {revalidateOnFocus: false},
  );

  return {
    error,
    isLoading,
    isValidating,
    receptions: data || [],
  };
};

export default useReceptions;
