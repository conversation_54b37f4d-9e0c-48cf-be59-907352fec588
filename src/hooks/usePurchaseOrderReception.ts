import {useCallback, useEffect, useState} from 'react';

import axios from 'axios';
import FormData from 'form-data';
import {useAtomValue} from 'jotai';
import {useTranslations} from 'next-intl';
import {useSWRConfig} from 'swr';

import {toast} from '@/components/ui/Toast';
import {useRouter} from '@/hooks/helpers/useRouter';
import usePurchaseOrder from '@/hooks/usePurchaseOrder';
import useReception from '@/hooks/useReception';
import useReceptionActions from '@/hooks/useReceptionActions';
import useSettings from '@/hooks/useSettings';
import {defaultCurrencyAtom} from '@/store/defaults';
import {handleError} from '@/utils/axios';
import useLeaveConfirm from 'hooks/helpers/useLeaveConfirm';
import {Reception, SupportingDocumentType} from 'types/purchases';

const usePurchaseOrderReception = (purchaseId: string) => {
  const t = useTranslations();
  const {replace} = useRouter();
  const [created, setCreated] = useState(false);
  const {isLoading: purchaseOrderIsLoading, purchaseOrder} = usePurchaseOrder(purchaseId);
  const {prepareData, processValues} = useReceptionActions();
  const defaultCurrency = useAtomValue(defaultCurrencyAtom);
  const {isLoading: settingsIsLoading, settings} = useSettings();
  const [isLoading, setIsLoading] = useState(true);
  const {mutate: globalMutate} = useSWRConfig();
  const {
    useFormActions: {
      control,
      formState: {dirtyFields, errors, ...restUseFormState},
      handleSubmit,
      register,
      reset,
      resetField,
      setValue,
      watch,
      ...restUseForm
    },
  } = useReception(undefined, {withoutLeaveConfirm: true});

  useEffect(() => {
    if (!created && !purchaseOrderIsLoading && !settingsIsLoading) {
      reset({
        additionalCosts: [],
        currency: purchaseOrder.items?.[0]?.price?.currency || defaultCurrency,
        files: [],
        goods: purchaseOrder.items.map((item) => ({
          ...item,
          additionalCostCustomValue: {amount: 0, currency: item.price?.currency || defaultCurrency},
          calculatedAdditionalCost: {
            amount: item.price?.amount || 0,
            currency: item.price?.currency || defaultCurrency,
          },
          inventoryUnit: {
            id: settings?.general?.inventoryAccountingSettings?.unitDesignations?.defaultInventoryUnit,
            name: '',
          },
          orderedQuantity: item.quantity,
          price: {amount: item.price?.amount || 0, currency: item.price?.currency || defaultCurrency},
          receivedQuantity: item.quantity,
          totalValue: {
            amount: (item.price?.amount || 0) * item.quantity,
            currency: item.price?.currency || defaultCurrency,
          },
        })),
        purchaseOrder: {id: purchaseOrder.id, name: purchaseOrder.number},
        receptionDate: new Date().toISOString(),
        supplier: purchaseOrder.supplier,
        supportingDocument: {type: SupportingDocumentType.NONE},
      });
      setIsLoading(false);
    }
  }, [
    defaultCurrency,
    purchaseOrderIsLoading,
    prepareData,
    purchaseOrder.id,
    purchaseOrder.items,
    purchaseOrder.number,
    purchaseOrder.supplier,
    reset,
    settings?.general?.inventoryAccountingSettings?.unitDesignations?.defaultInventoryUnit,
    settingsIsLoading,
    created,
  ]);

  const isDirty = !!Object.keys(dirtyFields).length;

  useLeaveConfirm(created ? false : isDirty, async () => await deliverPurchaseOrder());

  useEffect(() => {
    if (created) replace(`/purchases/orders/${purchaseId}`);
  }, [created, purchaseId, replace]);

  const deliverPurchaseOrder = useCallback(
    (files?: File[]) =>
      handleSubmit(
        (values) => {
          const newValues = processValues(values);

          const formData = new FormData();

          formData.append('goodsReceived', JSON.stringify({...newValues, files: undefined, supplier: undefined}));
          files?.forEach((file) => formData.append('files', file));

          return axios
            .post(`/api/purchases/orders/${purchaseId}/delivered`, formData)
            .then(() => {
              globalMutate(
                (key) =>
                  Array.isArray(key) &&
                  (key[0] === 'receptions' ||
                    key[0] === 'purchases' ||
                    (key[0] === 'purchase' && key[1] === purchaseId)),
              );
              toast.success(
                t('name has been created successfully', {
                  created: t('created.female'),
                  name: t('suffixed.reception.start'),
                }),
              );
              setCreated(true);
            })
            .catch((error) =>
              handleError(
                error,
                t('name has failed to create successfully', {
                  created: t('created.female'),
                  name: t('suffixed.reception.start'),
                }),
              ),
            );
        },
        () => {
          toast.warning(t('please fill in all mandatory fields before saving'));
          return Promise.reject();
        },
      )(),
    [globalMutate, handleSubmit, purchaseId, processValues, t],
  );

  return {
    deliverPurchaseOrder,
    isDirty,
    isLoading,
    purchaseReception: watch() as Reception,
    useFormActions: {
      control,
      formState: {dirtyFields, errors, ...restUseFormState},
      handleSubmit,
      register,
      reset,
      resetField,
      setValue: ((name, value: any, options = {shouldDirty: true}) =>
        setValue(name, value, options)) as typeof setValue,
      watch,
      ...restUseForm,
    },
  };
};

export default usePurchaseOrderReception;
