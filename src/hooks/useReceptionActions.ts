import {useCallback} from 'react';

import axios from 'axios';
import FormData from 'form-data';
import {filter} from 'lodash';
import {useTranslations} from 'next-intl';
import {useSWRConfig} from 'swr';

import {toast} from '@/components/ui/Toast';
import {Reception, ReceptionItem} from '@/types/purchases';
import {handleError} from '@/utils/axios';
import {parseCurrency} from '@/utils/common';

const useReceptionActions = () => {
  const {mutate: globalMutate} = useSWRConfig();
  const t = useTranslations();

  const prepareData = useCallback((values: any) => {
    if (!values) return {};
    return {
      ...values,
      additionalCosts:
        values?.additionalCosts?.map((additionalCost: ReceptionItem) => ({
          ...additionalCost,
          price: parseCurrency(additionalCost.price, false),
        })) || [],
      goods: values?.goods.map((good: ReceptionItem) => ({
        ...good,
        additionalCostCustomValue: parseCurrency(good.additionalCostCustomValue, false),
        calculatedAdditionalCost: parseCurrency(good.calculatedAdditionalCost, false),
        price: parseCurrency(good.price, false),
        totalValue: parseCurrency(good.totalValue, false),
      })),
    } as Reception;
  }, []);

  const processValues = useCallback(
    (values: Partial<Reception>) => ({
      ...values,
      additionalCosts: filter(values.additionalCosts, (additionalCost) => (additionalCost?.price?.amount || 0) > 0).map(
        (additionalCost) => ({
          ...additionalCost,
          price: parseCurrency(additionalCost?.price),
        }),
      ),
      goods: values?.goods?.map((good) => ({
        ...good,
        additionalCostCustomValue: parseCurrency(good?.additionalCostCustomValue),
        inventoryUnitId: good?.inventoryUnit?.id,
        materialGoodId: good?.materialGood?.id,
        price: parseCurrency(good?.price),
      })),
      purchaseOrderId: values?.purchaseOrder?.id,
      supplierId: values?.supplier?.id,
    }),
    [],
  );

  const createReception = useCallback(
    (values: Partial<Reception>, files: File[] = []) => {
      const newValues = processValues(values);

      const formData = new FormData();

      formData.append('goodsReceived', JSON.stringify({...newValues, files: undefined, supplier: undefined}));
      files.forEach((file) => formData.append('files', file));

      return axios
        .post('/api/inventory/receptions/receive', formData)
        .then((res) => res.data)
        .then((data) => {
          toast.success(
            t('name has been created successfully', {
              created: t('created.female'),
              name: `${t('suffixed.reception.start')} ${data.number}`,
            }),
          );
          globalMutate((key) => Array.isArray(key) && key[0] === 'receptions');

          return data;
        })
        .catch((error) =>
          handleError(
            error,
            t('name has failed to create successfully', {
              created: t('created.female'),
              name: t('suffixed.reception.start'),
            }),
          ),
        );
    },
    [globalMutate, processValues, t],
  );

  const importWinMENTORReception = useCallback(
    (file: File) => {
      const formData = new FormData();

      formData.append('files', file);
      return axios
        .post(`/api/import/receptions/winmentor-xls`, formData, {
          headers: {'X-Original-Filename': encodeURIComponent(file.name)},
        })
        .then((res) => res.data)
        .then((data) => {
          globalMutate((key) => Array.isArray(key) && key[0] === 'receptions');
          toast.success(
            t('name have been imported successfully', {
              imported: t('imported.female'),
              name: t('suffixed.reception.start'),
            }),
          );

          return prepareData({
            ...data,
            goods: data.goods?.map((good: any) => ({...good, measurementUnit: good.materialGood.measurementUnit})),
          }) as any;
        })
        .catch((error) =>
          handleError(
            error,
            t('name has failed to import successfully', {
              imported: t('imported.female'),
              name: t('suffixed.reception.start'),
            }),
          ),
        );
    },
    [globalMutate, prepareData, t],
  );

  return {
    createReception,
    importWinMENTORReception,
    prepareData,
    processValues,
  };
};

export default useReceptionActions;
