import {useCallback} from 'react';

import axios from 'axios';
import {useAtomValue} from 'jotai';
import {useTranslations} from 'next-intl';
import {useSWRConfig} from 'swr';

import {toast} from '@/components/ui/Toast';
import {defaultCurrencyAtom, defaultVATAtom} from '@/store/defaults';
import {Service} from '@/types/global';
import {handleError} from '@/utils/axios';
import {parseCurrency} from '@/utils/common';

const useServiceTemplateActions = () => {
  const t = useTranslations();
  const {mutate: globalMutate} = useSWRConfig();
  const defaultCurrency = useAtomValue(defaultCurrencyAtom);
  const defaultVat = useAtomValue(defaultVATAtom);

  const prepareData = useCallback(
    (values: any) => {
      if (!values) return {};
      return {
        ...values,
        cost: parseCurrency(values.cost, false),
        sellPrice: parseCurrency(values.sellPrice, false),
        vatRate: values.vatRate || defaultVat,
      } as Service;
    },
    [defaultVat],
  );

  const processValues = useCallback(
    (values: Partial<Service>) => ({
      ...values,
      cost: parseCurrency({
        amount: values.cost?.amount || 0,
        currency: values.cost?.currency || defaultCurrency,
      }),
      measurementUnit: values.measurementUnit || 'PIECE',
      sellPrice: parseCurrency({
        amount: values.sellPrice?.amount || 0,
        currency: values.sellPrice?.currency || defaultCurrency,
      }),
      vatRate: values.vatRate || defaultVat,
    }),
    [defaultCurrency, defaultVat],
  );

  const createService = useCallback(
    (values: Partial<Service>) => {
      const newValues = processValues(values);

      return axios
        .post('/api/service-templates/create', newValues)
        .then((res) => res.data)
        .then((data: Service) => {
          toast.success(
            t('name has been created successfully', {
              created: t('created.male'),
              name: `${t('suffixed.service.start')} ${data.name}`,
            }),
          );

          globalMutate((key) => Array.isArray(key) && key[0] === 'serviceTemplates');

          return data;
        })
        .catch((error) => {
          handleError(
            error,
            t('name has failed to create successfully', {
              created: t('created.male'),
              name: t('suffixed.service.start'),
            }),
          );

          return null;
        });
    },
    [globalMutate, processValues, t],
  );

  const updateService = useCallback(
    (id: string, values: Partial<Service>) => {
      const newValues = processValues(values);

      return axios
        .post(`/api/service-templates/${id}/update`, newValues)
        .then((res) => res.data)
        .then((data: Service) => {
          toast.success(
            t('name has been updated successfully', {
              name: `${t(`suffixed.service.start`)} ${name}`,
              updated: t('updated.male'),
            }),
          );

          globalMutate((key) => Array.isArray(key) && key[0] === 'serviceTemplates');

          return data;
        })
        .catch((error) =>
          handleError(
            error,
            t('name has failed to update successfully', {
              name: t('suffixed.service.start'),
              updated: t('updated.male'),
            }),
          ),
        );
    },
    [globalMutate, processValues, t],
  );

  const deleteService = useCallback(
    (id: string, name: string) =>
      axios
        .delete(`/api/service-templates/${id}/delete`)
        .then(() => {
          toast.success(
            t('name has been deleted successfully', {
              deleted: t('deleted.male'),
              name: `${t(`suffixed.service.start`)} ${name}`,
            }),
          );
          globalMutate((key) => Array.isArray(key) && key[0] === 'serviceTemplates');
        })
        .catch((error) =>
          handleError(error, t('name has failed to delete successfully', {deleted: t('deleted.male'), name: name})),
        ),
    [globalMutate, t],
  );

  return {
    createService,
    deleteService,
    prepareData,
    processValues,
    updateService,
  };
};

export default useServiceTemplateActions;
