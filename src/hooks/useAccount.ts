import axios from 'axios';
import {useTranslations} from 'next-intl';
import useSWR from 'swr';

import {Id} from '@/types/global';
import {handleError} from '@/utils/axios';

const useAccount = () => {
  const t = useTranslations();

  const {data, error, isLoading, isValidating} = useSWR<Id>(
    'account',
    () =>
      axios
        .get('/api/account/information/name')
        .then((res) => res.data)
        .catch((error) => handleError(error, t('an error occurred while loading name', {name: t('suffixed.account')}))),
    {revalidateOnFocus: false},
  );

  return {
    account: (data as Id) || undefined,
    error,
    isLoading,
    isValidating,
  };
};

export default useAccount;
