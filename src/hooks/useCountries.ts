import axios from 'axios';
import {useLocale, useTranslations} from 'next-intl';
import useSWR from 'swr';

import {handleError} from '@/utils/axios';
import {ConfigCountry} from 'types/global';

const useCountries = () => {
  const t = useTranslations();
  const locale = useLocale();

  const {data, error, isLoading, isValidating} = useSWR(
    ['countries'],
    () =>
      axios
        .get(`/api/configurations/countries${locale ? `?language=${locale}` : ''}`)
        .then((res) => res.data)
        .then((countries) => Object.entries(countries).map(([name, code]) => ({code, name}) as ConfigCountry))
        .catch((error) =>
          handleError(error, t('an error occurred while loading name', {name: t('suffixed.countries')})),
        ),
    {revalidateOnFocus: false},
  );

  return {
    countries: (data || []) as ConfigCountry[],
    error,
    isLoading,
    isValidating,
  };
};

export default useCountries;
