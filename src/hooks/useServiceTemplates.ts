import axios from 'axios';
import {useTranslations} from 'next-intl';
import useSWR from 'swr';

import useServiceTemplateActions from '@/hooks/useServiceTemplateActions';
import {handleError} from '@/utils/axios';
import {Service} from 'types/global';

const useServiceTemplates = () => {
  const t = useTranslations();
  const {prepareData} = useServiceTemplateActions();

  const {data, error, isLoading, isValidating} = useSWR(
    ['serviceTemplates'],
    () =>
      axios
        .get('/api/service-templates/list')
        .then((res) => res.data.map(prepareData))
        .catch((error) =>
          handleError(error, t('an error occurred while loading name', {name: t('suffixed.services')})),
        ),
    {revalidateOnFocus: false},
  );

  return {
    error,
    isLoading,
    isValidating,
    services: (data || []) as Service[],
  };
};
export default useServiceTemplates;
