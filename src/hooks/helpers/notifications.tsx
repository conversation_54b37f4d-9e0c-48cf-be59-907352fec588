import {ElementType, FC} from 'react';

import {CogIcon, MessageCircleIcon} from 'lucide-react';

import {Badge} from '@/components/ui/Badge';
import {ManufacturingTaskStatusReason} from '@/types/global';
import {
  CommentNotification,
  ManufacturingTaskNotification,
  Notification,
  NotificationMeta,
  NotificationSection,
  NotificationType,
} from '@/types/interface';

const NotificationIcon: FC<{
  icon: ElementType;
  variant?: 'default' | 'error' | 'info' | 'light' | 'none' | 'primary' | 'secondary' | 'success' | 'warning';
}> = ({icon: Icon, variant = 'default'}) => (
  <Badge className='flex size-10 items-center justify-center rounded-full' variant={variant}>
    <Icon className='size-5 shrink-0' />
  </Badge>
);

export const getNotificationMeta = (notification: Notification, t: any): NotificationMeta => {
  switch (notification.type) {
    case NotificationType.ISSUE:
      switch (notification.section) {
        case NotificationSection.MANUFACTURING_TASK: {
          const task = notification as ManufacturingTaskNotification;
          const meta = {
            ...task,
            href: `/manufacturing/orders/${task.details.orderId}`,
            icon: <NotificationIcon icon={CogIcon} variant='warning' />,
            relatedId: task.details.orderId,
            silent: false,
          };

          switch (task.details.statusReason) {
            case ManufacturingTaskStatusReason.BROKEN_EQUIPMENT:
              return {
                ...meta,
                message: t('order_number: name has broken down or needs servicing', {
                  name: task.details.workstationName || t('suffixed.workstation.start'),
                  order_number: task.details.orderNumber || t('order'),
                }),
              };
            case ManufacturingTaskStatusReason.EQUIPMENT_UNAVAILABLE:
              return {
                ...meta,
                message: t('order_number: name is not available', {
                  name: task.details.workstationName || t('suffixed.workstation.start'),
                  order_number: task.details.orderNumber || t('order'),
                }),
              };
            case ManufacturingTaskStatusReason.MISSING_MATERIAL:
              return {
                ...meta,
                message: t('order_number: missing material', {
                  order_number: task.details.orderNumber || t('order'),
                }),
              };
            default:
              return {...task, silent: true};
          }
        }
        default:
          return {...notification, silent: true};
      }
    case NotificationType.COMMENT:
      const comment = notification as CommentNotification;
      const meta = {
        ...comment,
        icon: <NotificationIcon icon={MessageCircleIcon} variant='info' />,
        relatedId: comment.targetEntityId,
        silent: false,
      };
      switch (notification.section) {
        case NotificationSection.CUSTOMERS:
          return {
            ...meta,
            href: `/customers/${comment.targetEntityId}?noteId=${comment.details.noteId}`,
            message: t('name: you have been tagged by triggeredBy in a comment', {
              name: comment.details.name || t('customer'),
              triggeredBy: comment.triggeredBy,
            }),
          };
        case NotificationSection.MANUFACTURING:
          return {
            ...meta,
            href: `/manufacturing/orders/${comment.targetEntityId}?noteId=${comment.details.noteId}`,
            message: t('name: you have been tagged by triggeredBy in a comment', {
              name: comment.details.name || t('order'),
              triggeredBy: comment.triggeredBy,
            }),
          };
        case NotificationSection.PURCHASES:
          return {
            ...meta,
            href: `/purchases/orders/${comment.targetEntityId}?noteId=${comment.details.noteId}`,
            message: t('name: you have been tagged by triggeredBy in a comment', {
              name: comment.details.name || t('order'),
              triggeredBy: comment.triggeredBy,
            }),
          };
        case NotificationSection.SALES:
          return {
            ...meta,
            href: `/sales/orders/${comment.targetEntityId}?noteId=${comment.details.noteId}`,
            message: t('name: you have been tagged by triggeredBy in a comment', {
              name: comment.details.name || t('order'),
              triggeredBy: comment.triggeredBy,
            }),
          };
        case NotificationSection.SERVICING:
          return {
            ...meta,
            href: `/manufacturing/services/${comment.targetEntityId}?noteId=${comment.details.noteId}`,
            message: t('name: you have been tagged by triggeredBy in a comment', {
              name: comment.details.name || t('order'),
              triggeredBy: comment.triggeredBy,
            }),
          };
        default:
          return {...comment, silent: true};
      }
    default:
      return {...notification, silent: false};
  }
};
