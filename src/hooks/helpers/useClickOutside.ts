import {RefObject, useEffect} from 'react';

import {filter} from 'lodash';

import useDeepEqualMemo from './useDeepEqualMemo';

const useClickOutside = (refs: RefObject<any>[], handler: Function) => {
  const refsMemo = useDeepEqualMemo(refs);

  useEffect(() => {
    const listener = (event: Event) => {
      if (
        filter(
          refs,
          (ref): ref is RefObject<any> =>
            ref && 'current' in ref && (!ref.current || ref.current.contains(event.target)),
        ).length > 0
      ) {
        return;
      }

      handler(event);
    };

    document.addEventListener('mousedown', listener);
    document.addEventListener('touchstart', listener);

    return () => {
      document.removeEventListener('mousedown', listener);
      document.removeEventListener('touchstart', listener);
    };
  }, [refsMemo, handler, refs]);
};

export default useClickOutside;

//
// import {filter} from 'lodash';
// import {useEffect} from 'react';
// import useDeepEqualMemo from './useDeepEqualMemo';
// import type {RefObject} from 'react';
//
// const useClickOutside = (refs: RefObject<any>[], handler: Function) => {
//   const refsMemo = useDeepEqualMemo(refs);
//
//   useEffect(() => {
//     const listener = (event: Event) => {
//       if (filter(refs, (ref) => !ref.current || ref.current.contains(event.target)).length > 0) {
//         return;
//       }
//
//       handler(event);
//     };
//
//     document.addEventListener('mousedown', listener);
//     document.addEventListener('touchstart', listener);
//
//     return () => {
//       document.removeEventListener('mousedown', listener);
//       document.removeEventListener('touchstart', listener);
//     };
//   }, [refsMemo, handler, refs]);
// };
//
// export default useClickOutside;
