import {useEffect, useState} from 'react';

import {debounce} from 'lodash';
import useSWR, {SWRConfiguration} from 'swr';

const useSWRWithDebounce = <T>(
  cacheKey: (boolean | Date | string | undefined)[] | null,
  fetchFn: () => Promise<T>,
  options: SWRConfiguration = {},
  debounceDelay = 1000,
) => {
  const [uniqueKey, setUniqueKey] = useState<typeof cacheKey>(null);

  useEffect(() => {
    const debouncedSetUniqueKey = debounce((key) => {
      setUniqueKey(key);
    }, debounceDelay);

    debouncedSetUniqueKey(cacheKey);

    return () => {
      debouncedSetUniqueKey.cancel();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [cacheKey ? JSON.stringify(cacheKey) : null]);

  return useSWR<T>(uniqueKey, fetchFn, options);
};

export default useSWRWithDebounce;
