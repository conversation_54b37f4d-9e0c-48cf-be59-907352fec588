import {useEffect, useMemo, useState} from 'react';

import {useUser} from '@auth0/nextjs-auth0/client';

import {Actions, PermissionsAbility, Subjects} from '@/types/permissions';
import {definePermissions} from 'utils/permissions';

export const useHasPermission = (): {
  hasPermission: (action: Actions, subject: Subjects) => boolean;
  isLoading: boolean;
} => {
  const {user} = useUser();
  const [isLoading, setIsLoading] = useState(true);

  const ability: null | PermissionsAbility = useMemo(() => {
    if (user && typeof user.permissions !== 'undefined') {
      return definePermissions(user.permissions);
    }
    return null;
  }, [user]);

  useEffect(() => {
    if (ability !== null) {
      setIsLoading(false);
    } else {
      setIsLoading(true);
    }
  }, [ability]);

  const hasPermission = (action: Actions, subject: Subjects): boolean => {
    if (!ability) return false;
    if (action === 'read') {
      return ability.can('read', subject) || ability.can('update', subject);
    }
    return ability.can(action, subject);
  };

  return {hasPermission, isLoading};
};
