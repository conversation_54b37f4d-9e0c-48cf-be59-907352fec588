import {type RefObject, useEffect, useState} from 'react';

const useScroll = (ref: RefObject<any>) => {
  const [isTop, setIsTop] = useState(true);

  useEffect(() => {
    const currentRef = ref.current;

    if (!currentRef) return;

    const handleScroll = () => {
      if (currentRef.scrollTop === 0) {
        setIsTop(true);
      } else {
        setIsTop(false);
      }
    };

    currentRef.addEventListener('scroll', handleScroll);

    return () => {
      currentRef.removeEventListener('scroll', handleScroll);
    };
  }, [setIsTop, ref]);

  return {isTop};
};

const useWheel = (
  ref: RefObject<any>,
  {
    offset,
    onScrollDown = () => {},
    onScrollUp = () => {},
  }: {offset?: {down?: number; up?: number}; onScrollDown?: () => void; onScrollUp?: () => void} = {},
) => {
  const {isTop} = useScroll(ref);
  const [lastTop, setLastTop] = useState<number>();

  useEffect(() => {
    const handleScroll = (event: WheelEvent) => {
      if (event.deltaY > (offset?.down || 0)) {
        onScrollDown();
        setLastTop(undefined);
      } else if (event.deltaY < (offset?.up || 0) && isTop) {
        if (lastTop && Date.now() - lastTop > 500) {
          onScrollUp();
        } else if (!lastTop) {
          setLastTop(Date.now());
        }
      }
    };

    window.addEventListener('wheel', handleScroll);
    return () => window.removeEventListener('wheel', handleScroll);
  }, [onScrollUp, onScrollDown, offset?.down, offset?.up, isTop, lastTop]);
};

export default useWheel;
