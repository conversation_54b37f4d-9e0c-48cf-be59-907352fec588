import {ManufacturingTaskStatus, PurchaseStatus, SaleStatus, ServicingStatus} from 'types/global';

export const saleActions = {
  [SaleStatus.CANCELED as string]: 'cancel',
  [SaleStatus.DELIVERED as string]: 'delivered',
  [SaleStatus.PROCESSING as string]: 'processing',
  [SaleStatus.READY_TO_SHIP as string]: 'ready-to-ship',
  [SaleStatus.SHIPPING as string]: 'ship',
  [SaleStatus.SUBMITTED as string]: 'confirm',
};

export const taskActions = {
  [ManufacturingTaskStatus.DONE as string]: 'done',
  [ManufacturingTaskStatus.IN_PROGRESS as string]: 'in-progress',
  [ManufacturingTaskStatus.STOPPED as string]: 'stopped',
};

export const purchaseActions = {
  [PurchaseStatus.DELIVERED as string]: 'delivered',
};

export const servicingActions = {
  [ServicingStatus.DONE as string]: 'done',
  [ServicingStatus.MANUFACTURED as string]: 'manufactured',
  [ServicingStatus.MANUFACTURING as string]: 'manufacturing',
};
