import {ManufacturingTaskStatus, PurchaseStatus, SaleStatus, ServicingStatus} from 'types/global';

export const saleActions = {
  [SaleStatus.CANCELED as string]: 'cancel',
  [SaleStatus.DELIVERED as string]: 'delivered',
  [SaleStatus.ON_HOLD as string]: 'on-hold',
  [SaleStatus.PICKING_PACKING as string]: 'picking-packing',
  [SaleStatus.PROCESSING as string]: 'processing',
  [SaleStatus.QUOTE_SENT as string]: 'quote-sent',
  [SaleStatus.READY_TO_SHIP as string]: 'ready-to-ship',
  [SaleStatus.SHIPPING as string]: 'ship',
  [SaleStatus.SUBMITTED as string]: 'confirm',
};

export const taskActions = {
  [ManufacturingTaskStatus.DONE as string]: 'done',
  [ManufacturingTaskStatus.IN_PROGRESS as string]: 'in-progress',
  [ManufacturingTaskStatus.STOPPED as string]: 'stopped',
};

export const purchaseActions = {
  [PurchaseStatus.DELIVERED as string]: 'delivered',
  [PurchaseStatus.SENT_FOR_QUOTE as string]: 'send-for-quote',
};

export const servicingActions = {
  [ServicingStatus.BLOCKED as string]: 'blocked',
  [ServicingStatus.CLOSED as string]: 'closed',
  [ServicingStatus.EXECUTED as string]: 'executed',
  [ServicingStatus.IN_PROGRESS as string]: 'in-progress',
};
