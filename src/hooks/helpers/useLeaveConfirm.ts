import {useEffect} from 'react';

import {useAtom, useSetAtom} from 'jotai';

import {useRouter} from '@/hooks/helpers/useRouter';
import {isNavigatingAtom, leaveConfirmModalOpenAtom, pendingUrlAtom, saveCallbackAtom} from '@/store/ui';

const useLeaveConfirm = (enabled: boolean, onSave?: () => Promise<void>) => {
  const router = useRouter();
  const setIsModalOpen = useSetAtom(leaveConfirmModalOpenAtom);
  const setPendingUrl = useSetAtom(pendingUrlAtom);
  const setSaveCallback = useSetAtom(saveCallbackAtom);
  const [isNavigating, setIsNavigating] = useAtom(isNavigatingAtom);

  useEffect(() => {
    const handleRouteChangeStart = (url: string) => {
      if (!enabled || isNavigating) return;

      router.events.emit('routeChangeError');

      setPendingUrl(url);
      setSaveCallback(() => (onSave ? () => onSave() : null));
      setIsModalOpen(true);

      throw 'Navigation cancelled due to unsaved changes.';
    };

    const handleBeforeUnload = (event: BeforeUnloadEvent) => {
      if (enabled) {
        event.preventDefault();
        event.returnValue = '';
        return '';
      }
    };

    const handleRouteChangeComplete = () => setIsNavigating(false);
    const handleRouteChangeError = () => setIsNavigating(false);

    router.events.on('routeChangeStart', handleRouteChangeStart);
    router.events.on('routeChangeComplete', handleRouteChangeComplete);
    router.events.on('routeChangeError', handleRouteChangeError);
    window.addEventListener('beforeunload', handleBeforeUnload);

    return () => {
      router.events.off('routeChangeStart', handleRouteChangeStart);
      router.events.off('routeChangeComplete', handleRouteChangeComplete);
      router.events.off('routeChangeError', handleRouteChangeError);
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [enabled, isNavigating, onSave, router, setPendingUrl, setIsModalOpen, setIsNavigating, setSaveCallback]);
};

export default useLeaveConfirm;
