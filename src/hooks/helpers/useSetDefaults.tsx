import {useEffect} from 'react';

import {useAtomValue, useSetAtom} from 'jotai';

import useSettings from '@/hooks/useSettings';
import {setDefaultsAtom} from '@/store/defaults';
import {localeAtom} from '@/store/locale';

const useSetDefaults = () => {
  const setDefaults = useSetAtom(setDefaultsAtom);
  const {isLoading, settings} = useSettings();
  const locale = useAtomValue(localeAtom);

  useEffect(() => {
    if (!isLoading && settings) setDefaults(settings);
  }, [isLoading, setDefaults, settings]);

  if (!locale) return null;

  return {};
};

export default useSetDefaults;
