import {useCallback, useEffect, useRef} from 'react';

import {isEqual} from 'lodash';

export const useDeepEffect = (callback: () => void, dependencies: any[]) => {
  const currentDependenciesRef = useRef<any[]>([]);

  if (!isEqual(currentDependenciesRef.current, dependencies)) {
    currentDependenciesRef.current = dependencies;
  }

  // eslint-disable-next-line react-hooks/exhaustive-deps
  useEffect(callback, [currentDependenciesRef.current]);
};

export const useDeepMemo = <T>(factory: () => T, dependencies: any[]): T => {
  const currentDependenciesRef = useRef<any[]>([]);
  const memoizedValueRef = useRef<T>(null);

  if (!isEqual(currentDependenciesRef.current, dependencies)) {
    currentDependenciesRef.current = dependencies;
    memoizedValueRef.current = factory();
  }

  return memoizedValueRef.current!;
};

export const useDeepCallback = <T extends (...args: any[]) => any>(callback: T, dependencies: any[]): T => {
  const currentDependenciesRef = useRef<any[]>([]);
  const memoizedCallbackRef = useRef<T>(callback);

  if (!isEqual(currentDependenciesRef.current, dependencies)) {
    currentDependenciesRef.current = dependencies;
    memoizedCallbackRef.current = callback;
  }

  // eslint-disable-next-line react-hooks/exhaustive-deps
  return useCallback(memoizedCallbackRef.current, [currentDependenciesRef.current]);
};
