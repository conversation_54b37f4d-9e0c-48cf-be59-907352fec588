import {useCallback, useEffect, useRef} from 'react';

const useHeight = ({dependencies = []}: {dependencies?: any[]} = {}) => {
  const elementRef = useRef<any>(null);
  const targetElementRef = useRef<any>(null);

  const calculateHeight = useCallback(() => {
    if (elementRef.current) {
      const viewportHeight = window.innerHeight;
      const elementTopPosition = elementRef.current.getBoundingClientRect().top;
      const targetHeight = targetElementRef?.current?.getBoundingClientRect().height || 0;

      const height = viewportHeight - (elementTopPosition + targetHeight);
      elementRef.current.style.height = `${height}px`;
    }
  }, []);

  useEffect(() => {
    window.addEventListener('resize', calculateHeight);
    setTimeout(() => calculateHeight(), 110);

    return () => {
      window.removeEventListener('resize', calculateHeight);
    };
  }, [calculateHeight]);

  useEffect(() => {
    setTimeout(() => calculateHeight(), 110);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [calculateHeight, ...dependencies]);

  return {elementRef, targetElementRef};
};

export default useHeight;
