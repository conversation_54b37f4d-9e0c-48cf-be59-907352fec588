import {useCallback} from 'react';

import axios from 'axios';
import {useAtomValue} from 'jotai';
import {useTranslations} from 'next-intl';
import {useSWRConfig} from 'swr';

import {toast} from '@/components/ui/Toast';
import {defaultCurrencyAtom} from '@/store/defaults';
import {InventoryItemManufacturingOperation, InventoryItemVariantOption} from '@/types/inventory';
import {handleError} from '@/utils/axios';
import {newId, parseCurrency} from '@/utils/common';
import {InventoryItem, InventoryItemRequiredMaterial} from 'types/inventory';

const useItemActions = () => {
  const t = useTranslations();
  const defaultCurrency = useAtomValue(defaultCurrencyAtom);
  const {mutate: globalMutate} = useSWRConfig();

  const processMaterials = useCallback((values: Partial<InventoryItem>) => {
    type MaterialWithWaste = InventoryItemRequiredMaterial & {
      _wasteCount: number;
      _wasteSum: number;
    };

    const materialsMap = new Map<string, MaterialWithWaste>();

    const accumulate = (materials: InventoryItemRequiredMaterial[]) => {
      for (const material of materials) {
        const primaryOption = material.options?.[0];
        if (!primaryOption) continue;

        const existing = materialsMap.get(primaryOption.id);

        if (!existing) {
          materialsMap.set(primaryOption.id, {
            ...structuredClone(material),
            _wasteCount: 1,
            _wasteSum: material.wastePercentage ?? 0,
          });
        } else {
          const newMaterial = {...existing};
          newMaterial.quantity += material.quantity ?? 0;
          const existingOption = newMaterial.options?.[0];

          if (existingOption && primaryOption.cost && existingOption.cost) {
            existingOption.cost.amount += primaryOption.cost.amount ?? 0;
            existingOption.available = existingOption.available && primaryOption.available;
          }
          newMaterial._wasteSum += material.wastePercentage ?? 0;
          newMaterial._wasteCount += 1;
          materialsMap.set(primaryOption.id, newMaterial);
        }
      }
    };

    for (const step of values.manufacturingOperations ?? []) {
      accumulate(step.materials ?? []);
    }

    return Array.from(materialsMap.values()).map(({_wasteCount, _wasteSum, ...material}) => ({
      ...material,
      wastePercentage: _wasteCount > 0 ? _wasteSum / _wasteCount : 0,
    }));
  }, []);

  const prepareData = useCallback(
    (values: any) => {
      if (!values) return {};
      const preparedValues = {
        ...values,
        estimatedAdministrativeCost: parseCurrency(values.estimatedAdministrativeCost, false),
        estimatedEmployeeAndWorkstationCost: parseCurrency(values.estimatedEmployeeAndWorkstationCost, false),
        estimatedLaborCost: parseCurrency(values.estimatedLaborCost, false),
        estimatedMaterialCost: parseCurrency(values.estimatedMaterialCost, false),
        estimatedProductionCost: parseCurrency(values.estimatedProductionCost, false),
        estimatedProductionOverheadCost: parseCurrency(values.estimatedProductionOverheadCost, false),
        inventoryCostPerItem: parseCurrency(values.inventoryCostPerItem, false),
        manufacturingOperations:
          values.manufacturingOperations?.map((operation: InventoryItemManufacturingOperation) => ({
            ...operation,
            costPerHour: operation.costPerHour ? parseCurrency(operation.costPerHour, false) : null,
            id: newId(),
            materials:
              operation.materials?.map((material: InventoryItemRequiredMaterial) => ({
                ...material,
                options: material.options.map((option: any) => ({
                  ...option,
                  cost: parseCurrency(option.cost, false),
                  material: option.material
                    ? {
                        dimensions: option.material.dimensions?.map((dimension: any) => ({
                          dimensionKey: dimension.key,
                          name: '',
                          value: dimension.value,
                        })),
                        material: {density: 0, key: option.material.key || '', name: ''},
                        shape: {name: '', shape: option.material.shape || ''},
                      }
                    : null,
                })),
              })) || [],
          })) || [],
        margin: Number((values.margin * 100).toFixed(2)),
        produced: values.produced === null ? false : values.produced,
        sellPrice: parseCurrency(values.sellPrice, false),
        ...(values.lastPurchase
          ? {lastPurchase: {...values.lastPurchase, price: parseCurrency(values.lastPurchase.price, false)}}
          : {}),
      } as InventoryItem;

      return {...preparedValues, requiredMaterials: processMaterials(preparedValues)};
    },
    [processMaterials],
  );

  const prepareLightData = useCallback((values: any) => {
    if (!values) return {};

    return {
      ...values,
      category: values.category || {id: values.categoryId || '', name: ''},
    } as InventoryItem;
  }, []);

  const processValues = useCallback(
    (values: Partial<InventoryItem>) => ({
      ...values,
      categoryId: values.category?.id || '',
      manufacturingOperations:
        values.manufacturingOperations?.map((operation: InventoryItemManufacturingOperation) => ({
          ...operation,
          candidateEmployeeIds: operation?.candidateEmployees?.map((employee) => employee?.id) || [],
          candidateWorkstationIds: operation?.candidateWorkstations?.map((workstation) => workstation?.id) || [],
          costPerHour: operation.costPerHour ? parseCurrency(operation.costPerHour) : null,
          materials:
            operation.materials?.map((material) => ({
              ...material,
              materialIds: material.options.map((option) => option.id),
            })) || [],
        })) || [],
      material: values.material
        ? {
            ...values.material,
            dimensions: values.material?.dimensions.map((dimension) => ({
              ...dimension,
              key: dimension.dimensionKey,
            })),
            key: values.material?.material.key || '',
            shape: values.material?.shape.shape || '',
          }
        : null,
      measurementUnit: values.measurementUnit?.id || '',
      sellPrice: parseCurrency({
        amount: values.sellPrice?.amount || 0,
        currency: values.sellPrice?.currency || defaultCurrency,
      }),
    }),
    [defaultCurrency],
  );

  const createItem = useCallback(
    (values: Partial<InventoryItem>, parentId?: string) => {
      const newValues = processValues(values);

      return axios
        .post(parentId ? `/api/products/${parentId}/variants/create` : `/api/goods/create`, newValues)
        .then((res) => res.data)
        .then((data) => {
          toast.success(
            t('name has been created successfully', {
              created: t('created.male'),
              name: `${t(`suffixed.${parentId ? 'variant' : 'item'}.start`)} ${newValues.name}`,
            }),
          );
          globalMutate(
            (key) =>
              Array.isArray(key) &&
              (key[0] === 'items' || (key[0] === 'variants' && key[1] === parentId) || key[0] === 'inventory-stats'),
          );

          return data;
        })
        .catch((error) =>
          handleError(
            error,
            t('name has failed to create successfully', {
              created: t('created.male'),
              name: `${t(`suffixed.${parentId ? 'variant' : 'item'}.start`)} ${newValues.name}`,
            }),
          ),
        );
    },
    [globalMutate, processValues, t],
  );

  const getItem = useCallback(
    (id: string | undefined): Promise<InventoryItem> =>
      axios
        .get(`/api/goods/${id}/details`)
        .then((res) => prepareData(res.data) as InventoryItem)
        .catch((error) =>
          handleError(error, t('an error occurred while loading name', {name: t('suffixed.item.end')})),
        ),
    [prepareData, t],
  );

  const updateItem = useCallback(
    (id: string, values: InventoryItem) => {
      console.log(values.material);
      const newValues = processValues(values);

      return axios
        .post(`/api/goods/${id}/update`, newValues)
        .then((res) => res.data)
        .then((data) => {
          globalMutate(
            (key) =>
              Array.isArray(key) &&
              (key[0] === 'items' ||
                (key[0] === 'item' && key[1] === values?.parentId) ||
                (key[0] === 'variants' && key[1] === values?.parentId) ||
                key[0] === 'inventory-stats'),
          );
          toast.success(
            t('name has been updated successfully', {
              name: `${t('suffixed.item.start')} ${newValues.name}`,
              updated: t('updated.male'),
            }),
          );
          return data;
        })
        .catch((error) =>
          handleError(
            error,
            t('name has failed to update successfully', {
              name: `${t('suffixed.item.start')} ${newValues.name}`,
              updated: t('updated.male'),
            }),
          ),
        );
    },
    [globalMutate, processValues, t],
  );

  const deleteItem = useCallback(
    (id: string, name: string, parentId?: null | string) =>
      axios
        .delete(`/api/goods/${id}/delete`)
        .then(() => {
          toast.success(
            t('name has been deleted successfully', {
              deleted: t('deleted.male'),
              name: `${t(`suffixed.${parentId ? 'variant' : 'item'}.start`)} ${name}`,
            }),
          );
          globalMutate(
            (key) =>
              Array.isArray(key) &&
              (key[0] === 'items' ||
                (key[0] === 'variants' && key[1] === parentId) ||
                (key[0] === 'item' && key[1] === parentId) ||
                key[0] === 'inventory-stats'),
          );
        })
        .catch((error) =>
          handleError(error, t('name has failed to delete successfully', {deleted: t('deleted.male'), name: name})),
        ),
    [globalMutate, t],
  );

  const updateVariants = useCallback(
    (id: string, variantOptions: InventoryItemVariantOption[]) => {
      axios
        .post(`/api/goods/${id}/variants/update`, variantOptions)
        .then(() => {
          globalMutate(
            (key) =>
              Array.isArray(key) &&
              (key[0] === 'items' || (key[0] === 'variants' && key[1] === id) || (key[0] === 'item' && key[1] === id)),
          );

          toast.success(t('variants have been updated successfully'));
        })
        .catch((error) => handleError(error, t('variants have failed to updated successfully')));
    },
    [globalMutate, t],
  );

  return {
    createItem,
    deleteItem,
    getItem,
    prepareData,
    prepareLightData,
    processMaterials,
    processValues,
    updateItem,
    updateVariants,
  };
};

export default useItemActions;
