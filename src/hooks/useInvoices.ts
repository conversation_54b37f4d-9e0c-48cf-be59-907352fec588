import axios from 'axios';
import {format} from 'date-fns';
import {useTranslations} from 'next-intl';
import useSWR from 'swr';

import {handleError} from '@/utils/axios';
import {getQueryString} from '@/utils/common';
import {Invoice} from 'types/sales';

const useInvoices = ({end, isProforma, start}: {end?: Date; isProforma?: boolean; start?: Date}) => {
  const t = useTranslations();

  const {data, error, isLoading, isValidating} = useSWR<Invoice[]>(
    ['invoices', start, end, isProforma],
    () =>
      axios
        .get(
          `/api/invoices/list?${getQueryString({
            end: end ? format(end || 0, 'yyyy-MM-dd') : undefined,
            proforma: isProforma,
            start: start ? format(start || 0, 'yyyy-MM-dd') : undefined,
          })}`,
        )
        .then((res) => res.data)
        .catch((error) =>
          handleError(
            error,
            t('an error occurred while loading name', {
              name: t('suffixed.invoices'),
            }),
          ),
        ),
    {revalidateOnFocus: false},
  );

  return {
    error,
    invoices: data || [],
    isLoading,
    isValidating,
  };
};

export default useInvoices;
