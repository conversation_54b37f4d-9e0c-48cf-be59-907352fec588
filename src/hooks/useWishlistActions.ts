import {useCallback} from 'react';

import axios from 'axios';
import {useAtomValue} from 'jotai';
import {useTranslations} from 'next-intl';
import {useSWRConfig} from 'swr';

import {toast} from '@/components/ui/Toast';
import {defaultCurrencyAtom} from '@/store/defaults';
import {handleError} from '@/utils/axios';
import {parseCurrency} from '@/utils/common';
import {Id} from 'types/global';
import {PurchaseOrder, Wishlist, WishlistItem} from 'types/purchases';

const useWishlistActions = () => {
  const {mutate: globalMutate} = useSWRConfig();
  const defaultCurrency = useAtomValue(defaultCurrencyAtom);
  const t = useTranslations();

  const prepareData = useCallback((values: any) => {
    if (!values) return {};
    return {
      ...values,
      wishlistItems: (values?.wishlistItems || []).map((item: WishlistItem) => ({
        ...item,
        price: parseCurrency(item.price, false),
        totalPrice: parseCurrency(item.totalPrice, false),
      })),
    } as Wishlist;
  }, []);

  const processValues = useCallback(
    (values: Partial<WishlistItem>) => ({
      ...values,
      ...(values?.price
        ? {
            expectedPrice: parseCurrency({
              amount: values.price.amount,
              currency: values.price.currency || defaultCurrency,
            }),
          }
        : {}),
      materialGoodId: values.materialGood?.id || '',
      supplierId: values.supplier?.id || '',
    }),
    [defaultCurrency],
  );

  const processWishlistOrder = useCallback(
    (values: Partial<Wishlist>) => ({
      items:
        values?.wishlistItems?.map((wishlist) => ({
          ...wishlist,
          materialGoodId: wishlist.materialGood.id,
          originalWishlistId: wishlist.id,
          price: parseCurrency(wishlist.price),
        })) || [],
      renderingDetails: values?.renderingDetails || {},
      supplierId: values?.id,
    }),
    [],
  );

  const processOrder = useCallback(
    (values: Partial<PurchaseOrder>) => ({
      items:
        values?.items?.map((item) => ({
          ...item,
          materialGoodId: item.materialGood.id,
          price: parseCurrency(item.price),
        })) || [],
      number: values?.number,
      renderingDetails: values?.renderingDetails || {},
      supplierId: values?.supplier?.id,
    }),
    [],
  );

  const createWishlistItem = useCallback(
    (value: Partial<WishlistItem>) => {
      const newValues = processValues(value);

      return axios.post('/api/purchases/wishlist/add', [newValues]).then((res) => res.data);
    },
    [processValues],
  );

  const createSalesOrderWishlistItem = useCallback(
    (value: Partial<WishlistItem>, saleId: string) => {
      const newValues = processValues(value);

      return axios
        .post(`/api/sales/orders/${saleId}/add-missing-to-wishlist`, {
          ...newValues,
          productId: newValues.materialGoodId,
        })
        .then((res) => res.data);
    },
    [processValues],
  );

  const updateWishlistItem = useCallback(
    (value: Partial<WishlistItem>) => {
      const newValues = processValues(value);

      return axios.post(`/api/purchases/wishlist/${value.id}/update`, newValues).then((res) => res.data);
    },
    [processValues],
  );

  const deleteWishlistItem = useCallback((id: string) => axios.delete(`/api/purchases/wishlist/${id}/delete`), []);

  const deleteWishlist = useCallback(
    (supplier: Id) =>
      axios
        .post(`/api/purchases/wishlist/suppliers/${supplier.id}/empty`)
        .then(() => {
          globalMutate((key) => Array.isArray(key) && key[0] === 'wishlists');
          toast.success(
            t('name has been deleted successfully', {
              deleted: t('deleted.female'),
              name: `${t('suffixed.wishlist.start')} ${supplier.name}`,
            }),
          );
        })
        .catch((error) =>
          handleError(
            error,
            t('name has failed to delete successfully', {
              deleted: t('deleted.female'),
              name: `${t('suffixed.wishlist.start')} ${supplier.name}`,
            }),
          ),
        ),
    [globalMutate, t],
  );

  const hasItems = useCallback(
    (id: string) =>
      axios
        .get(`/api/purchases/wishlist/suppliers/${id}/details`)
        .then((res) => res.data)
        .then((wishlist: Wishlist) => {
          return wishlist.wishlistItems.length > 0;
        })
        .catch((error) =>
          handleError(error, t('an error occurred while loading name', {name: t('suffixed.wishlist.end')})),
        ),
    [t],
  );

  return {
    createSalesOrderWishlistItem,
    createWishlistItem,
    deleteWishlist,
    deleteWishlistItem,
    hasItems,
    prepareData,
    processOrder,
    processValues,
    processWishlistOrder,
    updateWishlistItem,
  };
};

export default useWishlistActions;
