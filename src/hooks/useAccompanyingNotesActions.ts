import {useCallback} from 'react';

import axios from 'axios';
import {useTranslations} from 'next-intl';
import {useSWRConfig} from 'swr';

import {toast} from '@/components/ui/Toast';
import {AccompanyingNote, AccompanyingNoteItem, GoodsAccompanyingNote} from '@/types/global';
import {handleError} from '@/utils/axios';
import {parseCurrency} from '@/utils/common';

const useAccompanyingNotesActions = () => {
  const {mutate: globalMutate} = useSWRConfig();
  const t = useTranslations();

  const prepareData = useCallback((values: any) => {
    if (!values) return {};
    return {
      ...values,
      items: values.items.map((item: AccompanyingNoteItem) => ({
        ...item,
        unitPrice: parseCurrency(item.unitPrice),
      })),
    } as AccompanyingNote;
  }, []);

  const processValues = useCallback(
    (values: Partial<AccompanyingNote>) => ({
      ...values,
      delegateId: values.delegate?.id || null,
      items: values.items?.map((item) => ({
        ...item,
        unitPrice: parseCurrency(item.unitPrice, true),
      })),
    }),
    [],
  );

  const createSaleAccompanyingNote = useCallback(
    async (id: string, values: Partial<AccompanyingNote>) => {
      const newValues = processValues(values);

      return axios
        .post(`/api/sales/orders/${id}/goods-accompanying-note`, newValues)
        .then((res) => res.data)
        .then((data) => {
          toast.success(
            t('name has been created successfully', {
              created: t('created.female'),
              name: `${t('suffixed.accompanyingNote.start')} ${data.number}`,
            }),
          );
          globalMutate(
            (key) =>
              Array.isArray(key) &&
              (key[0] === 'accompanyingNotes' || key[0] === 'sales' || (key[0] === 'sale' && key[1] === id)),
          );

          return data;
        })
        .catch((error) =>
          handleError(
            error,
            t('name has failed to create successfully', {
              created: t('created.female'),
              name: t('suffixed.accompanyingNote.start'),
            }),
          ),
        );
    },
    [globalMutate, processValues, t],
  );

  const createManufacturingServiceAccompanyingNote = useCallback(
    async (id: string, values: Partial<AccompanyingNote>) => {
      const newValues = processValues(values);

      return axios
        .post(`/api/servicing/orders/${id}/goods-accompanying-note`, newValues)
        .then((res) => res.data)
        .then((data) => {
          toast.success(
            t('name has been created successfully', {
              created: t('created.female'),
              name: `${t('suffixed.accompanyingNote.start')} ${data.number}`,
            }),
          );
          globalMutate(
            (key) =>
              Array.isArray(key) &&
              (key[0] === 'accompanyingNotes' || key[0] === 'services' || (key[0] === 'service' && key[1] === id)),
          );

          return data;
        })
        .catch((error) =>
          handleError(
            error,
            t('name has failed to create successfully', {
              created: t('created.female'),
              name: t('suffixed.accompanyingNote.start'),
            }),
          ),
        );
    },
    [globalMutate, processValues, t],
  );

  const createAccompanyingNote = useCallback(
    async (values: Partial<AccompanyingNote>) => {
      const newValues = processValues(values);

      return axios
        .post('/api/goods-accompanying-notes/create', newValues)
        .then((res) => res.data)
        .then((data) => {
          toast.success(
            t('name has been created successfully', {
              created: t('created.female'),
              name: `${t('suffixed.accompanyingNote.start')} ${data.number}`,
            }),
          );
          globalMutate((key) => Array.isArray(key) && key[0] === 'accompanyingNotes');

          return data;
        })
        .catch((error) => {
          handleError(
            error,
            t('name has failed to create successfully', {
              created: t('created.female'),
              name: t('suffixed.accompanyingNote.start'),
            }),
          );
          return {} as GoodsAccompanyingNote;
        });
    },
    [globalMutate, processValues, t],
  );

  const updateAccompanyingNote = useCallback(
    async (id: string, values: AccompanyingNote) => {
      const newValues = processValues(values);

      return await axios
        .put(`/api/goods-accompanying-notes/${id}/update`, newValues)
        .then((res) => res.data)
        .then((data) => {
          globalMutate(
            (key) =>
              Array.isArray(key) &&
              (key[0] === 'accompanyingNotes' || (key[0] === 'accompanyingNoteDocument' && key[1] === id)),
          );
          toast.success(
            t('name has been updated successfully', {
              name: `${t('suffixed.accompanyingNote.start')} ${data.number}`,
              updated: t('updated.female'),
            }),
          );

          return data;
        })
        .catch((error) =>
          handleError(
            error,
            t('name has failed to update successfully', {
              name: `${t('suffixed.accompanyingNote.start')} ${newValues.number}`,
              updated: t('updated.female'),
            }),
          ),
        );
    },
    [globalMutate, processValues, t],
  );

  return {
    createAccompanyingNote,
    createManufacturingServiceAccompanyingNote,
    createSaleAccompanyingNote,
    prepareData,
    processValues,
    updateAccompanyingNote,
  };
};

export default useAccompanyingNotesActions;
