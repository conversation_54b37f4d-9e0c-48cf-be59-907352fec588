import {useCallback} from 'react';

import {parseCurrency} from '@/utils/common';
import {InventoryItemHistory, InventoryItemHistoryIncoming, InventoryItemHistoryStock} from 'types/inventory';

const useItemHistoryActions = () => {
  const prepareData = useCallback((values: any) => {
    if (!values) return {};
    return {
      ...values,
      incomingOrders: values.incomingOrders.map((incomingOrder: InventoryItemHistoryIncoming) => ({
        ...incomingOrder,
        price: parseCurrency(incomingOrder.price, false),
      })),
      stockHistory: values.stockHistory.map((stockHistory: InventoryItemHistoryStock) => ({
        ...stockHistory,
        averageCostAfter: parseCurrency(stockHistory.averageCostAfter, false),
        price: parseCurrency(stockHistory.price, false),
      })),
    } as InventoryItemHistory;
  }, []);

  return {
    prepareData,
  };
};

export default useItemHistoryActions;
