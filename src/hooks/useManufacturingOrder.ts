import {useCallback, useEffect, useState} from 'react';

import {joiResolver} from '@hookform/resolvers/joi';
import axios from 'axios';
import FormData from 'form-data';
import Joi from 'joi';
import {useTranslations} from 'next-intl';
import {useForm} from 'react-hook-form';
import useSWR, {useSWRConfig} from 'swr';

import {toast} from '@/components/ui/Toast';
import {useRouter} from '@/hooks/helpers/useRouter';
import useManufacturingOrderActions from '@/hooks/useManufacturingOrderActions';
import {handleError} from '@/utils/axios';
import {ManufacturingOrder} from 'types/manufacturing';

import useLeaveConfirm from './helpers/useLeaveConfirm';

export const manufacturingOrderSchema = Joi.object({
  id: Joi.string().optional(),
  manufacturingOperations: Joi.array()
    .items(
      Joi.object({
        durationInMinutes: Joi.number().positive().required(),
      }).unknown(),
    )
    .min(0),
  product: Joi.alternatives().conditional('id', {
    is: Joi.exist(),
    otherwise: Joi.object({
      id: Joi.string().required(), // 'product.id' is required when 'id' does not exist
    }).unknown(),
    then: Joi.optional(),
  }),
  quantity: Joi.number().positive().required(),
  requiredMaterials: Joi.array()
    .items(
      Joi.object({
        required: Joi.number().positive().required(),
      }).unknown(),
    )
    .min(0),
}).unknown();

const useManufacturingOrder = (id?: string) => {
  const t = useTranslations();
  const {replace} = useRouter();
  const [created, setCreated] = useState(false);
  const {mutate: globalMutate} = useSWRConfig();
  const {
    createManufacturingOrder: createManufacturingOrderAction,
    deleteManufacturingOrder: deleteManufacturingOrderAction,
    getOrder,
    updateManufacturingOrder: updateManufacturingOrderAction,
  } = useManufacturingOrderActions();

  const {
    control,
    formState: {dirtyFields, errors, ...restUseFormState},
    handleSubmit,
    register,
    reset,
    resetField,
    setError,
    setValue,
    watch,
    ...restUseForm
  } = useForm<ManufacturingOrder>({
    defaultValues: {
      customProduct: false,
      manufacturingTasks: [],
      quantity: 1,
    },
    mode: 'onSubmit',
    resolver: joiResolver(manufacturingOrderSchema),
  });

  const isDirty = !!Object.keys(dirtyFields).length;

  useLeaveConfirm(created ? false : isDirty, async () => await saveManufacturingOrder());

  useEffect(() => {
    if (watch('id') && created) replace(`/manufacturing/orders/${watch('id')}`);
  }, [created, replace, watch]);

  const {data, isLoading, isValidating, mutate} = useSWR(
    id ? ['manufacturing', id] : null,
    () =>
      getOrder(id).then((values) => {
        reset(values);
        return values;
      }),
    {revalidateOnFocus: false},
  );

  useEffect(() => {
    if (data) reset(data as ManufacturingOrder);
  }, [data, reset]);

  const saveManufacturingOrder = useCallback(
    () =>
      handleSubmit(
        (values) => {
          return id
            ? updateManufacturingOrderAction(id, values).then(() => {
                mutate();
                globalMutate((key) => Array.isArray(key) && key[0] === 'manufacturingDocument' && key[1] === id);
              })
            : createManufacturingOrderAction(values).then((data) => {
                setCreated(true);
                setValue('id', data.id);
              });
        },
        () => {
          toast.warning(t('please fill in all mandatory fields before saving'));
          return Promise.reject();
        },
      )(),
    [
      createManufacturingOrderAction,
      globalMutate,
      handleSubmit,
      id,
      mutate,
      setValue,
      t,
      updateManufacturingOrderAction,
    ],
  );

  const deleteManufacturingOrder = useCallback(() => {
    if (id) return deleteManufacturingOrderAction(id, watch('number')).then(() => replace(`/manufacturing/orders`));
  }, [deleteManufacturingOrderAction, id, replace, watch]);

  const finishManufacturingOrderCustomization = useCallback(
    () =>
      handleSubmit(
        () => {
          if (watch('manufacturingOperations')?.length === 0) {
            setError('manufacturingOperations', {type: 'minLength'});

            return toast.warning(t('please fill in all mandatory fields before saving'));
          }

          return axios
            .post(`/api/manufacturing/orders/${id}/customization-finished`)
            .then(() => {
              mutate();
              globalMutate(
                (key) =>
                  Array.isArray(key) &&
                  (key[0] === 'manufacturinsg' ||
                    key[0] === 'sales' ||
                    key[0] === 'items' ||
                    (key[0] === 'manufacturingDocument' && key[1] === id)),
              );
              toast.success(t('name customization has completed', {name: watch('number')}));
            })
            .catch((error) =>
              handleError(error, t('name customization has failed to complete', {name: watch('number')})),
            );
        },
        () => toast.warning(t('please fill in all mandatory fields before saving')),
      )(),
    [globalMutate, handleSubmit, id, mutate, setError, t, watch],
  );

  const uploadManufacturingOrderFile = useCallback(
    (file?: File) => {
      if (file) {
        const formData = new FormData();

        formData.append('files', file);
        return axios
          .post(`/api/attach-file/manufacturing/${id}`, formData, {
            headers: {'X-Original-Filename': encodeURIComponent(file.name)},
          })
          .then(() => {
            mutate();
          })
          .catch((error) =>
            handleError(
              error,
              t('name has failed to create successfully', {
                created: t('created.female'),
                name: t('suffixed.order.start'),
              }),
            ),
          );
      }
    },
    [id, mutate, t],
  );

  const deleteManufacturingOrderFile = useCallback(
    (id: string) =>
      axios
        .delete(`/api/manufacturing/orders/${watch('id')}/files/${id}/delete`)
        .then(() => {
          mutate();
        })
        .catch((error) =>
          handleError(
            error,
            t('name has failed to update successfully', {name: watch('number'), updated: t('deleted.male')}),
          ),
        ),
    [mutate, t, watch],
  );

  return {
    deleteManufacturingOrder,
    deleteManufacturingOrderFile,
    finishManufacturingOrderCustomization,
    isDirty,
    isLoading: isLoading || (id && !watch('id')),
    isValidating,
    manufacturingOrder: watch() as ManufacturingOrder,
    saveManufacturingOrder,
    uploadManufacturingOrderFile,
    useFormActions: {
      control,
      formState: {dirtyFields, errors, ...restUseFormState},
      handleSubmit,
      register,
      reset,
      resetField,
      setError,
      setValue: ((name, value: any, options = {shouldDirty: true}) =>
        setValue(name, value, options)) as typeof setValue,
      watch,
      ...restUseForm,
    },
  };
};

export default useManufacturingOrder;
