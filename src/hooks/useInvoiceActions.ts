import {useCallback} from 'react';

import axios from 'axios';
import {useTranslations} from 'next-intl';
import {useSWRConfig} from 'swr';

import {toast} from '@/components/ui/Toast';
import {handleError} from '@/utils/axios';

const useInvoiceActions = () => {
  const t = useTranslations();
  const {mutate: globalMutate} = useSWRConfig();

  const markInvoiceAsPaid = useCallback(
    (id: string, number: string) =>
      axios
        .post(`/api/invoices/${id}/mark-as-paid`)
        .then((res) => res.data)
        .then((data) => {
          toast.success(
            t('name has been updated successfully', {
              name: `${t('suffixed.invoice.start')} ${number}`,
              updated: t('updated.female'),
            }),
          );

          globalMutate((key) => Array.isArray(key) && key[0] === 'invoices');

          return data;
        })
        .catch((error) =>
          handleError(
            error,
            t('name has failed to update successfully', {
              name: t('suffixed.invoice.start'),
              updated: t('updated.female'),
            }),
          ),
        ),
    [globalMutate, t],
  );

  return {
    markInvoiceAsPaid,
  };
};

export default useInvoiceActions;
