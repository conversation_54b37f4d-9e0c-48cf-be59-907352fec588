import axios from 'axios';
import {useTranslations} from 'next-intl';
import useSWR from 'swr';

import {handleError} from '@/utils/axios';
import {UserSettings} from 'types/account';

const useSettings = () => {
  const t = useTranslations();

  const {data, error, isLoading, isValidating} = useSWR(
    ['settings'],
    () =>
      axios
        .get('/api/account/settings')
        .then((res) => res.data)
        .then((settings) => ({
          ...settings,
          taxRates: {
            defaultVAT: settings.defaultVAT,
          },
        }))
        .catch((error) =>
          handleError(error, t('an error occurred while loading name', {name: t('suffixed.settings')})),
        ),
    {revalidateOnFocus: false},
  );

  return {
    error,
    isLoading,
    isValidating,
    settings: data as UserSettings,
  };
};

export default useSettings;
