import {useCallback} from 'react';

import FormData from 'form-data';
import {useTranslations} from 'next-intl';
import {useSWRConfig} from 'swr';

import {toast} from '@/components/ui/Toast';
import {useRouter} from '@/hooks/helpers/useRouter';
import axios, {handleError} from '@/utils/axios';
import {newId, parseCurrency} from '@/utils/common';
import {DeepPartial, ManufacturingStatus} from 'types/global';
import {
  Consumption,
  ManufacturingOrder,
  ManufacturingOrderMaterial,
  ManufacturingOrderOperationStep,
} from 'types/manufacturing';

const useManufacturingOrderActions = () => {
  const {mutate: globalMutate} = useSWRConfig();
  const {push} = useRouter();
  const t = useTranslations();

  const processMaterials = useCallback((values: Partial<ManufacturingOrder>) => {
    type MaterialWithWaste = ManufacturingOrderMaterial & {
      _wasteCount: number;
      _wasteSum: number;
    };

    const materialsMap = new Map<string, MaterialWithWaste>();

    const accumulate = (materials: ManufacturingOrderMaterial[]) => {
      for (const material of materials) {
        const primaryGood = material.materialGoods?.[0];
        if (!primaryGood) continue;

        const existing = materialsMap.get(primaryGood.id);

        if (!existing) {
          materialsMap.set(primaryGood.id, {
            ...structuredClone(material),
            _wasteCount: 1,
            _wasteSum: material.wastePercentage ?? 0,
          });
        } else {
          const newMaterial: MaterialWithWaste = {...existing};
          newMaterial.cost = {...existing.cost};
          newMaterial.cost.amount += material.cost?.amount ?? 0;
          newMaterial.required += material.required ?? 0;
          newMaterial.requiredTotal += material.requiredTotal ?? 0;
          newMaterial.allAvailable = existing.allAvailable && material.allAvailable;
          newMaterial._wasteSum += material.wastePercentage ?? 0;
          newMaterial._wasteCount += 1;
          materialsMap.set(primaryGood.id, newMaterial);
        }
      }
    };

    const steps =
      values.status === ManufacturingStatus.CUSTOMIZATION_NEEDED
        ? values.manufacturingOperations
        : values.manufacturingTasks;

    for (const step of steps ?? []) {
      accumulate(step.materials ?? []);
    }

    return Array.from(materialsMap.values()).map(({_wasteCount, _wasteSum, ...material}) => ({
      ...material,
      wastePercentage: _wasteCount > 0 ? _wasteSum / _wasteCount : 0,
    }));
  }, []);

  const prepareData = useCallback(
    (values: any) => {
      if (!values) return {};

      const preparedValues = {
        ...values,
        manufacturingOperations:
          values.manufacturingOperations?.map((operation: ManufacturingOrderOperationStep) => ({
            ...operation,
            costPerHour: operation.costPerHour ? parseCurrency(operation.costPerHour, false) : operation.costPerHour,
            id: operation.id?.trim() ? operation.id : newId(),
            materials: operation.materials.map((material) => ({
              ...material,
              cost: parseCurrency(material.cost, false),
              materialGoods: material.materialGoods.map((materialGood: any) => ({
                ...materialGood,
                inventoryCostPerItem: parseCurrency(
                  {...materialGood.cost, amount: material.cost.amount / material.required / values.quantity},
                  false,
                ),
                material: materialGood.material
                  ? {
                      dimensions: materialGood.material.dimensions?.map((dimension: any) => ({
                        dimensionKey: dimension.key,
                        name: '',
                        value: dimension.value,
                      })),
                      material: {density: 0, key: materialGood.material.key || '', name: ''},
                      shape: {name: '', shape: materialGood.material.shape || ''},
                    }
                  : null,
              })),
              wastePercentage: Number((material.wastePercentage * 100).toFixed(2)),
            })),
          })) || [],
        manufacturingTasks:
          values.manufacturingTasks?.map((task: any) => ({
            ...task,
            costPerHour: task.costPerHour ? parseCurrency(task.costPerHour, false) : task.costPerHour,
          })) || [],
        product: {
          ...values.product,
          employeeAndWorkstationCosts: parseCurrency(values.product?.employeeAndWorkstationCosts, false),
          manufacturingOverheadCosts: parseCurrency(values.product?.manufacturingOverheadCosts, false),
          materialCosts: parseCurrency(values.product?.materialCosts, false),
        },
        totalCost: parseCurrency(values.totalCost, false),
      } as ManufacturingOrder;

      return {
        ...preparedValues,
        materials: processMaterials(preparedValues),
      };
    },
    [processMaterials],
  );

  const processValues = useCallback(
    (values: Partial<ManufacturingOrder>) => ({
      ...values,
      manufacturingOperations:
        values.manufacturingOperations?.map((operation) => ({
          ...operation,
          candidateEmployeeIds: operation?.candidateEmployees?.map((employee) => employee?.id) || [],
          candidateWorkstationIds: operation?.candidateWorkstations?.map((workstation) => workstation?.id) || [],
          costPerHour: operation?.costPerHour?.amount ? parseCurrency(operation.costPerHour) : null,
          materials: operation.materials?.map((material?: DeepPartial<ManufacturingOrderMaterial>) => ({
            ...material,
            materialIds: [material?.materialGoods?.[0]?.id || ''],
            quantity: material?.required || 0,
            wastePercentage: (material?.wastePercentage || 0) / 100,
          })),
        })) || [],
      productId: values.product?.id,
    }),
    [],
  );

  const createManufacturingOrder = useCallback(
    (values: Partial<ManufacturingOrder>, redirect?: boolean) => {
      const newValues = processValues(values);

      return axios
        .post('/api/manufacturing/orders/create', newValues)
        .then((res) => res.data)
        .then((data) => {
          toast.success(
            t('name has been created successfully', {
              created: t('created.female'),
              name: `${t('suffixed.order.start')} ${data.number}`,
            }),
          );
          globalMutate(
            (key) =>
              Array.isArray(key) &&
              (key[0] === 'manufacturings' || key[0] === 'sales' || key[0] === 'items' || key[0] === 'myTasks'),
          );

          if (redirect) push(`/manufacturing/order/${data.id}`);

          return data;
        })
        .catch((error) =>
          handleError(
            error,
            t('name has failed to create successfully', {
              created: t('created.female'),
              name: t('suffixed.order.start'),
            }),
          ),
        );
    },
    [globalMutate, processValues, push, t],
  );

  const createLinkedManufacturingOrder = useCallback(
    (
      {productId, quantity, serviceId}: {productId?: string; quantity: number; serviceId?: string},
      saleId: string,
      redirect?: boolean,
    ) =>
      axios
        .post(`/api/sales/orders/${saleId}/manufacture-missing`, {
          productId,
          quantity,
          serviceId,
        })
        .then((res) => res.data)
        .then((item: ManufacturingOrder) => {
          toast.success(
            t('name has been created successfully', {
              created: t('created.female'),
              name: `${t('suffixed.order.start')} ${item.number}`,
            }),
          );

          globalMutate(
            (key) =>
              Array.isArray(key) &&
              (key[0] === 'manufacturings' ||
                key[0] === 'sales' ||
                key[0] === 'items' ||
                key[0] === 'myTasks' ||
                (key[0] === 'sale' && key[1] === saleId)),
          );

          if (redirect) push(`/manufacturing/order/${item.id}`);
        })
        .catch((error) =>
          handleError(
            error,
            t('name has failed to create successfully', {
              created: t('created.female'),
              name: t('suffixed.order.start'),
            }),
          ),
        ),
    [globalMutate, push, t],
  );

  const createLinkedCustomizedManufacturingOrder = useCallback(
    (
      {customizationNote, id, quantity}: {customizationNote: string; id?: string; quantity: number},
      files: File[],
      saleId: string,
      redirect?: boolean,
    ) => {
      const formData = new FormData();

      formData.append('productOrder', JSON.stringify({customizationNote, productId: id, quantity}));
      files.forEach((file) => formData.append('files', file));

      return axios
        .post(`/api/manufacture-missing/sales/${saleId}`, formData)
        .then((res) => res.data)
        .then((item: ManufacturingOrder) => {
          toast.success(
            t('name has been created successfully', {
              created: t('created.female'),
              name: `${t('suffixed.order.start')} ${item.number}`,
            }),
          );

          globalMutate(
            (key) =>
              Array.isArray(key) &&
              (key[0] === 'manufacturings' ||
                key[0] === 'sales' ||
                key[0] === 'items' ||
                key[0] === 'myTasks' ||
                (key[0] === 'sale' && key[1] === saleId)),
          );

          if (redirect) push(`/manufacturing/order/${item.id}`);
        })
        .catch((error) =>
          handleError(
            error,
            t('name has failed to create successfully', {
              created: t('created.female'),
              name: t('suffixed.order.start'),
            }),
          ),
        );
    },
    [globalMutate, push, t],
  );

  const updateManufacturingOrder = useCallback(
    async (id: string, values: ManufacturingOrder) => {
      const newValues = processValues(values);

      await axios
        .post(`/api/manufacturing/orders/${id}/update`, newValues)
        .then((res) => res.data)
        .then((data) => {
          globalMutate(
            (key) =>
              Array.isArray(key) &&
              (key[0] === 'manufacturings' ||
                key[0] === 'sales' ||
                key[0] === 'items' ||
                key[0] === 'myTasks' ||
                (key[0] === 'manufacturing' && key[1] === id) ||
                (key[0] === 'manufacturingDocument' && key[1] === id)),
          );
          toast.success(
            t('name has been updated successfully', {
              name: `${t('suffixed.order.start')} ${data.number}`,
              updated: t('updated.female'),
            }),
          );

          return data;
        })
        .catch((error) =>
          handleError(
            error,
            t('name has failed to update successfully', {
              name: `${t('suffixed.order.start')} ${newValues.number}`,
              updated: t('updated.female'),
            }),
          ),
        );
    },
    [globalMutate, processValues, t],
  );

  const deleteManufacturingOrder = useCallback(
    (id: string, number?: string) =>
      axios
        .delete(`/api/manufacturing/orders/${id}/delete`)
        .then(() => {
          globalMutate((key) => Array.isArray(key) && (key[0] === 'manufacturings' || key[0] === 'myTasks'));
          toast.success(
            t('name has been deleted successfully', {
              deleted: t('deleted.female'),
              name: `${t('suffixed.order.start')} ${number || ''}`,
            }),
          );
        })
        .catch((error) =>
          handleError(
            error,
            t('name has failed to delete successfully', {
              deleted: t('deleted.female'),
              name: `${t('suffixed.order.start')} ${number || ''}`,
            }),
          ),
        ),
    [globalMutate, t],
  );

  const updateManufacturingOrderRank = useCallback(
    (value?: string[]) => {
      if (!value) return;

      axios
        .post('/api/manufacturing/orders/ranking', value)
        .then(() => {
          toast.success(
            t('name has been updated successfully', {
              name: t('order ranking'),
              updated: t('updated.female'),
            }),
          );
          globalMutate(
            (key) =>
              Array.isArray(key) &&
              (key[0] === 'manufacturings' || key[0] === 'manufacturingDocument' || key[0] === 'myTasks'),
          );
        })
        .catch((error) =>
          handleError(
            error,
            t('name has failed to update successfully', {
              name: t('order ranking'),
              updated: t('updated.female'),
            }),
          ),
        );
    },
    [globalMutate, t],
  );

  const recordConsumptionManufacturingOrder = useCallback(
    (id: string, values: Consumption) =>
      axios
        .post(`/api/manufacturing/orders/${id}/record-consumption`, values)
        .then((res) => res.data)
        .then((data) => {
          toast.success(
            t('name has been updated successfully', {
              name: `${t('suffixed.order.start')} ${data.number}`,
              updated: t('updated.female'),
            }),
          );

          globalMutate(
            (key) =>
              Array.isArray(key) &&
              (key[0] === 'manufacturings' ||
                key[0] === 'sales' ||
                key[0] === 'items' ||
                key[0] === 'myTasks' ||
                (key[0] === 'manufacturing' && key[1] === id) ||
                (key[0] === 'manufacturingDocument' && key[1] === id)),
          );

          return data;
        })
        .catch((error) =>
          handleError(
            error,
            t('name has failed to update successfully', {
              name: t('suffixed.order.start'),
              updated: t('updated.female'),
            }),
          ),
        ),
    [globalMutate, t],
  );

  // Keep the old function name for backward compatibility
  const finishManufacturingOrder = recordConsumptionManufacturingOrder;

  const getOrder = useCallback(
    (id: string | undefined) =>
      axios
        .get(`/api/manufacturing/orders/${id}/details`)
        .then((res) => prepareData(res.data) as ManufacturingOrder)
        .catch((error) =>
          handleError(error, t('an error occurred while loading name', {name: t('suffixed.order.end')})),
        ),
    [prepareData, t],
  );

  return {
    createLinkedCustomizedManufacturingOrder,
    createLinkedManufacturingOrder,
    createManufacturingOrder,
    deleteManufacturingOrder,
    finishManufacturingOrder,
    getOrder,
    prepareData,
    processMaterials,
    processValues,
    updateManufacturingOrder,
    updateManufacturingOrderRank,
  };
};

export default useManufacturingOrderActions;
