import {useCallback} from 'react';

import axios from 'axios';
import {findIndex} from 'lodash';
import {useTranslations} from 'next-intl';
import {useSWRConfig} from 'swr';

import {toast} from '@/components/ui/Toast';
import {handleError} from '@/utils/axios';
import {UserSettings} from 'types/account';

const useSettingsActions = () => {
  const {mutate: globalMutate} = useSWRConfig();
  const t = useTranslations();

  const updateSettings = useCallback(
    (settings: UserSettings, tab: keyof UserSettings, value: object) => {
      const tabIndex = findIndex(Object.keys(settings), (tabName) => tabName === tab);

      const newSettings = {
        ...settings,
        [tab]: Array.isArray(value) ? value : {...Object.values(settings).at(tabIndex), ...value},
      };

      axios
        .post('/api/account/settings/update', {
          ...newSettings,
          defaultVAT: newSettings.taxRates.defaultVAT,
        })
        .then(() => {
          globalMutate((key) => Array.isArray(key) && key[0] === 'settings');
          toast.success(t('changes have been successfully applied'));
        })
        .catch((error) => handleError(error, t('the changes have not been applied successfully')));
    },
    [globalMutate, t],
  );

  return {
    updateSettings,
  };
};

export default useSettingsActions;
