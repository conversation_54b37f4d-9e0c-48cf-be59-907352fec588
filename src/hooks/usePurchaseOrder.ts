import {useCallback, useEffect} from 'react';

import {joiResolver} from '@hookform/resolvers/joi';
import axios from 'axios';
import Joi from 'joi';
import {useTranslations} from 'next-intl';
import {useForm} from 'react-hook-form';
import useSWR, {useSWRConfig} from 'swr';

import {toast} from '@/components/ui/Toast';
import {useRouter} from '@/hooks/helpers/useRouter';
import usePurchaseOrderActions from '@/hooks/usePurchaseOrderActions';
import {handleError} from '@/utils/axios';
import useLeaveConfirm from 'hooks/helpers/useLeaveConfirm';
import {PurchaseOrder} from 'types/purchases';

const purchaseOrderSchema = Joi.object({
  expectedDelivery: Joi.string().required(),
}).unknown();

const usePurchaseOrder = (id?: string) => {
  const t = useTranslations();
  const {back} = useRouter();
  const {mutate: globalMutate} = useSWRConfig();
  const {prepareData, updatePurchaseOrder: updatePurchaseOrderAction} = usePurchaseOrderActions();

  const {
    control,
    formState: {dirtyFields, errors, ...restUseFormState},
    handleSubmit,
    register,
    reset,
    resetField,
    setValue,
    watch,
    ...restUseForm
  } = useForm<PurchaseOrder>({
    mode: 'onSubmit',
    resolver: joiResolver(purchaseOrderSchema),
  });

  const isDirty = !!Object.keys(dirtyFields).length;

  useLeaveConfirm(isDirty, async () => await savePurchaseOrder());

  const {data, isLoading, isValidating, mutate} = useSWR(
    id ? ['purchase', id] : null,
    () =>
      axios
        .get(`/api/purchases/orders/${id}/details`)
        .then((res) => prepareData(res.data))
        .then((values) => {
          reset(values);
          return values;
        })
        .catch((error) =>
          handleError(error, t('an error occurred while loading name', {name: t('suffixed.order.end')})),
        ),
    {revalidateOnFocus: false},
  );

  useEffect(() => {
    if (data) reset(data as PurchaseOrder);
  }, [data, reset]);

  const savePurchaseOrder = useCallback(() => {
    if (!id) return Promise.resolve();

    return handleSubmit(
      (values) => updatePurchaseOrderAction(id, values).then(() => mutate()),
      () => {
        toast.warning(t('please fill in all mandatory fields before saving'));
        return Promise.reject();
      },
    )();
  }, [handleSubmit, id, mutate, t, updatePurchaseOrderAction]);

  const cancelPurchaseOrder = useCallback(
    () =>
      axios
        .post(`/api/purchases/orders/${id}/cancel`)
        .then(() => {
          globalMutate((key) => Array.isArray(key) && key[0] === 'purchases');
          toast.success(
            t('name has been canceled successfully', {
              canceled: t('canceled.female'),
              name: `${t('suffixed.order.start')} ${watch('number')}`,
            }),
          );

          back('/purchases');
        })
        .catch((error) =>
          handleError(
            error,
            t('name has failed to cancel successfully', {
              canceled: t('canceled.female'),
              name: `${t('suffixed.order.start')} ${watch('number')}`,
            }),
          ),
        ),
    [back, globalMutate, id, t, watch],
  );

  return {
    cancelPurchaseOrder,
    isDirty,
    isLoading: isLoading || (id && !watch('id')),
    isValidating,
    purchaseOrder: watch() as PurchaseOrder,
    savePurchaseOrder,
    useFormActions: {
      control,
      formState: {dirtyFields, errors, ...restUseFormState},
      handleSubmit,
      register,
      reset,
      resetField,
      setValue: ((name, value: any, options = {shouldDirty: true}) =>
        setValue(name, value, options)) as typeof setValue,
      watch,
      ...restUseForm,
    },
  };
};

export default usePurchaseOrder;
