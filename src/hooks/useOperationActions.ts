import {useCallback} from 'react';

import axios from 'axios';
import {useTranslations} from 'next-intl';
import {useSWRConfig} from 'swr';

import {toast} from '@/components/ui/Toast';
import {handleError} from '@/utils/axios';
import {parseCurrency} from '@/utils/common';
import {Operation} from 'types/manufacturing';

const useOperationActions = () => {
  const {mutate: globalMutate} = useSWRConfig();
  const t = useTranslations();

  const prepareData = useCallback((values: any) => {
    if (!values) return {};
    return {
      ...values,
      cost: parseCurrency(values.cost, false),
      costPerHour: parseCurrency(values.costPerHour, false),
    } as Operation;
  }, []);

  const processValues = useCallback(
    (values: Partial<Operation>) => ({
      ...values,
      costPerHour: values.costPerHour ? parseCurrency(values.costPerHour) : null,
      employees:
        values.employees?.map((employee) => ({
          id: employee?.id || '',
          preferential: employee?.preferential || false,
        })) || [],
      workstationIds: values.workstations?.map((workstation) => workstation?.id) || [],
    }),
    [],
  );

  const createOperation = useCallback(
    (values: Partial<Operation>) => {
      const newValues = processValues(values);

      return axios
        .post('/api/manufacturing/operations/templates/create', newValues)
        .then((res) => res.data)
        .then((item) => {
          globalMutate((key) => Array.isArray(key) && key[0] === 'operations');
          toast.success(
            t('name has been created successfully', {
              created: t('created.female'),
              name: `${t('suffixed.task.start')} ${item.name}`,
            }),
          );

          return item;
        })
        .catch((error) =>
          handleError(
            error,
            t('name has failed to create successfully', {
              created: t('created.female'),
              name: `${t('suffixed.task.start')} ${newValues.name}`,
            }),
          ),
        );
    },
    [globalMutate, processValues, t],
  );

  const updateOperation = useCallback(
    (values: Partial<Operation>) => {
      const newValues = processValues(values);

      return axios
        .post(`/api/manufacturing/operations/templates/${values.id}/update`, newValues)
        .then(() => {
          globalMutate((key) => Array.isArray(key) && key[0] === 'operations');
          toast.success(
            t('name has been updated successfully', {
              name: `${t('suffixed.task.start')} ${newValues.name}`,
              updated: t('updated.female'),
            }),
          );
        })
        .catch((error) =>
          handleError(
            error,
            t('name has failed to update successfully', {
              name: `${t('suffixed.task.start')} ${newValues.name}`,
              updated: t('updated.female'),
            }),
          ),
        );
    },
    [globalMutate, processValues, t],
  );

  const deleteOperation = useCallback(
    (id: string, name?: string) =>
      axios
        .delete(`/api/manufacturing/operations/templates/${id}/delete`)
        .then(() => {
          globalMutate((key) => Array.isArray(key) && key[0] === 'operations');
          toast.success(
            t('name has been deleted successfully', {
              deleted: t('deleted.female'),
              name: `${t('suffixed.task.start')} ${name}`,
            }),
          );
        })
        .catch((error) =>
          handleError(
            error,
            t('name has failed to delete successfully', {
              deleted: t('deleted.female'),
              name: `${t('suffixed.task.start')} ${name}`,
            }),
          ),
        ),
    [globalMutate, t],
  );

  return {
    createOperation,
    deleteOperation,
    prepareData,
    processValues,
    updateOperation,
  };
};

export default useOperationActions;
