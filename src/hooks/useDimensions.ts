import axios from 'axios';
import {useTranslations} from 'next-intl';
import useSWR from 'swr';

import {handleError} from '@/utils/axios';
import {DimensionShape} from 'types/global';

const useDimensions = () => {
  const t = useTranslations();

  const {data, error, isLoading, isValidating} = useSWR(
    ['dimensions'],
    () =>
      axios
        .get('/api/configurations/materials/metals/list')
        .then((res) => res.data)
        .catch((error) =>
          handleError(error, t('an error occurred while loading name', {name: t('suffixed.dimensions')})),
        ),
    {revalidateOnFocus: false},
  );

  return {
    dimensions: (data || []) as {
      availableShapes: DimensionShape[];
      material: {
        density: number;
        key: string;
        name: string;
      };
    }[],
    error,
    isLoading,
    isValidating,
  };
};

export default useDimensions;
