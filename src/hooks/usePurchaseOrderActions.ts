import {useCallback} from 'react';

import axios from 'axios';
import {useTranslations} from 'next-intl';
import {useSWRConfig} from 'swr';

import {toast} from '@/components/ui/Toast';
import {handleError} from '@/utils/axios';
import {parseCurrency} from '@/utils/common';
import {Id} from 'types/global';
import {PurchaseOrder, PurchaseRenderingDetails, WishlistItem} from 'types/purchases';

const usePurchaseOrderActions = () => {
  const t = useTranslations();
  const {mutate: globalMutate} = useSWRConfig();

  const prepareData = useCallback((values: any) => {
    if (!values) return {} as PurchaseOrder;
    return {
      ...values,
      items: values.items?.map((item: any) => ({
        ...item,
        price: parseCurrency(item.price, false),
      })),
      totalAmount: parseCurrency(values.totalAmount, false),
    } as PurchaseOrder;
  }, []);

  const processValues = useCallback(
    (values: PurchaseOrder) => ({
      ...values,
      expectedBy: values.expectedDelivery,
      items: values.items.map((item) => ({
        ...item,
        price: parseCurrency(item.price),
      })),
      managedBy: values.managedBy?.id,
      totalAmount: parseCurrency(values.totalAmount),
    }),
    [],
  );

  const createPurchaseOrder = useCallback(
    (
      supplier: Id,
      wishlists: WishlistItem[],
      {
        emailMessage,
        renderingDetails,
      }: {
        emailMessage?: {body: string; subject: string};
        renderingDetails?: PurchaseRenderingDetails;
      } = {},
    ): Promise<PurchaseOrder | void> =>
      axios
        .post('/api/purchases/orders/create', {
          items: wishlists.map((wishlist) => ({
            ...wishlist,
            materialGoodId: wishlist.materialGood.id,
            originalWishlistId: wishlist.id,
          })),
          supplierId: supplier.id,
          ...(emailMessage ? {emailMessage} : {}),
          ...(renderingDetails ? {renderingDetails} : {}),
        })
        .then((res) => res.data)
        .then((data) => {
          globalMutate((key) => Array.isArray(key) && (key[0] === 'wishlists' || key[0] === 'purchases'));
          toast.success(
            t(
              emailMessage
                ? 'order orderName for supplierName has been sent successfully'
                : 'order orderName for supplierName has been created successfully',
              {
                orderName: data.number,
                supplierName: data.name,
              },
            ),
          );

          return data;
        })
        .catch((error) =>
          handleError(
            error,
            t('order for supplierName has failed to send successfully', {supplierName: supplier.name}),
          ),
        ),
    [globalMutate, t],
  );

  const updatePurchaseOrder = useCallback(
    async (id: string, values: PurchaseOrder) => {
      const newValues = processValues(values);

      return axios
        .post(`/api/purchases/orders/${id}/update`, newValues)
        .then((res) => res.data)
        .then((data) => {
          globalMutate((key) => Array.isArray(key) && key[0] === 'purchases');
          toast.success(
            t('name has been updated successfully', {
              name: `${t('suffixed.order.start')} ${newValues.number}`,
              updated: t('updated.female'),
            }),
          );
          return data;
        })
        .catch((error) =>
          handleError(
            error,
            t('name has failed to update successfully', {
              name: `${t('suffixed.order.start')} ${newValues.number}`,
              updated: t('updated.female'),
            }),
          ),
        );
    },
    [globalMutate, processValues, t],
  );

  return {
    createPurchaseOrder,
    prepareData,
    processValues,
    updatePurchaseOrder,
  };
};

export default usePurchaseOrderActions;
