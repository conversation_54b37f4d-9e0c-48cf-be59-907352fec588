import {useCallback} from 'react';

import axios from 'axios';
import {useAtomValue} from 'jotai';
import {useTranslations} from 'next-intl';
import {useSWRConfig} from 'swr';

import {toast} from '@/components/ui/Toast';
import {defaultCurrencyAtom} from '@/store/defaults';
import {Adjustment, InventoryEntry} from '@/types/inventory';
import {handleError} from '@/utils/axios';
import {parseCurrency} from '@/utils/common';

const useInventoryAdjustmentActions = () => {
  const {mutate: globalMutate} = useSWRConfig();
  const defaultCurrency = useAtomValue(defaultCurrencyAtom);
  const t = useTranslations();

  const prepareData = useCallback((values: any) => {
    if (!values) return {};
    return {
      ...values,
      inventoryEntries: values.inventoryEntries?.map((inventoryEntry: InventoryEntry) => ({
        ...inventoryEntry,
        price: parseCurrency(inventoryEntry.price, false),
      })),
      totalValue: parseCurrency(values.totalValue, false),
    } as Adjustment;
  }, []);

  const processValues = useCallback(
    (values: Partial<Adjustment>) => ({
      ...values,

      inventoryEntries: values.inventoryEntries?.map((inventoryEntry) => ({
        ...inventoryEntry,
        fromUnit: inventoryEntry.fromUnit?.id,
        materialGoodId: inventoryEntry.materialGood.id,
        price: parseCurrency({
          amount: inventoryEntry.price?.amount || 0,
          currency: inventoryEntry.price?.currency || defaultCurrency,
        }),
        unitId: inventoryEntry.unit.id,
      })),
      totalValue: parseCurrency({
        amount: values.totalValue?.amount || 0,
        currency: values.totalValue?.currency || defaultCurrency,
      }),
    }),
    [defaultCurrency],
  );

  const createAdjustment = useCallback(
    (values: Partial<Adjustment>) => {
      const newValues = processValues(values);

      return axios
        .post('/api/inventory/adjustments/adjust', newValues)
        .then((res) => res.data)
        .then((item) => {
          toast.success(
            t('name has been created successfully', {
              created: t('created.female'),
              name: `${t('suffixed.adjustment.start')} ${item.number}`,
            }),
          );
          globalMutate((key) => Array.isArray(key) && key[0] === 'adjustments');
          return item;
        })
        .catch((error) =>
          handleError(
            error,
            t('name has failed to create successfully', {
              created: t('created.female'),
              name: `${t('suffixed.adjustment.start')} ${newValues.number}`,
            }),
          ),
        );
    },
    [globalMutate, processValues, t],
  );
  const createMove = useCallback(
    (values: Partial<Adjustment>) => {
      const newValues = processValues(values);

      return axios
        .post('/api/inventory/adjustments/move-to-unit', newValues)
        .then((res) => res.data)
        .then((item) => {
          toast.success(
            t('name has been created successfully', {
              created: t('created.female'),
              name: `${t('suffixed.adjustment.start')} ${item.number}`,
            }),
          );
          globalMutate((key) => Array.isArray(key) && key[0] === 'adjustments');
          return item;
        })
        .catch((error) => {
          handleError(
            error,
            t('name has failed to create successfully', {
              created: t('created.female'),
              name: `${t('suffixed.adjustment.start')} ${newValues.number}`,
            }),
          );
          return null;
        });
    },
    [globalMutate, processValues, t],
  );

  return {
    createAdjustment,
    createMove,
    prepareData,
    processValues,
  };
};

export default useInventoryAdjustmentActions;
