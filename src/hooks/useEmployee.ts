import {useCallback, useEffect, useState} from 'react';

import {joiResolver} from '@hookform/resolvers/joi';
import axios from 'axios';
import Joi from 'joi';
import {useTranslations} from 'next-intl';
import {useForm} from 'react-hook-form';
import useSWR from 'swr';

import {toast} from '@/components/ui/Toast';
import {useRouter} from '@/hooks/helpers/useRouter';
import useEmployeeActions from '@/hooks/useEmployeeActions';
import {Employee} from '@/types/manufacturing';
import {handleError} from '@/utils/axios';

import useLeaveConfirm from './helpers/useLeaveConfirm';

export const employeeSchema = Joi.object({
  activeTimeoffs: Joi.array()
    .items(
      Joi.object({
        endTime: Joi.string().required(),
        startTime: Joi.string().required(),
      }).unknown(),
    )
    .min(0)
    .allow(null),
  monthlyGrossSalary: Joi.object({amount: Joi.number().positive().required().allow(0)})
    .required()
    .unknown(),
  name: Joi.string().required(),
  position: Joi.string().required(),
}).unknown();

const useEmployee = (id?: string) => {
  const t = useTranslations();
  const {replace} = useRouter();
  const [created, setCreated] = useState(false);
  const {
    createEmployee: createEmployeeAction,
    deleteEmployee: deleteEmployeeAction,
    prepareData,
    updateEmployee: updateEmployeeAction,
  } = useEmployeeActions();

  const {
    control,
    formState: {dirtyFields, errors, ...restUseFormState},
    handleSubmit,
    register,
    reset,
    resetField,
    setValue,
    watch,
    ...restUseForm
  } = useForm<Employee>({
    defaultValues: {
      activeTimeoffs: [],
    },
    mode: 'onSubmit',
    resolver: joiResolver(employeeSchema),
  });

  const isDirty = !!Object.keys(dirtyFields).length;

  useLeaveConfirm(created ? false : isDirty, async () => await saveEmployee());

  useEffect(() => {
    if (watch('id') && created) replace(`/employees/${watch('id')}`);
  }, [created, replace, watch]);

  const {data, isLoading, isValidating, mutate} = useSWR(
    id ? ['employee', id] : null,
    () =>
      axios
        .get(`/api/employees/${id}/details`)
        .then((res) => prepareData(res.data))
        .then((values) => {
          reset(values);
          return values;
        })
        .catch((error) =>
          handleError(error, t('an error occurred while loading name', {name: t('suffixed.employee.end')})),
        ),
    {
      revalidateOnFocus: false,
    },
  );

  useEffect(() => {
    if (data) reset(data as Employee);
  }, [data, reset]);

  const saveEmployee = useCallback(
    () =>
      handleSubmit(
        (values) => {
          return id
            ? updateEmployeeAction(id, values).then(() => mutate())
            : createEmployeeAction(values).then((data) => {
                setCreated(true);
                setValue('id', data.id);
              });
        },
        () => {
          toast.warning(t('please fill in all mandatory fields before saving'));
          return Promise.reject();
        },
      )(),
    [handleSubmit, id, updateEmployeeAction, createEmployeeAction, mutate, setValue, t],
  );

  const deleteEmployee = useCallback(() => {
    if (id) return deleteEmployeeAction(id, watch('name')).then(() => replace(`/employees`));
  }, [deleteEmployeeAction, id, replace, watch]);

  return {
    deleteEmployee,
    employee: watch() as Employee,
    isDirty,
    isLoading: isLoading || (id && !watch('id')),
    isValidating,
    saveEmployee,
    useFormActions: {
      control,
      formState: {dirtyFields, errors, ...restUseFormState},
      handleSubmit,
      register,
      reset,
      resetField,
      setValue: ((name, value: any, options = {shouldDirty: true}) =>
        setValue(name, value, options)) as typeof setValue,
      watch,
      ...restUseForm,
    },
  };
};

export default useEmployee;
