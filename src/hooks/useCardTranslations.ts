import {useLocale} from 'next-intl';

export type CardTranslations = {
  [key: string]: {
    en: string;
    hu: string;
    ro: string;
  };
};

export const useCardTranslations = (translations: CardTranslations) => {
  const locale = useLocale() || 'en';

  return (key: string): string => {
    if (!translations[key]) return key;

    return translations[key][locale as keyof (typeof translations)[typeof key]] || translations[key].en;
  };
};
