import axios from 'axios';
import {useTranslations} from 'next-intl';
import useSWR from 'swr';

import {handleError} from '@/utils/axios';
import {Unit} from 'types/global';

const useUnits = () => {
  const t = useTranslations();

  const {data, error, isLoading, isValidating} = useSWR(
    ['units'],
    () =>
      axios
        .get('/api/configurations/measurement-units')
        .then((res) => res.data)
        .then((data: {[x: string]: string}) =>
          Object.entries(data).map(([id, name]) => ({
            id,
            name,
          })),
        )
        .catch((error) => handleError(error, t('an error occurred while loading name', {name: t('suffixed.units')}))),
    {revalidateOnFocus: false},
  );

  return {
    error,
    isLoading,
    isValidating,
    units: (data || []) as Unit[],
  };
};

export default useUnits;
