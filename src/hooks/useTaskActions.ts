import {useCallback} from 'react';

import axios from 'axios';
import {useTranslations} from 'next-intl';
import {useSWRConfig} from 'swr';

import {toast} from '@/components/ui/Toast';
import {handleError} from '@/utils/axios';
import {getQueryString} from '@/utils/common';
import {taskActions} from 'hooks/helpers/actions';
import {
  ManufacturingTaskStatus,
  ManufacturingTaskStatusReason,
  ServicingTaskStatus,
  ServicingTaskStatusReason,
} from 'types/global';

const useTaskActions = () => {
  const t = useTranslations();
  const {mutate: globalMutate} = useSWRConfig();

  const updateTaskEmployees = useCallback(
    (id: string, employees: string[], manufacturingId: string, name: string) =>
      axios
        .post(`/api/manufacturing/tasks/${id}/employees`, employees)
        .then(() => {
          toast.success(
            t('name has been updated successfully', {
              name: `${t('suffixed.task allocation.start')} ${name}`,
              updated: t('updated.female'),
            }),
          );
        })
        .catch((error) =>
          handleError(
            error,
            t('name has failed to update successfully', {
              name: `${t('suffixed.task allocation.start')} ${name}`,
              updated: t('updated.female'),
            }),
          ),
        )
        .finally(() => {
          globalMutate(
            (key) =>
              Array.isArray(key) &&
              (key[0] === 'tasks' ||
                key[0] === 'manufacturings' ||
                (key[0] === 'manufacturing' && key[1] === manufacturingId)),
          );
        }),
    [globalMutate, t],
  );

  const updateTaskWorkstations = useCallback(
    (id: string, workstations: string[], manufacturingId: string, name: string) =>
      axios
        .post(`/api/manufacturing/tasks/${id}/workstations`, workstations)
        .then(() => {
          toast.success(
            t('name has been updated successfully', {
              name: `${t('suffixed.task allocation.start')} ${name}`,
              updated: t('updated.female'),
            }),
          );
        })
        .catch((error) =>
          handleError(
            error,
            t('name has failed to update successfully', {
              name: `${t('suffixed.task allocation.start')} ${name}`,
              updated: t('updated.female'),
            }),
          ),
        )
        .finally(() => {
          globalMutate(
            (key) =>
              Array.isArray(key) &&
              (key[0] === 'tasks' ||
                key[0] === 'manufacturings' ||
                (key[0] === 'manufacturing' && key[1] === manufacturingId)),
          );
        }),
    [globalMutate, t],
  );

  const updateManufacturingTaskStatus = useCallback(
    (
      id: string,
      status: ManufacturingTaskStatus,
      {name, orderId, reason}: {name?: string; orderId: string; reason?: ManufacturingTaskStatusReason},
    ) => {
      if (taskActions[status]) {
        axios
          .post(`/api/manufacturing/tasks/${id}/${taskActions[status]}?${getQueryString({reason})}`)
          .then(() => {
            toast.success(
              t('name has been updated successfully', {
                name: `${t('suffixed.task.start')} ${name}`,
                updated: t('updated.female'),
              }),
            );
          })
          .catch((error) =>
            handleError(
              error,
              t('name has failed to update successfully', {
                name: `${t('suffixed.task.start')} ${name}`,
                updated: t('updated.female'),
              }),
            ),
          )
          .finally(() => {
            globalMutate(
              (key) =>
                Array.isArray(key) &&
                (key[0] === 'tasks' ||
                  key[0] === 'manufacturings' ||
                  key[0] === 'services' ||
                  key[0] === 'myTasks' ||
                  (key[0] === 'manufacturing' && key[1] === orderId)),
            );
          });
      }
    },
    [globalMutate, t],
  );

  const updateTaskRequiredEmployees = useCallback(
    (id: string, value: number, orderId: string, name?: string) =>
      axios
        .post(`/api/manufacturing/tasks/${id}/change-assignee-count`, {numberOfAssignees: value})
        .then(() => {
          toast.success(
            t('name has been updated successfully', {
              name: `${t('suffixed.task.start')} ${name || ''}`,
              updated: t('updated.female'),
            }),
          );
        })
        .catch((error) =>
          handleError(
            error,
            t('name has failed to update successfully', {
              name: `${t('suffixed.task.start')} ${name || ''}`,
              updated: t('updated.female'),
            }),
          ),
        )
        .finally(() => {
          globalMutate(
            (key) =>
              Array.isArray(key) &&
              (key[0] === 'tasks' ||
                key[0] === 'manufacturings' ||
                key[0] === 'item' ||
                (key[0] === 'manufacturing' && key[1] === orderId)),
          );
        }),
    [globalMutate, t],
  );

  const getAvailableStatuses = useCallback(
    (
      status: ManufacturingTaskStatus | ServicingTaskStatus,
      {withoutNext = false, withoutSelf = false}: {withoutNext?: boolean; withoutSelf?: boolean} = {},
    ) => {
      const statuses = [];

      if (status in ManufacturingTaskStatus) {
        switch (status) {
          case ManufacturingTaskStatus.DONE:
            if (!withoutSelf) statuses.push(ManufacturingTaskStatus.DONE);
            break;
          case ManufacturingTaskStatus.IN_PROGRESS:
            if (!withoutSelf) statuses.push(ManufacturingTaskStatus.IN_PROGRESS);
            if (!withoutNext) statuses.push(ManufacturingTaskStatusReason.PAUSED);
            if (!withoutNext) statuses.push(ManufacturingTaskStatusReason.MISSING_MATERIAL);
            if (!withoutNext) statuses.push(ManufacturingTaskStatusReason.EQUIPMENT_UNAVAILABLE);
            if (!withoutNext) statuses.push(ManufacturingTaskStatusReason.BROKEN_EQUIPMENT);
            statuses.push(ManufacturingTaskStatus.DONE);
            break;
          case ManufacturingTaskStatus.STOPPED:
            if (!withoutNext) statuses.push(ManufacturingTaskStatus.IN_PROGRESS);
            if (!withoutSelf) statuses.push(ManufacturingTaskStatusReason.PAUSED);
            if (!withoutSelf) statuses.push(ManufacturingTaskStatusReason.MISSING_MATERIAL);
            if (!withoutSelf) statuses.push(ManufacturingTaskStatusReason.EQUIPMENT_UNAVAILABLE);
            if (!withoutSelf) statuses.push(ManufacturingTaskStatusReason.BROKEN_EQUIPMENT);
            statuses.push(ManufacturingTaskStatus.DONE);
            break;
          case ManufacturingTaskStatus.TODO:
            if (!withoutSelf) statuses.push(ManufacturingTaskStatus.TODO);
            if (!withoutNext) statuses.push(ManufacturingTaskStatus.IN_PROGRESS);
            statuses.push(ManufacturingTaskStatusReason.PAUSED);
            statuses.push(ManufacturingTaskStatusReason.MISSING_MATERIAL);
            statuses.push(ManufacturingTaskStatusReason.EQUIPMENT_UNAVAILABLE);
            statuses.push(ManufacturingTaskStatusReason.BROKEN_EQUIPMENT);
            statuses.push(ManufacturingTaskStatus.DONE);
            break;
          default:
            break;
        }
      } else {
        switch (status) {
          case ServicingTaskStatus.DONE:
            if (!withoutSelf) statuses.push(ServicingTaskStatus.DONE);
            break;
          case ServicingTaskStatus.IN_PROGRESS:
            if (!withoutSelf) statuses.push(ServicingTaskStatus.IN_PROGRESS);
            if (!withoutNext) statuses.push(ServicingTaskStatusReason.PAUSED);
            if (!withoutNext) statuses.push(ServicingTaskStatusReason.MISSING_MATERIAL);
            if (!withoutNext) statuses.push(ServicingTaskStatusReason.EQUIPMENT_UNAVAILABLE);
            if (!withoutNext) statuses.push(ServicingTaskStatusReason.BROKEN_EQUIPMENT);
            statuses.push(ServicingTaskStatus.DONE);
            break;
          case ServicingTaskStatus.STOPPED:
            if (!withoutNext) statuses.push(ServicingTaskStatus.IN_PROGRESS);
            if (!withoutSelf) statuses.push(ServicingTaskStatusReason.PAUSED);
            if (!withoutSelf) statuses.push(ServicingTaskStatusReason.MISSING_MATERIAL);
            if (!withoutSelf) statuses.push(ServicingTaskStatusReason.EQUIPMENT_UNAVAILABLE);
            if (!withoutSelf) statuses.push(ServicingTaskStatusReason.BROKEN_EQUIPMENT);
            statuses.push(ServicingTaskStatus.DONE);
            break;
          case ServicingTaskStatus.TODO:
            if (!withoutSelf) statuses.push(ServicingTaskStatus.TODO);
            if (!withoutNext) statuses.push(ServicingTaskStatus.IN_PROGRESS);
            statuses.push(ServicingTaskStatusReason.PAUSED);
            statuses.push(ServicingTaskStatusReason.MISSING_MATERIAL);
            statuses.push(ServicingTaskStatusReason.EQUIPMENT_UNAVAILABLE);
            statuses.push(ServicingTaskStatusReason.BROKEN_EQUIPMENT);
            statuses.push(ServicingTaskStatus.DONE);
            break;
          default:
            break;
        }
      }

      return statuses;
    },
    [],
  );

  return {
    getAvailableStatuses,
    updateManufacturingTaskStatus,
    updateTaskEmployees,
    updateTaskRequiredEmployees,
    updateTaskWorkstations,
  };
};

export default useTaskActions;
