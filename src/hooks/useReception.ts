import {useCallback, useEffect, useState} from 'react';

import {joiResolver} from '@hookform/resolvers/joi';
import axios from 'axios';
import FormData from 'form-data';
import Joi from 'joi';
import {useAtomValue} from 'jotai';
import {find, sumBy} from 'lodash';
import {useTranslations} from 'next-intl';
import {useForm} from 'react-hook-form';
import useSWR from 'swr';

import {toast} from '@/components/ui/Toast';
import {useRouter} from '@/hooks/helpers/useRouter';
import useReceptionActions from '@/hooks/useReceptionActions';
import {defaultCurrencyAtom} from '@/store/defaults';
import {handleError} from '@/utils/axios';
import useLeaveConfirm from 'hooks/helpers/useLeaveConfirm';
import {
  Reception,
  ReceptionAdditionalCostAllocationStrategy,
  ReceptionAdditionalCostType,
  SupportingDocumentType,
} from 'types/purchases';

const receptionSchema = Joi.object({
  additionalCosts: Joi.array()
    .items(
      Joi.object({
        price: Joi.when('type', {
          is: Joi.valid(ReceptionAdditionalCostType.TRANSPORTATION),
          then: Joi.object({
            amount: Joi.number().positive().required(),
          }).unknown(),
        }),
        quantity: Joi.number().positive().required(),
        type: Joi.string()
          .valid(...Object.values(ReceptionAdditionalCostType))
          .required(),
      }).unknown(),
    )
    .min(0),
  goods: Joi.array()
    .items(
      Joi.object({
        additionalCostCustomValue: Joi.object({
          amount: Joi.number().required(),
        })
          .optional()
          .unknown(),
        price: Joi.object({
          amount: Joi.number().positive().required(),
        })
          .required()
          .unknown(),
      }).unknown(),
    )
    .min(1),
  receptionDate: Joi.string().required().disallow('-'),
  supplier: Joi.object({id: Joi.string().required()}).unknown().required(),
  supportingDocument: Joi.object({
    date: Joi.when('type', {
      is: Joi.valid(SupportingDocumentType.NONE),
      otherwise: Joi.string().required().disallow('-'),
    }),
    number: Joi.when('type', {
      is: Joi.valid(SupportingDocumentType.NONE),
      otherwise: Joi.string().required(),
    }),
  }).unknown(),
})
  .unknown()
  .custom((values: Reception, helpers) => {
    const transport = find(values.additionalCosts, (cost) => cost?.type === ReceptionAdditionalCostType.TRANSPORTATION);

    if (transport) {
      const allocationStrategy = transport.allocationStrategy;
      const transportAmount = transport.price?.amount || 1;

      const totalCustomValue = sumBy(values.goods, (good) => good?.additionalCostCustomValue?.amount || 0);

      if (allocationStrategy === ReceptionAdditionalCostAllocationStrategy.CUSTOM && transportAmount > 0) {
        const percentage = (totalCustomValue / transportAmount) * 100;
        if (percentage !== 100) {
          return helpers.error('any.invalid', {message: 'The custom allocation strategy must sum up to 100%'});
        }
      }
    }

    return values;
  }, 'Transport Custom Allocation Check');

const useReception = (id?: string, {withoutLeaveConfirm}: {withoutLeaveConfirm?: boolean} = {}) => {
  const t = useTranslations();
  const {replace} = useRouter();
  const defaultCurrency = useAtomValue(defaultCurrencyAtom);
  const {createReception: createReceptionAction, prepareData} = useReceptionActions();
  const [created, setCreated] = useState(false);

  const {
    control,
    formState: {dirtyFields, errors, ...restUseFormState},
    handleSubmit,
    register,
    reset,
    resetField,
    setValue,
    watch,
    ...restUseForm
  } = useForm<Reception>({
    defaultValues: {
      additionalCosts: [],
      currency: defaultCurrency,
      files: [],
      goods: [],
      receptionDate: new Date().toISOString(),
      supportingDocument: {type: SupportingDocumentType.NONE},
    },
    mode: 'onSubmit',
    resolver: joiResolver(receptionSchema),
  });

  const isDirty = !!Object.keys(dirtyFields).length;

  useLeaveConfirm(created || withoutLeaveConfirm ? false : isDirty, async () => await saveReception());

  useEffect(() => {
    if (created) replace(`/inventory/receptions/${watch('id')}`);
  }, [created, replace, watch]);

  const {data, isLoading, isValidating, mutate} = useSWR(
    id ? ['reception', id] : null,
    () =>
      axios
        .get(`/api/inventory/receptions/${id}/details`)
        .then((res) => prepareData(res.data))
        .then((values) => {
          // reset(values);

          return values;
        })
        .catch((error) =>
          handleError(
            error,
            t('an error occurred while loading name', {
              name: t('suffixed.reception.end'),
            }),
          ),
        ),
    {revalidateOnFocus: false},
  );

  useEffect(() => {
    if (data) reset(data as Reception);
  }, [data, reset]);

  const saveReception = useCallback(
    (files?: File[]) =>
      handleSubmit(
        (values) => {
          return createReceptionAction(values, files).then((data) => {
            setCreated(true);
            setValue('id', data.id);
          });
        },
        () => {
          toast.warning(t('please fill in all mandatory fields before saving'));
          return Promise.reject();
        },
      )(),
    [createReceptionAction, handleSubmit, setValue, t],
  );

  const resetToDefault = useCallback(() => {
    reset({
      additionalCosts: [],
      currency: defaultCurrency,
      goods: [],
      notes: '',
      receivedBy: '',
      receptionDate: new Date().toISOString(),
      supplier: {id: ''},
      supportingDocument: {date: '', number: '', type: SupportingDocumentType.NONE},
      transportedBy: '',
      transportedWith: '',
    });
  }, [defaultCurrency, reset]);

  const uploadReceptionFile = useCallback(
    (file?: File) => {
      if (file) {
        const formData = new FormData();

        formData.append('files', file);
        return axios
          .post(`/api/attach-file/reception/${id}`, formData, {
            headers: {'X-Original-Filename': encodeURIComponent(file.name)},
          })
          .then(() => {
            mutate();
          })
          .catch((error) =>
            handleError(
              error,
              t('name has failed to create successfully', {
                created: t('created.female'),
                name: t('suffixed.reception.start'),
              }),
            ),
          );
      }
    },
    [id, mutate, t],
  );

  return {
    isDirty,
    isLoading,
    isValidating,
    reception: watch() as Reception,
    resetToDefault,
    saveReception,
    uploadReceptionFile,
    useFormActions: {
      control,
      formState: {dirtyFields, errors, ...restUseFormState},
      handleSubmit,
      register,
      reset,
      resetField,
      setValue: ((name, value: any, options = {shouldDirty: true}) =>
        setValue(name, value, options)) as typeof setValue,
      watch,
      ...restUseForm,
    },
  };
};

export default useReception;
