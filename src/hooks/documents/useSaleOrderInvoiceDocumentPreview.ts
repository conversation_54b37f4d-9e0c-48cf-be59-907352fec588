import {useCallback, useState} from 'react';

import axios from 'axios';
import {useTranslations} from 'next-intl';
import {useSWRConfig} from 'swr';

import {toast} from '@/components/ui/Toast';
import useSWRWithDebounce from '@/hooks/helpers/useDebouncedSWR';
import {InvoiceRenderingDetails} from '@/types/sales';
import {handleError} from '@/utils/axios';

const useSaleOrderInvoiceDocumentPreview = (
  id?: string,
  {isProforma, notes}: {isProforma?: boolean; notes?: string} = {},
) => {
  const t = useTranslations();
  const {mutate: globalMutate} = useSWRConfig();
  const [renderingDetails, setRenderingDetails] = useState<InvoiceRenderingDetails>({
    columnsToHide: [],
  });

  const {data, error, isLoading, isValidating, mutate} = useSWRWithDebounce<string>(
    id ? ['saleDocumentPreview', id, isProforma, notes, JSON.stringify(renderingDetails)] : null,
    () =>
      axios
        .post(`/api/sales/orders/${id}/invoice-preview?mediaType=text/html`, {
          isProforma,
          notes,
          ...(renderingDetails ? {renderingDetails} : {}),
        })
        .then((res) => res.data)
        .catch((error) =>
          handleError(error, t('an error occurred while loading name', {name: t('suffixed.invoice.end')})),
        ),
    {revalidateOnFocus: false},
  );

  const sendSaleOrderInvoice = useCallback(
    (
      saleId: string,
      {
        email = undefined,
        isQuote = false,
      }: {
        email?: undefined | {body: string; subject: string};
        isQuote?: boolean;
      },
    ) =>
      axios
        .post(`/api/sales/orders/${saleId}/send`, email)
        .then((res) => res.data)
        .then((data) => {
          mutate();
          globalMutate(
            (key) =>
              Array.isArray(key) &&
              (key[0] === 'sales' || key[0] === 'invoices' || (key[0] === 'sale' && key[1] === saleId)),
          );
          toast.success(
            t('name has been created successfully', {
              created: t('created.female'),
              name: `${isQuote ? t('suffixed.quote.start') : t('suffixed.invoice.start')} ${data.number || ''}`,
            }),
          );
          return data;
        })
        .catch((error) =>
          handleError(
            error,
            t('name has failed to create successfully', {
              created: t('created.female'),
              name: isQuote ? t('suffixed.quote.start') : t('suffixed.invoice.start'),
            }),
          ),
        ),
    [globalMutate, mutate, t],
  );

  const createSaleOrderInvoice = useCallback(
    (
      saleId: string,
      {
        email = undefined,
        isProforma = false,
        notes = '',
        renderingDetails,
      }: {
        email?: undefined | {body: string; subject: string};
        isProforma?: boolean;
        notes?: string;
        renderingDetails?: InvoiceRenderingDetails;
      },
    ) =>
      axios
        .post(`/api/sales/orders/${saleId}/invoice`, {
          emailMessage: email,
          notes,
          proforma: isProforma,
          renderingDetails,
        })
        .then((res) => res.data)
        .then((invoice) => {
          mutate();
          globalMutate(
            (key) =>
              Array.isArray(key) &&
              (key[0] === 'sales' || key[0] === 'invoices' || (key[0] === 'sale' && key[1] === saleId)),
          );
          toast.success(
            email
              ? t('name has been sent successfully', {
                  name: `${t('suffixed.invoice.start')} ${invoice.number}`,
                })
              : t('name has been created successfully', {
                  created: t('created.female'),
                  name: `${t('suffixed.invoice.start')} ${invoice.number}`,
                }),
          );
          return invoice;
        })
        .catch((error) =>
          handleError(
            error,
            email
              ? t('name has failed to send successfully', {
                  name: t('suffixed.invoice.start'),
                })
              : t('name has failed to create successfully', {
                  created: t('created.female'),
                  name: t('suffixed.invoice.start'),
                }),
          ),
        ),
    [globalMutate, mutate, t],
  );

  return {
    createSaleOrderInvoice,
    error,
    isLoading,
    isValidating,
    renderingDetails,
    saleInvoiceDocumentPreview: data,
    sendSaleOrderInvoice,
    setRenderingDetails,
  };
};

export default useSaleOrderInvoiceDocumentPreview;
