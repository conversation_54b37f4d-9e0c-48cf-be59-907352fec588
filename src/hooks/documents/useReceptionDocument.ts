import axios from 'axios';
import {useTranslations} from 'next-intl';
import useSWR from 'swr';

import {handleError} from '@/utils/axios';

const useReceptionDocument = (id?: string) => {
  const t = useTranslations();

  const {data, error, isLoading, isValidating} = useSWR<string>(
    id ? ['receptionDocument', id] : null,
    () =>
      axios
        .get(`/api/inventory/receptions/${id}/details?mediaType=text/html`)
        .then((res) => res.data)
        .catch((error) =>
          handleError(error, t('an error occurred while loading name', {name: t('suffixed.reception.end')})),
        ),
    {revalidateOnFocus: false},
  );

  return {
    error,
    isLoading,
    isValidating,
    receptionDocument: data,
  };
};

export default useReceptionDocument;
