import {useCallback} from 'react';

import axios from 'axios';
import {useTranslations} from 'next-intl';

import useSWRWithDebounce from '@/hooks/helpers/useDebouncedSWR';
import usePurchaseOrderActions from '@/hooks/usePurchaseOrderActions';
import useWishlist from '@/hooks/useWishlist';
import useWishlistActions from '@/hooks/useWishlistActions';
import {PurchaseRenderingDetails} from '@/types/purchases';
import {handleError} from '@/utils/axios';

const useWishlistDocumentPreview = (wishlistId?: string, renderingDetails?: PurchaseRenderingDetails) => {
  const t = useTranslations();
  const {createPurchaseOrder: createPurchaseOrderAction} = usePurchaseOrderActions();
  const {isLoading: wishlistIsLoading, wishlist} = useWishlist(wishlistId);
  const {processWishlistOrder} = useWishlistActions();

  const {data, error, isLoading, isValidating} = useSWRWithDebounce<string>(
    !wishlistIsLoading && wishlist.wishlistItems.length > 0 && wishlistId
      ? ['wishlistDocumentPreview', wishlistId, JSON.stringify(renderingDetails)]
      : null,
    () =>
      axios
        .post(
          '/api/purchases/orders/preview?mediaType=text/html',
          processWishlistOrder({...wishlist, renderingDetails}),
        )
        .then((res) => res.data)
        .catch((error) =>
          handleError(error, t('an error occurred while loading name', {name: t('suffixed.invoice.end')})),
        ),
    {revalidateOnFocus: false},
  );

  const createPurchaseOrder = useCallback(
    (email?: {body: string; subject: string}) =>
      createPurchaseOrderAction({id: wishlist.id, name: wishlist.name}, processWishlistOrder(wishlist)?.items, {
        emailMessage: email,
        renderingDetails,
      }).then((order) => {
        return order;
      }),
    [createPurchaseOrderAction, processWishlistOrder, renderingDetails, wishlist],
  );

  return {
    createPurchaseOrder,
    error,
    isLoading,
    isValidating,
    wishlistDocumentPreview: data,
  };
};

export default useWishlistDocumentPreview;
