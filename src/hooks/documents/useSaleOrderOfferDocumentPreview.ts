import {useCallback, useState} from 'react';

import axios from 'axios';
import {format} from 'date-fns';
import {useTranslations} from 'next-intl';
import {useSWRConfig} from 'swr';

import {toast} from '@/components/ui/Toast';
import useSWRWithDebounce from '@/hooks/helpers/useDebouncedSWR';
import useSaleOrder from '@/hooks/useSaleOrder';
import useSaleOrderActions from '@/hooks/useSaleOrderActions';
import {SalesRenderingDetails, SaleType} from '@/types/sales';
import {handleError} from '@/utils/axios';

const useSaleOrderOfferDocumentPreview = (
  id: string,
  {customerNotes, offerExpiration}: {customerNotes?: string; offerExpiration?: Date} = {},
) => {
  const t = useTranslations();
  const {mutate: globalMutate} = useSWRConfig();
  const {saleOrder} = useSaleOrder({id, type: SaleType.QUOTE});
  const {processValues} = useSaleOrderActions();
  const [renderingDetails, setRenderingDetails] = useState<SalesRenderingDetails>({
    columnsToHide: [],
  });

  const {data, error, isLoading, isValidating} = useSWRWithDebounce<string>(
    id && saleOrder?.id
      ? ['saleDocumentPreview', id, offerExpiration, JSON.stringify(renderingDetails), customerNotes]
      : null,
    () =>
      axios
        .post(
          '/api/sales/orders/preview?mediaType=text/html',
          processValues({
            ...saleOrder,
            ...(offerExpiration ? {offerExpiration: format(offerExpiration || 0, 'yyyy-MM-dd')} : {}),
            ...(renderingDetails ? {renderingDetails} : {}),
            customerNotes,
          }),
        )
        .then((res) => res.data)
        .catch((error) =>
          handleError(error, t('an error occurred while loading name', {name: t('suffixed.offer.end')})),
        ),
    {revalidateOnFocus: false},
  );

  const createSaleOrderOffer = useCallback(
    (
      {customerNotes, offerExpiration}: {customerNotes?: string; offerExpiration?: Date} = {},
      renderingDetails?: SalesRenderingDetails,
    ) =>
      axios
        .post(`/api/sales/orders/${id}/save-version`, {
          ...(offerExpiration ? {offerExpiration: format(offerExpiration || 0, 'yyyy-MM-dd')} : {}),
          ...(renderingDetails ? {renderingDetails} : {}),
          customerNotes,
        })
        .then((res) => res.data)
        .then((offer) => {
          globalMutate((key) => Array.isArray(key) && (key[0] === 'sales' || (key[0] === 'sale' && key[1] === id)));

          toast.success(
            t('name has been created successfully', {
              created: t('created.female'),
              name: `${t('suffixed.offer.start')} ${offer.number}`,
            }),
          );

          return offer;
        })
        .catch((error) =>
          handleError(
            error,
            t('name has failed to create successfully', {
              created: t('created.female'),
              name: t('suffixed.offer.start'),
            }),
          ),
        ),
    [globalMutate, id, t],
  );

  return {
    createSaleOrderOffer,
    error,
    isLoading,
    isValidating,
    renderingDetails,
    saleOfferDocumentPreview: data,
    setRenderingDetails,
  };
};

export default useSaleOrderOfferDocumentPreview;
