import axios from 'axios';
import {useTranslations} from 'next-intl';
import useSWR from 'swr';

import {handleError} from '@/utils/axios';

const useServiceReportDocument = (id?: string) => {
  const t = useTranslations();

  const {data, error, isLoading, isValidating} = useSWR<string>(
    id ? ['serviceReport', id] : null,
    () =>
      axios
        .get(`/api/servicing/orders/${id}/service-report?mediaType=text/html`)
        .then((res) => res.data)
        .catch((error) =>
          handleError(error, t('an error occurred while loading name', {name: t('suffixed.serviceReport.end')})),
        ),
    {revalidateOnFocus: false},
  );

  return {
    error,
    isLoading,
    isValidating,
    serviceReportDocument: data,
  };
};

export default useServiceReportDocument;
