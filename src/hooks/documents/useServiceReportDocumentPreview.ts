import axios from 'axios';
import {useTranslations} from 'next-intl';

import useSWRWithDebounce from '@/hooks/helpers/useDebouncedSWR';
import {handleError} from '@/utils/axios';

const useServiceReportDocumentPreview = (
  id?: string,
  {
    clientRepresentative,
    clientSignature,
    workerSignature,
  }: {
    clientRepresentative?: string;
    clientSignature?: string;
    workerSignature?: string;
  } = {},
) => {
  const t = useTranslations();

  const {data, error, isLoading, isValidating} = useSWRWithDebounce<string>(
    id ? ['serviceReportPreview', id, workerSignature, clientSignature, clientRepresentative] : null,
    () =>
      axios
        .post(`/api/servicing/orders/${id}/preview-service-report?mediaType=text/html`, {
          clientRepresentative,
          ...(workerSignature ? {base64WorkerSignature: workerSignature} : {}),
          ...(clientSignature ? {base64ClientSignature: clientSignature} : {}),
        })
        .then((res) => res.data)
        .catch((error) =>
          handleError(error, t('an error occurred while loading name', {name: t('suffixed.serviceReport.end')})),
        ),
    {revalidateOnFocus: false},
  );

  return {
    error,
    isLoading,
    isValidating,
    serviceReportDocumentPreview: data,
  };
};

export default useServiceReportDocumentPreview;
