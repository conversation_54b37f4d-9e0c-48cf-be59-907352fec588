import axios from 'axios';
import {useTranslations} from 'next-intl';

import useSWRWithDebounce from '@/hooks/helpers/useDebouncedSWR';
import {handleError} from '@/utils/axios';
import {getQueryString} from '@/utils/common';

const useSaleOrderOfferDocument = (id: string, orderId: string) => {
  const t = useTranslations();

  const {data, error, isLoading, isValidating} = useSWRWithDebounce<string>(
    id ? ['saleOfferDocument', id, orderId] : null,
    () =>
      axios
        .get(
          `/api/sales/orders/${orderId}/view-version/${id}?${getQueryString({
            mediaType: 'text/html',
          })}`,
        )
        .then((res) => res.data)
        .catch((error) =>
          handleError(error, t('an error occurred while loading name', {name: t('suffixed.offer.end')})),
        ),
    {revalidateOnFocus: false},
  );

  return {
    error,
    isLoading,
    isValidating,
    saleOfferDocument: data,
  };
};

export default useSaleOrderOfferDocument;
