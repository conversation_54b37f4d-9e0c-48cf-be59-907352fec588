import axios from 'axios';
import {useTranslations} from 'next-intl';
import useSWR from 'swr';

import {handleError} from '@/utils/axios';

const useConsumptionDocument = (manufactureId?: string) => {
  const t = useTranslations();

  const {data, error, isLoading, isValidating} = useSWR<string>(
    manufactureId ? ['manufacturingProductionDocument', manufactureId] : null,
    () =>
      axios
        .get(`/api/manufacturing/orders/${manufactureId}/reports/production?mediaType=text/html`)
        .then((res) => res.data)
        .catch((error) =>
          handleError(error, t('an error occurred while loading name', {name: t('suffixed.report.end')})),
        ),
    {revalidateOnFocus: false},
  );

  return {
    error,
    isLoading,
    isValidating,
    productionReportDocument: data,
  };
};

export default useConsumptionDocument;
