import axios from 'axios';
import {useTranslations} from 'next-intl';
import useSWR from 'swr';

import {handleError} from '@/utils/axios';

const useAccompanyingNoteDocument = (id: string) => {
  const t = useTranslations();

  const {data, error, isLoading, isValidating} = useSWR<string>(
    ['accompanyingNoteDocument', id],
    () =>
      axios
        .get(`/api/goods-accompanying-notes/${id}/details?mediaType=text/html`)
        .then((res) => res.data)
        .catch((error) =>
          handleError(error, t('an error occurred while loading name', {name: t('suffixed.accompanyingNote.end')})),
        ),
    {revalidateOnFocus: false},
  );

  return {
    accompanyingNoteDocument: data,
    error,
    isLoading,
    isValidating,
  };
};

export default useAccompanyingNoteDocument;
