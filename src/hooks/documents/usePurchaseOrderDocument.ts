import axios from 'axios';
import {useTranslations} from 'next-intl';
import useSWR from 'swr';

import {handleError} from '@/utils/axios';

const usePurchaseOrderDocument = (purchaseId: string) => {
  const t = useTranslations();

  const {data, error, isLoading, isValidating} = useSWR<string>(
    ['purchaseDocument', purchaseId],
    () =>
      axios
        .get(`/api/purchases/orders/${purchaseId}/details?mediaType=text/html`)
        .then((res) => res.data)
        .catch((error) =>
          handleError(error, t('an error occurred while loading name', {name: t('suffixed.invoice.end')})),
        ),
    {revalidateOnFocus: false},
  );

  return {
    error,
    isLoading,
    isValidating,
    purchaseDocument: data,
  };
};

export default usePurchaseOrderDocument;
