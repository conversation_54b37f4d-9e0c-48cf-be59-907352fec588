import {useEffect, useState} from 'react';

import axios from 'axios';
import {useTranslations} from 'next-intl';

import useSWRWithDebounce from '@/hooks/helpers/useDebouncedSWR';
import usePurchaseOrder from '@/hooks/usePurchaseOrder';
import useWishlistActions from '@/hooks/useWishlistActions';
import {PurchaseRenderingDetails} from '@/types/purchases';
import {handleError} from '@/utils/axios';

const usePurchaseOrderDocumentPreview = (id?: string) => {
  const t = useTranslations();
  const {processOrder} = useWishlistActions();
  const {purchaseOrder} = usePurchaseOrder(id);
  const [renderingDetails, setRenderingDetails] = useState<PurchaseRenderingDetails>({
    columnsToHide: [],
    language: 'ro',
    title: t('purchase order'),
  });

  useEffect(() => {
    if (purchaseOrder.renderingDetails) {
      setRenderingDetails({
        columnsToHide: purchaseOrder.renderingDetails.columnsToHide,
        language: purchaseOrder.renderingDetails.language,
        title: purchaseOrder.renderingDetails.title,
      });
    }
  }, [purchaseOrder.renderingDetails, t]);

  const {data, error, isLoading, isValidating} = useSWRWithDebounce<string>(
    (purchaseOrder?.items || []).length > 0
      ? ['purchaseDocumentPreview', purchaseOrder?.id, JSON.stringify(renderingDetails)]
      : null,
    () => {
      return axios
        .post('/api/purchases/orders/preview?mediaType=text/html', processOrder({...purchaseOrder, renderingDetails}))
        .then((res) => res.data)
        .catch((error) =>
          handleError(error, t('an error occurred while loading name', {name: t('suffixed.invoice.end')})),
        );
    },
    {revalidateOnFocus: false},
  );

  return {
    error,
    isLoading,
    isValidating,
    purchaseDocumentPreview: data,
    purchaseOrder,
    renderingDetails,
    setRenderingDetails,
  };
};

export default usePurchaseOrderDocumentPreview;
