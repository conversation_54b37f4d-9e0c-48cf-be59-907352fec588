import axios from 'axios';
import {useTranslations} from 'next-intl';

import useSWRWithDebounce from '@/hooks/helpers/useDebouncedSWR';
import {handleError} from '@/utils/axios';

const useManufacturingOrderDocument = (id?: string) => {
  const t = useTranslations();

  const {data, error, isLoading, isValidating} = useSWRWithDebounce<string>(
    ['manufacturingDocument', id],
    () =>
      axios
        .get(`/api/manufacturing/orders/${id}/details?mediaType=application/pdf`)
        .then((res) => res.data)
        .catch((error) =>
          handleError(error, t('an error occurred while loading name', {name: t('suffixed.order.end')})),
        ),
    {revalidateOnFocus: false},
  );

  return {
    error,
    isLoading,
    isValidating,
    manufacturingOrderDocument: data,
  };
};

export default useManufacturingOrderDocument;
