import axios from 'axios';
import {useTranslations} from 'next-intl';
import useSWR from 'swr';

import {handleError} from '@/utils/axios';

const useConsumptionDocument = (id?: string) => {
  const t = useTranslations();

  const {data, error, isLoading, isValidating} = useSWR<string>(
    id ? ['consumptionDocument', id] : null,
    () =>
      axios
        .get(`/api/manufacturing/material-issue-notes/${id}/details?mediaType=text/html`)
        .then((res) => res.data)
        .catch((error) =>
          handleError(error, t('an error occurred while loading name', {name: t('suffixed.consumption.end')})),
        ),
    {revalidateOnFocus: false},
  );

  return {
    consumptionDocument: data,
    error,
    isLoading,
    isValidating,
  };
};

export default useConsumptionDocument;
