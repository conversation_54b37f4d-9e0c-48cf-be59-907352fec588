import {useCallback} from 'react';

import axios from 'axios';
import {useTranslations} from 'next-intl';
import useSWR, {useSWRConfig} from 'swr';

import {toast} from '@/components/ui/Toast';
import {handleError} from '@/utils/axios';

const useInvoiceDocument = (id?: string) => {
  const t = useTranslations();
  const {mutate: globalMutate} = useSWRConfig();

  const {data, error, isLoading, isValidating, mutate} = useSWR<string>(
    id ? ['invoiceDocument', id] : null,
    () =>
      axios
        .get(`/api/invoices/${id}/details?mediaType=text/html`)
        .then((res) => res.data)
        .catch((error) =>
          handleError(error, t('an error occurred while loading name', {name: t('suffixed.invoice.end')})),
        ),
    {revalidateOnFocus: false},
  );

  const sendInvoice = useCallback(
    (email: {body: string; subject: string}) =>
      axios
        .post(`/api/invoices/${id}/send`, email)
        .then((res) => res.data)
        .then(() => {
          toast.success(
            t('name has been sent successfully', {
              name: t('suffixed.invoice.start'),
            }),
          );
          mutate();
          globalMutate((key) => Array.isArray(key) && key[0] === 'invoices');
        })
        .catch((error) =>
          handleError(
            error,
            t('name has failed to send successfully', {
              name: t('suffixed.invoice.start'),
            }),
          ),
        ),
    [globalMutate, id, mutate, t],
  );

  return {
    error,
    invoice: data,
    isLoading,
    isValidating,
    sendInvoice,
  };
};

export default useInvoiceDocument;
