import axios from 'axios';
import {useTranslations} from 'next-intl';

import useSWRWithDebounce from '@/hooks/helpers/useDebouncedSWR';
import useAccompanyingNotesActions from '@/hooks/useAccompanyingNotesActions';
import {AccompanyingNote} from '@/types/global';
import {handleError} from '@/utils/axios';

const useAccompanyingNoteDocumentPreview = (
  accompanyingNote: AccompanyingNote,
  {saleId, serviceId}: {saleId?: string; serviceId?: string} = {},
) => {
  const t = useTranslations();
  const {processValues} = useAccompanyingNotesActions();

  const {data, error, isLoading, isValidating} = useSWRWithDebounce<string>(
    ['accompanyingNoteDocumentPreview', saleId || serviceId, JSON.stringify(accompanyingNote)],
    () =>
      axios
        .post(
          saleId || serviceId
            ? `/api/${saleId ? `sales/orders/${saleId}` : `servicing/orders/${serviceId}`}/preview-goods-accompanying-note?mediaType=text/html`
            : `/api/goods-accompanying-notes/preview?mediaType=text/html`,
          processValues(accompanyingNote),
        )
        .then((res) => res.data)
        .catch((error) =>
          handleError(error, t('an error occurred while loading name', {name: t('suffixed.accompanyingNote.end')})),
        ),
    {revalidateOnFocus: false},
  );

  return {
    accompanyingNoteDocumentPreview: data,
    error,
    isLoading,
    isValidating,
  };
};

export default useAccompanyingNoteDocumentPreview;
