import axios from 'axios';
import {useTranslations} from 'next-intl';
import useSWR from 'swr';

import {Id} from '@/types/global';
import {handleError} from '@/utils/axios';

const useUsers = () => {
  const t = useTranslations();

  const {data, error, isLoading, isValidating} = useSWR(
    ['users'],
    () =>
      axios
        .get('/api/users/list')
        .then((res) => res.data)
        .catch((error) => handleError(error, t('an error occurred while loading name', {name: t('suffixed.users')}))),
    {revalidateOnFocus: false},
  );

  return {
    error,
    isLoading,
    isValidating,
    users: (data || []) as Id[],
  };
};

export default useUsers;
