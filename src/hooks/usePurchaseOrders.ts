import axios from 'axios';
import {useTranslations} from 'next-intl';
import useSWR from 'swr';

import usePurchaseOrderActions from '@/hooks/usePurchaseOrderActions';
import {handleError} from '@/utils/axios';
import {PurchaseStatus} from 'types/global';
import {PurchaseOrder} from 'types/purchases';
import {getQueryString} from 'utils/common';

const useSaleOrders = ({
  search,
  sort,
  statuses = Object.values(PurchaseStatus),
  supplierId = null,
}: {
  search?: string;
  sort?: ('-updateTime' | 'updateTime')[];
  statuses?: PurchaseStatus[];
  supplierId?: null | string;
}) => {
  const t = useTranslations();
  const {prepareData} = usePurchaseOrderActions();

  const {data, error, isLoading, isValidating} = useSWR<PurchaseOrder[]>(
    `purchases-status-${JSON.stringify(statuses)}${supplierId ? `-supplier-${supplierId}` : ''}${sort ? `-sort-${sort.join('-')}` : ''}${search ? `-search-${search}` : ''}`,
    () =>
      axios
        .get(
          `/api/purchases/orders/list?${getQueryString({
            q: search,
            sort: (sort || []).join(','),
            status: statuses.join(','),
            supplier: supplierId,
          })}`,
        )
        .then((res) => res.data.map(prepareData))
        .catch((error) => handleError(error, t('an error occurred while loading name', {name: t('suffixed.orders')}))),
    {revalidateOnFocus: false},
  );

  return {
    error,
    isLoading,
    isValidating,
    purchaseOrders: data || [],
  };
};

export default useSaleOrders;
