import {useCallback, useEffect, useState} from 'react';

import {joiResolver} from '@hookform/resolvers/joi';
import axios from 'axios';
import FormData from 'form-data';
import Joi from 'joi';
import {useTranslations} from 'next-intl';
import {useForm} from 'react-hook-form';
import useSWR, {useSWRConfig} from 'swr';

import {toast} from '@/components/ui/Toast';
import {useRouter} from '@/hooks/helpers/useRouter';
import useSaleOrderActions from '@/hooks/useSaleOrderActions';
import {SaleOrder, SaleType} from '@/types/sales';
import {handleError} from '@/utils/axios';
import {SaleStatus} from 'types/global';

import useLeaveConfirm from './helpers/useLeaveConfirm';

const saleOrderSchema = Joi.object({
  createTime: Joi.string().required(),
  customer: Joi.object({id: Joi.string().required()}).required().unknown(),
  items: Joi.array()
    .min(1)
    .items(
      Joi.object({
        price: Joi.object({amount: Joi.number().positive().required()}).required().unknown(),
      }).unknown(),
    ),
  shippingAddress: Joi.object({address1: Joi.string()}).unknown(),
}).unknown();

const useSaleOrder = ({id, type}: {id?: string; type: SaleType}) => {
  const t = useTranslations();
  const {push, replace} = useRouter();
  const [created, setCreated] = useState(false);
  const [initialStatus, setInitialStatus] = useState<SaleStatus>();
  const {mutate: globalMutate} = useSWRConfig();
  const {
    createSaleOrder: createSaleOrderAction,
    prepareData,
    updateSaleOrder: updateSaleOrderAction,
  } = useSaleOrderActions();

  const {
    control,
    formState: {dirtyFields, errors, ...restUseFormState},
    handleSubmit,
    register,
    reset,
    resetField,
    setValue,
    watch,
    ...restUseForm
  } = useForm<SaleOrder>({
    defaultValues: {
      createTime: new Date().toISOString(),
      goodsAccompanyingNotes: [],
      status: type === SaleType.QUOTE ? SaleStatus.IN_QUOTATION : SaleStatus.SUBMITTED,
    },
    mode: 'onSubmit',
    resolver: joiResolver(saleOrderSchema),
  });

  const isDirty = !!Object.keys(dirtyFields).length;

  useLeaveConfirm(created ? false : isDirty, () => saveSaleOrder());

  useEffect(() => {
    if (watch('id') && created) replace(`/sales/${type}/${watch('id')}`);
  }, [created, replace, type, watch]);

  const {data, isLoading, isValidating, mutate} = useSWR(
    id ? ['sale', id, type] : null,
    () =>
      axios
        .get(`/api/sales/orders/${id}/details`)
        .then((res) => prepareData(res.data))
        .then((values) => {
          setInitialStatus(values?.status);
          reset(values);

          return values;
        })
        .catch((error) =>
          handleError(
            error,
            t('an error occurred while loading name', {
              name: type === SaleType.QUOTE ? t('suffixed.quote.end') : t('suffixed.order.end'),
            }),
          ),
        ),
    {revalidateOnFocus: false},
  );

  useEffect(() => {
    if (data) reset(data as SaleOrder);
  }, [data, reset]);

  useEffect(() => {
    if (
      id &&
      !isDirty &&
      type === SaleType.QUOTE &&
      ![SaleStatus.CANCELED, SaleStatus.IN_QUOTATION, undefined].includes((data as SaleOrder)?.status)
    )
      push(`/sales/orders/${watch('id')}`);
  }, [data, id, isDirty, push, type, watch]);

  const saveSaleOrder = useCallback(
    () =>
      handleSubmit(
        (values) => {
          return id
            ? updateSaleOrderAction(id, type, values, initialStatus).then(() => mutate())
            : createSaleOrderAction(type, values).then((data) => {
                setCreated(true);
                setValue('id', data.id);
              });
        },
        () => {
          toast.warning(t('please fill in all mandatory fields before saving'));
          return Promise.reject();
        },
      )(),
    [createSaleOrderAction, handleSubmit, id, initialStatus, mutate, setValue, t, type, updateSaleOrderAction],
  );

  const uploadSaleOrderFile = useCallback(
    (file?: File) => {
      if (file) {
        const formData = new FormData();

        formData.append('files', file);
        return axios
          .post(`/api/attach-file/sales/${id}`, formData, {
            headers: {'X-Original-Filename': encodeURIComponent(file.name)},
          })
          .then(() => {
            mutate();
          })
          .catch((error) =>
            handleError(
              error,
              t('name has failed to create successfully', {
                created: t('created.male'),
                name: t('suffixed.product.start'),
              }),
            ),
          );
      }
    },
    [id, mutate, t],
  );

  const deleteSaleOrderFile = useCallback(
    (id: string) =>
      axios
        .delete(`/api/sales/orders/${watch('id')}/files/${id}/delete`)
        .then(() => {
          mutate();
        })
        .catch((error) =>
          handleError(
            error,
            t('name has failed to update successfully', {name: watch('number'), updated: t('deleted.male')}),
          ),
        ),
    [mutate, t, watch],
  );

  const cloneSaleOrder = useCallback(() => {
    axios
      .post(`/api/sales/orders/${id}/clone`)
      .then((res) => res.data)
      .then((item: SaleOrder) => {
        globalMutate((key) => Array.isArray(key) && key[0] === 'sales');
        toast.success(
          t('name has been created successfully', {
            created: t('created.female'),
            name: `${t(`suffixed.${type === SaleType.ORDER ? 'order' : 'quote'}.start`)} ${item.number}`,
          }),
        );
        push(`/sales/${type}/${item.id}`);
      })
      .catch((error) =>
        handleError(
          error,
          t('name has failed to create successfully', {
            created: t('created.female'),
            name: t(`suffixed.${type === SaleType.ORDER ? 'order' : 'quote'}.start`),
          }),
        ),
      );
  }, [globalMutate, id, push, t, type]);

  return {
    cloneSaleOrder,
    deleteSaleOrderFile,
    isDirty,
    isLoading: isLoading || (id && !watch('id')),
    isValidating,
    saleOrder: watch() as SaleOrder,
    saveSaleOrder,
    uploadSaleOrderFile,
    useFormActions: {
      control,
      formState: {dirtyFields, errors, ...restUseFormState},
      handleSubmit,
      register,
      reset,
      resetField,
      setValue: ((name, value: any, options = {shouldDirty: true}) =>
        setValue(name, value, options)) as typeof setValue,
      watch,
      ...restUseForm,
    },
  };
};

export default useSaleOrder;
