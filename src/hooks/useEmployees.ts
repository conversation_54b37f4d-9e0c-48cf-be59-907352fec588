import axios from 'axios';
import {useTranslations} from 'next-intl';
import useSWR from 'swr';

import useEmployeeActions from '@/hooks/useEmployeeActions';
import {Employee} from '@/types/manufacturing';
import {handleError} from '@/utils/axios';
import {getQueryString} from 'utils/common';

const useEmployees = ({search, workersOnly}: {search?: string; workersOnly?: boolean} = {}) => {
  const t = useTranslations();
  const {prepareData} = useEmployeeActions();

  const {data, error, isLoading, isValidating} = useSWR(
    ['employees', search, workersOnly],
    () =>
      axios
        .get(`/api/employees/list?${workersOnly ? 'workersOnly=true' : ''}${getQueryString({q: search})}`)
        .then((res) => res.data.map(prepareData))
        .catch((error) =>
          handleError(error, t('an error occurred while loading name', {name: t('suffixed.employees')})),
        ),
    {revalidateOnFocus: false},
  );

  return {
    employees: (data || []) as Employee[],
    error,
    isLoading,
    isValidating,
  };
};

export default useEmployees;
