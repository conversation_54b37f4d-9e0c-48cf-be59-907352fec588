import axios from 'axios';
import {useTranslations} from 'next-intl';
import useSWR from 'swr';

import {handleError} from '@/utils/axios';

type Props = {
  enabled?: boolean;
  inventoryTypeId: string;
};

/**
 * @deprecated
 */
const useInventoryCriticalCount = ({enabled, inventoryTypeId}: Props) => {
  const t = useTranslations();

  const {data, error, isLoading, isValidating} = useSWR<number>(
    enabled ? `inventory-${inventoryTypeId || 'all'}-criticalCount` : null,
    () =>
      axios
        .get(`/api/inventory/list/critical/count?${inventoryTypeId ? `inventoryType=${inventoryTypeId}` : ''}`)
        .then((res) => res.data?.count)
        .catch((error) =>
          handleError(error, t('an error occurred while loading name', {name: t('suffixed.inventories')})),
        ),
    {revalidateOnFocus: false},
  );

  return {
    criticalCount: data || 0,
    error,
    isLoading,
    isValidating,
  };
};

export default useInventoryCriticalCount;
