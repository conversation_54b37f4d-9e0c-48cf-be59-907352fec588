import {useCallback, useEffect, useState} from 'react';

import {joiResolver} from '@hookform/resolvers/joi';
import axios from 'axios';
import Joi from 'joi';
import {useTranslations} from 'next-intl';
import {useForm} from 'react-hook-form';
import useSWR, {useSWRConfig} from 'swr';

import {toast} from '@/components/ui/Toast';
import {useRouter} from '@/hooks/helpers/useRouter';
import useWishlistActions from '@/hooks/useWishlistActions';
import {handleError} from '@/utils/axios';
import useLeaveConfirm from 'hooks/helpers/useLeaveConfirm';
import {Wishlist} from 'types/purchases';

const wishlistSchema = Joi.object({
  id: Joi.string().required(),
  wishlistItems: Joi.array()
    .min(1)
    .items(
      Joi.object({
        materialGood: Joi.object({id: Joi.string().required()}).unknown(),
        price: Joi.object({amount: Joi.number().positive()}).unknown(),
        quantity: Joi.number().positive().required(),
      }).unknown(),
    ),
}).unknown();

const useWishlist = (id?: string) => {
  const t = useTranslations();
  const {push, replace} = useRouter();
  const [created, setCreated] = useState(false);
  const {mutate: globalMutate} = useSWRConfig();
  const {
    createWishlistItem: createWishlistItemAction,
    deleteWishlist: deleteWishlistAction,
    deleteWishlistItem: deleteWishlistItemAction,
    prepareData,
    updateWishlistItem: updateWishlistItemAction,
  } = useWishlistActions();

  const {
    control,
    formState: {dirtyFields, errors, ...restUseFormState},
    handleSubmit,
    register,
    reset,
    resetField,
    setValue,
    watch,
    ...restUseForm
  } = useForm<Wishlist>({
    defaultValues: {
      wishlistItems: [],
    },
    mode: 'onSubmit',
    resolver: joiResolver(wishlistSchema),
  });

  const isDirty = !!Object.keys(dirtyFields).length;

  useLeaveConfirm(created ? false : isDirty, async () => await saveWishlist());

  useEffect(() => {
    if (watch('id') && created) {
      if (id) {
        push(`/purchases/wishlists/${watch('id')}`);
      } else {
        replace(`/purchases/wishlists/${watch('id')}`);
      }
    }
  }, [created, id, push, replace, watch]);

  const {data, isLoading, isValidating, mutate} = useSWR(
    id ? ['wishlist', id] : null,
    () =>
      axios
        .get(`/api/purchases/wishlist/suppliers/${id}/details`)
        .then((res) => prepareData(res.data))
        .then((values) => {
          reset(values);
          return values;
        })
        .catch((error) =>
          handleError(error, t('an error occurred while loading name', {name: t('suffixed.wishlist.end')})),
        ),
    {revalidateOnFocus: false},
  );

  useEffect(() => {
    if (data) reset(data as Wishlist);
  }, [data, reset]);

  const saveWishlist = useCallback(
    () =>
      handleSubmit(
        async (values) => {
          const createOrUpdatePromises =
            values?.wishlistItems?.map((wishlist) => {
              const existingWishlist = (data as Wishlist)?.wishlistItems?.find(
                (existingWishlist) => existingWishlist.id === wishlist?.id,
              );
              if (existingWishlist) {
                if (
                  existingWishlist.quantity !== wishlist?.quantity ||
                  existingWishlist.price?.amount !== wishlist?.price?.amount
                )
                  return updateWishlistItemAction(wishlist);
              } else {
                return createWishlistItemAction(wishlist);
              }
            }) || [];

          const deletePromises =
            (data as Wishlist)?.wishlistItems?.map((existingWishlist) => {
              if (!values?.wishlistItems?.find((wishlist) => wishlist?.id === existingWishlist.id))
                return deleteWishlistItemAction(existingWishlist.id);
            }) || [];

          await Promise.all([...createOrUpdatePromises, ...deletePromises])
            .then(() => {
              toast.success(
                t(`name has been ${(data as Wishlist)?.id ? 'updated' : 'created'} successfully`, {
                  created: t('created.female'),
                  name: `${t('suffixed.wishlist.start')} ${(data as Wishlist)?.name || ''}`,
                  updated: t('updated.female'),
                }),
              );
              mutate();
              globalMutate((key) => Array.isArray(key) && key[0] === 'wishlists');
              if (!id) setCreated(true);
            })
            .catch((error) =>
              handleError(
                error,
                t(`name has failed to ${(data as Wishlist)?.id ? 'update' : 'create'} successfully`, {
                  created: t('created.female'),
                  name: `${t('suffixed.wishlist.start')} ${(data as Wishlist)?.name || ''}`,
                  updated: t('updated.female'),
                }),
              ),
            );
        },
        () => {
          toast.warning(t('please fill in all mandatory fields before saving'));
          return Promise.reject();
        },
      )(),
    [
      createWishlistItemAction,
      data,
      deleteWishlistItemAction,
      globalMutate,
      handleSubmit,
      id,
      mutate,
      t,
      updateWishlistItemAction,
    ],
  );

  const deleteWishlist = useCallback(() => {
    if (id) deleteWishlistAction({id, name: watch('name')}).then(() => replace('/purchases'));
  }, [deleteWishlistAction, id, replace, watch]);

  return {
    deleteWishlist,
    isDirty,
    isLoading: isLoading || (id && !watch('id')),
    isValidating,
    saveWishlist,
    useFormActions: {
      control,
      formState: {dirtyFields, errors, ...restUseFormState},
      handleSubmit,
      register,
      reset,
      resetField,
      setValue: ((name, value: any, options = {shouldDirty: true}) =>
        setValue(name, value, options)) as typeof setValue,
      watch,
      ...restUseForm,
    },
    wishlist: watch() as Wishlist,
  };
};

export default useWishlist;
