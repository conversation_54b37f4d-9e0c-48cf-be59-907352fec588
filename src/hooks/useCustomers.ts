import axios from 'axios';
import {useTranslations} from 'next-intl';
import useSWR from 'swr';

import useCustomerActions from '@/hooks/useCustomerActions';
import {Customer} from '@/types/sales';
import {handleError} from '@/utils/axios';
import {getQueryString} from 'utils/common';

const useCustomers = ({search}: {search?: string} = {}) => {
  const t = useTranslations();
  const {prepareData} = useCustomerActions();

  const {data, error, isLoading, isValidating} = useSWR(
    ['customers', search],
    () =>
      axios
        .get(`/api/customers/list?${getQueryString({q: search})}`)
        .then((res) => res.data.map(prepareData))
        .catch((error) =>
          handleError(error, t('an error occurred while loading name', {name: t('suffixed.customers')})),
        ),
    {revalidateOnFocus: false},
  );

  return {
    customers: (data || []) as Customer[],
    error,
    isLoading,
    isValidating,
  };
};

export default useCustomers;
