import {useCallback} from 'react';

import axios from 'axios';
import {sumBy} from 'lodash';
import {useTranslations} from 'next-intl';

import {handleError} from '@/utils/axios';
import {getQueryString} from '@/utils/common';
import {MaterialGood} from 'types/inventory';

const useItemStockActions = () => {
  const t = useTranslations();

  const getItemStocks = useCallback(
    async (
      id: string,
      options: {id?: string; inventoryUnitIds?: string[]; type?: 'manufacturing' | 'sales'; withoutCommited?: boolean},
    ) => {
      if (options?.id && options?.type) {
        return await axios
          .get(`/api/${options.type}/orders/${options.id}/available-stock/${id}`)
          .then((res) => res.data?.quantity || 0)
          .catch((error) => handleError(error, t('an error occurred while loading name', {name: t('suffixed.items')})));
      }

      const stocks = await axios
        .get(
          `/api/inventory/list?${getQueryString({
            ids: [id],
            inventoryUnitFilter: options?.inventoryUnitIds,
          })}`,
        )
        .then((res) => res.data as MaterialGood[])
        .catch((error) => {
          handleError(error, t('an error occurred while loading name', {name: t('suffixed.items')}));
          return [];
        });

      return sumBy(stocks, (stock) => (options.withoutCommited ? stock.quantity : stock.quantity - stock.committed));
    },
    [t],
  );

  const getItemAvailable = useCallback(
    async (
      id: string,
      quantity: number,
      options: {id?: string; inventoryUnitIds?: string[]; type?: 'manufacturing' | 'sales'; withoutCommited?: boolean},
    ) => {
      const stocks = await getItemStocks(id, options);

      return stocks >= quantity;
    },
    [getItemStocks],
  );

  return {getItemAvailable, getItemStocks};
};

export default useItemStockActions;
