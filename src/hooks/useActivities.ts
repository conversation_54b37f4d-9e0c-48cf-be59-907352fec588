import {useCallback} from 'react';

import axios from 'axios';
import {useTranslations} from 'next-intl';
import useSWR from 'swr';

import {Activity, ActivityType} from '@/types/global';
import {handleError} from '@/utils/axios';

const useActivities = (id: string | undefined, type: ActivityType) => {
  const t = useTranslations();

  const {data, error, isLoading, isValidating, mutate} = useSWR(
    id ? ['activities', id, type] : null,
    () =>
      axios
        .get(`/api/${type}/${id}/notes/list`)
        .then((res) => res.data)
        .catch((error) =>
          handleError(error, t('an error occurred while loading name', {name: t('suffixed.activities')})),
        ),
    {revalidateOnFocus: false},
  );

  const createActivity = useCallback(
    (note: string) => {
      return axios
        .post(`/api/${type}/${id}/notes/add`, {note})
        .then((res) => res.data)
        .then((item) => {
          // toast.success(
          //   t('name has been created successfully', {
          //     created: t('created.female'),
          //     name: t('suffixed.activity.start'),
          //   }),
          // );
          mutate();
          return item;
        })
        .catch((error) =>
          handleError(
            error,
            t('name has failed to create successfully', {
              created: t('created.female'),
              name: t('suffixed.customer.start'),
            }),
          ),
        );
    },
    [id, mutate, t, type],
  );

  return {
    activities: (data || []) as Activity[],
    createActivity,
    error,
    isLoading,
    isValidating,
  };
};

export default useActivities;
