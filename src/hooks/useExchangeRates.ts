import axios from 'axios';
import {useTranslations} from 'next-intl';
import useSWR from 'swr';

import {handleError} from '@/utils/axios';
import {ExchangeRates} from 'types/global';

const useExchangeRates = () => {
  const t = useTranslations();

  const {data, error, isLoading, isValidating} = useSWR(
    ['exchangeRates'],
    () =>
      axios
        .get('/api/configurations/currencies/exchange-rates/from/RON')
        .then((res) => res.data)
        .catch((error) =>
          handleError(error, t('an error occurred while loading name', {name: t('suffixed.exchangeRates')})),
        ),
    {revalidateOnFocus: false},
  );

  return {
    error,
    exchangeRates: (data || {}) as ExchangeRates,
    isLoading,
    isValidating,
  };
};

export default useExchangeRates;
