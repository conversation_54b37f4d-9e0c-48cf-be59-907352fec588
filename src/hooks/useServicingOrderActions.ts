import {useCallback} from 'react';

import {useTranslations} from 'next-intl';
import {useSWRConfig} from 'swr';

import {toast} from '@/components/ui/Toast';
import {servicingActions} from '@/hooks/helpers/actions';
import {useRouter} from '@/hooks/helpers/useRouter';
import {servicingOrderSchema} from '@/hooks/useServicingOrder';
import axios, {handleError} from '@/utils/axios';
import {parseCurrency} from '@/utils/common';
import {DeepPartial, ServicingStatus} from 'types/global';
import {Consumption, OperationStep, ServicingMaterial, ServicingOrder} from 'types/manufacturing';

const useServicingOrderActions = () => {
  const {mutate: globalMutate} = useSWRConfig();
  const {push} = useRouter();
  const t = useTranslations();

  const prepareData = useCallback((values: any) => {
    if (!values) return {};

    return {
      ...values,
      manufacturingOperations:
        values.manufacturingOperations?.map((operation: OperationStep) => ({
          ...operation,
          id: '',
        })) || [],
      materials:
        values.materials?.map((material: any) => ({
          ...material,
          cost: parseCurrency(material.cost, false),
          inventoryCostPerItem: parseCurrency(
            {...material.cost, amount: material.cost.amount / material.required / values.quantity},
            false,
          ),
          wastePercentage: Number((material.wastePercentage * 100).toFixed(2)),
        })) || [],
      service: {
        ...values.service,
        employeeAndWorkstationCosts: parseCurrency(values.service?.employeeAndWorkstationCosts, false),
        manufacturingOverheadCosts: parseCurrency(values.service?.manufacturingOverheadCosts, false),
        materialCosts: parseCurrency(values.service?.materialCosts, false),
      },
      totalCost: parseCurrency(values.totalCost, false),
    } as ServicingOrder;
  }, []);

  const processValues = useCallback(
    (values: DeepPartial<ServicingOrder>) => ({
      ...values,
      assignedTo: values.assignedTo?.id,
      manufacturingOperations:
        values.manufacturingOperations?.map((operation) => ({
          ...operation,
          candidateEmployeeIds: operation?.candidateEmployees?.map((employee) => employee?.id) || [],
          candidateWorkstationIds: operation?.candidateWorkstations?.map((workstation) => workstation?.id) || [],
        })) || [],
      materials: values.materials?.map((material?: DeepPartial<ServicingMaterial>) => ({
        ...material,
        id: material?.materialGoods?.[0]?.id || '',
        quantity: material?.required || 0,
        wastePercentage: (material?.wastePercentage || 0) / 100,
      })),
    }),
    [],
  );

  const createServicingOrder = useCallback(
    (values: DeepPartial<ServicingOrder>, redirect?: boolean) => {
      const newValues = processValues(values);

      return axios
        .post('/api/servicing/orders/create', newValues)
        .then((res) => res.data)
        .then((data) => {
          toast.success(
            t('name has been created successfully', {
              created: t('created.female'),
              name: `${t('suffixed.order.start')} ${data.number}`,
            }),
          );
          globalMutate(
            (key) =>
              Array.isArray(key) &&
              (key[0] === 'services' || key[0] === 'sales' || key[0] === 'items' || key[0] === 'myTasks'),
          );

          if (redirect) push(`/manufacturing/services/${data.id}`);

          return data;
        })
        .catch((error) =>
          handleError(
            error,
            t('name has failed to create successfully', {
              created: t('created.female'),
              name: t('suffixed.order.start'),
            }),
          ),
        );
    },
    [globalMutate, processValues, push, t],
  );

  const updateServicingOrder = useCallback(
    async (id: string, values: ServicingOrder) => {
      const newValues = processValues(values);

      return await axios
        .post(`/api/servicing/orders/${id}/update`, newValues)
        .then((res) => res.data)
        .then((data) => {
          globalMutate(
            (key) =>
              Array.isArray(key) &&
              (key[0] === 'services' ||
                key[0] === 'sales' ||
                key[0] === 'items' ||
                key[0] === 'myTasks' ||
                (key[0] === 'service' && key[1] === id) ||
                (key[0] === 'serviceDocument' && key[1] === id)),
          );
          toast.success(
            t('name has been updated successfully', {
              name: `${t('suffixed.order.start')} ${data.number}`,
              updated: t('updated.female'),
            }),
          );

          return data;
        })
        .catch((error) =>
          handleError(
            error,
            t('name has failed to update successfully', {
              name: `${t('suffixed.order.start')} ${newValues.number}`,
              updated: t('updated.female'),
            }),
          ),
        );
    },
    [globalMutate, processValues, t],
  );

  const deleteServicingOrder = useCallback(
    (id: string, number?: string) =>
      axios
        .delete(`/api/servicing/orders/${id}/delete`)
        .then(() => {
          globalMutate((key) => Array.isArray(key) && (key[0] === 'services' || key[0] === 'myTasks'));
          toast.success(
            t('name has been deleted successfully', {
              deleted: t('deleted.female'),
              name: `${t('suffixed.order.start')} ${number || ''}`,
            }),
          );
        })
        .catch((error) =>
          handleError(
            error,
            t('name has failed to delete successfully', {
              deleted: t('deleted.female'),
              name: `${t('suffixed.order.start')} ${number || ''}`,
            }),
          ),
        ),
    [globalMutate, t],
  );

  const finishServicingOrder = useCallback(
    (id: string, values: Consumption) =>
      axios
        .post(`/api/servicing/orders/${id}/done`, values)
        .then((res) => res.data)
        .then((data) => {
          toast.success(
            t('name has been updated successfully', {
              name: `${t('suffixed.order.start')} ${data.number}`,
              updated: t('updated.female'),
            }),
          );

          globalMutate(
            (key) =>
              Array.isArray(key) &&
              (key[0] === 'services' ||
                key[0] === 'sales' ||
                key[0] === 'items' ||
                key[0] === 'myTasks' ||
                (key[0] === 'service' && key[1] === id) ||
                (key[0] === 'serviceDocument' && key[1] === id)),
          );

          return data;
        })
        .catch((error) =>
          handleError(
            error,
            t('name has failed to update successfully', {
              name: t('suffixed.order.start'),
              updated: t('updated.female'),
            }),
          ),
        ),
    [globalMutate, t],
  );

  const updateServicingOrderStatus = useCallback(
    (
      item: Partial<ServicingOrder>,
      status: ServicingStatus,
      {
        clientRepresentative,
        clientSignature,
        workerSignature,
      }: {
        clientRepresentative?: string;
        clientSignature?: string;
        workerSignature?: string;
      } = {},
    ) => {
      if (servicingActions[status])
        return axios
          .post(`/api/servicing/orders/${item.id}/${servicingActions[status]}`, {
            ...(clientSignature && clientRepresentative && workerSignature
              ? {base64ClientSignature: clientSignature, base64WorkerSignature: workerSignature, clientRepresentative}
              : {}),
          })
          .then(() => {
            toast.success(
              t('name has been updated successfully', {
                name: `${t('suffixed.order.start')} ${item.number}`,
                updated: t('updated.female'),
              }),
            );
            globalMutate(
              (key) =>
                Array.isArray(key) &&
                (key[0] === 'services' || key[0] === 'myTasks' || (key[0] === 'service' && key[1] === item.id)),
            );
          })
          .catch((error) => {
            handleError(
              error,
              t('name has failed to update successfully', {
                name: `${t('suffixed.order.start')} ${item.number}`,
                updated: t('updated.female'),
              }),
            );
          });

      return Promise.resolve();
    },
    [globalMutate, t],
  );

  const getAvailableStatuses = useCallback(
    (
      status: ServicingStatus,
      {withoutNext = false, withoutSelf = false}: {withoutNext?: boolean; withoutSelf?: boolean} = {},
    ) => {
      const statuses = [];

      switch (status) {
        case ServicingStatus.MANUFACTURED:
          if (!withoutSelf) statuses.push(ServicingStatus.MANUFACTURED);
          if (!withoutNext) statuses.push(ServicingStatus.DONE);
          break;
        case ServicingStatus.MANUFACTURING:
          if (!withoutSelf) statuses.push(ServicingStatus.MANUFACTURING);
          if (!withoutNext) statuses.push(ServicingStatus.MANUFACTURED);
          break;
        case ServicingStatus.SUBMITTED:
          if (!withoutSelf) statuses.push(ServicingStatus.SUBMITTED);
          if (!withoutNext) statuses.push(ServicingStatus.MANUFACTURING);
          statuses.push(ServicingStatus.MANUFACTURED);
          break;
        default:
          break;
      }

      return statuses;
    },
    [],
  );

  const validateServicingOrder = useCallback(
    (values: DeepPartial<ServicingOrder>) => {
      const {error} = servicingOrderSchema.validate(values);

      if (error) {
        toast.warning(t('please fill in all mandatory fields before updating'));
        return false;
      }

      return true;
    },
    [t],
  );

  const getOrder = useCallback(
    (id: string | undefined) =>
      axios
        .get(`/api/servicing/orders/${id}/details`)
        .then((res) => prepareData(res.data) as ServicingOrder)
        .catch((error) => {
          handleError(error, t('an error occurred while loading name', {name: t('suffixed.order.end')}));
          return {} as ServicingOrder;
        }),
    [prepareData, t],
  );

  return {
    createServicingOrder,
    deleteServicingOrder,
    finishServicingOrder,
    getAvailableStatuses,
    getOrder,
    prepareData,
    processValues,
    updateServicingOrder,
    updateServicingOrderStatus,
    validateServicingOrder,
  };
};

export default useServicingOrderActions;
