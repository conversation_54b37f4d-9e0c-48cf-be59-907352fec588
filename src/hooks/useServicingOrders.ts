import axios from 'axios';
import {endOfMonth, endOfWeek, format, startOfMonth, startOfWeek} from 'date-fns';
import {useTranslations} from 'next-intl';
import useSWR from 'swr';

import useManufacturingOrderActions from '@/hooks/useManufacturingOrderActions';
import {handleError} from '@/utils/axios';
import {ServicingStatus} from 'types/global';
import {ServicingOrder} from 'types/manufacturing';
import {getQueryString} from 'utils/common';

const useServicingOrders = ({
  date,
  search,
  sort,
  statuses = Object.values(ServicingStatus),
  view,
}: {
  date?: Date;
  search?: string;
  sort?: ('-ranking' | '-updateTime' | 'ranking' | 'updateTime')[];
  statuses?: ServicingStatus[];
  view?: 'day' | 'month' | 'week';
} = {}) => {
  const t = useTranslations();
  const {prepareData} = useManufacturingOrderActions();

  const {data, error, isLoading, isValidating} = useSWR<ServicingOrder[]>(
    ['services', view, date, JSON.stringify(statuses), search, sort],
    () =>
      axios
        .get(
          `/api/servicing/orders/list?${getQueryString({
            day: view === 'day' ? format(date || 0, 'yyyy-MM-dd') : undefined,
            end:
              view === 'week'
                ? format(endOfWeek(date || 0, {weekStartsOn: 1}), 'yyyy-MM-dd')
                : view === 'month'
                  ? format(endOfMonth(date || 0), 'yyyy-MM-dd')
                  : undefined,
            q: search,
            sort: (sort || []).join(','),
            start:
              view === 'week'
                ? format(startOfWeek(date || 0, {weekStartsOn: 1}), 'yyyy-MM-dd')
                : view === 'month'
                  ? format(startOfMonth(date || 0), 'yyyy-MM-dd')
                  : undefined,
            status: statuses.join(','),
          })}`,
        )
        .then((res) => res.data.map(prepareData))
        .catch((error) => {
          handleError(error, t('an error occurred while loading name', {name: t('suffixed.orders')}));
          return [];
        }),
    {revalidateOnFocus: false},
  );

  return {
    error,
    isLoading,
    isValidating,
    servicingOrders: (data || []) as ServicingOrder[],
  };
};

export default useServicingOrders;
