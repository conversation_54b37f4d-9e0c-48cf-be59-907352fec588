import axios from 'axios';
import {useTranslations} from 'next-intl';
import useSWR from 'swr';

import useInventoryUnitActions from '@/hooks/useInventoryUnitActions';
import {InventoryUnit} from '@/types/inventory';
import {handleError} from '@/utils/axios';

const useInventoryUnits = () => {
  const t = useTranslations();
  const {prepareData} = useInventoryUnitActions();

  const {data, error, isLoading, isValidating} = useSWR<InventoryUnit[]>(
    ['inventoryUnits'],
    () =>
      axios
        .get('/api/inventory/units/list')
        .then((res) => res.data.map(prepareData))
        .catch((error) =>
          handleError(error, t('an error occurred while loading name', {name: t('suffixed.inventory units')})),
        ),
    {revalidateOnFocus: false},
  );

  return {
    error,
    inventoryUnits: data || [],
    isLoading,
    isValidating,
  };
};

export default useInventoryUnits;
