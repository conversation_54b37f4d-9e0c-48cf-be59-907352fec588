import {useCallback, useEffect, useState} from 'react';

import {joiResolver} from '@hookform/resolvers/joi';
import axios from 'axios';
import Joi from 'joi';
import {useTranslations} from 'next-intl';
import {useForm} from 'react-hook-form';
import useSWR from 'swr';

import {toast} from '@/components/ui/Toast';
import useLeaveConfirm from '@/hooks/helpers/useLeaveConfirm';
import {useRouter} from '@/hooks/helpers/useRouter';
import useInventoryAdjustmentActions from '@/hooks/useInventoryAdjustmentActions';
import {handleError} from '@/utils/axios';
import {Adjustment} from 'types/inventory';

export const adjustmentSchema = Joi.object({
  inventoryEntries: Joi.array()
    .items(
      Joi.object({
        fromUnit: Joi.alternatives()
          .try(
            Joi.object({
              id: Joi.string().required(),
            }).unknown(),
            null,
          )
          .optional(),
        materialGood: Joi.object({
          id: Joi.string().required(),
        })
          .required()
          .unknown(),
        price: Joi.object({
          amount: Joi.number().positive().required(),
        })
          .required()
          .unknown(),
        quantity: Joi.number().required().not(0),
        unit: Joi.object({
          id: Joi.string().required(),
        })
          .required()
          .unknown(),
      })
        .unknown()
        .custom((value, helpers) => {
          if (value.fromUnit?.id && value.unit.id && value.fromUnit.id === value.unit.id) {
            return helpers.error('any.invalid');
          }
          return value;
        }, 'fromUnit and unit validation'),
    )
    .min(1)
    .required(),
  reason: Joi.string().required(),
}).unknown();

const useInventoryAdjustment = (id?: string) => {
  const t = useTranslations();
  const {replace} = useRouter();
  const {
    createAdjustment: createAdjustmentAction,
    createMove: createMoveAction,
    prepareData,
  } = useInventoryAdjustmentActions();
  const [created, setCreated] = useState(false);
  const {
    control,
    formState: {dirtyFields, errors, ...restUseFormState},
    handleSubmit,
    register,
    reset,
    resetField,
    setValue,
    watch,
    ...restUseForm
  } = useForm<Adjustment>({
    defaultValues: {},
    mode: 'onSubmit',
    resolver: joiResolver(adjustmentSchema),
  });

  const isDirty = !!Object.keys(dirtyFields).length;

  useLeaveConfirm(created ? false : isDirty, async () => await saveAdjustment());

  useEffect(() => {
    if (watch('id') && created) replace('/inventory/adjustments');
  }, [created, replace, watch]);

  const {data, isLoading, isValidating} = useSWR(
    id ? ['adjustment', id] : null,
    () =>
      axios
        .get(`/api/inventory/adjustments/${id}/details`)
        .then((res) => prepareData(res.data))
        .then((values) => {
          reset(values);
          return values;
        })
        .catch((error) =>
          handleError(error, t('an error occurred while loading name', {name: t('suffixed.adjustment.end')})),
        ),
    {revalidateOnFocus: false},
  );

  useEffect(() => {
    if (data) reset(data as Adjustment);
  }, [data, reset]);

  const saveAdjustment = useCallback(
    () =>
      handleSubmit(
        (values) => {
          return createAdjustmentAction(values).then((data) => {
            setCreated(true);
            setValue('id', data.id);
          });
        },
        () => {
          toast.warning(t('please fill in all mandatory fields before saving'));
          return Promise.reject();
        },
      )(),
    [createAdjustmentAction, handleSubmit, setValue, t],
  );

  const saveMove = useCallback(
    () =>
      handleSubmit(
        (values) => {
          return createMoveAction(values).then((data) => {
            setCreated(true);
            setValue('id', data.id);
          });
        },
        () => toast.warning(t('please fill in all mandatory fields before saving')),
      )(),
    [createMoveAction, handleSubmit, setValue, t],
  );

  return {
    adjustment: watch() as Adjustment,
    isDirty,
    isLoading: isLoading || (id && !watch('id')),
    isValidating,
    saveAdjustment,
    saveMove,
    useFormActions: {
      control,
      formState: {dirtyFields, errors, ...restUseFormState},
      handleSubmit,
      register,
      reset,
      resetField,
      setValue: ((name, value: any, options = {shouldDirty: true}) =>
        setValue(name, value, options)) as typeof setValue,
      watch,
      ...restUseForm,
    },
  };
};

export default useInventoryAdjustment;
