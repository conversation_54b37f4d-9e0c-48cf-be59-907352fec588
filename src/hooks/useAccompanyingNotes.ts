import axios from 'axios';
import {format} from 'date-fns';
import {useTranslations} from 'next-intl';
import useSWR from 'swr';

import useAccompanyingNotesActions from '@/hooks/useAccompanyingNotesActions';
import {AccompanyingNote} from '@/types/global';
import {handleError} from '@/utils/axios';
import {getQueryString} from '@/utils/common';

const useAccompanyingNotes = ({
  customerIds,
  delegateIds,
  end,
  start,
}: {
  customerIds?: string[];
  delegateIds?: string[];
  end?: Date;
  start?: Date;
}) => {
  const t = useTranslations();
  const {prepareData} = useAccompanyingNotesActions();

  const {data, error, isLoading, isValidating} = useSWR<AccompanyingNote[]>(
    ['accompanyingNotes', start, end, JSON.stringify(delegateIds), JSON.stringify(customerIds)],
    () =>
      axios
        .get(
          `/api/goods-accompanying-notes/list?${getQueryString({
            customerIds,
            delegateIds,
            end: end ? format(end || 0, 'yyyy-MM-dd') : undefined,
            start: start ? format(start || 0, 'yyyy-MM-dd') : undefined,
          })}`,
        )
        .then((res) => res.data.map(prepareData))
        .catch((error) =>
          handleError(error, t('an error occurred while loading name', {name: t('suffixed.accompanyingNotes')})),
        ),
    {revalidateOnFocus: false},
  );

  return {
    accompanyingNotes: (data || []) as AccompanyingNote[],
    error,
    isLoading,
    isValidating,
  };
};

export default useAccompanyingNotes;
