import {useCallback, useEffect, useState} from 'react';

import {joiResolver} from '@hookform/resolvers/joi';
import axios from 'axios';
import FormData from 'form-data';
import Joi from 'joi';
import {useTranslations} from 'next-intl';
import {useForm} from 'react-hook-form';
import useSWR from 'swr';

import {toast} from '@/components/ui/Toast';
import {useRouter} from '@/hooks/helpers/useRouter';
import useSupplierActions from '@/hooks/useSupplierActions';
import {Supplier} from '@/types/sales';
import {handleError} from '@/utils/axios';

import useLeaveConfirm from './helpers/useLeaveConfirm';

export const supplierSchema = Joi.object({
  addresses: Joi.array()
    .items(
      Joi.object({
        address1: Joi.string().required(),
        city: Joi.string().required(),
      }).unknown(),
    )
    .min(0)
    .allow(null),
  bankAccounts: Joi.array()
    .items(
      Joi.object({
        bank: Joi.string().required(),
        name: Joi.string().required(),
        number: Joi.string().required(),
      }).unknown(),
    )
    .min(0)
    .allow(null),
  contacts: Joi.array()
    .items(
      Joi.object({
        name: Joi.string().required(),
      }).unknown(),
    )
    .min(0)
    .allow(null),
  name: Joi.string().required(),
  taxIdentificationNumber: Joi.string().required(),
}).unknown();

const useSupplier = (id?: string) => {
  const t = useTranslations();
  const {replace} = useRouter();
  const [created, setCreated] = useState(false);
  const {
    createSupplier: createSupplierAction,
    deleteSupplier: deleteSupplierAction,
    prepareData,
    updateSupplier: updateSupplierAction,
  } = useSupplierActions();

  const {
    control,
    formState: {dirtyFields, errors, ...restUseFormState},
    handleSubmit,
    register,
    reset,
    resetField,
    setValue,
    watch,
    ...restUseForm
  } = useForm<Supplier>({
    defaultValues: {
      addresses: [],
      bankAccounts: [],
      contacts: [],
    },
    mode: 'onSubmit',
    resolver: joiResolver(supplierSchema),
  });

  const isDirty = !!Object.keys(dirtyFields).length;

  useLeaveConfirm(created ? false : isDirty, async () => await saveSupplier());

  useEffect(() => {
    if (watch('id') && created) replace(`/suppliers/${watch('id')}`);
  }, [created, replace, watch]);

  const {data, isLoading, isValidating, mutate} = useSWR(
    id ? ['supplier', id] : null,
    () =>
      axios
        .get(`/api/suppliers/${id}/details`)
        .then((res) => prepareData(res.data))
        .then((values) => {
          reset(values);
          return values;
        })
        .catch((error) =>
          handleError(error, t('an error occurred while loading name', {name: t('suffixed.supplier.end')})),
        ),
    {
      revalidateOnFocus: false,
    },
  );

  useEffect(() => {
    if (data) reset(data as Supplier);
  }, [data, reset]);

  const saveSupplier = useCallback(
    () =>
      handleSubmit(
        (values) => {
          return id
            ? updateSupplierAction(id, values).then(() => mutate())
            : createSupplierAction(values).then((data) => {
                setCreated(true);
                setValue('id', data.id);
              });
        },
        () => {
          toast.warning(t('please fill in all mandatory fields before saving'));
          return Promise.reject();
        },
      )(),
    [handleSubmit, id, updateSupplierAction, createSupplierAction, mutate, setValue, t],
  );

  const deleteSupplier = useCallback(() => {
    if (id) return deleteSupplierAction(id, watch('name')).then(() => replace(`/suppliers`));
  }, [deleteSupplierAction, id, replace, watch]);

  const uploadSupplierFile = useCallback(
    (file?: File) => {
      if (file) {
        const formData = new FormData();

        formData.append('files', file);

        return axios
          .post(`/api/attach-file/suppliers/${id}`, formData, {
            headers: {'X-Original-Filename': encodeURIComponent(file.name)},
          })
          .then(() => {
            mutate();
          })
          .catch((error) =>
            handleError(
              error,
              t('name has failed to create successfully', {
                created: t('created.male'),
                name: t('suffixed.item.start'),
              }),
            ),
          );
      }
    },
    [id, mutate, t],
  );

  const deleteSupplierFile = useCallback(
    (id: string) =>
      axios
        .delete(`/api/suppliers/${watch('id')}/files/${id}/delete`)
        .then(() => {
          mutate();
        })
        .catch((error) =>
          handleError(
            error,
            t('name has failed to update successfully', {name: watch('name'), updated: t('deleted.male')}),
          ),
        ),
    [mutate, t, watch],
  );

  return {
    deleteSupplier,
    deleteSupplierFile,
    isDirty,
    isLoading: isLoading || (id && !watch('id')),
    isValidating,
    saveSupplier,
    supplier: watch() as Supplier,
    uploadSupplierFile,
    useFormActions: {
      control,
      formState: {dirtyFields, errors, ...restUseFormState},
      handleSubmit,
      register,
      reset,
      resetField,
      setValue: ((name, value: any, options = {shouldDirty: true}) =>
        setValue(name, value, options)) as typeof setValue,
      watch,
      ...restUseForm,
    },
  };
};

export default useSupplier;
