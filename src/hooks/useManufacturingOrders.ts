import axios from 'axios';
import {endOfMonth, endOfWeek, format, startOfMonth, startOfWeek} from 'date-fns';
import {useTranslations} from 'next-intl';
import useSWR from 'swr';

import useManufacturingOrderActions from '@/hooks/useManufacturingOrderActions';
import {handleError} from '@/utils/axios';
import {ManufacturingStatus} from 'types/global';
import {ManufacturingOrder} from 'types/manufacturing';
import {getQueryString} from 'utils/common';

const useManufacturingOrders = ({
  date,
  search,
  sort,
  statuses = Object.values(ManufacturingStatus),
  view,
}: {
  date?: Date;
  search?: string;
  sort?: ('-ranking' | '-updateTime' | 'ranking' | 'updateTime')[];
  statuses?: ManufacturingStatus[];
  view?: 'day' | 'month' | 'week';
}) => {
  const t = useTranslations();
  const {prepareData} = useManufacturingOrderActions();

  const {data, error, isLoading, isValidating} = useSWR<ManufacturingOrder[]>(
    ['manufacturings', view, date, JSON.stringify(statuses), search, sort],
    () =>
      axios
        .get(
          `/api/manufacturing/orders/list?${getQueryString({
            day: view === 'day' ? format(date || 0, 'yyyy-MM-dd') : undefined,
            end:
              view === 'week'
                ? format(endOfWeek(date || 0, {weekStartsOn: 1}), 'yyyy-MM-dd')
                : view === 'month'
                  ? format(endOfMonth(date || 0), 'yyyy-MM-dd')
                  : undefined,
            q: search,
            sort: (sort || []).join(','),
            start:
              view === 'week'
                ? format(startOfWeek(date || 0, {weekStartsOn: 1}), 'yyyy-MM-dd')
                : view === 'month'
                  ? format(startOfMonth(date || 0), 'yyyy-MM-dd')
                  : undefined,
            status: statuses.join(','),
          })}`,
        )
        .then((res) => res.data.map(prepareData))
        .catch((error) => handleError(error, t('an error occurred while loading name', {name: t('suffixed.orders')}))),
    {revalidateOnFocus: false},
  );

  return {
    error,
    isLoading,
    isValidating,
    manufacturingOrders: (data || []) as ManufacturingOrder[],
  };
};

export default useManufacturingOrders;
