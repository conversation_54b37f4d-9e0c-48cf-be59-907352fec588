import axios from 'axios';
import {useTranslations} from 'next-intl';
import useSWR from 'swr';

import useOperationActions from '@/hooks/useOperationActions';
import {handleError} from '@/utils/axios';
import {Operation} from 'types/manufacturing';

const useOperations = () => {
  const t = useTranslations();
  const {prepareData} = useOperationActions();

  const {data, error, isLoading, isValidating} = useSWR(
    ['operations'],
    () =>
      axios
        .get('/api/manufacturing/operations/templates/list')
        .then((res) => res.data.map(prepareData))
        .catch((error) => handleError(error, t('an error occurred while loading name', {name: t('suffixed.tasks')}))),
    {revalidateOnFocus: false},
  );

  return {
    error,
    isLoading,
    isValidating,
    operations: (data || []) as Operation[],
  };
};

export default useOperations;
