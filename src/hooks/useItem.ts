import {useCallback, useEffect, useState} from 'react';

import {joiResolver} from '@hookform/resolvers/joi';
import axios from 'axios';
import FormData from 'form-data';
import Joi from 'joi';
import {useTranslations} from 'next-intl';
import {useForm} from 'react-hook-form';
import useSWR, {useSWRConfig} from 'swr';

import {toast} from '@/components/ui/Toast';
import {useRouter} from '@/hooks/helpers/useRouter';
import useItemActions from '@/hooks/useItemActions';
import {handleError} from '@/utils/axios';
import {InventoryItem} from 'types/inventory';

import useLeaveConfirm from './helpers/useLeaveConfirm';

const inventoryItemSchema = Joi.object({
  category: Joi.object({
    id: Joi.string().required(),
  }).unknown(),
  code: Joi.string(),
  criticalOnHand: Joi.number().required().allow(0).positive(),
  isSubassembly: Joi.boolean(),
  manufacturingOperations: Joi.array().items(
    Joi.object({
      durationInMinutes: Joi.number().required().positive(),
    }).unknown(),
  ),
  measurementUnit: Joi.object({id: Joi.string().required()}).unknown(),
  name: Joi.string().required(),
  produced: Joi.boolean(),
  sellPrice: Joi.object({
    amount: Joi.number().required().allow(0).positive(),
    currency: Joi.string(),
  }).unknown(),
}).unknown();

const useItem = (id?: string) => {
  const t = useTranslations();
  const {push, replace} = useRouter();
  const {mutate: globalMutate} = useSWRConfig();
  const [created, setCreated] = useState(false);
  const {
    createItem: createItemAction,
    deleteItem: deleteItemAction,
    getItem,
    updateItem: updateItemAction,
  } = useItemActions();

  const {
    control,
    formState: {dirtyFields, errors, ...restUseFormState},
    handleSubmit,
    register,
    reset,
    resetField,
    setValue,
    watch,
    ...restUseForm
  } = useForm<InventoryItem>({
    defaultValues: {
      criticalOnHand: 0,
      produced: false,
      sellPrice: {amount: 0},
    },
    mode: 'onSubmit',
    resolver: joiResolver(inventoryItemSchema),
  });

  const isDirty = !!Object.keys(dirtyFields).length;

  useLeaveConfirm(created ? false : isDirty, async () => await saveItem());

  useEffect(() => {
    if (watch('id') && created) replace(`/inventory/items/${watch('id')}`);
  }, [created, replace, watch]);

  const {data, isLoading, isValidating, mutate} = useSWR(
    id ? ['item', id] : null,
    () =>
      getItem(id).then((values) => {
        reset(values);
        return values;
      }),
    {
      revalidateOnFocus: false,
    },
  );

  useEffect(() => {
    if (data) reset(data as InventoryItem);
  }, [data, reset]);

  const saveItem = useCallback(
    () =>
      handleSubmit(
        (values) => {
          return id
            ? updateItemAction(id, values).then(() => mutate())
            : createItemAction(values).then((data) => {
                setCreated(true);
                setValue('id', data.id);
              });
        },
        () => {
          toast.warning(t('please fill in all mandatory fields before saving'));
          return Promise.reject();
        },
      )(),
    [handleSubmit, id, updateItemAction, createItemAction, mutate, setValue, t],
  );

  const deleteItem = useCallback(() => {
    if (id)
      return deleteItemAction(id, watch('name'), watch('parentId'))
        .then(() => globalMutate((key) => Array.isArray(key) && key[0] === 'items'))
        .then(() => replace('/inventory'));
  }, [deleteItemAction, globalMutate, id, replace, watch]);

  const cloneItem = useCallback(() => {
    axios
      .post(`/api/goods/${id}/clone`)
      .then((res) => res.data)
      .then((item: InventoryItem) => {
        globalMutate((key) => Array.isArray(key) && key[0] === 'items');
        toast.success(
          t('name has been created successfully', {
            created: t('created.male'),
            name: `${t('suffixed.item.start')} ${item.name}`,
          }),
        );
        push(`/inventory/items/${item.id}`);
      })
      .catch((error) =>
        handleError(
          error,
          t('name has failed to create successfully', {
            created: t('created.male'),
            name: t('suffixed.item.start'),
          }),
        ),
      );
  }, [globalMutate, id, push, t]);

  const uploadItemFile = useCallback(
    (file?: File) => {
      if (file) {
        const formData = new FormData();

        formData.append('files', file);
        return axios
          .post(`/api/attach-file/goods/${id}`, formData, {
            headers: {'X-Original-Filename': encodeURIComponent(file.name)},
          })
          .then(() => {
            mutate();
          })
          .catch((error) =>
            handleError(
              error,
              t('name has failed to create successfully', {
                created: t('created.male'),
                name: t('suffixed.item.start'),
              }),
            ),
          );
      }
    },
    [id, mutate, t],
  );

  const deleteItemFile = useCallback(
    (id: string) =>
      axios
        .delete(`/api/goods/${watch('id')}/files/${id}/delete`)
        .then(() => {
          mutate();
        })
        .catch((error) =>
          handleError(
            error,
            t('name has failed to update successfully', {name: watch('name'), updated: t('deleted.male')}),
          ),
        ),
    [mutate, t, watch],
  );

  return {
    cloneItem,
    deleteItem,
    deleteItemFile,
    isDirty,
    isLoading: isLoading || (id && !watch('id')),
    isValidating,
    item: watch() as InventoryItem,
    saveItem,
    uploadItemFile,
    useFormActions: {
      control,
      formState: {dirtyFields, errors, ...restUseFormState},
      handleSubmit,
      register,
      reset,
      resetField,
      setValue: ((name, value: any, options = {shouldDirty: true}) =>
        setValue(name, value, options)) as typeof setValue,
      watch,
      ...restUseForm,
    },
  };
};

export default useItem;
