import {useCallback} from 'react';

import axios from 'axios';
import {endOfDay, format, startOfDay} from 'date-fns';
import {useAtomValue} from 'jotai';
import {useTranslations} from 'next-intl';
import {useSWRConfig} from 'swr';

import {toast} from '@/components/ui/Toast';
import {defaultCurrencyAtom} from '@/store/defaults';
import {Employee, EmployeeActiveTimeOffType} from '@/types/manufacturing';
import {handleError} from '@/utils/axios';
import {parseCurrency} from '@/utils/common';

const useEmployeeActions = () => {
  const {mutate: globalMutate} = useSWRConfig();
  const defaultCurrency = useAtomValue(defaultCurrencyAtom);
  const t = useTranslations();

  const prepareData = useCallback((values: any) => {
    if (!values) return {};

    return {
      ...values,
      hourlyRate: parseCurrency(values.hourlyRate, false),
      monthlyGrossSalary: parseCurrency(values.monthlyGrossSalary, false),
    } as Employee;
  }, []);

  const processValues = useCallback(
    (values: Partial<Employee>) => ({
      ...values,
      hourlyRate: parseCurrency({
        amount: values.hourlyRate?.amount || 0,
        currency: values.hourlyRate?.currency || defaultCurrency,
      }),
      monthlyGrossSalary: parseCurrency({
        amount: values.monthlyGrossSalary?.amount || 0,
        currency: values.monthlyGrossSalary?.currency || defaultCurrency,
      }),
    }),
    [defaultCurrency],
  );

  const deleteEmployee = useCallback(
    (id: string, name?: string) =>
      axios
        .delete(`/api/employees/${id}/delete`)
        .then(() => {
          globalMutate((key) => Array.isArray(key) && key[0] === 'employees');
          toast.success(
            t('name has been deleted successfully', {
              deleted: t('deleted.male'),
              name: `${t('suffixed.employee.start')} ${name}`,
            }),
          );
        })
        .catch((error) =>
          handleError(
            error,
            t('name has failed to delete successfully', {
              deleted: t('deleted.male'),
              name: `${t('suffixed.employee.start')} ${name}`,
            }),
          ),
        ),
    [globalMutate, t],
  );

  const createEmployee = useCallback(
    (values: Partial<Employee>) => {
      const newValues = processValues(values);

      return axios
        .post('/api/employees/create', newValues)
        .then((res) => res.data)
        .then((item) => {
          toast.success(
            t('name has been created successfully', {
              created: t('created.male'),
              name: `${t('suffixed.employee.start')} ${newValues?.name}`,
            }),
          );
          globalMutate((key) => Array.isArray(key) && key[0] === 'employees');

          return item;
        })
        .catch((error) =>
          handleError(
            error,
            t('name has failed to create successfully', {
              created: t('created.male'),
              name: `${t('suffixed.employee.start')} ${newValues?.name}`,
            }),
          ),
        );
    },
    [globalMutate, processValues, t],
  );

  const updateEmployee = useCallback(
    (id: string, values: Employee) => {
      const newValues = processValues(values);

      return axios
        .post(`/api/employees/${id}/update`, newValues)
        .then((res) => res.data)
        .then((data) => {
          globalMutate((key) => Array.isArray(key) && key[0] === 'employees');
          toast.success(
            t('name has been updated successfully', {
              name: `${t('suffixed.employee.start')} ${newValues.name}`,
              updated: t('updated.male'),
            }),
          );
          return data;
        })
        .catch((error) =>
          handleError(
            error,
            t('name has failed to update successfully', {
              name: `${t('suffixed.employee.start')} ${newValues.name}`,
              updated: t('updated.male'),
            }),
          ),
        );
    },
    [globalMutate, processValues, t],
  );

  const addEmployeeLeave = useCallback(
    (employeeId: string, from: Date, to: Date, {servicingOrderId}: {servicingOrderId?: string} = {}) =>
      axios
        .post(`/api/employees/${employeeId}/timeoff/add`, {
          endTime: format(endOfDay(to), "yyyy-MM-dd'T'HH:mm:ss"),
          servicingOrderId,
          startTime: format(startOfDay(from), "yyyy-MM-dd'T'HH:mm:ss"),
          type: servicingOrderId ? EmployeeActiveTimeOffType.BUSINESS : EmployeeActiveTimeOffType.PTO,
        })
        .then(() => {
          globalMutate((key) => Array.isArray(key) && key[0] === 'employee' && key[1] === employeeId);

          toast.success(
            t('name has been created successfully', {
              created: t('created.male'),
              name: t(`suffixed.${servicingOrderId ? 'delegation' : 'leave'}.start`),
            }),
          );
        })
        .catch((error) =>
          handleError(
            error,
            t('name has failed to create successfully', {
              created: t('created.male'),
              name: t(`suffixed.${servicingOrderId ? 'delegation' : 'leave'}.start`),
            }),
          ),
        ),
    [globalMutate, t],
  );

  const deleteEmployeeLeave = useCallback(
    (id: string, employeeId: string, {type}: {type?: EmployeeActiveTimeOffType} = {}) =>
      axios
        .delete(`/api/employees/${employeeId}/timeoff/${id}/delete`)
        .then(() => {
          globalMutate((key) => Array.isArray(key) && key[0] === 'employee' && key[1] === employeeId);

          toast.success(
            t('name has been deleted successfully', {
              deleted: t('deleted.male'),
              name: t(`suffixed.${type === EmployeeActiveTimeOffType.BUSINESS ? 'delegation' : 'leave'}.start`),
            }),
          );
        })
        .catch((error) =>
          handleError(
            error,
            t('name has failed to delete successfully', {
              deleted: t('deleted.male'),
              name: t(`suffixed.${type === EmployeeActiveTimeOffType.BUSINESS ? 'delegation' : 'leave'}.start`),
            }),
          ),
        ),
    [globalMutate, t],
  );

  return {
    addEmployeeLeave,
    createEmployee,
    deleteEmployee,
    deleteEmployeeLeave,
    prepareData,
    processValues,
    updateEmployee,
  };
};

export default useEmployeeActions;
