import {useCallback} from 'react';

import axios from 'axios';
import {useTranslations} from 'next-intl';
import useSWR from 'swr';

import {handleError} from '@/utils/axios';
import {parseCurrency} from '@/utils/common';
import {WishlistLastOrderedGoods} from 'types/purchases';

const useWishlistLastOrdered = (id?: string) => {
  const t = useTranslations();

  const processValues = useCallback(
    (values: Partial<WishlistLastOrderedGoods>) => ({
      ...values,
      price: parseCurrency(values.price, false),
    }),
    [],
  );

  const {data, error, isLoading, isValidating} = useSWR<WishlistLastOrderedGoods[]>(
    id ? ['wishlists', 'last-ordered-goods', id] : null,
    () =>
      axios
        .get(`/api/suppliers/${id}/last-ordered-goods`)
        .then((res) => res.data.map(processValues))
        .catch((error) =>
          handleError(error, t('an error occurred while loading name', {name: t('suffixed.wishlist.end')})),
        ),
    {revalidateOnFocus: false},
  );

  return {
    error,
    isLoading,
    isValidating,
    wishlistLastOrderedGoods: data as WishlistLastOrderedGoods[],
  };
};

export default useWishlistLastOrdered;
