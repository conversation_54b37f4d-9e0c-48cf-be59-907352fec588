import {useCallback} from 'react';

import axios from 'axios';
import {useTranslations} from 'next-intl';
import {useSWRConfig} from 'swr';

import {toast} from '@/components/ui/Toast';
import {Supplier} from '@/types/sales';
import {handleError} from '@/utils/axios';

const useSupplierActions = () => {
  const {mutate: globalMutate} = useSWRConfig();
  const t = useTranslations();

  const prepareData = useCallback((values: any) => {
    if (!values) return {};
    return values as Supplier;
  }, []);

  const processValues = useCallback((values: Partial<Supplier>) => values, []);

  const deleteSupplier = useCallback(
    (id: string, name?: string) =>
      axios
        .delete(`/api/suppliers/${id}/delete`)
        .then(() => {
          globalMutate((key) => Array.isArray(key) && key[0] === 'suppliers');
          toast.success(
            t('name has been deleted successfully', {
              deleted: t('deleted.male'),
              name: `${t('suffixed.supplier.start')} ${name}`,
            }),
          );
        })
        .catch((error) =>
          handleError(
            error,
            t('name has failed to delete successfully', {
              deleted: t('deleted.male'),
              name: `${t('suffixed.supplier.start')} ${name}`,
            }),
          ),
        ),
    [globalMutate, t],
  );

  const createSupplier = useCallback(
    (values: Partial<Supplier>) => {
      const newValues = processValues(values);

      return axios
        .post('/api/suppliers/create', values)
        .then((res) => res.data)
        .then((item) => {
          toast.success(
            t('name has been created successfully', {
              created: t('created.male'),
              name: `${t('suffixed.supplier.start')} ${newValues.name}`,
            }),
          );
          globalMutate((key) => Array.isArray(key) && key[0] === 'suppliers');
          return item;
        })
        .catch((error) =>
          handleError(
            error,
            t('name has failed to create successfully', {
              created: t('created.male'),
              name: `${t('suffixed.supplier.start')} ${newValues.name}`,
            }),
          ),
        );
    },
    [globalMutate, processValues, t],
  );

  const updateSupplier = useCallback(
    (id: string, values: Supplier) => {
      const newValues = processValues(values);

      return axios
        .post(`/api/suppliers/${id}/update`, newValues)
        .then((res) => res.data)
        .then((data) => {
          globalMutate((key) => Array.isArray(key) && key[0] === 'suppliers');
          toast.success(
            t('name has been updated successfully', {
              name: `${t('suffixed.supplier.start')} ${newValues.name}`,
              updated: t('updated.male'),
            }),
          );
          return data;
        })
        .catch((error) =>
          handleError(
            error,
            t('name has failed to update successfully', {
              name: `${t('suffixed.supplier.start')} ${newValues.name}`,
              updated: t('updated.male'),
            }),
          ),
        );
    },
    [globalMutate, processValues, t],
  );

  return {
    createSupplier,
    deleteSupplier,
    prepareData,
    processValues,
    updateSupplier,
  };
};

export default useSupplierActions;
