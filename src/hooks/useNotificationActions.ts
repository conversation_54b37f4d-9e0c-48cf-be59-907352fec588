import axios from 'axios';
import {useSet<PERSON>tom} from 'jotai';
import {useTranslations} from 'next-intl';
import {useSWRConfig} from 'swr';

import {toast} from '@/components/ui/Toast';
import {NotificationMeta, NotificationType} from '@/types/interface';
import {handleError} from '@/utils/axios';
import {selectedNotificationsAtom} from 'store/ui';

const useNotificationActions = () => {
  const setSelectedNotification = useSetAtom(selectedNotificationsAtom);
  const {mutate: globalMutate} = useSWRConfig();
  const t = useTranslations();

  const resolveNotification = (notification: NotificationMeta) =>
    axios
      .post(`/api/notifications/${notification.id}/resolved`)
      .then(() => {
        if (notification.type !== NotificationType.COMMENT) toast.success(t('issue has been resolved'));

        setSelectedNotification(undefined);

        globalMutate((key) => Array.isArray(key) && key[0] === 'notifications');
      })
      .catch((error) => {
        if (notification.type !== NotificationType.COMMENT) handleError(error, t('issue has failed to resolve'));
      });

  const markNotificationRead = (id: string) =>
    axios
      .post(`/api/notifications/${id}/read`)
      .then(() => {
        globalMutate((key) => Array.isArray(key) && key[0] === 'notifications');
      })
      .catch(() => {});

  return {
    markNotificationRead,
    resolveNotification,
  };
};

export default useNotificationActions;
