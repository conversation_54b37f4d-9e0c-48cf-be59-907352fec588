import {useCallback} from 'react';

import axios from 'axios';
import {useTranslations} from 'next-intl';
import {useSWRConfig} from 'swr';

import {toast} from '@/components/ui/Toast';
import {Customer} from '@/types/sales';
import {handleError} from '@/utils/axios';
import {parseCurrency} from '@/utils/common';

const useCustomerActions = () => {
  const {mutate: globalMutate} = useSWRConfig();
  const t = useTranslations();

  const prepareData = useCallback((values: any) => {
    if (!values) return {};
    return {...values, lifetimeValue: parseCurrency(values.lifetimeValue, false)} as Customer;
  }, []);

  const processValues = useCallback((values: Customer) => values, []);

  const deleteCustomer = useCallback(
    (id: string, name?: string) =>
      axios
        .delete(`/api/customers/${id}/delete`)
        .then(() => {
          globalMutate((key) => Array.isArray(key) && key[0] === 'customers');
          toast.success(
            t('name has been deleted successfully', {
              deleted: t('deleted.male'),
              name: `${t('suffixed.customer.start')} ${name}`,
            }),
          );
        })
        .catch((error) =>
          handleError(
            error,
            t('name has failed to delete successfully', {
              deleted: t('deleted.male'),
              name: `${t('suffixed.customer.start')} ${name}`,
            }),
          ),
        ),
    [globalMutate, t],
  );

  const createCustomer = useCallback(
    (values: Customer) => {
      const newValues = processValues(values);

      return axios
        .post('/api/customers/create', newValues)
        .then((res) => res.data)
        .then((item) => {
          toast.success(
            t('name has been created successfully', {
              created: t('created.male'),
              name: `${t('suffixed.customer.start')} ${newValues.name}`,
            }),
          );
          globalMutate((key) => Array.isArray(key) && key[0] === 'customers');
          return item;
        })
        .catch((error) =>
          handleError(
            error,
            t('name has failed to create successfully', {
              created: t('created.male'),
              name: `${t('suffixed.customer.start')} ${newValues.name}`,
            }),
          ),
        );
    },
    [globalMutate, processValues, t],
  );

  const updateCustomer = useCallback(
    (id: string, values: Customer) => {
      const newValues = processValues(values);

      return axios
        .post(`/api/customers/${id}/update`, newValues)
        .then((res) => res.data)
        .then((data) => {
          globalMutate((key) => Array.isArray(key) && key[0] === 'customers');
          toast.success(
            t('name has been updated successfully', {
              name: `${t('suffixed.customer.start')} ${newValues.name}`,
              updated: t('updated.male'),
            }),
          );
          return data;
        })
        .catch((error) =>
          handleError(
            error,
            t('name has failed to update successfully', {
              name: `${t('suffixed.customer.start')} ${newValues.name}`,
              updated: t('updated.male'),
            }),
          ),
        );
    },
    [globalMutate, processValues, t],
  );

  return {
    createCustomer,
    deleteCustomer,
    prepareData,
    processValues,
    updateCustomer,
  };
};

export default useCustomerActions;
