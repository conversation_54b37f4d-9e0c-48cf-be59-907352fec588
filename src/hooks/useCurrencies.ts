import axios from 'axios';
import {orderBy} from 'lodash';
import {useTranslations} from 'next-intl';
import useSWR from 'swr';

import {handleError} from '@/utils/axios';
import {ConfigCurrency} from 'types/global';

const useCurrencies = () => {
  const t = useTranslations();

  const {data, error, isLoading, isValidating} = useSWR(
    ['currencies'],
    () =>
      axios
        .get('/api/configurations/currencies')
        .then((res) => res.data)
        .then((data: {[x: string]: {displayName: string}}) =>
          Object.entries(data).map(([symbol, currency]) => ({
            displayName: currency.displayName,
            symbol,
          })),
        )
        .then((currencies) => orderBy(currencies, 'displayName'))
        .catch((error) =>
          handleError(error, t('an error occurred while loading name', {name: t('suffixed.currencies')})),
        ),
    {revalidateOnFocus: false},
  );

  return {
    currencies: (data || []) as ConfigCurrency[],
    error,
    isLoading,
    isValidating,
  };
};

export default useCurrencies;
