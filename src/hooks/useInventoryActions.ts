import {useCallback} from 'react';

import {parseCurrency} from '@/utils/common';
import {InventoryMaterialGood} from 'types/inventory';

const useInventoryActions = () => {
  const prepareData = useCallback((values: any) => {
    if (!values) return {};
    return {
      ...values,
      cost: parseCurrency(values.cost, false),
      profit: parseCurrency(values.profit, false),
      sellingPrice: parseCurrency(values.sellingPrice, false),
      stockValue: parseCurrency(values.stockValue, false),
    } as InventoryMaterialGood;
  }, []);

  return {
    prepareData,
  };
};

export default useInventoryActions;
