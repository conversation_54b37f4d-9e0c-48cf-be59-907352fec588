import axios from 'axios';
import {useTranslations} from 'next-intl';
import useSWR from 'swr';

import {handleError} from '@/utils/axios';
import {MyTask} from 'types/manufacturing';

const useMyTasks = () => {
  const t = useTranslations();

  const {data, error, isLoading, isValidating} = useSWR<MyTask[]>(
    ['myTasks'],
    () =>
      axios
        .get('/api/users/me/tasks/list')
        .then((res) => res.data)
        .catch((error) => handleError(error, t('an error occurred while loading name', {name: t('suffixed.myTasks')}))),
    {revalidateOnFocus: false},
  );

  return {
    error,
    isLoading,
    isValidating,
    myTasks: data || [],
  };
};

export default useMyTasks;
