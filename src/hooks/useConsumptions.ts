import axios from 'axios';
import {format} from 'date-fns';
import Jo<PERSON> from 'joi';
import {useTranslations} from 'next-intl';
import useSWR from 'swr';

import useConsumptionActions from '@/hooks/useConsumptionActions';
import {Consumption} from '@/types/inventory';
import {handleError} from '@/utils/axios';
import {getQueryString} from 'utils/common';

export const consumptionSchema = Joi.object({
  date: Joi.string().required(),
  materials: Joi.array()
    .items(
      Joi.object({
        id: Joi.string().required(),
        quantity: Joi.number().positive().required(),
      }).unknown(),
    )
    .min(1),
}).unknown();

const useConsumptions = ({end, start}: {end?: Date; start?: Date}) => {
  const t = useTranslations();
  const {prepareData} = useConsumptionActions();

  const {data, error, isLoading, isValidating} = useSWR<Consumption[]>(
    ['consumptions', start, end],
    () =>
      axios
        .get(
          `/api/manufacturing/material-issue-notes/list?${getQueryString({
            end: end ? format(end || 0, 'yyyy-MM-dd') : undefined,
            start: start ? format(start || 0, 'yyyy-MM-dd') : undefined,
          })}`,
        )
        .then((res) => res.data.map(prepareData))
        .catch((error) =>
          handleError(
            error,
            t('an error occurred while loading name', {
              name: t('suffixed.consumptions'),
            }),
          ),
        ),
    {revalidateOnFocus: false},
  );

  return {
    consumptions: data || [],
    error,
    isLoading,
    isValidating,
  };
};

export default useConsumptions;
