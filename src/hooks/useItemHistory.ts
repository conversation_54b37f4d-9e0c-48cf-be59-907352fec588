import axios from 'axios';
import {format} from 'date-fns';
import {useTranslations} from 'next-intl';
import useSWR from 'swr';

import useItemHistoryActions from '@/hooks/useItemHistoryActions';
import {DateRange} from '@/types/global';
import {InventoryItemHistory, StockHistoryEntryType} from '@/types/inventory';
import {handleError} from '@/utils/axios';
import {getQueryString} from '@/utils/common';

const useItemHistory = (
  id?: string,
  {dateRange, entryType}: {dateRange?: DateRange; entryType?: StockHistoryEntryType} = {},
) => {
  const t = useTranslations();
  const {prepareData} = useItemHistoryActions();

  const {data, error, isLoading, isValidating} = useSWR(
    id ? ['itemHistory', id, JSON.stringify(dateRange), entryType] : null,
    () =>
      axios
        .get(
          `/api/inventory/${id}/history?${getQueryString({
            end: dateRange?.to ? format(dateRange?.to || 0, 'yyyy-MM-dd') : undefined,
            start: dateRange?.from ? format(dateRange?.from || 0, 'yyyy-MM-dd') : undefined,
            stockHistoryEntryType: entryType,
          })}`,
        )
        .then((res) => prepareData(res.data))
        .catch((error) =>
          handleError(error, t('an error occurred while loading name', {name: t('suffixed.item history.end')})),
        ),
    {
      revalidateOnFocus: false,
    },
  );

  return {
    error,
    isLoading,
    isValidating,
    itemHistory: data as InventoryItemHistory,
  };
};

export default useItemHistory;
