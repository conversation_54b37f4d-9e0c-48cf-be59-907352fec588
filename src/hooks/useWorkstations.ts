import axios from 'axios';
import {useTranslations} from 'next-intl';
import useSWR from 'swr';

import useWorkstationActions from '@/hooks/useWorkstationActions';
import {handleError} from '@/utils/axios';
import {Workstation} from 'types/manufacturing';

const useWorkstations = () => {
  const t = useTranslations();
  const {prepareData} = useWorkstationActions();

  const {data, error, isLoading, isValidating} = useSWR(
    ['workstations'],
    () =>
      axios
        .get('/api/manufacturing/workstations/list')
        .then((res) => res.data.map(prepareData))
        .catch((error) =>
          handleError(error, t('an error occurred while loading name', {name: t('suffixed.workstations')})),
        ),
    {revalidateOnFocus: false},
  );

  return {
    error,
    isLoading,
    isValidating,
    workstations: (data || []) as Workstation[],
  };
};

export default useWorkstations;
