import axios from 'axios';
import {endOfMonth, endOfWeek, format, startOfMonth, startOfWeek} from 'date-fns';
import {useTranslations} from 'next-intl';
import useSWR from 'swr';

import {handleError} from '@/utils/axios';
import {ManufacturingTaskStatus} from 'types/global';
import {Task} from 'types/manufacturing';

const useTasksTest = (
  date: Date | undefined,
  {
    statuses = Object.values(ManufacturingTaskStatus),
    view = 'day',
  }: {
    statuses?: ManufacturingTaskStatus[];
    view?: 'day' | 'month' | 'week';
  } = {},
) => {
  const t = useTranslations();

  const {data, error, isLoading, isValidating} = useSWR<Task[]>(
    date ? ['tasks', view, date, JSON.stringify(statuses)] : null,
    () =>
      axios
        .get(
          `/api/manufacturing/tasks/timefold/list?status=${statuses.join(',')}&${
            view === 'day' ? `day=${format(date || 0, 'yyyy-MM-dd')}` : ''
          }${
            view === 'week'
              ? `start=${format(startOfWeek(date || 0, {weekStartsOn: 1}), 'yyyy-MM-dd')}&end=${format(
                  endOfWeek(date || 0, {weekStartsOn: 1}),
                  'yyyy-MM-dd',
                )}`
              : ''
          }${
            view === 'month'
              ? `start=${format(startOfMonth(date || 0), 'yyyy-MM-dd')}&end=${format(
                  endOfMonth(date || 0),
                  'yyyy-MM-dd',
                )}`
              : ''
          }`,
        )
        .then((res) => res.data)
        .catch((error) => handleError(error, t('an error occurred while loading name', {name: t('suffixed.tasks')}))),
    {revalidateOnFocus: false},
  );

  return {
    error,
    isLoading,
    isValidating,
    tasks: data || [],
  };
};

export default useTasksTest;
