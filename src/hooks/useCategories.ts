import axios from 'axios';
import {useTranslations} from 'next-intl';
import useSWR from 'swr';

import useCategoryActions from '@/hooks/useCategoryActions';
import {handleError} from '@/utils/axios';
import {Category} from 'types/account';

const useCategories = () => {
  const t = useTranslations();
  const {prepareData} = useCategoryActions();

  const {data, error, isLoading, isValidating} = useSWR(
    ['categories'],
    () =>
      axios
        .get('/api/categories/list')
        .then((res) => res.data.map(prepareData))
        .catch((error) =>
          handleError(error, t('an error occurred while loading name', {name: t('suffixed.categories')})),
        ),
    {revalidateOnFocus: false},
  );

  return {
    categories: (data || []) as Category[],
    error,
    isLoading,
    isValidating,
  };
};

export default useCategories;
