import {useCallback} from 'react';

import {parseCurrency} from '@/utils/common';
import {InventoryStats} from 'types/inventory';

const useInventoryStatsActions = () => {
  const prepareData = useCallback((values: any) => {
    if (!values) return {};
    return {
      ...values,
      potentialProfit: parseCurrency(values.potentialProfit, false),
      potentialRevenue: parseCurrency(values.potentialRevenue, false),
      value: parseCurrency(values.value, false),
    } as InventoryStats;
  }, []);

  return {
    prepareData,
  };
};

export default useInventoryStatsActions;
