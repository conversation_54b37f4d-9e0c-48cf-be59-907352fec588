import {useCallback} from 'react';

import axios from 'axios';
import {useTranslations} from 'next-intl';
import {useSWRConfig} from 'swr';

import {toast} from '@/components/ui/Toast';
import {Category} from '@/types/account';
import {handleError} from '@/utils/axios';

const useCategoryActions = () => {
  const {mutate: globalMutate} = useSWRConfig();
  const t = useTranslations();

  const prepareData = useCallback((values: any) => {
    if (!values) return {};
    return values as Category;
  }, []);

  const processValues = useCallback((values: Partial<Category>) => values, []);

  const createCategory = useCallback(
    (name: string) => {
      const newValues = processValues({details: {name}});

      return axios
        .post('/api/categories/create', {name: newValues.details?.name})
        .then((res) => res.data)
        .then((item) => {
          globalMutate((key) => Array.isArray(key) && key[0] === 'categories');
          toast.success(
            t('name has been created successfully', {
              created: t('created.female'),
              name: `${t('suffixed.category')} ${name}`,
            }),
          );

          return item;
        })
        .catch((error) =>
          handleError(
            error,
            t('name has failed to create successfully', {created: t('created.female'), name: t('suffixed.category')}),
          ),
        );
    },
    [globalMutate, processValues, t],
  );

  const updateCategory = useCallback(
    (id: string, name: string) => {
      const newValues = processValues({details: {name}});

      return axios
        .post(`/api/categories/${id}/update`, {name: newValues.details?.name})
        .then(() => {
          globalMutate((key) => Array.isArray(key) && key[0] === 'categories');
          toast.success(
            t('name has been updated successfully', {
              name: `${t('suffixed.category')} ${name}`,
              updated: t('updated.female'),
            }),
          );
        })
        .catch((error) =>
          handleError(
            error,
            t('name has failed to update successfully', {
              name: `${t('suffixed.category')} ${name}`,
              updated: t('updated.female'),
            }),
          ),
        );
    },
    [globalMutate, processValues, t],
  );

  const deleteCategory = useCallback(
    (id: string, name: string) =>
      axios
        .delete(`/api/categories/${id}/delete`)
        .then(() => {
          globalMutate((key) => Array.isArray(key) && key[0] === 'categories');
          toast.success(
            t('name has been deleted successfully', {
              deleted: t('deleted.female'),
              name: `${t('suffixed.category')} ${name}`,
            }),
          );
        })
        .catch((error) =>
          handleError(
            error,
            t('name has failed to delete successfully', {
              deleted: t('deleted.female'),
              name: `${t('suffixed.category')} ${name}`,
            }),
          ),
        ),
    [globalMutate, t],
  );

  return {
    createCategory,
    deleteCategory,
    prepareData,
    processValues,
    updateCategory,
  };
};

export default useCategoryActions;
