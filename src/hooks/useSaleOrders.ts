import axios from 'axios';
import {useTranslations} from 'next-intl';
import useSWR from 'swr';

import useSaleOrderActions from '@/hooks/useSaleOrderActions';
import {SaleOrder} from '@/types/sales';
import {handleError} from '@/utils/axios';
import {SaleStatus} from 'types/global';
import {getQueryString} from 'utils/common';

const useSaleOrders = ({
  customerId = null,
  search,
  sort,
  statuses = Object.values(SaleStatus),
}: {
  customerId?: null | string;
  search?: string;
  sort?: ('-ranking' | '-updateTime' | 'ranking' | 'updateTime')[];
  statuses?: SaleStatus[];
}) => {
  const t = useTranslations();
  const {prepareData} = useSaleOrderActions();

  const {data, error, isLoading, isValidating} = useSWR(
    ['sales', JSON.stringify(statuses), customerId, search, sort],
    () =>
      axios
        .get(
          `/api/sales/orders/list?${getQueryString({
            customer: customerId,
            q: search,
            sort: (sort || []).join(','),
            status: statuses.join(','),
          })}`,
        )
        .then((res) => res.data.map(prepareData))
        .catch((error) => handleError(error, t('an error occurred while loading name', {name: t('suffixed.orders')}))),
    {revalidateOnFocus: false},
  );

  return {
    error,
    isLoading,
    isValidating,
    saleOrders: (data || []) as SaleOrder[],
  };
};
export default useSaleOrders;
