import axios from 'axios';
import {useTranslations} from 'next-intl';
import useSWRInfinite from 'swr/infinite';

import useInventoryActions from '@/hooks/useInventoryActions';
import {InventoryMaterialGood} from '@/types/inventory';
import {handleError} from '@/utils/axios';
import {getQueryString} from 'utils/common';

export const INVENTORY_PAGE_SIZE = 50;

const useInventory = ({
  categoryIds,
  critical,
  inventoryUnitIds,
  onlyOnStockItems,
  search,
}: {
  categoryIds?: string[];
  critical?: boolean;
  inventoryUnitIds?: string[];
  onlyOnStockItems?: boolean;
  search?: string;
} = {}) => {
  const t = useTranslations();
  const {prepareData} = useInventoryActions();

  const {data, error, isLoading, isValidating, mutate, setSize, size} = useSWRInfinite(
    (pageIndex, previousPageData) => {
      if (previousPageData && previousPageData.length != INVENTORY_PAGE_SIZE) return null;

      return `/api/inventory/list${critical ? '/critical' : ''}?${getQueryString({
        categoryIds,
        inventoryUnitFilter: inventoryUnitIds,
        limit: INVENTORY_PAGE_SIZE,
        offset: INVENTORY_PAGE_SIZE * pageIndex,
        onlyOnStockItems,
        q: search,
      })}`;
    },
    (url) =>
      axios
        .get(url)
        .then((res) => res.data)
        .then((data) => data.map(prepareData))
        .catch((error) =>
          handleError(error, t('an error occurred while loading name', {name: t('suffixed.inventories')})),
        ),
    {
      // initialSize: 2,
      parallel: true,
      revalidateAll: false,
      revalidateFirstPage: false,
    },
  );

  const loadMore = () => {
    if (!isValidating && (data ? data[data.length - 1]?.length : 0) === INVENTORY_PAGE_SIZE) setSize(size + 1);
  };

  return {
    error,
    inventory: (data ? [].concat(...data) : []) as InventoryMaterialGood[],
    isLoading,
    isValidating,
    loadMore,
    mutate,
  };
};

export default useInventory;
