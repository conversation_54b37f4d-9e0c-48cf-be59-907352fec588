import {useCallback} from 'react';

import axios from 'axios';
import {useTranslations} from 'next-intl';
import useSWR from 'swr';

import {toast} from '@/components/ui/Toast';
import {DeepPartial} from '@/types/global';
import {handleError} from '@/utils/axios';
import {AccountInformation} from 'types/account';

const useAccountInformation = () => {
  const t = useTranslations();

  const {data, error, isLoading, isValidating, mutate} = useSWR(
    ['accountInformation'],
    () =>
      axios
        .get('/api/account/information')
        .then((res) => res.data)
        .catch((error) =>
          handleError(error, t('an error occurred while loading name', {name: t('suffixed.settings')})),
        ),
    {revalidateOnFocus: false},
  );

  const updateAccountInformation = useCallback(
    (value: DeepPartial<AccountInformation>) => {
      const newInformation = {
        ...data,
        ...value,
      };

      return axios
        .put('/api/account/information/update', newInformation)
        .then(() => {
          mutate();
          toast.success(t('changes have been successfully applied'));
        })
        .catch((error) => handleError(error, t('the changes have not been applied successfully')));
    },
    [data, mutate, t],
  );

  return {
    accountInformation: data as AccountInformation,
    error,
    isLoading,
    isValidating,
    updateAccountInformation,
  };
};

export default useAccountInformation;
