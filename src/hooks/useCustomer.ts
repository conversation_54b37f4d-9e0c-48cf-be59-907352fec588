import {useCallback, useEffect, useState} from 'react';

import {joiResolver} from '@hookform/resolvers/joi';
import axios from 'axios';
import FormData from 'form-data';
import Joi from 'joi';
import {useTranslations} from 'next-intl';
import {useForm} from 'react-hook-form';
import useSWR from 'swr';

import {toast} from '@/components/ui/Toast';
import {useRouter} from '@/hooks/helpers/useRouter';
import useCustomerActions from '@/hooks/useCustomerActions';
import {Customer} from '@/types/sales';
import {handleError} from '@/utils/axios';

import useLeaveConfirm from './helpers/useLeaveConfirm';

export const customerSchema = Joi.object({
  addresses: Joi.array()
    .items(
      Joi.object({
        address1: Joi.string().required(),
        city: Joi.string().required(),
      }).unknown(),
    )
    .min(0)
    .allow(null),
  bankAccounts: Joi.array()
    .items(
      Joi.object({
        bank: Joi.string().required(),
        name: Joi.string().required(),
        number: Joi.string().required(),
      }).unknown(),
    )
    .min(0)
    .allow(null),
  contacts: Joi.array()
    .items(
      Joi.object({
        name: Joi.string().required(),
      }).unknown(),
    )
    .min(0)
    .allow(null),
  name: Joi.string().required(),
  taxIdentificationNumber: Joi.string().required(),
  type: Joi.string().required(),
}).unknown();

const useCustomer = (id?: string) => {
  const t = useTranslations();
  const {replace} = useRouter();
  const [created, setCreated] = useState(false);
  const {
    createCustomer: createCustomerAction,
    deleteCustomer: deleteCustomerAction,
    prepareData,
    updateCustomer: updateCustomerAction,
  } = useCustomerActions();

  const {
    control,
    formState: {dirtyFields, errors, ...restUseFormState},
    handleSubmit,
    register,
    reset,
    resetField,
    setValue,
    watch,
    ...restUseForm
  } = useForm<Customer>({
    defaultValues: {
      addresses: [],
      bankAccounts: [],
      contacts: [],
    },
    mode: 'onSubmit',
    resolver: joiResolver(customerSchema),
  });

  const isDirty = !!Object.keys(dirtyFields).length;

  useLeaveConfirm(created ? false : isDirty, async () => await saveCustomer());

  useEffect(() => {
    if (watch('id') && created) replace(`/customers/${watch('id')}`);
  }, [created, replace, watch]);

  const {data, isLoading, isValidating, mutate} = useSWR(
    id ? ['customer', id] : null,
    () =>
      axios
        .get(`/api/customers/${id}/details`)
        .then((res) => prepareData(res.data))
        .then((values) => {
          reset(values);
          return values;
        })
        .catch((error) =>
          handleError(error, t('an error occurred while loading name', {name: t('suffixed.customer.end')})),
        ),
    {
      revalidateOnFocus: false,
    },
  );

  useEffect(() => {
    if (data) reset(data as Customer);
  }, [data, reset]);

  const saveCustomer = useCallback(
    () =>
      handleSubmit(
        (values) => {
          return id
            ? updateCustomerAction(id, values).then(() => mutate())
            : createCustomerAction(values).then((data) => {
                setCreated(true);
                setValue('id', data.id);
              });
        },
        () => {
          toast.warning(t('please fill in all mandatory fields before saving'));
          return Promise.reject();
        },
      )(),
    [handleSubmit, id, updateCustomerAction, createCustomerAction, mutate, setValue, t],
  );

  const deleteCustomer = useCallback(() => {
    if (id) return deleteCustomerAction(id, watch('name')).then(() => replace(`/customers`));
  }, [deleteCustomerAction, id, replace, watch]);

  const uploadCustomerFile = useCallback(
    (file?: File) => {
      if (file) {
        const formData = new FormData();

        formData.append('files', file);
        return axios
          .post(`/api/attach-file/customers/${id}`, formData, {
            headers: {'X-Original-Filename': encodeURIComponent(file.name)},
          })
          .then(() => {
            mutate();
          })
          .catch((error) =>
            handleError(
              error,
              t('name has failed to create successfully', {
                created: t('created.male'),
                name: t('suffixed.item.start'),
              }),
            ),
          );
      }
    },
    [id, mutate, t],
  );

  const deleteCustomerFile = useCallback(
    (id: string) =>
      axios
        .delete(`/api/customers/${watch('id')}/files/${id}/delete`)
        .then(() => {
          mutate();
        })
        .catch((error) =>
          handleError(
            error,
            t('name has failed to update successfully', {name: watch('name'), updated: t('deleted.male')}),
          ),
        ),
    [mutate, t, watch],
  );

  return {
    customer: watch() as Customer,
    deleteCustomer,
    deleteCustomerFile,
    isDirty,
    isLoading: isLoading || (id && !watch('id')),
    isValidating,
    saveCustomer,
    uploadCustomerFile,
    useFormActions: {
      control,
      formState: {dirtyFields, errors, ...restUseFormState},
      handleSubmit,
      register,
      reset,
      resetField,
      setValue: ((name, value: any, options = {shouldDirty: true}) =>
        setValue(name, value, options)) as typeof setValue,
      watch,
      ...restUseForm,
    },
  };
};

export default useCustomer;
