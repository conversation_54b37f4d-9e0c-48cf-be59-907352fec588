import axios from 'axios';
import {sortBy} from 'lodash';
import {useTranslations} from 'next-intl';
import useSWR, {SWRConfiguration} from 'swr';

import useItemActions from '@/hooks/useItemActions';
import {handleError} from '@/utils/axios';
import {getQueryString} from '@/utils/common';
import {InventoryItem, LightInventoryItem} from 'types/inventory';

type BaseOptions = {
  categoryIds: string[];
  includeVariants: boolean;
  onlyProduced: boolean;
  onlyPurchased: boolean;
};

interface UseItems {
  (options: Partial<BaseOptions> & {full: true}, config?: SWRConfiguration): UseItemsReturn<LightInventoryItem[]>;
  (options?: Partial<BaseOptions>, config?: SWRConfiguration): UseItemsReturn<InventoryItem[]>;
}

type UseItemsReturn<T> = {
  error: any;
  isLoading: boolean;
  isValidating: boolean;
  items: T;
};

const useItems = ((
  {
    categoryIds = [],
    full = false,
    includeVariants = false,
    onlyProduced = false,
    onlyPurchased = false,
  }: Partial<BaseOptions & {full: boolean}> = {},
  {revalidateOnFocus}: SWRConfiguration = {revalidateOnFocus: false},
) => {
  const t = useTranslations();
  const {prepareData, prepareLightData} = useItemActions();

  const {data, error, isLoading, isValidating} = useSWR(
    ['items', onlyPurchased, JSON.stringify(categoryIds), includeVariants, full, onlyProduced],
    async () =>
      axios
        .get(
          `/api/goods/list${full ? '' : '/light'}?${getQueryString({
            categoryIds,
            includeOnlyProduced: onlyProduced,
            includeOnlyPurchased: onlyPurchased,
            includeVariants,
          })}`,
        )
        .then((res) => sortBy(res.data.map(full ? prepareData : prepareLightData), 'name'))
        .catch((error) => handleError(error, t('an error occurred while loading name', {name: t('suffixed.items')}))),
    {revalidateOnFocus},
  );

  return {
    error,
    isLoading,
    isValidating,
    items: data || [],
  };
}) as UseItems;

export default useItems;
