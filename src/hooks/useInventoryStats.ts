import axios from 'axios';
import {useTranslations} from 'next-intl';
import useSWR from 'swr';

import useInventoryStatsActions from '@/hooks/useInventoryStatsActions';
import {InventoryStats} from '@/types/inventory';
import {handleError} from '@/utils/axios';
import {getQueryString} from 'utils/common';

const useInventoryStats = ({
  categoryIds,
  critical,
  inventoryUnitIds,
  search,
}: {
  categoryIds?: string[];
  critical?: boolean;
  inventoryUnitIds?: string[];
  search?: string;
} = {}) => {
  const {prepareData} = useInventoryStatsActions();
  const t = useTranslations();

  const {data, error, isLoading, isValidating} = useSWR(
    ['inventory-stats', search, JSON.stringify(inventoryUnitIds), JSON.stringify(categoryIds), critical],
    () =>
      axios
        .get(
          `/api/inventory/stats?${getQueryString({
            categoryIds,
            critical,
            inventoryUnitFilter: inventoryUnitIds,
            q: search,
          })}`,
        )
        .then((res) => res.data)
        .then((data) => prepareData(data))
        .catch((error) =>
          handleError(error, t('an error occurred while loading name', {name: t('suffixed.inventory stats')})),
        ),
    {
      revalidateOnFocus: false,
    },
  );

  return {
    error,
    inventoryStats: (data || {}) as InventoryStats,
    isLoading,
    isValidating,
  };
};

export default useInventoryStats;
