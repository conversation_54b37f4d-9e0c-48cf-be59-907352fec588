import {useCallback} from 'react';

import {Consumption} from '@/types/inventory';

const useConsumptionActions = () => {
  const prepareData = useCallback((values: any) => {
    if (!values) return {};
    return values as Consumption;
  }, []);

  const processValues = useCallback((values: Consumption) => values, []);

  return {
    prepareData,
    processValues,
  };
};

export default useConsumptionActions;
