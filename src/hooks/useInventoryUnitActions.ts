import {useCallback} from 'react';

import axios from 'axios';
import {useTranslations} from 'next-intl';
import {useSWRConfig} from 'swr';

import {toast} from '@/components/ui/Toast';
import {InventoryUnit} from '@/types/inventory';
import {handleError} from '@/utils/axios';

const useInventoryUnitActions = () => {
  const {mutate: globalMutate} = useSWRConfig();
  const t = useTranslations();

  const prepareData = useCallback((values: any) => {
    if (!values) return {};
    return values as InventoryUnit;
  }, []);

  const processValues = useCallback((values: Partial<InventoryUnit>) => values, []);

  const createInventoryUnit = useCallback(
    (name: string) => {
      const newValues = processValues({name});

      return axios
        .post('/api/inventory/units/create', newValues)
        .then(() => {
          toast.success(
            t('name has been created successfully', {
              created: t('created.male'),
              name: `${t('suffixed.inventory unit.start')} ${name}`,
            }),
          );
          globalMutate((key) => Array.isArray(key) && key[0] === 'inventoryUnits');
        })
        .catch((error) =>
          handleError(
            error,
            t('name has failed to create successfully', {
              created: t('created.male'),
              name: t('suffixed.inventory unit.start'),
            }),
          ),
        );
    },
    [globalMutate, processValues, t],
  );

  const updatedInventoryUnit = useCallback(
    (id: string, name: string) => {
      const newValues = processValues({name});

      return axios
        .post(`/api/inventory/units/${id}/update`, newValues)
        .then(() => {
          toast.success(
            t('name has been updated successfully', {
              name: `${t('suffixed.inventory unit.start')} ${name}`,
              updated: t('updated.male'),
            }),
          );
          globalMutate((key) => Array.isArray(key) && key[0] === 'inventoryUnits');
        })
        .catch((error) =>
          handleError(
            error,
            t('name has failed to update successfully', {
              name: `${t('suffixed.inventory unit.start')} ${name}`,
              updated: t('updated.male'),
            }),
          ),
        );
    },
    [globalMutate, processValues, t],
  );

  const deleteInventoryUnit = useCallback(
    (id: string, name?: string) =>
      axios
        .delete(`/api/inventory/units/${id}/delete`)
        .then(() => {
          toast.success(
            t('name has been deleted successfully', {
              deleted: t('deleted.male'),
              name: `${t('suffixed.inventory unit.start')} ${name}`,
            }),
          );
          globalMutate((key) => Array.isArray(key) && key[0] === 'inventoryUnits');
        })
        .catch((error) =>
          handleError(
            error,
            t('name has failed to delete successfully', {
              deleted: t('deleted.male'),
              name: `${t('suffixed.inventory unit.start')} ${name}`,
            }),
          ),
        ),
    [globalMutate, t],
  );

  return {
    createInventoryUnit,
    deleteInventoryUnit,
    prepareData,
    processValues,
    updatedInventoryUnit,
  };
};

export default useInventoryUnitActions;
