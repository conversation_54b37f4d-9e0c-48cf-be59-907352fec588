import {useCallback, useEffect, useState} from 'react';

import {joiResolver} from '@hookform/resolvers/joi';
import axios from 'axios';
import FormData from 'form-data';
import Joi from 'joi';
import {useTranslations} from 'next-intl';
import {useForm} from 'react-hook-form';
import useSWR, {useSWRConfig} from 'swr';

import {toast} from '@/components/ui/Toast';
import {useRouter} from '@/hooks/helpers/useRouter';
import useServicingOrderActions from '@/hooks/useServicingOrderActions';
import {handleError} from '@/utils/axios';
import {ServicingOrder} from 'types/manufacturing';

import useLeaveConfirm from './helpers/useLeaveConfirm';

export const servicingOrderSchema = Joi.object({
  id: Joi.string().optional(),
  manufacturingOperations: Joi.array()
    .items(
      Joi.object({
        durationInMinutes: Joi.number().positive().required(),
      }).unknown(),
    )
    .min(0),
  quantity: Joi.number().positive().required(),
}).unknown();

const useServicingOrder = (id?: string) => {
  const t = useTranslations();
  const {replace} = useRouter();
  const [created, setCreated] = useState(false);
  const {mutate: globalMutate} = useSWRConfig();
  const {
    createServicingOrder: createServicingOrderAction,
    deleteServicingOrder: deleteServicingOrderAction,
    getOrder,
    updateServicingOrder: updateServicingOrderAction,
  } = useServicingOrderActions();

  const {
    control,
    formState: {dirtyFields, errors, ...restUseFormState},
    handleSubmit,
    register,
    reset,
    resetField,
    setError,
    setValue,
    watch,
    ...restUseForm
  } = useForm<ServicingOrder>({
    defaultValues: {
      materials: [],
      quantity: 1,
    },
    mode: 'onSubmit',
    resolver: joiResolver(servicingOrderSchema),
  });

  const isDirty = !!Object.keys(dirtyFields).length;

  useLeaveConfirm(created ? false : isDirty, async () => await saveServicingOrder());

  useEffect(() => {
    if (watch('id') && created) replace(`/manufacturing/services/${watch('id')}`);
  }, [created, replace, watch]);

  const {data, isLoading, isValidating, mutate} = useSWR(
    id ? ['service', id] : null,
    () =>
      getOrder(id).then((values) => {
        reset(values);
        return values;
      }),
    {revalidateOnFocus: false},
  );

  useEffect(() => {
    if (data) reset(data as ServicingOrder);
  }, [data, reset]);

  const saveServicingOrder = useCallback(
    () =>
      handleSubmit(
        (values) => {
          return id
            ? updateServicingOrderAction(id, values).then((data) => {
                if (data?.id) {
                  mutate();
                  globalMutate((key) => Array.isArray(key) && key[0] === 'serviceDocument' && key[1] === id);
                }
              })
            : createServicingOrderAction(values).then((data) => {
                setCreated(true);
                setValue('id', data.id);
              });
        },
        () => {
          toast.warning(t('please fill in all mandatory fields before saving'));
          return Promise.reject();
        },
      )(),
    [createServicingOrderAction, globalMutate, handleSubmit, id, mutate, setValue, t, updateServicingOrderAction],
  );

  const deleteServicingOrder = useCallback(() => {
    if (id) return deleteServicingOrderAction(id, watch('number')).then(() => replace(`/manufacturing/services`));
  }, [deleteServicingOrderAction, id, replace, watch]);

  const uploadServicingOrderFile = useCallback(
    (file?: File) => {
      if (file) {
        const formData = new FormData();

        formData.append('files', file);
        return axios
          .post(`/api/attach-file/servicing/${id}`, formData, {
            headers: {'X-Original-Filename': encodeURIComponent(file.name)},
          })
          .then(() => {
            mutate();
          })
          .catch((error) =>
            handleError(
              error,
              t('name has failed to create successfully', {
                created: t('created.female'),
                name: t('suffixed.order.start'),
              }),
            ),
          );
      }
    },
    [id, mutate, t],
  );

  const deleteManufacturingOrderFile = useCallback(
    (id: string) =>
      axios
        .delete(`/api/servicing/orders/${watch('id')}/files/${id}/delete`)
        .then(() => {
          mutate();
        })
        .catch((error) =>
          handleError(
            error,
            t('name has failed to update successfully', {name: watch('number'), updated: t('deleted.male')}),
          ),
        ),
    [mutate, t, watch],
  );

  return {
    deleteManufacturingOrderFile,
    deleteServicingOrder,
    isDirty,
    isLoading: isLoading || (id && !watch('id')),
    isValidating,
    saveServicingOrder,
    servicingOrder: watch() as ServicingOrder,
    uploadServicingOrderFile,
    useFormActions: {
      control,
      formState: {dirtyFields, errors, ...restUseFormState},
      handleSubmit,
      register,
      reset,
      resetField,
      setError,
      setValue: ((name, value: any, options = {shouldDirty: true}) =>
        setValue(name, value, options)) as typeof setValue,
      watch,
      ...restUseForm,
    },
  };
};

export default useServicingOrder;
