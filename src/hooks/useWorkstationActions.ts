import {useCallback} from 'react';

import axios from 'axios';
import {useTranslations} from 'next-intl';
import {useSWRConfig} from 'swr';

import {toast} from '@/components/ui/Toast';
import {handleError} from '@/utils/axios';
import {parseCurrency} from '@/utils/common';
import {Workstation} from 'types/manufacturing';

const useWorkstationActions = () => {
  const {mutate: globalMutate} = useSWRConfig();
  const t = useTranslations();

  const prepareData = useCallback((values: any) => {
    if (!values) return {};
    return {...values, costPerHour: parseCurrency(values.costPerHour, false)} as Workstation;
  }, []);

  const processValues = useCallback(
    (values: Partial<Workstation>) => ({
      ...values,
      costPerHour: parseCurrency(values.costPerHour),
      manufacturingOperationTemplates: (values.manufacturingOperationTemplates || []).map((operation) => operation?.id),
    }),
    [],
  );

  const createWorkstation = useCallback(
    (values: Partial<Workstation>) => {
      const newValues = processValues(values);

      return axios
        .post('/api/manufacturing/workstations/create', newValues)
        .then((res) => res.data)
        .then((item) => {
          globalMutate((key) => Array.isArray(key) && key[0] === 'workstations');
          toast.success(
            t('name has been created successfully', {
              created: t('created.male'),
              name: `${t('suffixed.workstation.start')} ${item.name}`,
            }),
          );

          return item;
        })
        .catch((error) =>
          handleError(
            error,
            t('name has failed to create successfully', {
              created: t('created.male'),
              name: t('suffixed.workstation.start'),
            }),
          ),
        );
    },
    [globalMutate, processValues, t],
  );

  const updateWorkstation = useCallback(
    (values: Partial<Workstation>) => {
      const newValues = processValues(values);

      return axios
        .post(`/api/manufacturing/workstations/${values.id}/update`, newValues)
        .then(() => {
          globalMutate((key) => Array.isArray(key) && key[0] === 'workstations');
          toast.success(
            t('name has been updated successfully', {
              name: `${t('suffixed.workstation.start')} ${newValues.name}`,
              updated: t('updated.male'),
            }),
          );
        })
        .catch((error) =>
          handleError(
            error,
            t('name has failed to update successfully', {
              name: `${t('suffixed.workstation.start')} ${newValues.name}`,
              updated: t('updated.male'),
            }),
          ),
        );
    },
    [globalMutate, processValues, t],
  );

  const deleteWorkstation = (id: string, name?: string) =>
    axios
      .delete(`/api/manufacturing/workstations/${id}/delete`)
      .then(() => {
        globalMutate((key) => Array.isArray(key) && key[0] === 'workstations');
        toast.success(
          t('name has been deleted successfully', {
            deleted: t('deleted.male'),
            name: `${t('suffixed.workstation.start')} ${name}`,
          }),
        );
      })
      .catch((error) =>
        handleError(
          error,
          t('name has failed to delete successfully', {
            deleted: t('deleted.male'),
            name: `${t('suffixed.workstation.start')} ${name}`,
          }),
        ),
      );

  return {
    createWorkstation,
    deleteWorkstation,
    prepareData,
    processValues,
    updateWorkstation,
  };
};

export default useWorkstationActions;
