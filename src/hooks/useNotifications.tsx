import {useEffect} from 'react';

import axios from 'axios';
import {MessagePayload} from 'firebase/messaging';
import {useAtom, useSetAtom} from 'jotai';
import {orderBy} from 'lodash';
import {useTranslations} from 'next-intl';
import {toast} from 'sonner';
import useSWR from 'swr';

import {Button} from '@/components/ui/Button';
import {getNotificationMeta} from '@/hooks/helpers/notifications';
import {useRouter} from '@/hooks/helpers/useRouter';
import {notificationsAtom, notificationsConfigAtom, selectedNotificationsAtom} from 'store/ui';
import {Notification, NotificationMeta} from 'types/interface';
import {firebaseCloudMessagingInit} from 'utils/firebase';

let firebaseInitialized = false;

const useNotifications = () => {
  const setNotifications = useSetAtom(notificationsAtom);
  const [notificationsConfig, setNotificationsConfig] = useAtom(notificationsConfigAtom);
  const setSelectedNotification = useSetAtom(selectedNotificationsAtom);
  const t = useTranslations();
  const {push} = useRouter();

  const {mutate} = useSWR(
    ['notifications', notificationsConfig.currentFilter],
    async () => {
      const {data} = await axios.get(
        `/api/notifications/list${
          notificationsConfig.currentFilter !== 'all'
            ? `?resolved=${notificationsConfig.currentFilter === 'resolved'}`
            : ''
        }`,
      );

      const items = orderBy(
        data.notifications
          .map((notification: Notification) => getNotificationMeta(notification, t))
          .filter((notification: NotificationMeta) => !notification.silent),
        'createTime',
        'desc',
      );

      setNotifications(items);

      setNotificationsConfig((cfg) => ({
        ...cfg,
        count: data.unresolved,
        unread: cfg.count < data.unresolved,
      }));
    },
    {revalidateOnFocus: false},
  );

  useEffect(() => {
    if (firebaseInitialized) return;

    firebaseInitialized = true;

    (async () => {
      await firebaseCloudMessagingInit((payload: MessagePayload) => {
        if (!payload?.data) return;

        const liveNotification: any = {
          ...payload.data,
          createTime: new Date().toISOString(),
        };
        const details: Record<string, any> = {};

        Object.keys(liveNotification).forEach((key) => {
          if (key.startsWith('details_')) {
            const newKey = key.replace('details_', '');
            details[newKey] = liveNotification[key];
            delete liveNotification[key];
          }
        });

        if (Object.keys(details).length > 0) liveNotification.details = details;

        const meta = getNotificationMeta(liveNotification, t);

        if (meta.silent) return;

        const options = {
          action: meta.href ? (
            <Button
              onClick={() => {
                push(meta.href || '');
                setSelectedNotification(meta);
              }}
              size='sm'
            >
              {t('view')}
            </Button>
          ) : undefined,
          icon: meta.icon || undefined,
        };

        toast.message(meta.message, options);

        setNotificationsConfig((cfg) => ({
          ...cfg,
          count: cfg.count + 1,
          unread: true,
        }));
      });
    })();
  }, [t, setNotificationsConfig, push, setSelectedNotification]);

  return {refreshNotifications: mutate};
};

export default useNotifications;
