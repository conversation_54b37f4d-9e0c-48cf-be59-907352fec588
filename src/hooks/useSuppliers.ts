import axios from 'axios';
import {useTranslations} from 'next-intl';
import useSWR from 'swr';

import useSupplierActions from '@/hooks/useSupplierActions';
import {Supplier} from '@/types/sales';
import {handleError} from '@/utils/axios';
import {getQueryString} from 'utils/common';

const useSuppliers = ({search}: {search?: string} = {}) => {
  const t = useTranslations();
  const {prepareData} = useSupplierActions();

  const {data, error, isLoading, isValidating} = useSWR(
    ['suppliers', search],
    () =>
      axios
        .get(`/api/suppliers/list?${getQueryString({q: search})}`)
        .then((res) => res.data.map(prepareData))
        .catch((error) =>
          handleError(error, t('an error occurred while loading name', {name: t('suffixed.suppliers')})),
        ),
    {revalidateOnFocus: false},
  );

  return {
    error,
    isLoading,
    isValidating,
    suppliers: (data || []) as Supplier[],
  };
};

export default useSuppliers;
