import {FC} from 'react';

import {IconProps} from 'types/icon';

const ArrowLeftIcon: FC<IconProps> = ({className}) => (
  <svg className={className} fill='none' height='20' viewBox='0 0 20 20' width='20' xmlns='http://www.w3.org/2000/svg'>
    <path
      d='M16.667 10L3.33366 10'
      stroke='currentColor'
      strokeLinecap='round'
      strokeLinejoin='round'
      strokeWidth='1.5'
      vectorEffect='non-scaling-stroke'
    />
    <path
      d='M8.33301 15L3.33301 10L8.33301 5'
      stroke='currentColor'
      strokeLinecap='round'
      strokeLinejoin='round'
      strokeWidth='1.5'
      vectorEffect='non-scaling-stroke'
    />
  </svg>
);

export default ArrowLeftIcon;
