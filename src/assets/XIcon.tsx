import {FC} from 'react';

import {IconProps} from 'types/icon';

const XIcon: FC<IconProps> = ({className}) => (
  <svg
    className={className}
    fill='none'
    stroke='currentColor'
    strokeWidth='1.5'
    viewBox='0 0 24 24'
    xmlns='http://www.w3.org/2000/svg'
  >
    <path d='M6 18L18 6M6 6l12 12' strokeLinecap='round' strokeLinejoin='round' vectorEffect='non-scaling-stroke' />
  </svg>
);

export default XIcon;
