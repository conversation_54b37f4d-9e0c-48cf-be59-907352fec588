import {FC} from 'react';

import {IconProps} from 'types/icon';

const ChevronDoubleLeftIcon: FC<IconProps> = ({className}) => (
  <svg className={className} fill='none' height='16' viewBox='0 0 16 16' width='16' xmlns='http://www.w3.org/2000/svg'>
    <path
      d='M7.33333 11.3332L4 7.99984L7.33333 4.6665'
      stroke='currentColor'
      strokeLinecap='round'
      strokeLinejoin='round'
      strokeWidth='1.5'
    />
    <path
      d='M11.9993 11.3332L8.66602 7.99984L11.9993 4.6665'
      stroke='currentColor'
      strokeLinecap='round'
      strokeLinejoin='round'
      strokeWidth='1.5'
    />
  </svg>
);

export default ChevronDoubleLeftIcon;
