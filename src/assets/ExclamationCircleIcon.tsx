import {FC} from 'react';

import {IconProps} from 'types/icon';

const ExclamationCircleIcon: FC<IconProps> = ({className}) => (
  <svg
    className={className}
    fill='none'
    stroke='currentColor'
    strokeWidth='1.5'
    viewBox='0 0 24 24'
    xmlns='http://www.w3.org/2000/svg'
  >
    <path
      d='M12 9v3.75m9-.75a9 9 0 11-18 0 9 9 0 0118 0zm-9 3.75h.008v.008H12v-.008z'
      strokeLinecap='round'
      strokeLinejoin='round'
      vectorEffect='non-scaling-stroke'
    />
  </svg>
);
export default ExclamationCircleIcon;
