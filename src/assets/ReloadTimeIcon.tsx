import {FC} from 'react';

import {IconProps} from 'types/icon';

const ReloadTimeIcon: FC<IconProps> = ({className}) => (
  <svg className={className} fill='none' height='16' viewBox='0 0 16 16' width='16' xmlns='http://www.w3.org/2000/svg'>
    <path
      d='M1.75 1.75098L2.35353 4.62857L5.13704 4.29655'
      stroke='currentColor'
      strokeLinecap='round'
      strokeLinejoin='round'
      strokeWidth='1.5'
      vectorEffect='non-scaling-stroke'
    />
    <path
      d='M1.33203 8.00065C1.33203 11.6825 4.3168 14.6673 7.9987 14.6673C11.6806 14.6673 14.6654 11.6825 14.6654 8.00065C14.6654 4.31875 11.6806 1.33398 7.9987 1.33398C6.20201 1.33398 4.57132 2.04473 3.37246 3.20039'
      stroke='currentColor'
      strokeLinecap='round'
      strokeLinejoin='round'
      strokeWidth='1.5'
      vectorEffect='non-scaling-stroke'
    />
    <path
      d='M8 4.66699V8.41699L10.5 9.66699'
      stroke='currentColor'
      strokeLinecap='round'
      strokeLinejoin='round'
      strokeWidth='1.5'
      vectorEffect='non-scaling-stroke'
    />
  </svg>
);

export default ReloadTimeIcon;
