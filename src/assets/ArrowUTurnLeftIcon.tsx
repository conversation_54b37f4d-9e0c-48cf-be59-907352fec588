import {FC} from 'react';

import {IconProps} from 'types/icon';

const ArrowUTurnLeftIcon: FC<IconProps> = ({className}) => (
  <svg className={className} fill='none' height='24' viewBox='0 0 24 24' width='24' xmlns='http://www.w3.org/2000/svg'>
    <path
      d='M2 3.99072V9.99072H8'
      stroke='currentColor'
      strokeLinecap='round'
      strokeLinejoin='round'
      vectorEffect='non-scaling-stroke'
    />
    <path
      d='M4.51 14.9907C5.15839 16.831 6.38734 18.4109 8.01166 19.492C9.63598 20.5732 11.5677 21.0973 13.5157 20.9851C15.4637 20.873 17.3226 20.1308 18.8121 18.8704C20.3017 17.61 21.3413 15.8996 21.7742 13.997C22.2072 12.0944 22.0101 10.1026 21.2126 8.32177C20.4152 6.54091 19.0605 5.06747 17.3528 4.12344C15.6451 3.17941 13.6769 2.81593 11.7447 3.08779C9.81245 3.35964 8.02091 4.25209 6.64 5.63067L2 9.99067'
      stroke='currentColor'
      strokeLinecap='round'
      strokeLinejoin='round'
      vectorEffect='non-scaling-stroke'
    />
  </svg>
);
export default ArrowUTurnLeftIcon;
