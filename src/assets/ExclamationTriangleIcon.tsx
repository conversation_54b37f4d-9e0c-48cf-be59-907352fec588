import {FC} from 'react';

import {IconProps} from 'types/icon';

const ExclamationTriangleIcon: FC<IconProps> = ({className}) => (
  <svg className={className} fill='none' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg'>
    <path
      d='M8.52699 2.79773L1.23087 14.5142C1.08044 14.7648 1.00085 15.0489 1.00001 15.3383C0.999164 15.6276 1.0771 15.9121 1.22607 16.1635C1.37504 16.4149 1.58984 16.6244 1.84911 16.7712C2.10838 16.9179 2.40308 16.9968 2.70388 17H17.2961C17.5969 16.9968 17.8916 16.9179 18.1509 16.7712C18.4102 16.6244 18.625 16.4149 18.7739 16.1635C18.9229 15.9121 19.0008 15.6276 19 15.3383C18.9992 15.0489 18.9196 14.7648 18.7691 14.5142L11.473 2.79773C11.3194 2.55421 11.1032 2.35287 10.8452 2.21314C10.5872 2.07341 10.2961 2 10 2C9.7039 2 9.41281 2.07341 9.15479 2.21314C8.89678 2.35287 8.68056 2.55421 8.52699 2.79773Z'
      stroke='currentColor'
      strokeLinecap='round'
      strokeLinejoin='round'
      strokeWidth='1.5'
      vectorEffect='non-scaling-stroke'
    />
    <path
      d='M10 7.05664V10.3711'
      stroke='currentColor'
      strokeLinecap='round'
      strokeLinejoin='round'
      strokeWidth='1.5'
      vectorEffect='non-scaling-stroke'
    />
    <path
      d='M10 13.6875H10.0083'
      stroke='currentColor'
      strokeLinecap='round'
      strokeLinejoin='round'
      strokeWidth='1.5'
      vectorEffect='non-scaling-stroke'
    />
  </svg>
);
export default ExclamationTriangleIcon;
