import {FC} from 'react';

import {IconProps} from 'types/icon';

const ChevronLeftIcon: FC<IconProps> = ({className, ...props}) => (
  <svg
    className={className}
    fill='none'
    stroke='currentColor'
    strokeWidth='1.5'
    viewBox='0 0 24 24'
    xmlns='http://www.w3.org/2000/svg'
    {...props}
  >
    <path
      d='M15.75 19.5L8.25 12l7.5-7.5'
      strokeLinecap='round'
      strokeLinejoin='round'
      vectorEffect='non-scaling-stroke'
    />
  </svg>
);

export default ChevronLeftIcon;
