import {FC} from 'react';

import {IconProps} from 'types/icon';

const Logo: FC<IconProps> = ({className}) => (
  <svg className={className} fill='none' viewBox='0 0 19 19' xmlns='http://www.w3.org/2000/svg'>
    <rect
      height='6.95937'
      rx='1'
      stroke='currentColor'
      strokeWidth='1.5'
      transform='rotate(-45 1.16016 9.36621)'
      vectorEffect='non-scaling-stroke'
      width='12.6139'
      x='1.16016'
      y='9.36621'
    />
    <path
      d='M5.9375 5.22412L14.4674 13.754'
      stroke='currentColor'
      strokeLinecap='round'
      strokeWidth='1.5'
      vectorEffect='non-scaling-stroke'
    />
  </svg>
);

export default Logo;
