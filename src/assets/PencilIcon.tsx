import {FC} from 'react';

import {IconProps} from 'types/icon';

const PencilIcon: FC<IconProps> = ({className}) => (
  <svg className={className} fill='none' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg'>
    <path
      d='M14.3003 2.65325C14.5192 2.43438 14.779 2.26076 15.065 2.14231C15.3509 2.02386 15.6574 1.96289 15.967 1.96289C16.2765 1.96289 16.583 2.02386 16.869 2.14231C17.1549 2.26076 17.4148 2.43438 17.6336 2.65325C17.8525 2.87212 18.0261 3.13195 18.1446 3.41792C18.263 3.70389 18.324 4.01038 18.324 4.31991C18.324 4.62944 18.263 4.93594 18.1446 5.22191C18.0261 5.50787 17.8525 5.76771 17.6336 5.98658L6.38363 17.2366L1.80029 18.4866L3.05029 13.9032L14.3003 2.65325Z'
      stroke='black'
      strokeLinecap='round'
      strokeLinejoin='round'
      strokeWidth='1.5'
      vectorEffect='non-scaling-stroke'
    />
  </svg>
);
export default PencilIcon;
