import {FC} from 'react';

import {IconProps} from 'types/icon';

const MenuChevronDownIcon: FC<IconProps> = ({className}) => (
  <svg className={className} fill='none' height='20' viewBox='0 0 20 20' width='20' xmlns='http://www.w3.org/2000/svg'>
    <path
      d='M10.6547 11.9331C10.2789 12.2585 9.72112 12.2585 9.34535 11.9331L7.69744 10.5059C6.99754 9.89979 7.42622 8.75 8.3521 8.75L11.6479 8.75C12.5738 8.75 13.0025 9.89979 12.3026 10.5059L10.6547 11.9331Z'
      fill='currentColor'
      vectorEffect='non-scaling-stroke'
    />
  </svg>
);
export default MenuChevronDownIcon;
