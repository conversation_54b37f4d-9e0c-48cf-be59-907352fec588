import {FC} from 'react';

import {IconProps} from 'types/icon';

const DocumentTextIcon: FC<IconProps> = ({className}) => (
  <svg className={className} fill='none' height='24' viewBox='0 0 24 24' width='24' xmlns='http://www.w3.org/2000/svg'>
    <path
      d='M13.9912 1.90033H5.99121C5.46078 1.90033 4.95207 2.11104 4.577 2.48612C4.20192 2.86119 3.99121 3.3699 3.99121 3.90033V19.9003C3.99121 20.4308 4.20192 20.9395 4.577 21.3145C4.95207 21.6896 5.46078 21.9003 5.99121 21.9003H17.9912C18.5216 21.9003 19.0304 21.6896 19.4054 21.3145C19.7805 20.9395 19.9912 20.4308 19.9912 19.9003V7.90033L13.9912 1.90033Z'
      stroke='currentColor'
      strokeLinecap='round'
      strokeLinejoin='round'
      strokeWidth='1.5'
      vectorEffect='non-scaling-stroke'
    />
    <path
      d='M13.9912 1.90033V7.90033H19.9912'
      stroke='currentColor'
      strokeLinecap='round'
      strokeLinejoin='round'
      strokeWidth='1.5'
      vectorEffect='non-scaling-stroke'
    />
    <path
      d='M15.9912 12.9003H7.99121'
      stroke='currentColor'
      strokeLinecap='round'
      strokeLinejoin='round'
      strokeWidth='1.5'
      vectorEffect='non-scaling-stroke'
    />
    <path
      d='M15.9912 16.9003H7.99121'
      stroke='currentColor'
      strokeLinecap='round'
      strokeLinejoin='round'
      strokeWidth='1.5'
      vectorEffect='non-scaling-stroke'
    />
    <path
      d='M9.99121 8.90033H8.99121H7.99121'
      stroke='currentColor'
      strokeLinecap='round'
      strokeLinejoin='round'
      strokeWidth='1.5'
      vectorEffect='non-scaling-stroke'
    />
  </svg>
);

export default DocumentTextIcon;
