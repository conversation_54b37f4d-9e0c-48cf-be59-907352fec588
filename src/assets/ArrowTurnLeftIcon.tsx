import {FC} from 'react';

import {IconProps} from 'types/icon';

const ArrowTurnLeftIcon: FC<IconProps> = ({className}) => (
  <svg
    className={className}
    fill='none'
    stroke='currentColor'
    strokeWidth='1.5'
    viewBox='0 0 24 24'
    xmlns='http://www.w3.org/2000/svg'
  >
    <path
      d='M9 15L3 9m0 0l6-6M3 9h12a6 6 0 010 12h-3'
      strokeLinecap='round'
      strokeLinejoin='round'
      vectorEffect='non-scaling-stroke'
    />
  </svg>
);

export default ArrowTurnLeftIcon;
