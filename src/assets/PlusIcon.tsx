import {FC} from 'react';

import {IconProps} from 'types/icon';

const PlusIcon: FC<IconProps> = ({className}) => (
  <svg
    className={className}
    fill='none'
    stroke='currentColor'
    strokeWidth='1.5'
    viewBox='0 0 24 24'
    xmlns='http://www.w3.org/2000/svg'
  >
    <path d='M12 4.5v15m7.5-7.5h-15' strokeLinecap='round' strokeLinejoin='round' vectorEffect='non-scaling-stroke' />
  </svg>
);

export default PlusIcon;
