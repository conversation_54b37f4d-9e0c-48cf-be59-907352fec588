import {FC} from 'react';

import {IconProps} from 'types/icon';

const BellIcon: FC<IconProps> = ({className}) => (
  <svg className={className} fill='none' height='24' viewBox='0 0 24 24' width='24' xmlns='http://www.w3.org/2000/svg'>
    <path
      d='M22 17.5C22.2761 17.5 22.5 17.2761 22.5 17C22.5 16.7239 22.2761 16.5 22 16.5V17.5ZM2 16.5C1.72386 16.5 1.5 16.7239 1.5 17C1.5 17.2761 1.72386 17.5 2 17.5V16.5ZM14 21L14.4153 21.2785C14.5181 21.1251 14.5281 20.9276 14.4412 20.7647C14.3543 20.6018 14.1846 20.5 14 20.5V21ZM10 21V20.5C9.81536 20.5 9.64574 20.6018 9.55884 20.7647C9.47194 20.9276 9.48189 21.1251 9.58473 21.2785L10 21ZM22 16.5H2V17.5H22V16.5ZM2 17.5C3.933 17.5 5.5 15.933 5.5 14H4.5C4.5 15.3807 3.38071 16.5 2 16.5V17.5ZM5.5 14V9H4.5V14H5.5ZM5.5 9C5.5 5.41015 8.41015 2.5 12 2.5V1.5C7.85786 1.5 4.5 4.85786 4.5 9H5.5ZM12 2.5C15.5899 2.5 18.5 5.41015 18.5 9H19.5C19.5 4.85786 16.1421 1.5 12 1.5V2.5ZM18.5 9V14H19.5V9H18.5ZM18.5 14C18.5 15.933 20.067 17.5 22 17.5V16.5C20.6193 16.5 19.5 15.3807 19.5 14H18.5ZM13.5847 20.7215C13.2753 21.1829 12.6792 21.4965 12 21.4965V22.4965C12.9695 22.4965 13.8974 22.0507 14.4153 21.2785L13.5847 20.7215ZM12 21.4965C11.3208 21.4965 10.7247 21.1829 10.4153 20.7215L9.58473 21.2785C10.1026 22.0507 11.0305 22.4965 12 22.4965V21.4965ZM10 21.5H14V20.5H10V21.5Z'
      fill='currentColor'
      vectorEffect='non-scaling-stroke'
    />
  </svg>
);

export default BellIcon;
