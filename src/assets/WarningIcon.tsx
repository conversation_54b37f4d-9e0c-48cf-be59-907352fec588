import {FC} from 'react';

import {IconProps} from 'types/icon';

const WarningIcon: FC<IconProps> = ({className}) => (
  <svg className={className} fill='none' height='24' viewBox='0 0 24 24' width='24' xmlns='http://www.w3.org/2000/svg'>
    <path
      d='M12 21C16.9706 21 21 16.9706 21 12C21 7.02944 16.9706 3 12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21Z'
      fill='currentFill'
    />
    <path
      d='M12 8.40039V12.0004'
      stroke='currentColor'
      strokeLinecap='round'
      strokeLinejoin='round'
      strokeWidth='1.5'
    />
    <path d='M12 15.5996H12.0083' stroke='currentColor' strokeLinecap='round' strokeLinejoin='round' strokeWidth='2' />
  </svg>
);
export default WarningIcon;
