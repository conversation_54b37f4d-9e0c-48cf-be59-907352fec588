import {FC} from 'react';

import {useAtomValue} from 'jotai';
import {useTranslations} from 'next-intl';

import {
  Table,
  TableBody,
  TableContainer,
  TableHead,
  TableHeadActions,
  TableHeader,
  TableRow,
} from '@/components/ui/Table';
import useHeight from '@/hooks/helpers/useHeight';
import useInvoices from '@/hooks/useInvoices';

import {invoicesDateFilterAtom, InvoiceView, invoiceViewAtom} from './invoicesListStore';
import InvoicesListTableRow from './InvoicesListTableRow';

const InvoicesListTable: FC = () => {
  const dateFilter = useAtomValue(invoicesDateFilterAtom);
  const {elementRef} = useHeight();
  const view = useAtomValue(invoiceViewAtom);
  const {invoices, isLoading} = useInvoices({
    end: dateFilter?.to,
    isProforma: view === InvoiceView.proforma,
    start: dateFilter?.from,
  });
  const t = useTranslations();

  return (
    <TableContainer ref={elementRef}>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>{t('number')}</TableHead>
            <TableHead>{t('date')}</TableHead>
            <TableHead>{t('customer')}</TableHead>
            <TableHead className='text-right'>{t('value')}</TableHead>
            <TableHead className='text-right'>{t('vat')}</TableHead>
            <TableHead>{t('due date')}</TableHead>
            <TableHead>{t('status')}</TableHead>
            <TableHeadActions />
          </TableRow>
        </TableHeader>
        <TableBody className='overflow-y-hidden' isValidating={isLoading}>
          {invoices.map((invoice, index) => (
            <InvoicesListTableRow invoice={invoice} key={`${invoice.id}-row-${index}`} />
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );
};

export default InvoicesListTable;
