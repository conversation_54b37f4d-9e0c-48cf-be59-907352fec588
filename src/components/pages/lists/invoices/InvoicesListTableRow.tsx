import {FC} from 'react';

import {useAtomValue} from 'jotai';
import {CloudDownloadIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';

import {Button} from '@/components/ui/Button';
import {DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger} from '@/components/ui/DropdownMenu';
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from '@/components/ui/Select';
import {Link} from '@/components/ui/special/Link';
import {WithoutEmpty} from '@/components/ui/special/WithoutEmpty';
import {TableActions, TableCell, TableRow} from '@/components/ui/Table';
import useInvoiceActions from '@/hooks/useInvoiceActions';
import {InvoiceStatus} from '@/types/global';
import {Invoice} from '@/types/sales';
import {formatCurrency, formatDate} from '@/utils/format';

import {InvoiceView, invoiceViewAtom} from './invoicesListStore';

type Props = {
  invoice: Invoice;
};

const InvoicesListTableRow: FC<Props> = ({invoice}) => {
  const t = useTranslations();
  const {markInvoiceAsPaid} = useInvoiceActions();
  const view = useAtomValue(invoiceViewAtom);

  return (
    <TableRow>
      <TableCell>
        <Link
          className='hover:underline'
          href={`/invoices/${invoice.id}${view === InvoiceView.proforma ? '?isProforma=true' : ''}`}
        >
          {invoice.number}
        </Link>
      </TableCell>
      <TableCell>{formatDate(invoice.creationDate)}</TableCell>
      <TableCell>
        <WithoutEmpty value={invoice.customer?.id}>
          <Link className='whitespace-nowrap hover:underline' href={`/customers/${invoice.customer?.id}`}>
            {invoice.customer?.name}
          </Link>
        </WithoutEmpty>
      </TableCell>
      <TableCell className='text-right'>{formatCurrency(invoice.total)}</TableCell>
      <TableCell className='text-right'>{formatCurrency(invoice.vat)}</TableCell>
      <TableCell>{formatDate(invoice.dueDate)}</TableCell>
      <TableCell>
        <Select
          onValueChange={(value) => {
            if (value === InvoiceStatus.PAID) markInvoiceAsPaid(invoice.id, invoice.number);
          }}
          value={invoice.status}
        >
          <SelectTrigger
            className='w-fit self-end'
            size='badge-md'
            variant={
              invoice.status === InvoiceStatus.DUE
                ? 'badge-warning'
                : invoice.status === InvoiceStatus.OVERDUE
                  ? 'badge-error'
                  : 'badge-success'
            }
          >
            <SelectValue>{t(invoice.status)}</SelectValue>
          </SelectTrigger>
          <SelectContent>
            <SelectItem value={invoice.status}>{t(invoice.status)}</SelectItem>
            <SelectItem value={InvoiceStatus.PAID}>{t(InvoiceStatus.PAID)}</SelectItem>
          </SelectContent>
        </Select>
      </TableCell>
      <TableActions>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant='secondary'>
              <CloudDownloadIcon />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuItem asChild>
              <Link download href={`/api/invoices/${invoice.id}/details?mediaType=application/pdf`}>
                {t('pdf')}
              </Link>
            </DropdownMenuItem>
            <DropdownMenuItem asChild>
              <Link download href={`/api/invoices/${invoice.id}/details?mediaType=factura-saga%2Bxml`}>
                {t('saga')}
              </Link>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </TableActions>
    </TableRow>
  );
};

export default InvoicesListTableRow;
