import {format} from 'date-fns';
import {useAtomValue} from 'jotai';
import {useAtom} from 'jotai';
import {CloudDownloadIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';

import {Button} from '@/components/ui/Button';
import {DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger} from '@/components/ui/DropdownMenu';
import {Link} from '@/components/ui/special/Link';
import {Page, PageContent, PageFilters, PageHeader, PageHeaderTitle, PageTitle} from '@/components/ui/special/Page';
import {Tabs, TabsList, TabsTrigger} from '@/components/ui/Tabs';
import {NextPageWithLayout} from '@/types/global';
import {getQueryString} from '@/utils/common';

import InvoicesListFilters from './InvoicesListFilters';
import InvoicesListFiltersButton from './InvoicesListFiltersButton';
import {invoicesDateFilterAtom, InvoiceView, invoiceViewAtom} from './invoicesListStore';
import InvoicesListTable from './InvoicesListTable';

const InvoicesList: NextPageWithLayout = () => {
  const dateFilter = useAtomValue(invoicesDateFilterAtom);
  const [view, setView] = useAtom(invoiceViewAtom);
  const t = useTranslations();

  return (
    <Page>
      <PageTitle>{t('invoices')}</PageTitle>
      <PageHeader>
        <PageHeaderTitle>{t('invoices')}</PageHeaderTitle>
        <Tabs onValueChange={(newView) => setView(newView as InvoiceView)} value={view} variant='menu'>
          <TabsList variant='menu'>
            <TabsTrigger value={InvoiceView.fiscal} variant='menu'>
              {t('fiscal')}
            </TabsTrigger>
            <TabsTrigger value={InvoiceView.proforma} variant='menu'>
              {t('proforma')}
            </TabsTrigger>
          </TabsList>
        </Tabs>
        <div className='grow' />
        <InvoicesListFiltersButton dateAtom={invoicesDateFilterAtom} />
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant='secondary'>
              <CloudDownloadIcon /> {t('download')}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuItem asChild>
              <Link
                href={`/api/invoices/list?mediaType=application/pdf%2Bzip&${getQueryString({
                  end: format(dateFilter?.to || new Date(), 'yyyy-MM-dd'),
                  start: format(dateFilter?.from || 0, 'yyyy-MM-dd'),
                })}`}
              >
                {t('pdf')}
              </Link>
            </DropdownMenuItem>
            <DropdownMenuItem asChild>
              <Link
                href={`/api/invoices/list?mediaType=application/factura-saga%2Bzip&${getQueryString({
                  end: format(dateFilter?.to || new Date(), 'yyyy-MM-dd'),
                  start: format(dateFilter?.from || 0, 'yyyy-MM-dd'),
                })}`}
              >
                {t('saga')}
              </Link>
            </DropdownMenuItem>
            <DropdownMenuItem asChild>
              <Link
                href={`/api/invoices/list?mediaType=application/factura-winmentor%2Bzip&${getQueryString({
                  end: format(dateFilter?.to || new Date(), 'yyyy-MM-dd'),
                  start: format(dateFilter?.from || 0, 'yyyy-MM-dd'),
                })}`}
              >
                {t('winmentor')}
              </Link>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </PageHeader>
      <PageFilters>
        <InvoicesListFilters dateAtom={invoicesDateFilterAtom} />
      </PageFilters>
      <PageContent>
        <InvoicesListTable />
      </PageContent>
    </Page>
  );
};

export default InvoicesList;
