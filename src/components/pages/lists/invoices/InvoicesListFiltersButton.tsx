import {Dispatch, FC, useCallback, useEffect, useMemo, useState} from 'react';

import {SetStateAction, useAtom} from 'jotai';
import {WritableAtom} from 'jotai';
import {isEqual} from 'lodash';
import {useTranslations} from 'next-intl';

import {Accordion, AccordionContent, AccordionItem, AccordionTrigger} from '@/components/ui/Accordion';
import {DatePicker} from '@/components/ui/DatePicker';
import {FiltersSheet} from '@/components/ui/special/FiltersSheet';
import {DateRange} from '@/types/global';

type OwnFiltersProps = {
  dateFilters: {
    filters: DateRange | undefined;
    setFilters: Dispatch<SetStateAction<DateRange | undefined>>;
  };
};

const OwnFilters: FC<OwnFiltersProps> = ({dateFilters}) => {
  const t = useTranslations();

  return (
    <Accordion defaultValue={['date']} type='multiple'>
      <AccordionItem className='border-b' value='date'>
        <AccordionTrigger className='px-6'>{t('date range')}</AccordionTrigger>
        <AccordionContent className='px-6'>
          <DatePicker
            onChange={(value) => {
              if (value.to && value.from) dateFilters.setFilters({from: value.from, to: value.to});
            }}
            value={dateFilters.filters}
            withRange
          />
        </AccordionContent>
      </AccordionItem>
    </Accordion>
  );
};

const useInventoryFilters = (
  dateAtom: WritableAtom<DateRange | undefined, [SetStateAction<DateRange | undefined>], void>,
) => {
  const [dateFilter, setDateFilter] = useAtom(dateAtom);
  const [ownDateFilter, setOwnDateFilter] = useState(dateFilter);

  useEffect(() => {
    setOwnDateFilter(dateFilter);
  }, [dateFilter]);

  const isApplyEnabled = useMemo(() => {
    return !isEqual(dateFilter, ownDateFilter);
  }, [ownDateFilter, dateFilter]);

  const handleApply = useCallback(() => {
    setDateFilter(ownDateFilter);
  }, [ownDateFilter, setDateFilter]);

  const handleClear = useCallback(() => {
    setDateFilter(undefined);
  }, [setDateFilter]);

  const handleReset = useCallback(() => {
    setOwnDateFilter(dateFilter);
  }, [dateFilter]);

  return {
    dateFilter: ownDateFilter,
    handleApply,
    handleClear,
    handleReset,
    isApplyEnabled,
    setDateFilter: setOwnDateFilter,
  };
};

type Props = {
  dateAtom: WritableAtom<DateRange | undefined, [SetStateAction<DateRange | undefined>], void>;
};

const InvoicesListFiltersButton: FC<Props> = ({dateAtom}) => {
  const [open, setOpen] = useState(false);
  const {dateFilter, handleApply, handleClear, handleReset, isApplyEnabled, setDateFilter} =
    useInventoryFilters(dateAtom);

  return (
    <FiltersSheet
      isApplyEnabled={isApplyEnabled}
      onApply={handleApply}
      onClear={handleClear}
      onClick={() => setOpen(true)}
      onOpenChange={(isOpen) => {
        if (!isOpen) handleReset();
      }}
    >
      {open && <OwnFilters dateFilters={{filters: dateFilter, setFilters: setDateFilter}} />}
    </FiltersSheet>
  );
};

export default InvoicesListFiltersButton;
