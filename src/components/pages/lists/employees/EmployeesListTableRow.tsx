import {FC} from 'react';

import {isEmpty} from 'lodash';
import {LinkIcon, QrCodeIcon, Trash2Icon} from 'lucide-react';
import {useTranslations} from 'next-intl';

import {Button} from '@/components/ui/Button';
import {Link} from '@/components/ui/special/Link';
import {WithoutEmpty} from '@/components/ui/special/WithoutEmpty';
import {TableActions, TableCell, TableRow} from '@/components/ui/Table';
import {Tooltip, TooltipContent, TooltipTrigger} from '@/components/ui/Tooltip';
import {useHasPermission} from '@/hooks/helpers/useHasPermission';
import useEmployeeActions from '@/hooks/useEmployeeActions';
import {Employee} from '@/types/manufacturing';
import {classes} from '@/utils/common';
import {formatCurrency} from '@/utils/format';

type Props = {
  employee: Employee;
  onMobileAppConnect: (employeeId: string) => void;
};

const EmployeesListTableRow: FC<Props> = ({employee, onMobileAppConnect}) => {
  const {deleteEmployee} = useEmployeeActions();
  const {hasPermission, isLoading} = useHasPermission();
  const t = useTranslations();

  if (isLoading) return null;

  return (
    <TableRow className='h-[61px]'>
      <TableCell>
        <Link className='whitespace-nowrap hover:underline' href={`/employees/${employee.id}`}>
          {employee.name}
        </Link>
      </TableCell>
      <TableCell>
        <WithoutEmpty value={employee.position}>{employee.position}</WithoutEmpty>
      </TableCell>
      {hasPermission('financial', 'employees') && (
        <>
          <TableCell className='text-right'>{formatCurrency(employee.hourlyRate)}</TableCell>
          <TableCell className='text-right'>{formatCurrency(employee.monthlyGrossSalary)}</TableCell>
        </>
      )}
      <TableCell>
        <WithoutEmpty value={!isEmpty(employee.manufacturingOperationTemplates)}>
          {employee.manufacturingOperationTemplates.length > 0 && (
            <>
              {employee.manufacturingOperationTemplates
                .map((operation, index) => (
                  <span
                    className={classes(
                      'border-b-2 pb-0.5',
                      operation.preferential ? 'border-oldprimary' : 'border-transparent',
                    )}
                    key={`${operation.id}-${index}`}
                  >
                    {operation.name}
                  </span>
                ))
                .reduce((prev, curr) => (
                  <>
                    {prev}, {curr}
                  </>
                ))}
            </>
          )}
        </WithoutEmpty>
      </TableCell>
      <TableCell>
        <div className='flex gap-2'>
          <Button onClick={() => onMobileAppConnect(employee.id)} size='icon' variant='none'>
            <QrCodeIcon className='size-5' />
          </Button>
          {employee.isDevicePaired && (
            <Tooltip>
              <TooltipTrigger asChild>
                <Button size='icon' variant='none'>
                  <LinkIcon className='size-5' />
                </Button>
              </TooltipTrigger>
              <TooltipContent>{t('paired')}</TooltipContent>
            </Tooltip>
          )}
        </div>
      </TableCell>
      <TableActions>
        <Button onClick={() => deleteEmployee(employee.id, employee.name)} size='icon' variant='none'>
          <Trash2Icon className='size-5 text-red' strokeWidth={1} />
        </Button>
      </TableActions>
    </TableRow>
  );
};

export default EmployeesListTableRow;
