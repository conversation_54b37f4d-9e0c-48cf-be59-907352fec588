import {useState} from 'react';

import {PlusIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';

import {Button} from '@/components/ui/Button';
import {Page, PageContent, PageHeader, PageHeaderTitle, PageTitle} from '@/components/ui/special/Page';
import {StoreSearchInput} from '@/components/ui/special/StoreSearchInput';
import {useRouter} from '@/hooks/helpers/useRouter';
import {NextPageWithLayout} from '@/types/global';

import {employeesSearchQueryAtom} from './EmployeesListStore';
import EmployeesListTable from './EmployeesListTable';
import NewEmployeeSheet from './NewEmployeeSheet';

const EmployeesList: NextPageWithLayout = () => {
  const [showNew, setShowNew] = useState(false);
  const {push} = useRouter();

  const t = useTranslations();

  return (
    <>
      {showNew && (
        <NewEmployeeSheet
          onClose={() => {
            setShowNew(false);
          }}
          onCreate={(employee) => push(`/employees/${employee.id}`)}
        />
      )}
      <Page>
        <PageTitle>{t('employees')}</PageTitle>
        <PageHeader>
          <PageHeaderTitle>{t('employees')}</PageHeaderTitle>
          <div className='grow' />
          <StoreSearchInput
            autoFocus
            div={{className: 'grow'}}
            placeholder={t('search by name, position')}
            store={employeesSearchQueryAtom}
          />
          {/*<SuppliersListFiltersButton />*/}
          <Button onClick={() => setShowNew(true)}>
            <PlusIcon />
            {t('new employee')}
          </Button>
        </PageHeader>
        {/*<PageFilters className='flex justify-between'>*/}
        {/*  <SuppliersListFilters />*/}
        {/*</PageFilters>*/}
        <PageContent>
          <EmployeesListTable />
        </PageContent>
      </Page>
    </>
  );
};

export default EmployeesList;
