import {FC, useState} from 'react';

import {useAtomValue} from 'jotai';
import {first, sumBy} from 'lodash';
import {useTranslations} from 'next-intl';

import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableFooter,
  TableHead,
  TableHeadActions,
  TableHeader,
  TableRow,
} from '@/components/ui/Table';
import {useHasPermission} from '@/hooks/helpers/useHasPermission';
import useHeight from '@/hooks/helpers/useHeight';
import useEmployees from '@/hooks/useEmployees';
import {formatCurrency, formatNumber} from '@/utils/format';

import ConnectMobileAppModal from './EmployeeConnectMobileAppModal';
import {employeesSearchQueryAtom} from './EmployeesListStore';
import EmployeesListTableRow from './EmployeesListTableRow';

const EmployeesListTable: FC = () => {
  const {elementRef} = useHeight();
  const search = useAtomValue(employeesSearchQueryAtom);
  const {hasPermission, isLoading: permissionIsLoading} = useHasPermission();
  const {employees, isLoading} = useEmployees({search});
  const [selectedEmployeeId, setSelectedEmployeeId] = useState<string>();
  const t = useTranslations();

  return (
    <>
      {selectedEmployeeId && (
        <ConnectMobileAppModal
          onClose={() => setSelectedEmployeeId(undefined)}
          selectedEmployeeId={selectedEmployeeId}
        />
      )}
      <TableContainer ref={elementRef}>
        <Table className='h-full'>
          <TableHeader>
            <TableRow>
              <TableHead>{t('name')}</TableHead>
              <TableHead>{t('position')}</TableHead>
              {hasPermission('financial', 'employees') && (
                <>
                  <TableHead className='text-right'>{t('cost per hour')}</TableHead>
                  <TableHead className='text-right'>{t('monthly gross salary')}</TableHead>
                </>
              )}
              <TableHead>{t('qualified for')}</TableHead>
              <TableHead className='w-40 text-wrap'>{t('connect mobile app')}</TableHead>
              <TableHeadActions />
            </TableRow>
          </TableHeader>
          <TableBody className='overflow-y-hidden' isValidating={isLoading || permissionIsLoading}>
            {employees?.map((employee, index) => (
              <EmployeesListTableRow
                employee={employee}
                key={`${employee.id}-row-${index}`}
                onMobileAppConnect={setSelectedEmployeeId}
              />
            ))}
          </TableBody>
          <TableFooter className='sticky bottom-0 h-[60px] shadow-[0_-4px_8px_0_rgba(0,0,0,0.07843137255)]'>
            <TableRow>
              <TableCell colSpan={hasPermission('financial', 'employees') ? 2 : 5}>
                <div className='flex flex-col'>
                  <div className='text-xs font-medium uppercase text-border-foreground'>{t('total employees')}</div>
                  <div className='font-medium'>{formatNumber(employees.length)}</div>
                </div>
              </TableCell>
              {hasPermission('financial', 'employees') && (
                <>
                  <TableCell className='text-right'>
                    <div className='flex flex-col'>
                      <div className='text-xs font-medium uppercase text-border-foreground'>
                        {t('total total cost per hour')}
                      </div>
                      <div className='font-medium'>
                        {formatCurrency({
                          amount: sumBy(employees, (employee) => employee?.hourlyRate?.amount),
                          currency: first(employees)?.hourlyRate?.currency,
                        })}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell className='text-right'>
                    <div className='flex flex-col'>
                      <div className='text-xs font-medium uppercase text-border-foreground'>
                        {t('total monthly gross salary')}
                      </div>
                      <div className='font-medium'>
                        {formatCurrency({
                          amount: sumBy(employees, (employee) => employee?.monthlyGrossSalary?.amount),
                          currency: first(employees)?.monthlyGrossSalary?.currency,
                        })}
                      </div>
                    </div>
                  </TableCell>
                </>
              )}
              <TableCell colSpan={3} />
            </TableRow>
          </TableFooter>
        </Table>
      </TableContainer>
    </>
  );
};

export default EmployeesListTable;
