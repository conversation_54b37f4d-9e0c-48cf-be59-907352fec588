import {FC, useState} from 'react';

import axios from 'axios';
import copy from 'clipboard-copy';
import {CopyIcon, LoaderCircleIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';
import {QRCodeSVG} from 'qrcode.react';
import useSWR from 'swr';

import {Button} from '@/components/ui/Button';
import {Sheet, SheetContent, SheetOverlay, SheetPage} from '@/components/ui/Sheet';
import {toast} from '@/components/ui/Toast';
import {EmployeePairing} from 'types/manufacturing';

type Props = {
  onClose: () => void;
  selectedEmployeeId?: string;
};

const EmployeeConnectMobileAppModal: FC<Props> = ({onClose, selectedEmployeeId}) => {
  const t = useTranslations();
  const [copied, setCopied] = useState(false);

  const {data} = useSWR<EmployeePairing>(
    selectedEmployeeId ? `pairing-${selectedEmployeeId}` : null,
    () =>
      axios
        .get(`/api/employees/${selectedEmployeeId}/initiate-device-pairing`)
        .then((res) => res.data)
        .catch(() => toast.error(t('an error occurred while loading name', {name: t('suffixed.employee.end')}))),
    {revalidateOnFocus: false},
  );

  return (
    <Sheet onOpenChange={onClose} open>
      <SheetOverlay />
      <SheetPage size='lg'>
        <SheetContent>
          {!data && (
            <div className='absolute top-1/2 -translate-y-1/2 text-blue-900'>
              <LoaderCircleIcon className='size-40 animate-spin' />
            </div>
          )}

          {data && (
            <div className='m-4 flex flex-col items-center'>
              <QRCodeSVG className='mx-8 rounded-xl bg-white p-4' fgColor='#1e3a8a' size={500} value={data.token} />
              <Button
                className='mt-6'
                onClick={() => {
                  copy(data.pairingLink);
                  setCopied(true);
                }}
                variant='secondary'
              >
                <CopyIcon className='size-5' strokeWidth={2} />
                {t(copied ? 'copied' : 'copy link')}
              </Button>
            </div>
          )}
        </SheetContent>
      </SheetPage>
    </Sheet>
  );
};

export default EmployeeConnectMobileAppModal;
