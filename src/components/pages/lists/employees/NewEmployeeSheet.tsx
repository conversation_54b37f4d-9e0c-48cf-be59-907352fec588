import {FC, useCallback, useEffect, useState} from 'react';

import {joiResolver} from '@hookform/resolvers/joi';
import {useAtomValue} from 'jotai';
import {isEmpty} from 'lodash';
import {StarIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';
import {useFieldArray, useForm} from 'react-hook-form';

import {Button} from '@/components/ui/Button';
import {Checkbox} from '@/components/ui/Checkbox';
import {Input, InputLabel} from '@/components/ui/Input';
import {WithLabel} from '@/components/ui/Label';
import {Sheet, SheetClose, SheetContent, SheetFooter, SheetHeader, SheetPage, SheetTitle} from '@/components/ui/Sheet';
import {useHasPermission} from '@/hooks/helpers/useHasPermission';
import {employeeSchema} from '@/hooks/useEmployee';
import useEmployeeActions from '@/hooks/useEmployeeActions';
import useOperationActions from '@/hooks/useOperationActions';
import useOperations from '@/hooks/useOperations';
import {defaultCurrencyAtom} from '@/store/defaults';
import {Employee} from '@/types/manufacturing';
import {Supplier} from '@/types/sales';
import {classes} from '@/utils/common';

type Props = {
  onClose: () => void;
  onCreate: (supplier: Supplier) => void;
};

const NewEmployeeSheet: FC<Props> = ({onClose, onCreate}) => {
  const t = useTranslations();
  const {createEmployee} = useEmployeeActions();
  const {operations} = useOperations();
  const defaultCurrency = useAtomValue(defaultCurrencyAtom);
  const {hasPermission} = useHasPermission();
  const {
    control,
    formState: {errors},
    register,
    trigger,
    watch,
  } = useForm<Employee>({
    defaultValues: {
      activeTimeoffs: [],
      monthlyGrossSalary: {amount: 0, currency: defaultCurrency},
    },
    mode: 'onSubmit',
    resolver: joiResolver(employeeSchema),
  });
  const {append, remove, update} = useFieldArray({control, name: 'manufacturingOperationTemplates'});
  const [newTaskName, setNewTaskName] = useState('');
  const {createOperation} = useOperationActions();

  useEffect(() => {
    setTimeout(trigger, 50);
  }, [trigger]);

  const handleAddTask = useCallback(
    (name: string) => {
      createOperation({name}).then((operation) => {
        if (operation) append({...operation, preferential: false});
        setNewTaskName('');
      });
    },
    [append, createOperation],
  );

  return (
    <Sheet defaultOpen={true} onOpenChange={() => setTimeout(onClose, 500)}>
      <SheetPage side='right'>
        <SheetTitle className='text-2xl'>{t('new employee')}</SheetTitle>
        <SheetHeader>
          <div className='flex flex-col gap-2 rounded-lg border p-4'>
            <WithLabel>
              <Input {...register('name')} error={!!errors.name} />
              <InputLabel>{t('name')}*</InputLabel>
            </WithLabel>
            <WithLabel>
              <Input {...register('position')} error={!!errors?.position} />
              <InputLabel>{t('position')}*</InputLabel>
            </WithLabel>
            {hasPermission('financial', 'employees') && (
              <WithLabel>
                <Input type='number' {...register('monthlyGrossSalary.amount')} error={!!errors?.monthlyGrossSalary} />
                <InputLabel>
                  {t('monthly gross salary')} ({watch('monthlyGrossSalary.currency') || defaultCurrency})
                </InputLabel>
              </WithLabel>
            )}
          </div>
          {hasPermission('update', 'manufacturing') && (
            <>
              <div className='text-base font-medium'>{t('qualified for')}</div>
              <div className='flex items-center gap-4'>
                <Input
                  onChange={({target: {value}}) => setNewTaskName(value)}
                  onKeyDown={({key}) => {
                    if (key === 'Enter') handleAddTask(newTaskName);
                  }}
                  placeholder={t('operation')}
                  value={newTaskName}
                />
                <Button disabled={!newTaskName} onClick={() => handleAddTask(newTaskName)} variant='secondary'>
                  {t('add')}
                </Button>
              </div>
            </>
          )}
        </SheetHeader>
        {hasPermission('update', 'manufacturing') && (
          <SheetContent className='mx-6 flex flex-col gap-4 rounded-lg border p-4'>
            {operations.map((operation, index) => {
              const operationTemplateIndex = watch('manufacturingOperationTemplates')?.findIndex(
                (op) => op.id === operation.id,
              );
              const operationTemplate = watch('manufacturingOperationTemplates')?.[operationTemplateIndex];

              return (
                <div className='flex items-center justify-between border-b pb-2' key={`${operation.id}-${index}`}>
                  <div className='flex gap-4'>
                    <Button
                      className={classes(
                        'hover:fill-button-primary-hovered',
                        operationTemplate?.preferential ? 'fill-button-primary' : 'fill-white',
                      )}
                      onClick={() => {
                        if (operationTemplate) {
                          update(operationTemplateIndex, {
                            ...operationTemplate,
                            preferential: !operationTemplate.preferential,
                          });
                        } else {
                          append({
                            id: operation.id,
                            name: operation.name,
                            preferential: true,
                          });
                        }
                      }}
                      size='icon'
                      variant='none'
                    >
                      <StarIcon className='size-5' fill='' strokeWidth={1.5} />
                    </Button>
                    <div>{operation.name}</div>
                  </div>
                  <Checkbox
                    checked={!!operationTemplate}
                    onCheckedChange={(checked) => {
                      if (checked) {
                        append({
                          id: operation.id,
                          name: operation.name,
                          preferential: false,
                        });
                      } else {
                        remove(operationTemplateIndex);
                      }
                    }}
                  />
                </div>
              );
            })}
          </SheetContent>
        )}
        {!hasPermission('update', 'manufacturing') && <SheetContent />}
        <SheetFooter className='px-6'>
          <SheetClose asChild disabled={!isEmpty(errors)}>
            <Button
              onClick={() => {
                createEmployee(watch()).then((newEmployee) => {
                  if (newEmployee) {
                    onCreate(newEmployee);
                    onClose();
                  }
                });
              }}
            >
              {t('add employee')}
            </Button>
          </SheetClose>
        </SheetFooter>
      </SheetPage>
    </Sheet>
  );
};

export default NewEmployeeSheet;
