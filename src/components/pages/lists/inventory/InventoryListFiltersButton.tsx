import {Dispatch, FC, useCallback, useEffect, useMemo, useState} from 'react';

import {SetStateAction, useAtom, WritableAtom} from 'jotai';
import {isEqual} from 'lodash';
import {useTranslations} from 'next-intl';
import {SWRInfiniteKeyedMutator} from 'swr/infinite';

import {Accordion, AccordionContent, AccordionItem, AccordionTrigger} from '@/components/ui/Accordion';
import {Checkbox} from '@/components/ui/Checkbox';
import {Label, WithLabel} from '@/components/ui/Label';
import {Skeleton} from '@/components/ui/Skeleton';
import {FiltersSheet} from '@/components/ui/special/FiltersSheet';
import useCategories from '@/hooks/useCategories';
import useInventoryUnits from '@/hooks/useInventoryUnits';

type OwnFiltersProps = {
  categoryFilters: {
    filters: string[];
    setFilters: Dispatch<SetStateAction<string[]>>;
  };
  inventoryUnitFilters: {
    filters: string[];
    setFilters: Dispatch<SetStateAction<string[]>>;
  };
};

const OwnFilters: FC<OwnFiltersProps> = ({categoryFilters, inventoryUnitFilters}) => {
  const {inventoryUnits, isLoading} = useInventoryUnits();
  const {categories, isLoading: categoriesIsLoading} = useCategories();

  const t = useTranslations();

  const defaultValue = useMemo(() => {
    return [
      ...(!!inventoryUnitFilters.filters?.length ? ['inventoryUnit'] : []),
      ...(!!categoryFilters.filters?.length ? ['category'] : []),
    ];
  }, [categoryFilters.filters?.length, inventoryUnitFilters.filters?.length]);

  const handleCheckedChange = useCallback(
    (
      value: string,
      checked: boolean | string,
      store: {
        filters: string[];
        setFilters: Dispatch<SetStateAction<string[]>>;
      },
    ) => {
      store.setFilters((prev) => {
        if (checked) return [...prev, value];

        return prev.filter((id) => id !== value);
      });
    },
    [],
  );

  if (isLoading || categoriesIsLoading)
    return (
      <div className='flex flex-col gap-4'>
        <Skeleton className='h-4 w-full' />
        <Skeleton className='h-4 w-full' />
        <Skeleton className='h-4 w-full' />
        <Skeleton className='h-4 w-full' />
      </div>
    );

  return (
    <Accordion defaultValue={defaultValue} type='multiple'>
      <AccordionItem className='border-b' value='inventoryUnit'>
        <AccordionTrigger className='px-6'>{t('inventory unit')}</AccordionTrigger>
        <AccordionContent className='px-6'>
          <div className='flex flex-col gap-4'>
            {inventoryUnits.map((unit) => (
              <WithLabel direction='horizontal' key={unit.id}>
                <Checkbox
                  defaultChecked={inventoryUnitFilters.filters.includes(unit.id)}
                  id={unit.id}
                  onCheckedChange={(checked) => handleCheckedChange(unit.id, checked, inventoryUnitFilters)}
                />
                <Label htmlFor={unit.id}>{unit.name}</Label>
              </WithLabel>
            ))}
          </div>
        </AccordionContent>
      </AccordionItem>
      <AccordionItem className='border-b' value='category'>
        <AccordionTrigger className='px-6'>{t('category')}</AccordionTrigger>
        <AccordionContent className='px-6'>
          <div className='flex flex-col gap-4'>
            {categories.map((category) => (
              <WithLabel direction='horizontal' key={category.id}>
                <Checkbox
                  defaultChecked={categoryFilters.filters.includes(category.id)}
                  id={category.id}
                  onCheckedChange={(checked) => handleCheckedChange(category.id, checked, categoryFilters)}
                />
                <Label htmlFor={category.id}>{category.details.name}</Label>
              </WithLabel>
            ))}
          </div>
        </AccordionContent>
      </AccordionItem>
    </Accordion>
  );
};

const useInventoryFilters = (
  inventoryUnitIdsAtom: WritableAtom<string[], [SetStateAction<string[]>], void>,
  categoryIdsAtom: WritableAtom<string[], [SetStateAction<string[]>], void>,
) => {
  const [inventoryUnitFilters, setInventoryUnitFilters] = useAtom(inventoryUnitIdsAtom);
  const [ownInventoryUnitFilters, setOwnInventoryUnitFilters] = useState(inventoryUnitFilters);
  const [categoryFilters, setCategoryFilters] = useAtom(categoryIdsAtom);
  const [ownCategoryFilters, setOwnCategoryFilters] = useState(categoryFilters);

  useEffect(() => {
    setOwnInventoryUnitFilters(inventoryUnitFilters);
    setOwnCategoryFilters(categoryFilters);
  }, [categoryFilters, inventoryUnitFilters]);

  const isApplyEnabled = useMemo(() => {
    return !isEqual(inventoryUnitFilters, ownInventoryUnitFilters) || !isEqual(categoryFilters, ownCategoryFilters);
  }, [categoryFilters, inventoryUnitFilters, ownCategoryFilters, ownInventoryUnitFilters]);

  const handleApply = useCallback(() => {
    setInventoryUnitFilters(ownInventoryUnitFilters);
    setCategoryFilters(ownCategoryFilters);
  }, [setInventoryUnitFilters, ownInventoryUnitFilters, setCategoryFilters, ownCategoryFilters]);

  const handleClear = useCallback(() => {
    setInventoryUnitFilters([]);
    setCategoryFilters([]);
  }, [setCategoryFilters, setInventoryUnitFilters]);

  const handleReset = useCallback(() => {
    setOwnInventoryUnitFilters(inventoryUnitFilters);
    setOwnCategoryFilters(categoryFilters);
  }, [categoryFilters, inventoryUnitFilters]);

  return {
    categoryFilters: ownCategoryFilters,
    handleApply,
    handleClear,
    handleReset,
    inventoryUnitFilters: ownInventoryUnitFilters,
    isApplyEnabled,
    setCategoryFilters: setOwnCategoryFilters,
    setInventoryUnitFilters: setOwnInventoryUnitFilters,
  };
};

type Props = {
  categoryIdsAtom: WritableAtom<string[], [SetStateAction<string[]>], void>;
  inventoryUnitIdsAtom: WritableAtom<string[], [SetStateAction<string[]>], void>;
  mutate: SWRInfiniteKeyedMutator<any[]>;
};

const InventoryListFiltersButton: FC<Props> = ({categoryIdsAtom, inventoryUnitIdsAtom, mutate}) => {
  const [open, setOpen] = useState(false);
  const {
    categoryFilters,
    handleApply,
    handleClear,
    handleReset,
    inventoryUnitFilters,
    isApplyEnabled,
    setCategoryFilters,
    setInventoryUnitFilters,
  } = useInventoryFilters(inventoryUnitIdsAtom, categoryIdsAtom);

  return (
    <FiltersSheet
      isApplyEnabled={isApplyEnabled}
      onApply={() => {
        handleApply();
        mutate();
      }}
      onClear={handleClear}
      onClick={() => setOpen(true)}
      onOpenChange={(isOpen) => {
        if (!isOpen) handleReset();
      }}
    >
      {open && (
        <OwnFilters
          categoryFilters={{filters: categoryFilters, setFilters: setCategoryFilters}}
          inventoryUnitFilters={{filters: inventoryUnitFilters, setFilters: setInventoryUnitFilters}}
        />
      )}
    </FiltersSheet>
  );
};

export default InventoryListFiltersButton;
