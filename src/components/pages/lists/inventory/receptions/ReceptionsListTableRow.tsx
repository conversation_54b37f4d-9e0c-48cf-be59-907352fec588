import {FC} from 'react';

import {CloudDownloadIcon} from 'lucide-react';

import {Button} from '@/components/ui/Button';
import {Link} from '@/components/ui/special/Link';
import {WithoutEmpty} from '@/components/ui/special/WithoutEmpty';
import {TableActions, TableCell, TableRow} from '@/components/ui/Table';
import {InventoryReception} from '@/types/inventory';
import {formatDate} from '@/utils/format';

type Props = {
  reception: InventoryReception;
};

const ReceptionsListTableRow: FC<Props> = ({reception}) => {
  return (
    <TableRow>
      <TableCell>
        <Link className='hover:underline' href={`/inventory/receptions/${reception.id}`}>
          {reception.number}
        </Link>
      </TableCell>
      <TableCell>
        <WithoutEmpty value={reception.purchaseOrder?.id}>
          <Link className='hover:underline' href={`/purchases/orders/${reception?.purchaseOrder?.id}`}>
            {reception?.purchaseOrder?.name}
          </Link>
        </WithoutEmpty>
      </TableCell>
      <TableCell>{formatDate(reception.receptionDate)}</TableCell>
      <TableActions>
        <Button asChild size='icon' variant='none'>
          <Link href={`/api/inventory/receptions/${reception.id}/details?mediaType=application/pdf`}>
            <CloudDownloadIcon />
          </Link>
        </Button>
      </TableActions>
    </TableRow>
  );
};

export default ReceptionsListTableRow;
