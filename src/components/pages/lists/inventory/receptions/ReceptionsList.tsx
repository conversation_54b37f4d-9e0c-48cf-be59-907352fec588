import {format} from 'date-fns';
import {useAtomValue} from 'jotai';
import {CloudDownloadIcon, PlusIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';

import {Button} from '@/components/ui/Button';
import {DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger} from '@/components/ui/DropdownMenu';
import {Link} from '@/components/ui/special/Link';
import {Page, PageContent, PageFilters, PageHeader, PageHeaderTitle, PageTitle} from '@/components/ui/special/Page';
import {StoreSearchInput} from '@/components/ui/special/StoreSearchInput';
import {NextPageWithLayout} from '@/types/global';
import {getQueryString} from '@/utils/common';

import ReceptionsListFilters from './ReceptionsListFilters';
import ReceptionsListFiltersButton from './ReceptionsListFiltersButton';
import {receptionsDateFilter<PERSON>tom, receptionsSearchQueryAtom} from './receptionsListStore';
import ReceptionsListTable from './ReceptionsListTable';

const ReceptionsList: NextPageWithLayout = () => {
  const dateFilter = useAtomValue(receptionsDateFilterAtom);
  const search = useAtomValue(receptionsSearchQueryAtom);
  const t = useTranslations();

  return (
    <Page>
      <PageTitle>{t('receptions')}</PageTitle>
      <PageHeader>
        <PageHeaderTitle>{t('receptions')}</PageHeaderTitle>
        <div className='grow' />
        <StoreSearchInput
          autoFocus
          div={{className: 'grow'}}
          placeholder={t('search by name, sku')}
          store={receptionsSearchQueryAtom}
        />
        <ReceptionsListFiltersButton dateAtom={receptionsDateFilterAtom} />
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant='secondary'>
              <CloudDownloadIcon /> {t('download')}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuItem asChild>
              <Link
                download
                href={`/api/inventory/receptions/list?mediaType=application/json&${getQueryString({
                  end: format(dateFilter?.to || new Date(), 'yyyy-MM-dd'),
                  q: search,
                  start: format(dateFilter?.from || 0, 'yyyy-MM-dd'),
                })}`}
              >
                {t('json')}
              </Link>
            </DropdownMenuItem>
            <DropdownMenuItem asChild>
              <Link
                href={`/api/inventory/receptions/list?mediaType=application/pdf%2Bzip&${getQueryString({
                  end: format(dateFilter?.to || new Date(), 'yyyy-MM-dd'),
                  q: search,
                  start: format(dateFilter?.from || 0, 'yyyy-MM-dd'),
                })}`}
              >
                {t('pdf')}
              </Link>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
        <Button asChild>
          <Link href={'/inventory/receptions/new'}>
            <PlusIcon />
            {t('add reception')}
          </Link>
        </Button>
      </PageHeader>
      <PageFilters>
        <ReceptionsListFilters dateAtom={receptionsDateFilterAtom} />
      </PageFilters>
      <PageContent>
        <ReceptionsListTable />
      </PageContent>
    </Page>
  );
};

export default ReceptionsList;
