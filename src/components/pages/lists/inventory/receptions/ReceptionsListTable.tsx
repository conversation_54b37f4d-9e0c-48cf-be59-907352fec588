import {FC} from 'react';

import {useAtomValue} from 'jotai';
import {useTranslations} from 'next-intl';

import {
  Table,
  TableBody,
  TableContainer,
  TableHead,
  TableHeadActions,
  TableHeader,
  TableRow,
} from '@/components/ui/Table';
import useHeight from '@/hooks/helpers/useHeight';
import useReceptions from '@/hooks/useReceptions';

import {receptionsDateFilterAtom, receptionsSearchQueryAtom} from './receptionsListStore';
import ReceptionsListTableRow from './ReceptionsListTableRow';

const ReceptionsListTable: FC = () => {
  const dateFilter = useAtomValue(receptionsDateFilterAtom);
  const search = useAtomValue(receptionsSearchQueryAtom);
  const {elementRef} = useHeight();
  const {isLoading, receptions} = useReceptions({end: dateFilter?.to, search, start: dateFilter?.from});
  const t = useTranslations();

  return (
    <TableContainer ref={elementRef}>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>{t('number')}</TableHead>
            <TableHead>{t('order')}</TableHead>
            <TableHead>{t('reception date')}</TableHead>
            <TableHeadActions />
          </TableRow>
        </TableHeader>
        <TableBody className='overflow-y-hidden' isValidating={isLoading}>
          {receptions.map((reception, index) => (
            <ReceptionsListTableRow key={`${reception.id}-row-${index}`} reception={reception} />
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );
};

export default ReceptionsListTable;
