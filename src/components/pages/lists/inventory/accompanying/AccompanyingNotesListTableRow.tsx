import {FC} from 'react';

import {Link} from '@/components/ui/special/Link';
import {WithoutEmpty} from '@/components/ui/special/WithoutEmpty';
import {TableCell, TableRow} from '@/components/ui/Table';
import {AccompanyingNote} from '@/types/global';
import {formatDate} from '@/utils/format';

type Props = {
  accompanyingNote: AccompanyingNote;
};

const AccompanyingNotesListTableRow: FC<Props> = ({accompanyingNote}) => {
  return (
    <TableRow>
      <TableCell>
        <Link className='hover:underline' href={`/inventory/accompanying/${accompanyingNote.id}`}>
          {accompanyingNote.number}
        </Link>
      </TableCell>
      <TableCell>
        <WithoutEmpty value={accompanyingNote.salesOrder?.id}>
          <Link className='hover:underline' href={`/sales/orders/${accompanyingNote?.salesOrder?.id}`}>
            {accompanyingNote?.salesOrder?.name}
          </Link>
        </WithoutEmpty>
      </TableCell>
      <TableCell>
        <WithoutEmpty value={accompanyingNote.servicingOrder?.id}>
          <Link className='hover:underline' href={`/manufacturing/services/${accompanyingNote?.servicingOrder?.id}`}>
            {accompanyingNote?.servicingOrder?.name}
          </Link>
        </WithoutEmpty>
      </TableCell>
      <TableCell>
        <WithoutEmpty value={accompanyingNote.customer?.id}>
          <Link className='hover:underline' href={`/customers/${accompanyingNote?.customer?.id}`}>
            {accompanyingNote?.customer?.name}
          </Link>
        </WithoutEmpty>
      </TableCell>
      <TableCell>
        <WithoutEmpty value={accompanyingNote.delegate?.id}>
          <Link className='hover:underline' href={`/employees/${accompanyingNote?.delegate?.id}`}>
            {accompanyingNote?.delegate?.name}
          </Link>
        </WithoutEmpty>
      </TableCell>
      <TableCell>{formatDate(accompanyingNote.deliveryDate)}</TableCell>
    </TableRow>
  );
};

export default AccompanyingNotesListTableRow;
