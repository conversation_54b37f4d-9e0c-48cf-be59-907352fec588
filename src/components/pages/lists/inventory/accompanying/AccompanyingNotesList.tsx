import {PlusIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';

import {Button} from '@/components/ui/Button';
import {Link} from '@/components/ui/special/Link';
import {Page, PageContent, PageFilters, PageHeader, PageHeaderTitle, PageTitle} from '@/components/ui/special/Page';
import {NextPageWithLayout} from '@/types/global';

import AccompanyingNotesListFilters from './AccompanyingNotesListFilters';
import AccompanyingNotesListFiltersButton from './AccompanyingNotesListFiltersButton';
import {
  accompanyingNotesCustomerFilterAtom,
  accompanyingNotesDateFilterAtom,
  accompanyingNotesDelegateFilterAtom,
} from './accompanyingNotesListStore';
import AccompanyingNotesListTable from './AccompanyingNotesListTable';

const AccompanyingNotesList: NextPageWithLayout = () => {
  const t = useTranslations();

  return (
    <Page>
      <PageTitle>{t('goods accompanying notes')}</PageTitle>
      <PageHeader>
        <PageHeaderTitle>{t('goods accompanying notes')}</PageHeaderTitle>
        <div className='grow' />
        <AccompanyingNotesListFiltersButton
          customerAtom={accompanyingNotesCustomerFilterAtom}
          dateAtom={accompanyingNotesDateFilterAtom}
          delegateAtom={accompanyingNotesDelegateFilterAtom}
        />
        <Button asChild>
          <Link href={'/inventory/accompanying/new'}>
            <PlusIcon />
            {t('add goods accompanying note')}
          </Link>
        </Button>
      </PageHeader>
      <PageFilters>
        <AccompanyingNotesListFilters
          customerAtom={accompanyingNotesCustomerFilterAtom}
          dateAtom={accompanyingNotesDateFilterAtom}
          delegateAtom={accompanyingNotesDelegateFilterAtom}
        />
      </PageFilters>
      <PageContent>
        <AccompanyingNotesListTable />
      </PageContent>
    </Page>
  );
};

export default AccompanyingNotesList;
