import {FC, useMemo} from 'react';

import {SetStateAction, useAtom, WritableAtom} from 'jotai';
import {find, sortBy} from 'lodash';
import {XIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';

import {Badge} from '@/components/ui/Badge';
import useCustomers from '@/hooks/useCustomers';
import useEmployees from '@/hooks/useEmployees';
import {DateRange} from '@/types/global';
import {formatDate} from '@/utils/format';

type Props = {
  customerAtom: WritableAtom<string[], [SetStateAction<string[]>], void>;
  dateAtom: WritableAtom<DateRange | undefined, [SetStateAction<DateRange | undefined>], void>;
  delegateAtom: WritableAtom<string[], [SetStateAction<string[]>], void>;
};

const AccompanyingNotesListFilters: FC<Props> = ({customerAtom, dateAtom, delegateAtom}) => {
  const [dateFilter, setDateFilter] = useAtom(dateAtom);
  const [customerFilter, setCustomerFilter] = useAtom(customerAtom);
  const [delegateFilter, setDelegateFilter] = useAtom(delegateAtom);
  const t = useTranslations();
  const {customers, isLoading} = useCustomers();
  const {employees, isLoading: employeesIsLoading} = useEmployees();

  const actualFilters = useMemo(() => {
    if (isLoading || employeesIsLoading) return [];

    return sortBy(
      [
        ...(customerFilter || []).map((customerId) => ({
          action: setCustomerFilter,
          filterName: t('customer'),
          text: find(customers, (customer) => customer.id === customerId)?.name,
          value: customerId,
        })),
        ...(delegateFilter || []).map((employeeId) => ({
          action: setDelegateFilter,
          filterName: t('delegate'),
          text: find(employees, (employee) => employee.id === employeeId)?.name,
          value: employeeId,
        })),
      ],
      'text',
    );
  }, [
    customerFilter,
    customers,
    delegateFilter,
    employees,
    employeesIsLoading,
    isLoading,
    setCustomerFilter,
    setDelegateFilter,
    t,
  ]);

  return (
    <div className='flex flex-wrap items-center gap-2'>
      {dateFilter && (
        <Badge className='group cursor-pointer gap-1 hover:bg-input' onClick={() => setDateFilter(undefined)}>
          {t('date range')}: {formatDate(dateFilter.from)} - {formatDate(dateFilter.to)}
          <XIcon className='mt-0.5 size-4' />
        </Badge>
      )}
      {actualFilters.map((filter) => (
        <Badge
          className='group cursor-pointer gap-1 hover:bg-input'
          key={filter.value}
          onClick={() => filter.action((prev) => prev?.filter((id) => id !== filter.value))}
        >
          {filter.filterName}: {filter.text}
          <XIcon className='mt-0.5 size-4' />
        </Badge>
      ))}
      {!dateFilter && actualFilters.length === 0 && t('no filters applied')}
    </div>
  );
};

export default AccompanyingNotesListFilters;
