import {FC} from 'react';

import {useAtomValue} from 'jotai';
import {useTranslations} from 'next-intl';

import {Table, TableBody, TableContainer, TableHead, TableHeader, TableRow} from '@/components/ui/Table';
import useHeight from '@/hooks/helpers/useHeight';
import useAccompanyingNotes from '@/hooks/useAccompanyingNotes';

import {
  accompanyingNotesCustomerFilterAtom,
  accompanyingNotesDateFilterAtom,
  accompanyingNotesDelegateFilterAtom,
} from './accompanyingNotesListStore';
import AccompanyingNotesListTableRow from './AccompanyingNotesListTableRow';

const AccompanyingNotesListTable: FC = () => {
  const dateFilter = useAtomValue(accompanyingNotesDateFilterAtom);
  const customerFilter = useAtomValue(accompanyingNotesCustomerFilterAtom);
  const delegateFilter = useAtomValue(accompanyingNotesDelegateFilterAtom);
  const {elementRef} = useHeight();
  const {accompanyingNotes, isLoading} = useAccompanyingNotes({
    customerIds: customerFilter,
    delegateIds: delegateFilter,
    end: dateFilter?.to,
    start: dateFilter?.from,
  });
  const t = useTranslations();

  return (
    <TableContainer ref={elementRef}>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>{t('number')}</TableHead>
            <TableHead>{t('order')}</TableHead>
            <TableHead>{t('service')}</TableHead>
            <TableHead>{t('customer')}</TableHead>
            <TableHead>{t('delegate')}</TableHead>
            <TableHead>{t('delivery date')}</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody className='overflow-y-hidden' isValidating={isLoading}>
          {accompanyingNotes.map((accompanyingNote, index) => (
            <AccompanyingNotesListTableRow
              accompanyingNote={accompanyingNote}
              key={`${accompanyingNote.id}-row-${index}`}
            />
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );
};

export default AccompanyingNotesListTable;
