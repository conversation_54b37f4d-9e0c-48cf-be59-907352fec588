import {Dispatch, FC, useCallback, useEffect, useMemo, useState} from 'react';

import {SetStateAction, useAtom, WritableAtom} from 'jotai';
import {isEqual} from 'lodash';
import {useTranslations} from 'next-intl';

import {Accordion, AccordionContent, AccordionItem, AccordionTrigger} from '@/components/ui/Accordion';
import {Checkbox} from '@/components/ui/Checkbox';
import {DatePicker} from '@/components/ui/DatePicker';
import {Label, WithLabel} from '@/components/ui/Label';
import {Skeleton} from '@/components/ui/Skeleton';
import {FiltersSheet} from '@/components/ui/special/FiltersSheet';
import useCustomers from '@/hooks/useCustomers';
import useEmployees from '@/hooks/useEmployees';
import {DateRange} from '@/types/global';

type OwnFiltersProps = {
  customerFilters: {
    filters: string[];
    setFilters: Dispatch<SetStateAction<string[]>>;
  };
  dateFilters: {
    filters: DateRange | undefined;
    setFilters: Dispatch<SetStateAction<DateRange | undefined>>;
  };
  delegateFilters: {
    filters: string[];
    setFilters: Dispatch<SetStateAction<string[]>>;
  };
};

const OwnFilters: FC<OwnFiltersProps> = ({customerFilters, dateFilters, delegateFilters}) => {
  const t = useTranslations();
  const {customers, isLoading} = useCustomers();
  const {employees, isLoading: employeesIsLoading} = useEmployees();

  const defaultValue = useMemo(() => {
    return [
      ...(!!dateFilters.filters?.to || !!dateFilters.filters?.from ? ['date'] : []),
      ...(!!customerFilters.filters?.length ? ['customer'] : []),
      ...(!!delegateFilters.filters?.length ? ['delegate'] : []),
    ];
  }, [
    customerFilters.filters?.length,
    dateFilters.filters?.from,
    dateFilters.filters?.to,
    delegateFilters.filters?.length,
  ]);

  const handleCheckedChange = useCallback(
    (
      value: string,
      checked: boolean | string,
      store: {
        filters: string[];
        setFilters: Dispatch<SetStateAction<string[]>>;
      },
    ) => {
      store.setFilters((prev) => {
        if (checked) return [...prev, value];

        return prev.filter((id) => id !== value);
      });
    },
    [],
  );

  if (isLoading || employeesIsLoading)
    return (
      <div className='flex flex-col gap-4'>
        <Skeleton className='h-4 w-full' />
        <Skeleton className='h-4 w-full' />
        <Skeleton className='h-4 w-full' />
        <Skeleton className='h-4 w-full' />
      </div>
    );

  return (
    <Accordion defaultValue={defaultValue} type='multiple'>
      <AccordionItem className='border-b' value='date'>
        <AccordionTrigger className='px-6'>{t('date range')}</AccordionTrigger>
        <AccordionContent className='px-6'>
          <DatePicker
            onChange={(value) => {
              if (value.to && value.from) dateFilters.setFilters({from: value.from, to: value.to});
            }}
            value={dateFilters.filters}
            withRange
          />
        </AccordionContent>
      </AccordionItem>
      <AccordionItem className='border-b' value='delegate'>
        <AccordionTrigger className='px-6'>{t('delegate')}</AccordionTrigger>
        <AccordionContent className='px-6'>
          <div className='flex flex-col gap-4'>
            {employees.map((employee) => (
              <WithLabel direction='horizontal' key={employee.id}>
                <Checkbox
                  defaultChecked={delegateFilters.filters.includes(employee.id)}
                  id={employee.id}
                  onCheckedChange={(checked) => handleCheckedChange(employee.id, checked, delegateFilters)}
                />
                <Label htmlFor={employee.id}>{employee.name}</Label>
              </WithLabel>
            ))}
          </div>
        </AccordionContent>
      </AccordionItem>
      <AccordionItem className='border-b' value='customer'>
        <AccordionTrigger className='px-6'>{t('customer')}</AccordionTrigger>
        <AccordionContent className='px-6'>
          <div className='flex flex-col gap-4'>
            {customers.map((customer) => (
              <WithLabel direction='horizontal' key={customer.id}>
                <Checkbox
                  defaultChecked={customerFilters.filters.includes(customer.id)}
                  id={customer.id}
                  onCheckedChange={(checked) => handleCheckedChange(customer.id, checked, customerFilters)}
                />
                <Label htmlFor={customer.id}>{customer.name}</Label>
              </WithLabel>
            ))}
          </div>
        </AccordionContent>
      </AccordionItem>
    </Accordion>
  );
};

const useInventoryFilters = (
  dateAtom: WritableAtom<DateRange | undefined, [SetStateAction<DateRange | undefined>], void>,
  customerAtom: WritableAtom<string[], [SetStateAction<string[]>], void>,
  delegateAtom: WritableAtom<string[], [SetStateAction<string[]>], void>,
) => {
  const [dateFilter, setDateFilter] = useAtom(dateAtom);
  const [ownDateFilter, setOwnDateFilter] = useState(dateFilter);
  const [customerFilter, setCustomerFilter] = useAtom(customerAtom);
  const [ownCustomerFilter, setOwnCustomerFilter] = useState(customerFilter);
  const [delegateFilter, setDelegateFilter] = useAtom(delegateAtom);
  const [ownDelegateFilter, setOwnDelegateFilter] = useState(delegateFilter);

  useEffect(() => {
    setOwnDateFilter(dateFilter);
    setOwnCustomerFilter(customerFilter);
    setOwnDelegateFilter(delegateFilter);
  }, [customerFilter, dateFilter, delegateFilter]);

  const isApplyEnabled = useMemo(() => {
    return (
      !isEqual(dateFilter, ownDateFilter) ||
      !isEqual(customerFilter, ownCustomerFilter) ||
      !isEqual(delegateFilter, ownDelegateFilter)
    );
  }, [dateFilter, ownDateFilter, customerFilter, ownCustomerFilter, delegateFilter, ownDelegateFilter]);

  const handleApply = useCallback(() => {
    setDateFilter(ownDateFilter);
    setCustomerFilter(ownCustomerFilter);
    setDelegateFilter(ownDelegateFilter);
  }, [ownCustomerFilter, ownDateFilter, ownDelegateFilter, setCustomerFilter, setDateFilter, setDelegateFilter]);

  const handleClear = useCallback(() => {
    setDateFilter(undefined);
    setCustomerFilter([]);
    setDelegateFilter([]);
  }, [setCustomerFilter, setDateFilter, setDelegateFilter]);

  const handleReset = useCallback(() => {
    setOwnDateFilter(dateFilter);
    setOwnCustomerFilter(customerFilter);
    setOwnDelegateFilter(delegateFilter);
  }, [customerFilter, dateFilter, delegateFilter]);

  return {
    customerFilter: ownCustomerFilter,
    dateFilter: ownDateFilter,
    delegateFilter: ownDelegateFilter,
    handleApply,
    handleClear,
    handleReset,
    isApplyEnabled,
    setCustomerFilter: setOwnCustomerFilter,
    setDateFilter: setOwnDateFilter,
    setDelegateFilter: setOwnDelegateFilter,
  };
};

type Props = {
  customerAtom: WritableAtom<string[], [SetStateAction<string[]>], void>;
  dateAtom: WritableAtom<DateRange | undefined, [SetStateAction<DateRange | undefined>], void>;
  delegateAtom: WritableAtom<string[], [SetStateAction<string[]>], void>;
};

const AccompanyingNotesListFiltersButton: FC<Props> = ({customerAtom, dateAtom, delegateAtom}) => {
  const [open, setOpen] = useState(false);
  const {
    customerFilter,
    dateFilter,
    delegateFilter,
    handleApply,
    handleClear,
    handleReset,
    isApplyEnabled,
    setCustomerFilter,
    setDateFilter,
    setDelegateFilter,
  } = useInventoryFilters(dateAtom, customerAtom, delegateAtom);

  return (
    <FiltersSheet
      isApplyEnabled={isApplyEnabled}
      onApply={handleApply}
      onClear={handleClear}
      onClick={() => setOpen(true)}
      onOpenChange={(isOpen) => {
        if (!isOpen) handleReset();
      }}
    >
      {open && (
        <OwnFilters
          customerFilters={{filters: customerFilter, setFilters: setCustomerFilter}}
          dateFilters={{filters: dateFilter, setFilters: setDateFilter}}
          delegateFilters={{filters: delegateFilter, setFilters: setDelegateFilter}}
        />
      )}
    </FiltersSheet>
  );
};

export default AccompanyingNotesListFiltersButton;
