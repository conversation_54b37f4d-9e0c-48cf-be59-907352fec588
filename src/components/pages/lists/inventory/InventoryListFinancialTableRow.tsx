import {FC, memo, Ref, useCallback} from 'react';

import Jo<PERSON> from 'joi';
import {useTranslations} from 'next-intl';
import {SWRInfiniteKeyedMutator} from 'swr/infinite';

import {PopoverCombobox} from '@/components/ui/Combobox';
import {PopoverInput} from '@/components/ui/Input';
import InventoryItemLink from '@/components/ui/special/InventoryItemLink/InventoryItemLink';
import {TableCell, TableRow} from '@/components/ui/Table';
import {useHasPermission} from '@/hooks/helpers/useHasPermission';
import useItemActions from '@/hooks/useItemActions';
import {Category} from '@/types/account';
import {InventoryItem, InventoryMaterialGood} from '@/types/inventory';
import {formatCurrency, formatNumber} from '@/utils/format';

type Props = {
  categories: Category[];
  item: InventoryMaterialGood;
  loadMoreRef: Ref<HTMLTableRowElement> | undefined;
  mutate: SWRInfiniteKeyedMutator<any[]>;
};

const InventoryListFinancialTable: FC<Props> = ({categories, item, loadMoreRef, mutate}) => {
  const {getItem, updateItem} = useItemActions();
  const {hasPermission, isLoading} = useHasPermission();
  const t = useTranslations();

  const handleUpdateItem = useCallback(
    async (value: Partial<InventoryItem>) => {
      const newItem = await getItem(item.materialGoodId);

      if (newItem) updateItem(newItem.id, {...newItem, ...value}).then(() => mutate());
    },
    [getItem, item.materialGoodId, mutate, updateItem],
  );

  if (isLoading) return null;

  return (
    <TableRow className='h-[61px]' ref={loadMoreRef}>
      <TableCell>
        <InventoryItemLink copyCode fullscreen item={{...item, id: item.materialGoodId}} />
      </TableCell>
      <TableCell>
        <PopoverCombobox
          className='w-fit'
          containerClassName='max-w-none'
          onChange={(value) => handleUpdateItem({category: {...item.category, id: value}})}
          open
          options={categories.map((category) => ({id: category.id, value: category.details.name}))}
          placeholder={t('category')}
          renderNotFound={() => t('category not found')}
          searchPlaceholder={t('search category')}
          value={item.category?.id}
        >
          {item.category?.name || '-'}
        </PopoverCombobox>
      </TableCell>
      <TableCell className='text-right'>{formatNumber(item.quantity)}</TableCell>
      <TableCell className='text-right'>{formatCurrency(item.cost)}</TableCell>
      <TableCell className='text-right'>{formatCurrency(item.stockValue)}</TableCell>
      <TableCell className='text-right'>
        {hasPermission('financial', 'inventory') ? (
          <PopoverInput
            className='w-fit justify-end'
            defaultValue={item.sellingPrice.amount.toString()}
            label={`${t('selling price')} (${item.sellingPrice.currency})`}
            onChange={(value) => {
              handleUpdateItem({sellPrice: {amount: Number(value), currency: item.sellingPrice.currency}});
            }}
            type='number'
            validation={Joi.number().required()}
          >
            {formatCurrency(item.sellingPrice)}
          </PopoverInput>
        ) : (
          formatCurrency(item.sellingPrice)
        )}
      </TableCell>
      <TableCell className='text-right'>
        {formatCurrency(
          item.produced
            ? {
                amount: item.sellingPrice?.amount - item.cost?.amount,
                currency: item.sellingPrice?.currency,
              }
            : undefined,
        )}
      </TableCell>
      <TableCell className='text-right'>
        {hasPermission('financial', 'inventory') ? (
          <PopoverInput
            className='w-fit justify-end'
            defaultValue={Number((Number(item.margin) * 100).toFixed(2)).toString()}
            label={`${t('markup')} (%)`}
            onChange={(value) => {
              handleUpdateItem({
                sellPrice: {
                  amount: item.cost.amount + (item.cost.amount * Number(value)) / 100,
                  currency: item.sellingPrice.currency,
                },
              });
            }}
            type='number'
            validation={Joi.number().required()}
          >
            {formatNumber(Number((Number(item.margin) * 100).toFixed(2)))}%
          </PopoverInput>
        ) : (
          <>{formatNumber(Number((Number(item.margin) * 100).toFixed(2)))}%</>
        )}
      </TableCell>
      <TableCell className='text-right'>
        {formatCurrency({
          amount: item.quantity * item.sellingPrice?.amount,
          currency: item.sellingPrice?.currency,
        })}
      </TableCell>
      <TableCell className='text-right'>
        {formatCurrency(
          item.sellingPrice?.amount > 0
            ? {
                amount: item.quantity * (item.sellingPrice?.amount - item.cost?.amount),
                currency: item.sellingPrice?.currency,
              }
            : undefined,
        )}
      </TableCell>
    </TableRow>
  );
};

export default memo(InventoryListFinancialTable);
