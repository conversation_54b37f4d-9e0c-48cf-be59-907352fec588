import {FC, useEffect} from 'react';

import {useAtom} from 'jotai';
import {useAtomValue} from 'jotai';
import {PlusIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';

import {Button} from '@/components/ui/Button';
import {Link} from '@/components/ui/special/Link';
import {Page, PageContent, PageFilters, PageHeader, PageHeaderTitle, PageTitle} from '@/components/ui/special/Page';
import {StoreSearchInput} from '@/components/ui/special/StoreSearchInput';
import {Tabs, TabsList, TabsTrigger} from '@/components/ui/Tabs';
import {useHasPermission} from '@/hooks/helpers/useHasPermission';
import useInfiniteScroll from '@/hooks/helpers/useInfiniteScroll';
import useCategories from '@/hooks/useCategories';
import useInventory from '@/hooks/useInventory';

import InventoryListFilters from './InventoryListFilters';
import InventoryListFiltersButton from './InventoryListFiltersButton';
import InventoryListFinancialTable from './InventoryListFinancialTable';
import InventoryListInventoryTable from './InventoryListInventoryTable';
import {
  inventoryItemsCategoryFilterAtom,
  inventoryItemsCriticalQueryAtom,
  inventoryItemsInventoryUnitFilterAtom,
  inventoryItemsOnlyOnStockQueryAtom,
  inventoryItemsSearchQueryAtom,
  InventoryView,
  inventoryViewAtom,
} from './inventoryListStore';

const InventoryList: FC = () => {
  const t = useTranslations();
  const {hasPermission, isLoading} = useHasPermission();
  const {categories, isLoading: categoriesIsLoading} = useCategories();
  const [view, setView] = useAtom(inventoryViewAtom);
  const critical = useAtomValue(inventoryItemsCriticalQueryAtom);
  const onlyOnStockItems = useAtomValue(inventoryItemsOnlyOnStockQueryAtom);
  const search = useAtomValue(inventoryItemsSearchQueryAtom);
  const inventoryUnitIds = useAtomValue(inventoryItemsInventoryUnitFilterAtom);
  const categoryIds = useAtomValue(inventoryItemsCategoryFilterAtom);
  const {inventory, isValidating, loadMore, mutate} = useInventory({
    categoryIds,
    critical,
    inventoryUnitIds,
    onlyOnStockItems,
    search,
  });

  const loadMoreRef = useInfiniteScroll<HTMLTableRowElement>(loadMore);

  useEffect(() => {
    mutate();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  if (isLoading || categoriesIsLoading) return null;

  return (
    <Page>
      <PageTitle>{`${t(view)} - ${t('items')}`}</PageTitle>
      <PageHeader>
        <PageHeaderTitle>{t('items')}</PageHeaderTitle>
        <Tabs onValueChange={(newView) => setView(newView as InventoryView)} value={view} variant='menu'>
          <TabsList variant='menu'>
            <TabsTrigger value={InventoryView.inventory} variant='menu'>
              {t('inventory')}
            </TabsTrigger>
            {hasPermission('financial', 'inventory') && (
              <TabsTrigger value={InventoryView.financial} variant='menu'>
                {t('financial')}
              </TabsTrigger>
            )}
          </TabsList>
        </Tabs>
        <div className='grow' />
        <StoreSearchInput
          autoFocus
          div={{className: 'grow'}}
          placeholder={t('search by name, sku')}
          store={inventoryItemsSearchQueryAtom}
        />
        <InventoryListFiltersButton
          categoryIdsAtom={inventoryItemsCategoryFilterAtom}
          inventoryUnitIdsAtom={inventoryItemsInventoryUnitFilterAtom}
          mutate={mutate}
        />
        <Button asChild>
          <Link href={`/inventory/items/new`}>
            <PlusIcon />
            {t('add item')}
          </Link>
        </Button>
      </PageHeader>
      <div className='flex items-center'>
        <PageFilters className='flex w-full justify-between'>
          <InventoryListFilters mutate={mutate} />
        </PageFilters>
      </div>
      <PageContent>
        {view === InventoryView.inventory && (
          <InventoryListInventoryTable
            categories={categories}
            isValidating={isValidating}
            items={inventory}
            loadMoreRef={loadMoreRef}
            mutate={mutate}
          />
        )}
        {view === InventoryView.financial && (
          <InventoryListFinancialTable
            categories={categories}
            isValidating={isValidating}
            items={inventory}
            loadMoreRef={loadMoreRef}
            mutate={mutate}
          />
        )}
      </PageContent>
    </Page>
  );
};

export default InventoryList;
