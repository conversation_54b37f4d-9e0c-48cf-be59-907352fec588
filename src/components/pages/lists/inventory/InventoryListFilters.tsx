import {FC, useMemo} from 'react';

import {useAtom} from 'jotai';
import {find, sortBy} from 'lodash';
import {XIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';
import {SWRInfiniteKeyedMutator} from 'swr/infinite';

import {Badge} from '@/components/ui/Badge';
import {Checkbox} from '@/components/ui/Checkbox';
import {Label, WithLabel} from '@/components/ui/Label';
import useCategories from '@/hooks/useCategories';
import useInventoryUnits from '@/hooks/useInventoryUnits';

import {
  inventoryItemsCategoryFilterAtom,
  inventoryItemsCriticalQueryAtom,
  inventoryItemsInventoryUnitFilterAtom,
  inventoryItemsOnlyOnStockQueryAtom,
} from './inventoryListStore';

type Props = {
  mutate: SWRInfiniteKeyedMutator<any[]>;
};

const InventoryListFilters: FC<Props> = ({mutate}) => {
  const [critical, setCritical] = useAtom(inventoryItemsCriticalQueryAtom);
  const [onlyOnStock, setOnlyOnStock] = useAtom(inventoryItemsOnlyOnStockQueryAtom);
  const {inventoryUnits, isLoading} = useInventoryUnits();
  const {categories, isLoading: categoriesIsLoading} = useCategories();
  const [inventoryUnitFilters, setInventoryUnitFilters] = useAtom(inventoryItemsInventoryUnitFilterAtom);
  const [categoryFilters, setCategoryFilters] = useAtom(inventoryItemsCategoryFilterAtom);
  const t = useTranslations();

  const actualFilters = useMemo(() => {
    if (isLoading || categoriesIsLoading) return [];

    return sortBy(
      [
        ...(inventoryUnitFilters || []).map((unitId) => ({
          action: setInventoryUnitFilters,
          filterName: t('inventory unit'),
          text: find(inventoryUnits, (unit) => unit.id === unitId)?.name,
          value: unitId,
        })),
        ...(categoryFilters || []).map((categoryId) => ({
          action: setCategoryFilters,
          filterName: t('category'),
          text: find(categories, (category) => category.id === categoryId)?.details?.name,
          value: categoryId,
        })),
      ],
      'text',
    );
  }, [
    categories,
    categoriesIsLoading,
    categoryFilters,
    inventoryUnitFilters,
    inventoryUnits,
    isLoading,
    setCategoryFilters,
    setInventoryUnitFilters,
    t,
  ]);

  return (
    <>
      <div className='flex flex-wrap items-center gap-2'>
        {actualFilters.map((filter) => (
          <Badge
            className='group cursor-pointer gap-1 hover:bg-input'
            key={filter.value}
            onClick={() => filter.action((prev) => prev?.filter((id) => id !== filter.value))}
          >
            {filter.filterName}: {filter.text}
            <XIcon className='mt-0.5 size-4' />
          </Badge>
        ))}
        {!isLoading && actualFilters.length === 0 && t('no filters applied')}
      </div>
      <div className='flex flex-col justify-around'>
        <WithLabel className='shrink-0' direction='horizontal'>
          <Checkbox
            checked={critical}
            id='critical'
            onCheckedChange={(checked) => {
              mutate();
              setCritical(Boolean(checked));
            }}
          />
          <Label htmlFor='critical'>{t('show items below the preset min value')}</Label>
        </WithLabel>
        <WithLabel className='shrink-0' direction='horizontal'>
          <Checkbox
            checked={onlyOnStock}
            id='empty'
            onCheckedChange={(checked) => {
              mutate();
              setOnlyOnStock(Boolean(checked));
            }}
          />
          <Label htmlFor='empty'>{t('hide items with quantity zero')}</Label>
        </WithLabel>
      </div>
    </>
  );
};

export default InventoryListFilters;
