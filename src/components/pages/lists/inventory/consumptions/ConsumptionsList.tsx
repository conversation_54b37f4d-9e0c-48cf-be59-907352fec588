import {format} from 'date-fns';
import {useAtomValue} from 'jotai';
import {CloudDownloadIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';

import {Button} from '@/components/ui/Button';
import {DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger} from '@/components/ui/DropdownMenu';
import {Link} from '@/components/ui/special/Link';
import {Page, PageContent, PageFilters, PageHeader, PageHeaderTitle, PageTitle} from '@/components/ui/special/Page';
import {NextPageWithLayout} from '@/types/global';
import {getQueryString} from '@/utils/common';

import ConsumptionsListFilters from './ConsumptionsListFilters';
import ConsumptionsListFiltersButton from './ConsumptionsListFiltersButton';
import {consumptionsDateFilterAtom} from './consumptionsListStore';
import ConsumptionsListTable from './ConsumptionsListTable';

const ConsumptionsList: NextPageWithLayout = () => {
  const dateFilter = useAtomValue(consumptionsDateFilterAtom);
  const t = useTranslations();

  return (
    <Page>
      <PageTitle>{t('consumptions')}</PageTitle>
      <PageHeader>
        <PageHeaderTitle>{t('consumptions')}</PageHeaderTitle>
        <div className='grow' />
        <ConsumptionsListFiltersButton dateAtom={consumptionsDateFilterAtom} />
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant='secondary'>
              <CloudDownloadIcon /> {t('download')}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuItem asChild>
              <Link
                href={`/api/manufacturing/material-issue-notes/list?mediaType=application/pdf%2Bzip&${getQueryString({
                  end: format(dateFilter?.to || new Date(), 'yyyy-MM-dd'),
                  start: format(dateFilter?.from || 0, 'yyyy-MM-dd'),
                })}`}
              >
                {t('pdf')}
              </Link>
            </DropdownMenuItem>
            <DropdownMenuItem asChild>
              <Link
                href={`/api/manufacturing/material-issue-notes/list?mediaType=application/bonconsum-winmentor%2Bzip&${getQueryString(
                  {
                    end: format(dateFilter?.to || new Date(), 'yyyy-MM-dd'),
                    start: format(dateFilter?.from || 0, 'yyyy-MM-dd'),
                  },
                )}`}
              >
                {t('winmentor')}
              </Link>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </PageHeader>
      <PageFilters>
        <ConsumptionsListFilters dateAtom={consumptionsDateFilterAtom} />
      </PageFilters>
      <PageContent>
        <ConsumptionsListTable />
      </PageContent>
    </Page>
  );
};

export default ConsumptionsList;
