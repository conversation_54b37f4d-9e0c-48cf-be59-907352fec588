import {FC} from 'react';

import {useAtomValue} from 'jotai';
import {useTranslations} from 'next-intl';

import {
  Table,
  TableBody,
  TableContainer,
  TableHead,
  TableHeadActions,
  TableHeader,
  TableRow,
} from '@/components/ui/Table';
import useHeight from '@/hooks/helpers/useHeight';
import useConsumptions from '@/hooks/useConsumptions';

import {consumptionsDateFilterAtom} from './consumptionsListStore';
import ConsumptionsListTableRow from './ConsumptionsListTableRow';

const ConsumptionsListTable: FC = () => {
  const dateFilter = useAtomValue(consumptionsDateFilterAtom);
  const {elementRef} = useHeight();
  const {consumptions, isLoading} = useConsumptions({end: dateFilter?.to, start: dateFilter?.from});
  const t = useTranslations();

  return (
    <TableContainer ref={elementRef}>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>{t('number')}</TableHead>
            <TableHead>{t('order')}</TableHead>
            <TableHead>{t('date')}</TableHead>
            <TableHeadActions />
          </TableRow>
        </TableHeader>
        <TableBody className='overflow-y-hidden' isValidating={isLoading}>
          {consumptions.map((consumption, index) => (
            <ConsumptionsListTableRow consumption={consumption} key={`${consumption.id}-row-${index}`} />
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );
};

export default ConsumptionsListTable;
