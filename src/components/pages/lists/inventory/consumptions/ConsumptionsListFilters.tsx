import {FC} from 'react';

import {SetStateAction, useAtom, WritableAtom} from 'jotai';
import {XIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';

import {Badge} from '@/components/ui/Badge';
import {DateRange} from '@/types/global';
import {formatDate} from '@/utils/format';

type Props = {
  dateAtom: WritableAtom<DateRange | undefined, [SetStateAction<DateRange | undefined>], void>;
};

const ConsumptionsListFilters: FC<Props> = ({dateAtom}) => {
  const [dateFilter, setDateFilter] = useAtom(dateAtom);
  const t = useTranslations();

  return (
    <div className='flex flex-wrap items-center gap-2'>
      {dateFilter && (
        <Badge className='group cursor-pointer gap-1 hover:bg-input' onClick={() => setDateFilter(undefined)}>
          {t('date range')}: {formatDate(dateFilter.from)} - {formatDate(dateFilter.to)}
          <XIcon className='mt-0.5 size-4' />
        </Badge>
      )}
      {!dateFilter && t('no filters applied')}
    </div>
  );
};

export default ConsumptionsListFilters;
