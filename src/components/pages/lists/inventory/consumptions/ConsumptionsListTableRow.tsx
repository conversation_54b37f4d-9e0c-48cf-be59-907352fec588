import {FC} from 'react';

import {CloudDownloadIcon} from 'lucide-react';

import {Button} from '@/components/ui/Button';
import {Link} from '@/components/ui/special/Link';
import {TableActions, TableCell, TableRow} from '@/components/ui/Table';
import {Consumption} from '@/types/inventory';
import {formatDate} from '@/utils/format';

type Props = {
  consumption: Consumption;
};

const ConsumptionsListTableRow: FC<Props> = ({consumption}) => {
  return (
    <TableRow>
      <TableCell>
        <Link className='hover:underline' href={`/inventory/consumptions/${consumption.id}`}>
          {consumption.number}
        </Link>
      </TableCell>
      <TableCell>
        <Link className='hover:underline' href={`/manufacturing/orders/${consumption.manufacturingOrder.id}`}>
          {consumption.manufacturingOrder.name}
        </Link>
      </TableCell>
      <TableCell>{formatDate(consumption.date)}</TableCell>
      <TableActions>
        <Button asChild size='icon' variant='none'>
          <Link href={`/api/manufacturing/material-issue-notes/${consumption.id}/details?mediaType=application/pdf`}>
            <CloudDownloadIcon />
          </Link>
        </Button>
      </TableActions>
    </TableRow>
  );
};

export default ConsumptionsListTableRow;
