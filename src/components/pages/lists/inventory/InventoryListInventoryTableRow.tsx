import {FC, memo, Ref, useCallback, useEffect, useMemo, useState} from 'react';

import {useAtom} from 'jotai';
import {ArrowDownRightIcon, ArrowUpRightIcon, HistoryIcon, PencilIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';
import {SWRInfiniteKeyedMutator} from 'swr/infinite';

import {adjustmentItemsAtom} from '@/components/pages/details/inventory/adjustments/adjustmentStore';
import {Badge} from '@/components/ui/Badge';
import {Button} from '@/components/ui/Button';
import {PopoverCombobox} from '@/components/ui/Combobox';
import {NumberInput} from '@/components/ui/Input';
import {Popover, PopoverClose, PopoverContent, PopoverTrigger} from '@/components/ui/Popover';
import InventoryItemLink from '@/components/ui/special/InventoryItemLink/InventoryItemLink';
import {ItemHistoryModal} from '@/components/ui/special/ItemHistoryModal';
import {Link} from '@/components/ui/special/Link';
import {OrderButton} from '@/components/ui/special/OrderButton';
import {WithoutEmpty} from '@/components/ui/special/WithoutEmpty';
import {TableActions, TableCell, TableRow} from '@/components/ui/Table';
import useItemActions from '@/hooks/useItemActions';
import {Category} from '@/types/account';
import {InventoryMaterialGood} from '@/types/inventory';
import {formatNumber} from '@/utils/format';

type Props = {
  categories: Category[];
  item: InventoryMaterialGood;
  loadMoreRef: Ref<HTMLTableRowElement> | undefined;
  mutate: SWRInfiniteKeyedMutator<any[]>;
  style?: React.CSSProperties;
};

const InventoryListInventoryTableRow: FC<Props> = ({categories, item, loadMoreRef, mutate, style}) => {
  const {getItem, updateItem} = useItemActions();
  const t = useTranslations();
  const [adjustmentItems, setAdjustmentItems] = useAtom(adjustmentItemsAtom);
  const [quantity, setQuantity] = useState(item.quantity);

  useEffect(() => {
    setQuantity(item.quantity);
  }, [item.quantity]);

  const handleUpdate = useCallback(
    async (value: Partial<InventoryMaterialGood>) => {
      const newItem = await getItem(item.materialGoodId);
      if (newItem) updateItem(newItem.id, {...newItem, ...value}).then(() => mutate());
    },
    [getItem, item.materialGoodId, mutate, updateItem],
  );

  const adjustment = useMemo(
    () =>
      adjustmentItems.find(
        (adjustment) => adjustment.id === item.materialGoodId && adjustment.unit.id === item.inventoryUnit?.id,
      ),
    [adjustmentItems, item.materialGoodId, item.inventoryUnit?.id],
  );

  useEffect(() => {
    if (adjustment) setQuantity(adjustment.quantity);
  }, [adjustment]);

  return (
    <TableRow className='h-[61px]' ref={loadMoreRef} style={style}>
      <TableCell>
        <InventoryItemLink copyCode fullscreen item={{...item, id: item.materialGoodId}} />
      </TableCell>
      <TableCell>{item.inventoryUnit.name}</TableCell>
      <TableCell>
        <WithoutEmpty value={item.lastOrderedFrom?.name}>
          <Link className='hover:underline' href={`/suppliers/${item.lastOrderedFrom?.id}`}>
            {item.lastOrderedFrom?.name}
          </Link>
        </WithoutEmpty>
      </TableCell>
      <TableCell>
        <PopoverCombobox
          className='w-fit'
          containerClassName='max-w-none'
          onChange={(value) => handleUpdate({category: {...item.category, id: value}})}
          open
          options={categories.map((category) => ({id: category.id, value: category.details.name}))}
          placeholder={t('category')}
          renderNotFound={() => t('category not found')}
          searchPlaceholder={t('search category')}
          value={item.category?.id}
        >
          {item.category?.name || '-'}
        </PopoverCombobox>
      </TableCell>
      <TableCell className='text-right'>
        <Popover>
          <PopoverTrigger asChild>
            <Button className='group justify-normal' size='none' variant='none'>
              <div className='relative flex items-center'>
                {formatNumber(adjustment?.quantity || item.quantity)}{' '}
                {t(`unit.name.${item.measurementUnit.name}` as any)}{' '}
                {adjustment && adjustment.quantity !== item.quantity && (
                  <>
                    {adjustment.quantity > item.quantity ? (
                      <ArrowUpRightIcon className='text-alert-green' />
                    ) : (
                      <ArrowDownRightIcon className='text-alert-red' />
                    )}
                  </>
                )}
                <div className='absolute -right-1 top-1/2 hidden -translate-y-1/2 translate-x-full group-hover:block'>
                  <PencilIcon className='size-4' />
                </div>
              </div>
            </Button>
          </PopoverTrigger>
          <PopoverContent align='end' className='flex max-w-none flex-col items-center gap-4'>
            {t('adjust stock')}
            <NumberInput min={0} onChange={setQuantity} value={quantity} />
            <PopoverClose asChild>
              <Button
                onClick={() => {
                  setAdjustmentItems((prev) => [
                    ...prev.filter(
                      (adjustment) =>
                        !(adjustment.id === item.materialGoodId && adjustment.unit.id === item.inventoryUnit.id),
                    ),
                    ...(quantity !== item.quantity
                      ? [
                          {
                            ...item,
                            available: item.quantity,
                            id: item.materialGoodId,
                            quantity,
                            unit: item.inventoryUnit,
                          },
                        ]
                      : []),
                  ]);
                }}
              >
                {t('adjust')}
              </Button>
            </PopoverClose>
          </PopoverContent>
        </Popover>
      </TableCell>
      <TableCell className='text-right'>
        <Badge variant={item.available < item.criticalOnHand ? 'error' : 'none'}>
          {formatNumber(item.available)} {t(`unit.name.${item.measurementUnit.name}` as any)}
        </Badge>
      </TableCell>
      <TableCell className='text-right'>{formatNumber(item.committed)}</TableCell>
      <TableCell className='text-right'>{formatNumber(item.incoming)}</TableCell>
      <TableCell className='text-right'>{formatNumber(item.criticalOnHand)}</TableCell>
      <TableCell className='text-right'>
        <ItemHistoryModal item={{...item, id: item.materialGoodId}}>
          <Button size='icon' variant='none'>
            <HistoryIcon className='size-5' />
          </Button>
        </ItemHistoryModal>
      </TableCell>
      <TableActions>
        <OrderButton forTable item={{...item, id: item.materialGoodId}} quantity={item.unitOfProduction} />
      </TableActions>
    </TableRow>
  );
};

export default memo(InventoryListInventoryTableRow);
