import {FC} from 'react';

import {useTranslations} from 'next-intl';

import {Table, TableBody, TableContainer, TableHead, TableHeader, TableRow} from '@/components/ui/Table';
import {useHasPermission} from '@/hooks/helpers/useHasPermission';
import useHeight from '@/hooks/helpers/useHeight';
import useInventoryAdjustments from 'hooks/useInventoryAdjustments';

import AdjustmentsListTableRow from './AdjustmentsListTableRow';

const AdjustmentsListTable: FC = () => {
  const {elementRef} = useHeight();
  const {adjustments, isLoading} = useInventoryAdjustments();
  const {hasPermission, isLoading: permissionIsLoading} = useHasPermission();
  const t = useTranslations();

  return (
    <TableContainer ref={elementRef}>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>{t('ia#')}</TableHead>
            <TableHead>{t('date')}</TableHead>
            <TableHead>{t('adjustment type')}</TableHead>
            <TableHead>{t('reason for adjustment')}</TableHead>
            {hasPermission('financial', 'inventory') && <TableHead className='text-right'>{t('value')}</TableHead>}
          </TableRow>
        </TableHeader>
        <TableBody className='overflow-y-hidden' isValidating={isLoading || permissionIsLoading}>
          {adjustments?.map((adjustment, index) => (
            <AdjustmentsListTableRow adjustment={adjustment} key={`${adjustment.id}-row-${index}`} />
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );
};

export default AdjustmentsListTable;
