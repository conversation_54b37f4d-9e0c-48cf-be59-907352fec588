import {BlocksIcon, PlusIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';

import {Button} from '@/components/ui/Button';
import {Link} from '@/components/ui/special/Link';
import {Page, PageContent, PageHeader, PageHeaderTitle, PageTitle} from '@/components/ui/special/Page';
import {NextPageWithLayout} from '@/types/global';

import AdjustmentsListTable from './AdjustmentsListTable';

const AdjustmentsList: NextPageWithLayout = () => {
  const t = useTranslations();

  return (
    <Page>
      <PageTitle>{t('adjustments')}</PageTitle>
      <PageHeader>
        <PageHeaderTitle>{t('adjustments')}</PageHeaderTitle>
        <div className='grow' />
        {/*<CustomersListFiltersButton />*/}
        <Button asChild variant='secondary'>
          <Link
            className='group inline-flex h-10 shrink-0 items-center justify-center gap-1 whitespace-nowrap rounded-lg border border-border bg-background px-4 text-sm text-foreground ring-0 transition-colors hover:bg-gray-200 focus-visible:outline-hidden disabled:pointer-events-none disabled:opacity-50'
            href={'/inventory/adjustments/move'}
          >
            <BlocksIcon />
            {t('transfer between units')}
          </Link>
        </Button>
        <Button asChild>
          <Link href={'/inventory/adjustments/new'}>
            <PlusIcon />
            {t('add adjustment')}
          </Link>
        </Button>
      </PageHeader>
      {/*<PageFilters className='flex justify-between'>*/}
      {/*  <CustomersListFilters />*/}
      {/*</PageFilters>*/}
      <PageContent>
        <AdjustmentsListTable />
      </PageContent>
    </Page>
  );
};

export default AdjustmentsList;
