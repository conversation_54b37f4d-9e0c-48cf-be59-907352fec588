import {FC} from 'react';

import {useTranslations} from 'next-intl';

import {Link} from '@/components/ui/special/Link';
import {TableCell, TableRow} from '@/components/ui/Table';
import {useHasPermission} from '@/hooks/helpers/useHasPermission';
import {Adjustment, AdjustmentType} from '@/types/inventory';
import {formatCurrency, formatDate} from '@/utils/format';

type Props = {
  adjustment: Adjustment;
};

const AdjustmentsListTableRow: FC<Props> = ({adjustment}) => {
  const t = useTranslations();
  const {hasPermission, isLoading} = useHasPermission();

  if (isLoading) return null;

  return (
    <TableRow>
      <TableCell>
        <Link
          className='hover:underline'
          href={`/inventory/adjustments/${adjustment.type === AdjustmentType.TYPE_CHANGE ? 'move/' : ''}${adjustment.id}`}
        >
          {adjustment.number}
        </Link>
      </TableCell>
      <TableCell>{formatDate(adjustment.createTime)}</TableCell>
      <TableCell>{t(adjustment.type)}</TableCell>
      <TableCell>{adjustment.reason}</TableCell>
      {hasPermission('financial', 'inventory') && (
        <TableCell className='text-right'>{formatCurrency(adjustment.totalValue)}</TableCell>
      )}
    </TableRow>
  );
};

export default AdjustmentsListTableRow;
