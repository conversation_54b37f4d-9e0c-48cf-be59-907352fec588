import {atom} from 'jotai';
import {atomWithStorage} from 'jotai/utils';

export enum InventoryView {
  financial = 'financial',
  inventory = 'inventory',
}

export const inventoryViewAtom = atom<InventoryView>(InventoryView.inventory);

export const inventoryItemsSearchQueryAtom = atom('');
export const inventoryItemsCriticalQueryAtom = atomWithStorage('inventoryItemCriticalFilter', false);
export const inventoryItemsOnlyOnStockQueryAtom = atomWithStorage('inventoryItemOnlyOnStockFilter', false);
export const inventoryItemsInventoryUnitFilterAtom = atomWithStorage<string[]>('inventoryItemUnitFilter', []);
export const inventoryItemsCategoryFilterAtom = atomWithStorage<string[]>('inventoryItemCategoryFilter', []);
