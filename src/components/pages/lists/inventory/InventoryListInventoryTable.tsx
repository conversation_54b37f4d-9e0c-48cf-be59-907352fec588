import {FC, Ref} from 'react';

import {useAtom} from 'jotai';
import {ClipboardIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';
import {SWRInfiniteKeyedMutator} from 'swr/infinite';

import {adjustmentItemsAtom} from '@/components/pages/details/inventory/adjustments/adjustmentStore';
import {Button} from '@/components/ui/Button';
import {Link} from '@/components/ui/special/Link';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableFooter,
  TableHead,
  TableHeadActions,
  TableHeader,
  TableRow,
} from '@/components/ui/Table';
import useHeight from '@/hooks/helpers/useHeight';
import {INVENTORY_PAGE_SIZE} from '@/hooks/useInventory';
import {Category} from '@/types/account';
import {InventoryMaterialGood} from '@/types/inventory';

import InventoryListInventoryTableRow from './InventoryListInventoryTableRow';

type Props = {
  categories: Category[];
  isValidating: boolean;
  items: InventoryMaterialGood[];
  loadMoreRef: Ref<HTMLTableRowElement>;
  mutate: SWRInfiniteKeyedMutator<any[]>;
};

const InventoryListInventoryTable: FC<Props> = ({categories, isValidating, items, loadMoreRef, mutate}) => {
  const {elementRef} = useHeight();
  const t = useTranslations();
  const [adjustmentItems, setAdjustmentItems] = useAtom(adjustmentItemsAtom);

  return (
    <TableContainer ref={elementRef}>
      <Table className='h-full'>
        <TableHeader>
          <TableRow>
            <TableHead>{t('name')}</TableHead>
            <TableHead>{t('inventory unit')}</TableHead>
            <TableHead>{t('supplier')}</TableHead>
            <TableHead>{t('category')}</TableHead>
            <TableHead className='text-right'>{t('current stock')}</TableHead>
            <TableHead className='text-right'>{t('available', {isPlural: 'true'})}</TableHead>
            <TableHead className='text-right'>{t('committed')}</TableHead>
            <TableHead className='text-right'>{t('incoming')}</TableHead>
            <TableHead className='text-right'>{t('critical on hand')}</TableHead>
            <TableHead className='text-right'>{t('history')}</TableHead>
            <TableHeadActions className='text-right'>{t('order')}</TableHeadActions>
          </TableRow>
        </TableHeader>
        <TableBody className='overflow-y-hidden' isValidating={isValidating}>
          {items?.map((item, index) => (
            <InventoryListInventoryTableRow
              categories={categories}
              item={item}
              key={`${item.id}-row-${index}`}
              loadMoreRef={index === items.length - INVENTORY_PAGE_SIZE ? loadMoreRef : undefined}
              mutate={mutate}
            />
          ))}
        </TableBody>
        {adjustmentItems.length > 0 && (
          <TableFooter className='sticky bottom-0 h-[66px] shadow-[0_-4px_8px_0_rgba(0,0,0,0.07843137255)]'>
            <TableRow>
              <TableCell colSpan={11}>
                <div className='flex items-center justify-between'>
                  <div className='flex items-center gap-2'>
                    <div className='shrink-0 rounded-full border border-border p-2'>
                      <ClipboardIcon className='text-alert-red' strokeWidth={1.5} />
                    </div>
                    <div className='flex flex-col gap-1'>
                      <div className=' font-medium '>
                        {t('stock adjustment required for count items', {count: adjustmentItems.length})}
                      </div>
                      <div className='text-xs font-medium text-border-foreground'>
                        {t('please review you adjustments to save all the changes')}
                      </div>
                    </div>
                  </div>
                  <div className='flex items-center gap-4'>
                    <Button onClick={() => setAdjustmentItems([])} variant='secondary'>
                      {t('cancel')}
                    </Button>
                    <Link href='/inventory/adjustments/new?inventory=true'>
                      <Button>{t('review adjustments')}</Button>
                    </Link>
                  </div>
                </div>
              </TableCell>
            </TableRow>
          </TableFooter>
        )}
      </Table>
    </TableContainer>
  );
};

export default InventoryListInventoryTable;
