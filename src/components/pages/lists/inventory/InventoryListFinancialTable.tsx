import {FC, Ref} from 'react';

import {useAtomValue} from 'jotai';
import {useTranslations} from 'next-intl';
import {SWRInfiniteKeyedMutator} from 'swr/infinite';

import {
  inventoryItemsCategoryFilterAtom,
  inventoryItemsCriticalQueryAtom,
  inventoryItemsInventoryUnitFilterAtom,
  inventoryItemsSearchQueryAtom,
} from '@/components/pages/lists/inventory/inventoryListStore';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableFooter,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/Table';
import useHeight from '@/hooks/helpers/useHeight';
import {INVENTORY_PAGE_SIZE} from '@/hooks/useInventory';
import useInventoryStats from '@/hooks/useInventoryStats';
import {Category} from '@/types/account';
import {InventoryMaterialGood} from '@/types/inventory';
import {formatCurrency, formatNumber} from '@/utils/format';

import InventoryListFinancialTableRow from './InventoryListFinancialTableRow';

type Props = {
  categories: Category[];
  isValidating: boolean;
  items: InventoryMaterialGood[];
  loadMoreRef: Ref<HTMLTableRowElement>;
  mutate: SWRInfiniteKeyedMutator<any[]>;
};

const InventoryListFinancialTable: FC<Props> = ({categories, isValidating, items, loadMoreRef, mutate}) => {
  const {elementRef} = useHeight();
  const critical = useAtomValue(inventoryItemsCriticalQueryAtom);
  const search = useAtomValue(inventoryItemsSearchQueryAtom);
  const inventoryUnitIds = useAtomValue(inventoryItemsInventoryUnitFilterAtom);
  const categoryIds = useAtomValue(inventoryItemsCategoryFilterAtom);
  const {inventoryStats} = useInventoryStats({categoryIds, critical, inventoryUnitIds, search});
  const t = useTranslations();

  return (
    <TableContainer ref={elementRef}>
      <Table className='h-full'>
        <TableHeader>
          <TableRow>
            <TableHead>{t('name')}</TableHead>
            <TableHead>{t('category')}</TableHead>
            <TableHead className='text-right'>{t('current stock')}</TableHead>
            <TableHead className='text-right'>{t('average cost')}</TableHead>
            <TableHead className='text-right'>{t('stock value')}</TableHead>
            <TableHead className='text-right'>{t('selling price')}</TableHead>
            <TableHead className='text-right'>{t('gross margin')}</TableHead>
            <TableHead className='text-right'>{t('markup')}</TableHead>
            <TableHead className='text-right'>{t('realizable sales')}</TableHead>
            <TableHead className='text-right'>{t('potential margin')}</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody className='overflow-y-hidden' isValidating={isValidating} ref={elementRef}>
          {items.map((item, index) => (
            <InventoryListFinancialTableRow
              categories={categories}
              item={item}
              key={`${item.id}-row-${index}`}
              loadMoreRef={index === items.length - INVENTORY_PAGE_SIZE ? loadMoreRef : undefined}
              mutate={mutate}
            />
          ))}
        </TableBody>
        <TableFooter className='sticky bottom-0 h-[60px] shadow-[0_-4px_8px_0_rgba(0,0,0,0.07843137255)]'>
          <TableRow>
            <TableCell colSpan={4}>
              <div className='flex flex-col'>
                <div className='text-xs font-medium uppercase text-border-foreground'>{t('total items')}</div>
                <div className='font-medium'>{formatNumber(inventoryStats.count)}</div>
              </div>
            </TableCell>
            <TableCell className='text-right'>
              <div className='flex flex-col'>
                <div className='text-xs font-medium uppercase text-border-foreground'>{t('total stock value')}</div>
                <div className='font-medium'>{formatCurrency(inventoryStats.value)}</div>
              </div>
            </TableCell>
            <TableCell className='text-right' colSpan={4}>
              <div className='flex flex-col'>
                <div className='text-xs font-medium uppercase text-border-foreground'>
                  {t('total realizable sales')}
                </div>
                <div className='font-medium'>{formatCurrency(inventoryStats.potentialRevenue)}</div>
              </div>
            </TableCell>
            <TableCell className='text-right' colSpan={4}>
              <div className='flex flex-col'>
                <div className='text-xs font-medium uppercase text-border-foreground'>
                  {t('total potential margin')}
                </div>
                <div className='font-medium'>{formatCurrency(inventoryStats.potentialProfit)}</div>
              </div>
            </TableCell>
          </TableRow>
        </TableFooter>
      </Table>
    </TableContainer>
  );
};

export default InventoryListFinancialTable;
