import {FC} from 'react';

import {sumBy} from 'lodash';
import {ReceiptIcon, ReceiptTextIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';

import {Badge} from '@/components/ui/Badge';
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from '@/components/ui/Select';
import {Link} from '@/components/ui/special/Link';
import {WithoutEmpty} from '@/components/ui/special/WithoutEmpty';
import {TableActions, TableCell, TableRow} from '@/components/ui/Table';
import {Tooltip, TooltipContent, TooltipTrigger} from '@/components/ui/Tooltip';
import {useHasPermission} from '@/hooks/helpers/useHasPermission';
import {useRouter} from '@/hooks/helpers/useRouter';
import {PurchaseStatus} from '@/types/global';
import {PurchaseOrder} from '@/types/purchases';
import {formatCurrency, formatDate, formatNumber} from '@/utils/format';

import {PurchasesView} from './purchasesListStore';

type Props = {
  order: PurchaseOrder;
  preview?: boolean;
  supplierId?: string;
  view: PurchasesView;
};

const PurchasesListTableRow: FC<Props> = ({order, preview, supplierId, view}) => {
  const {hasPermission, isLoading: permissionIsLoading} = useHasPermission();
  const {push} = useRouter();
  const t = useTranslations();

  if (permissionIsLoading) return null;

  return (
    <TableRow>
      <TableCell>
        <Link className='group flex flex-col whitespace-nowrap' href={`/purchases/orders/${order.id}`}>
          <div className='group-hover:underline'>{order.number}</div>
        </Link>
      </TableCell>
      <TableCell>{formatDate(order.createTime)}</TableCell>
      {!supplierId && (
        <TableCell>
          <Link className='group flex flex-col whitespace-nowrap' href={`/suppliers/${order.supplier.id}`}>
            <div className='group-hover:underline'>{order.supplier.name}</div>
          </Link>
        </TableCell>
      )}
      <TableCell className='text-right'>{formatNumber(sumBy(order.items, 'quantity'))}</TableCell>
      {view === PurchasesView.active && <TableCell>{formatDate(order.expectedDelivery)}</TableCell>}
      {view === PurchasesView.delivered && <TableCell>{formatDate(order.deliveredAt)}</TableCell>}
      <TableCell>
        <WithoutEmpty value={order?.managedBy?.id}>{order.managedBy?.name}</WithoutEmpty>
      </TableCell>
      {hasPermission('financial', 'purchases') && (
        <TableCell className='text-right'>{formatCurrency(order.totalAmount)}</TableCell>
      )}
      <TableCell className='text-right'>
        {(view !== PurchasesView.active || !hasPermission('financial', 'purchases') || preview) && (
          <Badge variant={order.status === PurchaseStatus.SUBMITTED ? 'info' : 'success'}>{t(order.status)}</Badge>
        )}
        {!preview && view === PurchasesView.active && hasPermission('financial', 'purchases') && (
          <div className='flex items-center justify-end'>
            <Select
              onValueChange={(value) => {
                if (value === PurchaseStatus.DELIVERED) push(`/purchases/orders/${order.id}/reception`);
              }}
              value={order.status}
            >
              <SelectTrigger
                className='w-fit self-end'
                size='badge-md'
                variant={order.status === PurchaseStatus.SUBMITTED ? 'badge-info' : 'badge-success'}
              >
                <SelectValue>{t(order.status)}</SelectValue>
              </SelectTrigger>
              <SelectContent>
                <SelectItem value={PurchaseStatus.SUBMITTED}>{t(PurchaseStatus.SUBMITTED)}</SelectItem>
                <SelectItem value={PurchaseStatus.DELIVERED}>{t(PurchaseStatus.DELIVERED)}</SelectItem>
              </SelectContent>
            </Select>
          </div>
        )}
      </TableCell>
      {hasPermission('financial', 'purchases') && (
        <TableActions className='text-right'>
          {view === PurchasesView.delivered && order.receptionReceipt?.id && (
            <Tooltip>
              <TooltipTrigger>
                <Link href={`/inventory/receptions/${order.receptionReceipt.id}`}>
                  <ReceiptTextIcon strokeWidth={1} />
                </Link>
              </TooltipTrigger>
              <TooltipContent>
                <div>{order.receptionReceipt?.name}</div>
              </TooltipContent>
            </Tooltip>
          )}
          <Tooltip>
            <TooltipTrigger>
              <Link href={`/purchases/orders/${order.id}/invoice`}>
                <ReceiptIcon strokeWidth={1} />
              </Link>
            </TooltipTrigger>
            <TooltipContent>{t('invoice')}</TooltipContent>
          </Tooltip>
        </TableActions>
      )}
    </TableRow>
  );
};

export default PurchasesListTableRow;
