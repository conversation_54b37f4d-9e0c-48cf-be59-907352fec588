import {FC, useMemo} from 'react';

import {useAtomValue} from 'jotai';
import {useTranslations} from 'next-intl';

import {
  Table,
  TableBody,
  TableContainer,
  TableHead,
  TableHeadActions,
  TableHeader,
  TableRow,
} from '@/components/ui/Table';
import {useHasPermission} from '@/hooks/helpers/useHasPermission';
import useHeight from '@/hooks/helpers/useHeight';
import usePurchaseOrders from '@/hooks/usePurchaseOrders';
import {PurchaseStatus} from '@/types/global';

import {purchasesSearchQueryAtom, PurchasesView} from './purchasesListStore';
import PurchasesListTableRow from './PurchasesListTableRow';

type Props = {
  disableHeight?: boolean;
  preview?: boolean;
  supplierId?: string;
  view: PurchasesView;
};

const PurchasesListTable: FC<Props> = ({disableHeight, preview, supplierId, view}) => {
  const {elementRef} = useHeight();
  const search = useAtomValue(purchasesSearchQueryAtom);
  const sort = useMemo(
    () => ([PurchasesView.all, PurchasesView.delivered].includes(view) ? ['-updateTime'] : []),
    [view],
  );
  const statuses = useMemo(
    () => (view === PurchasesView.delivered ? [PurchaseStatus.DELIVERED] : [PurchaseStatus.SUBMITTED]),
    [view],
  );
  const {isLoading, purchaseOrders} = usePurchaseOrders({search, sort: sort as any, statuses, supplierId});
  const {hasPermission, isLoading: permissionIsLoading} = useHasPermission();
  const t = useTranslations();

  return (
    <TableContainer ref={!disableHeight ? elementRef : null}>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>{t('order')}</TableHead>
            <TableHead>{t('order date')}</TableHead>
            {!supplierId && <TableHead>{t('supplier')}</TableHead>}
            <TableHead className='text-right'>{t('total items')}</TableHead>
            {view === PurchasesView.active && <TableHead>{t('expected by')}</TableHead>}
            {view === PurchasesView.delivered && <TableHead>{t('delivery date')}</TableHead>}
            <TableHead>{t('managed by')}</TableHead>
            {hasPermission('financial', 'purchases') && (
              <TableHead className='text-right'>{t('total value')}</TableHead>
            )}
            <TableHead className='text-right'>{t('order status')}</TableHead>
            {hasPermission('financial', 'purchases') && <TableHeadActions>{t('documents')}</TableHeadActions>}
          </TableRow>
        </TableHeader>
        <TableBody className='overflow-y-hidden' isValidating={isLoading || permissionIsLoading}>
          {purchaseOrders?.map((order, index) => (
            <PurchasesListTableRow
              key={`${order.id}-${index}`}
              order={order}
              preview={preview}
              supplierId={supplierId}
              view={view}
            />
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );
};

export default PurchasesListTable;
