import {FC} from 'react';

import {useAtomValue} from 'jotai';
import {sumBy} from 'lodash';
import {EllipsisVerticalIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';

import {Button} from '@/components/ui/Button';
import {DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger} from '@/components/ui/DropdownMenu';
import {Link} from '@/components/ui/special/Link';
import {TableActions, TableCell, TableRow} from '@/components/ui/Table';
import useWishlistActions from '@/hooks/useWishlistActions';
import {defaultCurrencyAtom} from '@/store/defaults';
import {Wishlist} from '@/types/purchases';
import {addDays} from '@/utils/common';
import {formatCurrency, formatDate, formatNumber} from '@/utils/format';

type Props = {
  wishlist: Wishlist;
};

const WishlistsListTableRow: FC<Props> = ({wishlist}) => {
  const defaultCurrency = useAtomValue(defaultCurrencyAtom);
  const {deleteWishlist} = useWishlistActions();
  const t = useTranslations();

  return (
    <TableRow>
      <TableCell>
        <Link className='flex flex-col whitespace-nowrap' href={`/purchases/wishlists/${wishlist.id}`}>
          <div className='hover:underline'>{wishlist.name}</div>
        </Link>
      </TableCell>
      <TableCell>{formatDate(addDays(wishlist.lastOrderDeliveredIn || 0))}</TableCell>
      <TableCell className='text-right'>
        {formatCurrency({
          amount: sumBy(wishlist.wishlistItems, 'totalPrice.amount'),
          currency: defaultCurrency,
        })}
      </TableCell>{' '}
      <TableCell className='text-right'>{formatNumber(sumBy(wishlist.wishlistItems, 'quantity'))}</TableCell>
      <TableActions className='py-1'>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button className='px-1.5' variant='ghost'>
              <EllipsisVerticalIcon />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align='end'>
            <DropdownMenuItem>
              <Link href={`/purchases/wishlists/${wishlist.id}/invoice`}>{t('preview')}</Link>
            </DropdownMenuItem>
            <DropdownMenuItem onSelect={() => deleteWishlist(wishlist)}>{t('delete')}</DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </TableActions>
    </TableRow>
  );
};

export default WishlistsListTableRow;
