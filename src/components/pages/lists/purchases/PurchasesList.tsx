import {FC, useEffect} from 'react';

import {useAtom} from 'jotai';
import {PlusIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';

import {Button} from '@/components/ui/Button';
import {Link} from '@/components/ui/special/Link';
import {Page, PageContent, PageHeader, PageHeaderTitle, PageTitle} from '@/components/ui/special/Page';
import {StoreSearchInput} from '@/components/ui/special/StoreSearchInput';
import {Tabs, TabsList, TabsTrigger} from '@/components/ui/Tabs';
import {useHasPermission} from '@/hooks/helpers/useHasPermission';
import {useRouter} from '@/hooks/helpers/useRouter';

import {purchasesSearchQueryAtom, PurchasesView, purchasesViewAtom} from './purchasesListStore';
import PurchasesListTable from './PurchasesListTable';
import WishlistsListTable from './WishlistsListTable';

const PurchasesList: FC = () => {
  const t = useTranslations();
  const {hasPermission, isLoading} = useHasPermission();
  const [view, setView] = useAtom(purchasesViewAtom);
  const {
    query: {tab},
  } = useRouter();

  useEffect(() => {
    if (!isLoading && view === PurchasesView.wishlist && !hasPermission('financial', 'purchases')) {
      setView(PurchasesView.active);
    }
  }, [hasPermission, isLoading, setView, view]);

  useEffect(() => {
    if (tab) setView(tab as PurchasesView);
  }, [setView, tab]);

  if (isLoading) return null;

  return (
    <Page>
      <PageTitle>{`${t('lists')} - ${t('purchases')}`}</PageTitle>
      <PageHeader>
        <PageHeaderTitle>{t('purchases')}</PageHeaderTitle>
        <Tabs onValueChange={(newView) => setView(newView as PurchasesView)} value={view} variant='menu'>
          <TabsList variant='menu'>
            {hasPermission('financial', 'purchases') && (
              <TabsTrigger value={PurchasesView.wishlist} variant='menu'>
                {t('lists')}
              </TabsTrigger>
            )}
            <TabsTrigger value={PurchasesView.active} variant='menu'>
              {t('active')}
            </TabsTrigger>
            <TabsTrigger value={PurchasesView.delivered} variant='menu'>
              {t('delivered')}
            </TabsTrigger>
          </TabsList>
        </Tabs>
        <div className='grow' />
        {view !== PurchasesView.wishlist && (
          <StoreSearchInput
            autoFocus
            div={{className: 'grow'}}
            placeholder={t('search by order number')}
            store={purchasesSearchQueryAtom}
          />
        )}
        {/*<PurchasesListFiltersButton />*/}
        {hasPermission('financial', 'purchases') && view === PurchasesView.wishlist && (
          <Button asChild>
            <Link href={`/purchases/wishlists/new`}>
              <PlusIcon />
              {t('add list')}
            </Link>
          </Button>
        )}
      </PageHeader>
      {/*<PageFilters className='flex justify-between'>*/}
      {/*  <PurchasesListFilters />*/}
      {/*</PageFilters>*/}
      <PageContent>
        {view === PurchasesView.wishlist && <WishlistsListTable />}
        {view !== PurchasesView.wishlist && <PurchasesListTable view={view} />}
      </PageContent>
    </Page>
  );
};

export default PurchasesList;
