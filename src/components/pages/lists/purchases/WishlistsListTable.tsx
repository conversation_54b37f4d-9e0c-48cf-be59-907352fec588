import {FC, useEffect} from 'react';

import {useSetAtom} from 'jotai';
import {useTranslations} from 'next-intl';

import {
  Table,
  TableBody,
  TableContainer,
  TableHead,
  TableHeadActions,
  TableHeader,
  TableRow,
} from '@/components/ui/Table';
import {useHasPermission} from '@/hooks/helpers/useHasPermission';
import useHeight from '@/hooks/helpers/useHeight';
import useWishlists from '@/hooks/useWishlists';

import {PurchasesView, purchasesViewAtom} from './purchasesListStore';
import WishlistsListTableRow from './WishlistsListTableRow';

const WishlistsListTable: FC = () => {
  const {elementRef} = useHeight();
  const {isLoading, wishlists} = useWishlists();
  const {hasPermission, isLoading: permissionIsLoading} = useHasPermission();
  const setView = useSetAtom(purchasesViewAtom);
  const t = useTranslations();

  useEffect(() => {
    if (!permissionIsLoading && !hasPermission('financial', 'purchases')) {
      setView(PurchasesView.active);
    }
  }, [hasPermission, isLoading, permissionIsLoading, setView]);

  if (permissionIsLoading || !hasPermission('financial', 'purchases')) return null;

  return (
    <TableContainer ref={elementRef}>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>{t('supplier')}</TableHead>
            <TableHead>{t('expected delivery')}</TableHead>
            <TableHead className='text-right'>{t('total price')}</TableHead>
            <TableHead className='text-right'>{t('total items')}</TableHead>
            <TableHeadActions />
          </TableRow>
        </TableHeader>
        <TableBody className='overflow-y-hidden' isValidating={isLoading || permissionIsLoading}>
          {wishlists?.map((wishlist, index) => (
            <WishlistsListTableRow key={`${wishlist.id}-${index}`} wishlist={wishlist} />
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );
};

export default WishlistsListTable;
