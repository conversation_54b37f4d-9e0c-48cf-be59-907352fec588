import {atom} from 'jotai';
import {atomFamily} from 'jotai/utils';

import {SaleType} from '@/types/sales';

export enum SalesView {
  active = 'active',
  all = '',
  canceled = 'canceled orders',
  delivered = 'delivered',
}

export const salesViewAtom = atomFamily((_key: SaleType) => atom<SalesView>(SalesView.active));

export const salesSearchQueryAtom = atomFamily((_key: SaleType) => atom<string>(''));
