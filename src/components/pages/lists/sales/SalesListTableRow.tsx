import {FC} from 'react';

import {useSortable} from '@dnd-kit/sortable';
import {CSS} from '@dnd-kit/utilities';
import {BookTextIcon, GripHorizontalIcon, ReceiptIcon, ReceiptTextIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';

import {Badge} from '@/components/ui/Badge';
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from '@/components/ui/Select';
import {Link} from '@/components/ui/special/Link';
import {WithoutEmpty} from '@/components/ui/special/WithoutEmpty';
import {TableActions, TableCell, TableRow} from '@/components/ui/Table';
import {Tooltip, TooltipContent, TooltipTrigger} from '@/components/ui/Tooltip';
import {useHasPermission} from '@/hooks/helpers/useHasPermission';
import useSaleOrderActions from '@/hooks/useSaleOrderActions';
import {SaleStatus} from '@/types/global';
import {SaleOrder, SaleType} from '@/types/sales';
import {classes} from '@/utils/common';
import {formatCurrency, formatDate, formatNumber} from '@/utils/format';

import {SalesView} from './salesListStore';

type Props = {
  customerId?: string;
  order: SaleOrder;
  preview?: boolean;
  saleType: SaleType;
  view: SalesView;
};

const SalesListTableRow: FC<Props> = ({customerId, order, preview, saleType, view}) => {
  const {attributes, isDragging, listeners, setNodeRef, transform, transition} = useSortable({id: order.id});
  const {hasPermission, isLoading} = useHasPermission();
  const {getAvailableStatuses, updateSaleOrderStatus} = useSaleOrderActions();
  const t = useTranslations();

  if (isLoading) return null;

  return (
    <TableRow
      className={classes(isDragging && 'bg-background shadow-xl hover:bg-background')}
      ref={setNodeRef}
      style={{
        ...(transform ? {transform: CSS.Transform.toString({...transform, scaleX: 1, scaleY: 1})} : {}),
        transition,
      }}
    >
      <TableCell className='inline-flex h-14 items-center gap-2'>
        {!preview && view === SalesView.active && order.status !== SaleStatus.SHIPPING ? (
          <span {...listeners} {...attributes}>
            <GripHorizontalIcon className='size-5 text-slate-900/40' />
          </span>
        ) : order.status === SaleStatus.SHIPPING ? (
          <div className='w-5' />
        ) : null}
        <Link
          className='group flex flex-col whitespace-nowrap'
          href={`/sales/${order.status === SaleStatus.IN_QUOTATION ? 'quotes' : 'orders'}/${order.id}`}
        >
          <div className='group-hover:underline'>{order.number}</div>
        </Link>
      </TableCell>
      <TableCell>{formatDate(order.createTime)}</TableCell>
      {!customerId && <TableCell>{order.customer.name}</TableCell>}
      {hasPermission('financial', 'sales') && (
        <TableCell className='text-right'>
          {formatCurrency({
            amount: order.subTotalAmount.amount - order.discountAmount.amount,
            currency: order.subTotalAmount.currency,
          })}
        </TableCell>
      )}
      <TableCell>
        {formatDate(
          view === SalesView.active
            ? saleType === SaleType.ORDER
              ? order.deliveryDeadline
              : order.offerExpiration
            : order.updateTime,
        )}
      </TableCell>
      <TableCell className='text-right'>
        <Badge variant={order.numberOfAvailableItems >= order.numberOfItems ? 'success' : 'error'}>
          {formatNumber(order.numberOfAvailableItems)}/{formatNumber(order.numberOfItems)}
        </Badge>
      </TableCell>
      <TableCell className='text-right'>
        {(preview || order.status === SaleStatus.CANCELED) && (
          <Badge
            variant={
              [SaleStatus.IN_QUOTATION, SaleStatus.SUBMITTED].includes(order.status)
                ? 'secondary'
                : [SaleStatus.DELIVERED, SaleStatus.SHIPPING].includes(order.status)
                  ? 'success'
                  : order.status === SaleStatus.PROCESSING
                    ? 'info'
                    : order.status === SaleStatus.CANCELED
                      ? 'error'
                      : order.status === SaleStatus.READY_TO_SHIP
                        ? 'warning'
                        : 'default'
            }
          >
            {t(order.status)}
          </Badge>
        )}
        {!preview && order.status !== SaleStatus.CANCELED && (
          <div className='flex items-center justify-end'>
            <Select onValueChange={(value) => updateSaleOrderStatus(order, value as SaleStatus)} value={order.status}>
              <SelectTrigger
                className='w-fit self-end'
                size='badge-md'
                variant={
                  [SaleStatus.IN_QUOTATION, SaleStatus.SUBMITTED].includes(order.status)
                    ? 'badge-secondary'
                    : [SaleStatus.DELIVERED, SaleStatus.SHIPPING].includes(order.status)
                      ? 'badge-success'
                      : order.status === SaleStatus.PROCESSING
                        ? 'badge-info'
                        : order.status === SaleStatus.READY_TO_SHIP
                          ? 'badge-warning'
                          : 'none'
                }
              >
                <SelectValue>{t(order.status)}</SelectValue>
              </SelectTrigger>
              <SelectContent>
                {getAvailableStatuses(order.status).map((status) => (
                  <SelectItem key={status} value={status}>
                    {t(status)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        )}
      </TableCell>
      {(saleType !== SaleType.QUOTE || view === SalesView.canceled) && hasPermission('financial', 'sales') && (
        <TableActions>
          <WithoutEmpty
            value={order.invoice?.id || order.proformaInvoice?.id || order.goodsAccompanyingNotes.length > 0}
          >
            <div className='flex flex-wrap justify-end gap-x-0.5 gap-y-1'>
              {order.proformaInvoice?.id && (
                <Tooltip>
                  <TooltipTrigger>
                    <Link href={`/sales/${saleType}/${order.id}/invoice/${order.proformaInvoice?.id}?isProforma=true`}>
                      <ReceiptTextIcon strokeWidth={1} />
                    </Link>
                  </TooltipTrigger>
                  <TooltipContent className='flex flex-col items-center'>
                    <div>{t('proforma')}</div>
                    <div>{order.proformaInvoice?.number}</div>
                  </TooltipContent>
                </Tooltip>
              )}
              {order.invoice?.id && (
                <Tooltip>
                  <TooltipTrigger>
                    <Link href={`/sales/${saleType}/${order.id}/invoice/${order.invoice?.id}`}>
                      <ReceiptIcon strokeWidth={1} />
                    </Link>
                  </TooltipTrigger>
                  <TooltipContent className='flex flex-col items-center'>
                    <div>{t('invoice')}</div>
                    <div>{order.invoice?.number}</div>
                  </TooltipContent>
                </Tooltip>
              )}
              {order.goodsAccompanyingNotes.map((note, index) => (
                <Tooltip key={`note-${note.id}-${index}`}>
                  <TooltipTrigger>
                    <Link href={`/sales/${saleType}/${order.id}/notes/${note.id}`}>
                      <BookTextIcon strokeWidth={1} />
                    </Link>
                  </TooltipTrigger>
                  <TooltipContent className='flex flex-col items-center'>
                    <div>{t('goods accompanying note')}</div>
                    <div>{note.number}</div>
                  </TooltipContent>
                </Tooltip>
              ))}
            </div>
          </WithoutEmpty>
        </TableActions>
      )}
    </TableRow>
  );
};

export default SalesListTableRow;
