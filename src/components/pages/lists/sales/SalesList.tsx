import {FC} from 'react';

import {useAtom} from 'jotai';
import {PlusIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';

import {Button} from '@/components/ui/Button';
import {Link} from '@/components/ui/special/Link';
import {Page, PageContent, PageHeader, PageHeaderTitle, PageTitle} from '@/components/ui/special/Page';
import {StoreSearchInput} from '@/components/ui/special/StoreSearchInput';
import {Tabs, TabsList, TabsTrigger} from '@/components/ui/Tabs';
import {SaleType} from '@/types/sales';

import {salesSearchQueryAtom, SalesView, salesViewAtom} from './salesListStore';
import SalesListTable from './SalesListTable';

type Props = {
  saleType: SaleType;
};

const SalesList: FC<Props> = ({saleType}) => {
  const t = useTranslations();
  const [view, setView] = useAtom(salesViewAtom(saleType));

  return (
    <Page>
      <PageTitle>{`${t(view as any)} - ${t(saleType)} - ${t('sales')}`}</PageTitle>
      <PageHeader>
        <PageHeaderTitle>{t(saleType)}</PageHeaderTitle>
        <Tabs onValueChange={(newView) => setView(newView as SalesView)} value={view} variant='menu'>
          <TabsList variant='menu'>
            <TabsTrigger value={SalesView.active} variant='menu'>
              {t('active')}
            </TabsTrigger>
            {saleType === SaleType.ORDER && (
              <TabsTrigger value={SalesView.delivered} variant='menu'>
                {t('delivered')}
              </TabsTrigger>
            )}
            <TabsTrigger value={SalesView.canceled} variant='menu'>
              {t('canceled orders')}
            </TabsTrigger>
          </TabsList>
        </Tabs>
        <div className='grow' />
        <StoreSearchInput
          autoFocus
          div={{className: 'grow'}}
          placeholder={t('search by order number')}
          store={salesSearchQueryAtom(saleType)}
        />
        {/*<SalesListFiltersButton />*/}
        <Button asChild>
          <Link href={`/sales/${saleType}/new`}>
            <PlusIcon />
            {t(saleType === SaleType.ORDER ? 'new order' : 'new quote')}
          </Link>
        </Button>
      </PageHeader>
      {/*<PageFilters className='flex justify-between'>*/}
      {/*  <SalesListFilters />*/}
      {/*</PageFilters>*/}
      <PageContent>
        <SalesListTable saleType={saleType} view={view} />
      </PageContent>
    </Page>
  );
};

export default SalesList;
