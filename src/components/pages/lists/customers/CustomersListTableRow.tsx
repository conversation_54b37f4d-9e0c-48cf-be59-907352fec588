import {FC} from 'react';

import {Trash2Icon} from 'lucide-react';
import {useTranslations} from 'next-intl';

import {Button} from '@/components/ui/Button';
import {Link} from '@/components/ui/special/Link';
import {WithoutEmpty} from '@/components/ui/special/WithoutEmpty';
import {TableActions, TableCell, TableRow} from '@/components/ui/Table';
import useCustomerActions from '@/hooks/useCustomerActions';
import {Customer} from '@/types/sales';

type Props = {
  customer: Customer;
};

const CustomersListTableRow: FC<Props> = ({customer}) => {
  const {deleteCustomer} = useCustomerActions();
  const t = useTranslations();

  return (
    <TableRow>
      <TableCell>
        <Link className='whitespace-nowrap hover:underline' href={`/customers/${customer.id}`}>
          {customer.name}
        </Link>
      </TableCell>
      <TableCell>{t(customer.type as any)}</TableCell>
      <TableCell>
        <WithoutEmpty value={customer.contacts?.[0]?.email}>{customer.contacts?.[0]?.email}</WithoutEmpty>
      </TableCell>
      <TableCell>
        <WithoutEmpty value={customer.contacts?.[0]?.phoneNumber}>{customer.contacts?.[0]?.phoneNumber}</WithoutEmpty>
      </TableCell>
      <TableCell>
        <WithoutEmpty value={customer.addresses?.[0]?.city}>{customer.addresses?.[0]?.city}</WithoutEmpty>
      </TableCell>
      <TableActions>
        <Button onClick={() => deleteCustomer(customer.id, customer.name)} size='icon' variant='none'>
          <Trash2Icon className='size-5 text-red' strokeWidth={1} />
        </Button>
      </TableActions>
    </TableRow>
  );
};

export default CustomersListTableRow;
