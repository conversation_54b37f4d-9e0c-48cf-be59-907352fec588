import {useState} from 'react';

import {PlusIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';

import {Button} from '@/components/ui/Button';
import NewCustomerSheet from '@/components/ui/special/NewCustomerSheet';
import {Page, PageContent, PageHeader, PageHeaderTitle, PageTitle} from '@/components/ui/special/Page';
import {StoreSearchInput} from '@/components/ui/special/StoreSearchInput';
import {useRouter} from '@/hooks/helpers/useRouter';
import {NextPageWithLayout} from '@/types/global';

import {customersSearchQueryAtom} from './customersListStore';
import CustomersListTable from './CustomersListTable';

const CustomersList: NextPageWithLayout = () => {
  const [showNew, setShowNew] = useState(false);
  const t = useTranslations();
  const {push} = useRouter();

  return (
    <>
      {showNew && (
        <NewCustomerSheet
          onClose={() => {
            setShowNew(false);
          }}
          onCreate={(customer) => push(`/customers/${customer.id}`)}
        />
      )}
      <Page>
        <PageTitle>{t('customers')}</PageTitle>
        <PageHeader>
          <PageHeaderTitle>{t('customers')}</PageHeaderTitle>
          <div className='grow' />
          <StoreSearchInput
            autoFocus
            div={{className: 'grow'}}
            placeholder={t(
              'search by name, email, phone number, city, tax identification number, identification number',
            )}
            store={customersSearchQueryAtom}
          />
          {/*<CustomersListFiltersButton />*/}
          <Button onClick={() => setShowNew(true)}>
            <PlusIcon />
            {t('add customer')}
          </Button>
        </PageHeader>
        {/*<PageFilters className='flex justify-between'>*/}
        {/*  <CustomersListFilters />*/}
        {/*</PageFilters>*/}
        <PageContent>
          <CustomersListTable />
        </PageContent>
      </Page>
    </>
  );
};

export default CustomersList;
