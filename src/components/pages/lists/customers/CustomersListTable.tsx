import {FC} from 'react';

import {useAtomValue} from 'jotai';
import {useTranslations} from 'next-intl';

import {
  Table,
  TableBody,
  TableContainer,
  TableHead,
  TableHeadActions,
  TableHeader,
  TableRow,
} from '@/components/ui/Table';
import useHeight from '@/hooks/helpers/useHeight';
import useCustomers from '@/hooks/useCustomers';

import {customersSearchQueryAtom} from './customersListStore';
import CustomersListTableRow from './CustomersListTableRow';

const CustomersListTable: FC = () => {
  const {elementRef} = useHeight();
  const search = useAtomValue(customersSearchQueryAtom);
  const {customers, isLoading} = useCustomers({search});
  const t = useTranslations();

  return (
    <TableContainer ref={elementRef}>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>{t('name')}</TableHead>
            <TableHead>{t('inventory unit')}</TableHead>
            <TableHead>{t('email')}</TableHead>
            <TableHead>{t('phone number')}</TableHead>
            <TableHead>{t('city')}</TableHead>
            <TableHeadActions />
          </TableRow>
        </TableHeader>
        <TableBody className='overflow-y-hidden' isValidating={isLoading}>
          {customers?.map((customer, index) => (
            <CustomersListTableRow customer={customer} key={`${customer.id}-row-${index}`} />
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );
};

export default CustomersListTable;
