import {FC, useEffect, useState} from 'react';

import {closestCorners, DndContext, DragEndEvent, PointerSensor, useSensor, useSensors} from '@dnd-kit/core';
import {arrayMove, SortableContext, verticalListSortingStrategy} from '@dnd-kit/sortable';
import {useAtomValue} from 'jotai';
import {useTranslations} from 'next-intl';

import {
  Table,
  TableBody,
  TableContainer,
  TableHead,
  TableHeadActions,
  TableHeader,
  TableRow,
} from '@/components/ui/Table';
import {useHasPermission} from '@/hooks/helpers/useHasPermission';
import useHeight from '@/hooks/helpers/useHeight';
import useManufacturingOrderActions from '@/hooks/useManufacturingOrderActions';
import {ManufacturingStatus} from '@/types/global';
import {ManufacturingOrder} from '@/types/manufacturing';

import {ManufacturingOrdersView} from './manufacturingOrdersListStore';
import {manufacturingOrdersHighlightedOrderIdsAtom} from './manufacturingOrdersListStore';
import ManufacturingOrdersListTableRow from './ManufacturingOrdersListTableRow';

type Props = {
  isLoading: boolean;
  manufacturingOrders: ManufacturingOrder[];
  view: ManufacturingOrdersView;
};

const ManufacturingOrdersListTable: FC<Props> = ({isLoading, manufacturingOrders, view}) => {
  const {elementRef} = useHeight();
  const [orders, setOrders] = useState(manufacturingOrders);
  const {updateManufacturingOrderRank} = useManufacturingOrderActions();
  const {hasPermission, isLoading: permissionIsLoading} = useHasPermission();
  const t = useTranslations();
  const highlightedIds = useAtomValue(manufacturingOrdersHighlightedOrderIdsAtom);

  useEffect(() => {
    setOrders(manufacturingOrders);
  }, [manufacturingOrders, isLoading]);

  const sensors = useSensors(useSensor(PointerSensor));

  const handleDragEnd = ({active, over}: DragEndEvent) => {
    if (active.id !== over?.id) {
      const oldIndex = manufacturingOrders?.findIndex((order) => order.id === active.id);
      const newIndex = over ? manufacturingOrders?.findIndex((order) => order.id === over?.id) : -1;
      if (oldIndex !== undefined && newIndex !== undefined && oldIndex !== -1 && newIndex !== -1) {
        const newOrders = arrayMove(manufacturingOrders, oldIndex, newIndex);
        updateManufacturingOrderRank(
          newOrders
            .filter((order) =>
              [ManufacturingStatus.CUSTOMIZATION_NEEDED, ManufacturingStatus.SUBMITTED].includes(order.status),
            )
            .map((order) => order.id),
        );
        setOrders(newOrders);
      }
    }
  };

  return (
    <TableContainer ref={elementRef}>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>{t('order#')}</TableHead>
            <TableHead>{t('customer')}</TableHead>
            <TableHead>{t('item')}</TableHead>
            <TableHead>{t('category')}</TableHead>
            <TableHead className='text-right'>{t('quantity')}</TableHead>
            <TableHead>{t('production time')}</TableHead>
            <TableHead>{t('production deadline')}</TableHead>
            <TableHead>{t('expected by')}</TableHead>
            {view === ManufacturingOrdersView.completed && <TableHead>{t('date completed')}</TableHead>}
            <TableHead>{t('materials')}</TableHead>
            <TableHead>{t('order status')}</TableHead>
            {hasPermission('financial', 'manufacturing') && (
              <TableHeadActions className='text-right'>{t('documents')}</TableHeadActions>
            )}
          </TableRow>
        </TableHeader>
        <TableBody className='overflow-y-hidden' isValidating={isLoading || permissionIsLoading}>
          <DndContext collisionDetection={closestCorners} onDragEnd={handleDragEnd} sensors={sensors}>
            <SortableContext items={orders} strategy={verticalListSortingStrategy}>
              {orders?.map((order, index) => (
                <ManufacturingOrdersListTableRow
                  isHighlighted={highlightedIds.includes(order.id)}
                  key={`${order.id}-${index}`}
                  order={order}
                  view={view}
                />
              ))}
            </SortableContext>
          </DndContext>
        </TableBody>
      </Table>
    </TableContainer>
  );
};

export default ManufacturingOrdersListTable;
