import {FC, useMemo} from 'react';

import {useSortable} from '@dnd-kit/sortable';
import {CSS} from '@dnd-kit/utilities';
import {isBefore, isSameDay, parseISO} from 'date-fns';
import {useAtomValue, useSetAtom} from 'jotai';
import {CheckIcon, FileChartColumnIcon, GripHorizontalIcon, LayersIcon, ReceiptTextIcon, XIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';

import {Badge} from '@/components/ui/Badge';
import {IconButton} from '@/components/ui/Button';
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from '@/components/ui/Select';
import {Link} from '@/components/ui/special/Link';
import TimeAgo from '@/components/ui/special/TimeAgo';
import {WithoutEmpty} from '@/components/ui/special/WithoutEmpty';
import {TableActions, TableCell, TableRow} from '@/components/ui/Table';
import {Tooltip, TooltipContent, TooltipTrigger} from '@/components/ui/Tooltip';
import {useHasPermission} from '@/hooks/helpers/useHasPermission';
import {useRouter} from '@/hooks/helpers/useRouter';
import {defaultWorkingHoursPerDayAtom} from '@/store/defaults';
import {ManufacturingStatus} from '@/types/global';
import {ManufacturingOrder} from '@/types/manufacturing';
import {classes, minutesToWorkHoursTimeString} from '@/utils/common';
import {formatDate, formatNumber} from '@/utils/format';

import {manufacturingOrdersHighlightedOrderIdsAtom, ManufacturingOrdersView} from './manufacturingOrdersListStore';

type Props = {
  isHighlighted: boolean;
  order: ManufacturingOrder;
  view: ManufacturingOrdersView;
};

const ManufacturingOrdersListTableRow: FC<Props> = ({isHighlighted, order, view}) => {
  const {attributes, isDragging, listeners, setNodeRef, transform, transition} = useSortable({id: order.id});
  const workingHoursPerDay = useAtomValue(defaultWorkingHoursPerDayAtom);
  const {hasPermission, isLoading: permissionIsLoading} = useHasPermission();
  const {push} = useRouter();
  const t = useTranslations();
  const setHighlightedIds = useSetAtom(manufacturingOrdersHighlightedOrderIdsAtom);

  const relatedIds = useMemo(() => {
    const ids = new Set<string>();
    ids.add(order.id);

    if (order.parent?.id) {
      ids.add(order.parent.id);
    }

    order.manufacturingOperations
      .flatMap((operation) => operation.materials)
      .forEach((material) => {
        if (material.subassemblyManufacturingOrder?.id) {
          ids.add(order.id);
          ids.add(material.subassemblyManufacturingOrder.id);
        }
      });

    return ids.size > 1 ? Array.from(ids) : [];
  }, [order]);

  if (permissionIsLoading) return null;

  return (
    <TableRow
      className={classes(isDragging && 'bg-background shadow-xl hover:bg-background', isHighlighted && 'bg-blue-light')}
      ref={setNodeRef}
      style={{
        ...(transform ? {transform: CSS.Transform.toString({...transform, scaleX: 1, scaleY: 1})} : {}),
        transition,
      }}
    >
      <TableCell className='inline-flex h-14 items-center gap-2'>
        {[ManufacturingStatus.CUSTOMIZATION_NEEDED, ManufacturingStatus.SUBMITTED].includes(order.status) ? (
          <span {...listeners} {...attributes}>
            <GripHorizontalIcon className='size-5 text-slate-900/40' />
          </span>
        ) : ![ManufacturingStatus.CONSUMPTION_RECORDED, ManufacturingStatus.ACCOUNTED].includes(order.status) ? (
          <div className='w-5' />
        ) : null}
        <Link className='group flex flex-col whitespace-nowrap' href={`/manufacturing/orders/${order.id}`}>
          <div className='group-hover:underline'>{order.number}</div>
        </Link>
        {relatedIds?.length > 1 && (
          <IconButton
            icon={<LayersIcon className='size-5 p-1' />}
            onClick={() =>
              setHighlightedIds((pre) => {
                if (pre.includes(order.id)) {
                  return [];
                } else {
                  return relatedIds;
                }
              })
            }
          />
        )}
      </TableCell>
      <TableCell>
        <WithoutEmpty value={order.customer?.id}>
          <Link className='whitespace-nowrap hover:underline' href={`/customers/${order.customer?.id}`}>
            {order.customer?.name}
          </Link>
        </WithoutEmpty>
      </TableCell>
      <TableCell>
        <WithoutEmpty value={order.product?.id}>
          <Link className='whitespace-nowrap hover:underline' href={`/inventory/items/${order.product?.id}`}>
            {order.product?.name}
          </Link>
        </WithoutEmpty>
      </TableCell>
      <TableCell>
        <WithoutEmpty value={order.category?.id}>{order.category?.name}</WithoutEmpty>
      </TableCell>
      <TableCell className='text-right'>
        {formatNumber(order.quantity)}{' '}
        {t(
          order.measurementUnit?.name
            ? (`unit.name.${order.measurementUnit.name.toLowerCase()}` as any)
            : order.product?.measurementUnit
              ? (`unit.id.${order.product.measurementUnit.toLowerCase()}` as any)
              : 'unit.name.pcs',
        )}
      </TableCell>
      <TableCell>{minutesToWorkHoursTimeString(order.productionTime, workingHoursPerDay)}</TableCell>
      <TableCell>
        <WithoutEmpty value={order.productionDeadline}>{formatDate(order.productionDeadline)}</WithoutEmpty>
      </TableCell>
      <TableCell>
        <WithoutEmpty value={order.estimatedCompletionDate}>
          <Badge
            variant={
              order.productionDeadline
                ? isBefore(parseISO(order.estimatedCompletionDate || ''), parseISO(order.productionDeadline || '')) ||
                  isSameDay(parseISO(order.estimatedCompletionDate || ''), parseISO(order.productionDeadline || ''))
                  ? 'success'
                  : 'error'
                : 'none'
            }
          >
            {formatDate(order.estimatedCompletionDate)}
          </Badge>
        </WithoutEmpty>
      </TableCell>
      {view === ManufacturingOrdersView.completed && (
        <TableCell>
          <TimeAgo date={order.updateTime} hide={['days', 'week', 'weeks']} useYesterday />
        </TableCell>
      )}
      {order.status === ManufacturingStatus.CUSTOMIZATION_NEEDED && (
        <TableCell colSpan={2}>
          <Badge className='w-full justify-center' variant='error'>
            {t(ManufacturingStatus.CUSTOMIZATION_NEEDED)}
          </Badge>
        </TableCell>
      )}
      {order.status !== ManufacturingStatus.CUSTOMIZATION_NEEDED && (
        <>
          <TableCell>
            <Badge variant={order.allMaterialsAvailable ? 'success' : 'error'}>
              {order.allMaterialsAvailable ? (
                <>
                  <CheckIcon className='mr-1 shrink-0' />
                  {t('available', {isPlural: 'true'})}
                </>
              ) : (
                <>
                  <XIcon className='mr-1 shrink-0' />
                  {t('unavailable', {isPlural: 'true'})}
                </>
              )}
            </Badge>
          </TableCell>
          <TableCell>
            {order.status !== ManufacturingStatus.MANUFACTURED && (
              <Badge
                variant={
                  order.status === ManufacturingStatus.SUBMITTED
                    ? 'secondary'
                    : order.status === ManufacturingStatus.CONSUMPTION_RECORDED
                      ? 'success'
                      : order.status === ManufacturingStatus.ACCOUNTED
                        ? 'success'
                        : order.status === ManufacturingStatus.MANUFACTURING
                          ? 'info'
                          : order.status === ManufacturingStatus.MANUFACTURED
                            ? 'warning'
                            : 'none'
                }
              >
                {t(order.status)}
              </Badge>
            )}
            {order.status === ManufacturingStatus.MANUFACTURED && (
              <div className='flex items-center'>
                <Select
                  onValueChange={(value) => {
                    if (value === ManufacturingStatus.CONSUMPTION_RECORDED) push(`/manufacturing/orders/${order.id}/consumption`);
                  }}
                  value={order.status}
                >
                  <SelectTrigger className='w-fit' size='badge-md' variant='badge-warning'>
                    <SelectValue>{t(order.status)}</SelectValue>
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value={ManufacturingStatus.MANUFACTURED}>
                      {t(ManufacturingStatus.MANUFACTURED)}
                    </SelectItem>
                    <SelectItem value={ManufacturingStatus.CONSUMPTION_RECORDED}>{t(ManufacturingStatus.CONSUMPTION_RECORDED)}</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            )}
          </TableCell>
        </>
      )}
      {hasPermission('financial', 'manufacturing') && (
        <TableActions>
          <WithoutEmpty value={!!order.materialIssueNote}>
            <div className='flex flex-wrap justify-end gap-x-0.5 gap-y-1'>
              <Tooltip>
                <TooltipTrigger>
                  <Link href={`/inventory/consumptions/${order.materialIssueNote?.id}`}>
                    <ReceiptTextIcon strokeWidth={1} />
                  </Link>
                </TooltipTrigger>
                <TooltipContent className='flex flex-col items-center'>
                  <div>{t('consumption')}</div>
                  <div>{order.materialIssueNote?.number}</div>
                </TooltipContent>
              </Tooltip>

              <Tooltip>
                <TooltipTrigger>
                  <Link href={`/manufacturing/orders/${order.id}/production`}>
                    <FileChartColumnIcon strokeWidth={1} />
                  </Link>
                </TooltipTrigger>
                <TooltipContent>{t('production report')}</TooltipContent>
              </Tooltip>
            </div>
          </WithoutEmpty>
        </TableActions>
      )}
    </TableRow>
  );
};

export default ManufacturingOrdersListTableRow;
