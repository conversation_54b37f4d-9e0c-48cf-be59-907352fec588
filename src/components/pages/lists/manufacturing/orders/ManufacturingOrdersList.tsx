import {FC, useMemo, useState} from 'react';

import {useAtom} from 'jotai';
import {useAtomValue} from 'jotai/index';
import {PlusIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';

import {Button} from '@/components/ui/Button';
import {Page, PageContent, PageHeader, PageHeaderTitle, PageTitle} from '@/components/ui/special/Page';
import {StoreSearchInput} from '@/components/ui/special/StoreSearchInput';
import {Tabs, TabsList, TabsTrigger} from '@/components/ui/Tabs';
import {useRouter} from '@/hooks/helpers/useRouter';
import useManufacturingOrders from '@/hooks/useManufacturingOrders';
import {ManufacturingStatus} from '@/types/global';

import ManufacturingOrdersListMissingSheetButton from './ManufacturingOrdersListMissingSheetButton';
import {
  manufacturingOrdersSearchQueryAtom,
  ManufacturingOrdersView,
  manufacturingOrdersViewAtom,
} from './manufacturingOrdersListStore';
import ManufacturingOrdersListTable from './ManufacturingOrdersListTable';
import NewManufacturingOrderSheet from './NewManufacturingOrderSheet';

const ManufacturingOrdersList: FC = () => {
  const t = useTranslations();
  const {
    query: {create},
  } = useRouter();
  const [showNew, setShowNew] = useState(create === 'true');
  const [view, setView] = useAtom(manufacturingOrdersViewAtom);
  const {push} = useRouter();

  const statuses = useMemo(() => {
    let defaultValue: ManufacturingStatus[] = [];
    switch (view) {
      case ManufacturingOrdersView.completed:
        defaultValue = [ManufacturingStatus.DONE];
        break;
      case ManufacturingOrdersView.open:
        defaultValue = [
          ManufacturingStatus.SUBMITTED,
          ManufacturingStatus.MANUFACTURING,
          ManufacturingStatus.MANUFACTURED,
          ManufacturingStatus.CUSTOMIZATION_NEEDED,
        ];
        break;
    }

    return defaultValue;
  }, [view]);
  const search = useAtomValue(manufacturingOrdersSearchQueryAtom);
  const sort = useMemo(() => (view === ManufacturingOrdersView.completed ? ['-updateTime'] : []), [view]);
  const {isLoading, manufacturingOrders} = useManufacturingOrders({
    search,
    sort: sort as any,
    statuses,
  });

  return (
    <>
      {showNew && (
        <NewManufacturingOrderSheet
          onClose={() => {
            setShowNew(false);
          }}
          onCreate={(order) => push(`/manufacturing/orders/${order.id}`)}
        />
      )}
      <Page>
        <PageTitle>{`${t(view as any)} - ${t('orders')} - ${t('execution')}`}</PageTitle>
        <PageHeader>
          <PageHeaderTitle>{t('orders')}</PageHeaderTitle>
          <Tabs onValueChange={(newView) => setView(newView as ManufacturingOrdersView)} value={view} variant='menu'>
            <TabsList variant='menu'>
              <TabsTrigger value={ManufacturingOrdersView.open} variant='menu'>
                {t('open')}
              </TabsTrigger>
              <TabsTrigger value={ManufacturingOrdersView.completed} variant='menu'>
                {t('completed')}
              </TabsTrigger>
            </TabsList>
          </Tabs>
          <div className='grow' />
          {view === ManufacturingOrdersView.open && (
            <ManufacturingOrdersListMissingSheetButton
              orders={manufacturingOrders.filter((order) =>
                order.manufacturingOperations.some((operation) =>
                  operation.materials.some((material) => !material.allAvailable),
                ),
              )}
            />
          )}
          <StoreSearchInput
            autoFocus
            div={{className: 'grow'}}
            placeholder={t('search by order number')}
            store={manufacturingOrdersSearchQueryAtom}
          />
          {/*<ManufacturingListFiltersButton />*/}
          <Button autoFocus onClick={() => setShowNew(true)}>
            <PlusIcon />
            {t('add manufacturing order')}
          </Button>
        </PageHeader>
        {/*<PageFilters className='flex justify-between'>*/}
        {/*  <ManufacturingListFilters />*/}
        {/*</PageFilters>*/}
        <PageContent>
          <ManufacturingOrdersListTable isLoading={isLoading} manufacturingOrders={manufacturingOrders} view={view} />
        </PageContent>
      </Page>
    </>
  );
};

export default ManufacturingOrdersList;
