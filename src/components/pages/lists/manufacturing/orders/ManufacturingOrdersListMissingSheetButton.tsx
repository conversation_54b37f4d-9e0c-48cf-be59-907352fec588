import {FC, useCallback, useMemo, useState} from 'react';

import {NotebookPenIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';

import {Badge} from '@/components/ui/Badge';
import {Button} from '@/components/ui/Button';
import {Checkbox} from '@/components/ui/Checkbox';
import {Combobox} from '@/components/ui/Combobox';
import {NumberInput} from '@/components/ui/Input';
import {Label, WithLabel} from '@/components/ui/Label';
import {
  Sheet,
  SheetClose,
  SheetContent,
  SheetFooter,
  SheetOverlay,
  SheetPage,
  SheetTitle,
  SheetTrigger,
} from '@/components/ui/Sheet';
import {WithoutEmpty} from '@/components/ui/special/WithoutEmpty';
import {toast} from '@/components/ui/Toast';
import useSuppliers from '@/hooks/useSuppliers';
import useWishlistActions from '@/hooks/useWishlistActions';
import {ManufacturingOrder} from '@/types/manufacturing';

type Material = {
  checked: boolean;
  id: string;
  name: string;
  quantity: number;
  required: number;
  supplier: string | undefined;
};

type Props = {
  orders: ManufacturingOrder[];
};

type SheetOrder = {
  client: {id: string; name: string};
  materials: Material[];
  order: {id: string; name: string};
};

const ManufacturingOrdersListMissingSheetButton: FC<Props> = ({orders}) => {
  const t = useTranslations();
  const [sheetOpen, setSheetOpen] = useState(false);
  const [sheetOrders, setSheetOrders] = useState<SheetOrder[]>([]);
  const {suppliers} = useSuppliers();
  const {createWishlistItem} = useWishlistActions();

  const initializeSheetOrders = useCallback(() => {
    const formatted: SheetOrder[] = orders
      .map((order) => ({
        client: {id: order.customer?.id ?? '', name: order.customer?.name ?? ''},
        materials: order.manufacturingOperations
          .flatMap((operation) => operation.materials)
          .filter((material) => !material.allAvailable && material.materialGoods[0])
          .map((material) => ({
            checked: true,
            id: material.materialGoods[0].id,
            name: material.materialGoods[0].name,
            quantity: material.required,
            required: material.required,
            supplier: material.materialGoods[0]?.lastOrderedFrom?.id,
          })),
        order: {id: order.id, name: order.number},
      }))
      .filter((o) => o.materials.length > 0);

    setSheetOrders(formatted);
  }, [orders]);

  const updateMaterialField = useCallback((orderIdx: number, materialIdx: number, update: Partial<Material>) => {
    setSheetOrders((prev) => {
      const newOrders = [...prev];
      const newMaterials = [...newOrders[orderIdx].materials];
      newMaterials[materialIdx] = {...newMaterials[materialIdx], ...update};
      newOrders[orderIdx] = {...newOrders[orderIdx], materials: newMaterials};
      return newOrders;
    });
  }, []);

  const handleGeneratePurchaseOrders = useCallback(async () => {
    const promises: Promise<any>[] = [];

    sheetOrders.forEach((orderObj) => {
      orderObj.materials
        .filter((m) => m.checked && m.supplier && m.quantity > 0)
        .forEach((item) => {
          promises.push(
            createWishlistItem({
              materialGood: {code: '', id: item.id, name: item.name},
              quantity: item.quantity,
              supplier: {id: item.supplier!, name: ''},
            }),
          );
        });
    });

    try {
      await Promise.all(promises);
      toast.success(
        t('name has been created successfully', {
          created: t('created.female'),
          name: t('wishlist'),
        }),
      );
    } catch (error) {
      toast.error(t('name has failed to create successfully', {created: t('created.female'), name: t('wishlist')}));
      console.error(error);
    }
  }, [sheetOrders, createWishlistItem, t]);

  const isGenerateDisabled = useMemo(
    () =>
      sheetOrders.every((orderObj) => orderObj.materials.every((material) => !material.checked || !material.supplier)),
    [sheetOrders],
  );

  const missingMaterialsCount = useMemo(() => {
    return orders
      .flatMap((order) => order.manufacturingOperations)
      .flatMap((operation) => operation.materials)
      .filter((material) => !material.allAvailable && material.materialGoods[0]).length;
  }, [orders]);

  return (
    <Sheet
      onOpenChange={(open) => {
        setSheetOpen(open);
        if (open) initializeSheetOrders();
      }}
      open={sheetOpen}
    >
      <SheetOverlay variant='light' />
      <SheetTrigger asChild disabled={missingMaterialsCount === 0}>
        <Button variant='secondary'>
          <NotebookPenIcon className='mr-2 h-4 w-4' />
          {t('missing materials')}
          <Badge className='ml-1' size='sm' variant='error'>
            {missingMaterialsCount}
          </Badge>
        </Button>
      </SheetTrigger>
      <SheetPage side='right' size='lg'>
        <SheetTitle className='text-2xl'>{t('manufacturing orders with missing materials')}</SheetTitle>
        <SheetContent className='flex flex-col gap-4 p-6'>
          {sheetOrders.map((orderObj, orderIdx) => (
            <div className='flex flex-col gap-2 border p-2' key={orderObj.order.id}>
              <div className='flex items-center justify-between'>
                <strong>{orderObj.order.name}</strong>{' '}
                <WithoutEmpty value={orderObj.client.name}>{orderObj.client.name}</WithoutEmpty>
              </div>
              <div className='flex flex-col gap-2'>
                {orderObj.materials.map((material, materialIdx) => {
                  const idKey = `${orderObj.order.id}-${material.id}-${materialIdx}`;
                  return (
                    <div className='flex flex-col' key={idKey}>
                      <div className='flex justify-between items-center gap-1'>
                        <WithLabel direction='horizontal'>
                          <Checkbox
                            checked={material.checked}
                            id={idKey}
                            onCheckedChange={(checked) =>
                              updateMaterialField(orderIdx, materialIdx, {
                                checked: !!checked,
                                quantity: !!checked ? material.required : 0,
                              })
                            }
                          />
                          <Label htmlFor={idKey}>{material.name}</Label>
                        </WithLabel>
                        <NumberInput
                          disabled={!material.checked}
                          min={0}
                          onChange={(value) =>
                            updateMaterialField(orderIdx, materialIdx, {
                              checked: value > 0,
                              quantity: value,
                            })
                          }
                          value={material.quantity}
                        />
                      </div>
                      <Combobox
                        disabled={!material.checked}
                        onChange={(value) =>
                          updateMaterialField(orderIdx, materialIdx, {
                            supplier: value,
                          })
                        }
                        options={suppliers.map((s) => ({
                          id: s.id,
                          value: s.name,
                        }))}
                        placeholder={t('supplier')}
                        renderNotFound={() => t('supplier not found')}
                        searchPlaceholder={t('search supplier')}
                        value={material.supplier}
                      />
                    </div>
                  );
                })}
              </div>
            </div>
          ))}
        </SheetContent>
        <SheetFooter className='px-6'>
          <SheetClose asChild>
            <Button disabled={isGenerateDisabled} onClick={handleGeneratePurchaseOrders}>
              {t('generate wishlists')}
            </Button>
          </SheetClose>
          <SheetClose asChild>
            <Button variant='secondary'>{t('close')}</Button>
          </SheetClose>
        </SheetFooter>
      </SheetPage>
    </Sheet>
  );
};

export default ManufacturingOrdersListMissingSheetButton;
