import {FC, useMemo} from 'react';

import {CalendarIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';

import TasksListTableRow from '@/components/pages/lists/manufacturing/tasks/TasksListTableRow';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableHeadActions,
  TableHeader,
  TableRow,
} from '@/components/ui/Table';
import useHeight from '@/hooks/helpers/useHeight';
import useMyTasks from '@/hooks/useMyTasks';
import {MyTask} from '@/types/manufacturing';
import {formatDate, formatDay} from '@/utils/format';

const TasksListTable: FC = () => {
  const {elementRef} = useHeight();
  const {isLoading, myTasks} = useMyTasks();
  const t = useTranslations();

  const tasksByDate = useMemo(() => {
    const grouped = myTasks.reduce(
      (acc, task) => {
        const date = task.startTime ? formatDate(task.startTime) : null;
        const key = date || '';

        if (!acc[key]) {
          acc[key] = [];
        }
        acc[key].push(task);
        return acc;
      },
      {} as Record<string, MyTask[]>,
    );

    return Object.entries(grouped).sort(([dateA], [dateB]) => {
      if (!dateA) return -1;
      if (!dateB) return 1;
      return new Date(dateA).getTime() - new Date(dateB).getTime();
    });
  }, [myTasks]);

  return (
    <TableContainer ref={elementRef}>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>{t('number')}</TableHead>
            <TableHead>{t('name')}</TableHead>
            <TableHead>{t('item')}</TableHead>
            <TableHead>{t('type')}</TableHead>
            <TableHead>{t('start time')}</TableHead>
            <TableHead>{t('duration')}</TableHead>
            <TableHeadActions>{t('status')}</TableHeadActions>
          </TableRow>
        </TableHeader>
        <TableBody className='overflow-y-hidden' isValidating={isLoading}>
          {tasksByDate.map(([date, tasks], index) => (
            <>
              {date && (
                <TableRow className='bg-slate-100' key={`${date}-${index}`}>
                  <TableCell className='capitalize' colSpan={999}>
                    <div className='flex items-center gap-1'>
                      <CalendarIcon className='size-5' strokeWidth={1} /> {formatDay(date)} {formatDate(date)}
                    </div>
                  </TableCell>
                </TableRow>
              )}
              {tasks?.map((task, index) => <TasksListTableRow key={`${task.id}-row-${index}`} task={task} />)}
            </>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );
};

export default TasksListTable;
