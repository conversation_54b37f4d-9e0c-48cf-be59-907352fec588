import {FC} from 'react';

import {useTranslations} from 'next-intl';

import {Page, PageContent, PageHeader, PageHeaderTitle, PageTitle} from '@/components/ui/special/Page';

import TasksListTable from './TasksListTable';

const TasksList: FC = () => {
  const t = useTranslations();

  return (
    <Page>
      <PageTitle>{`${t('my tasks')} - ${t('manufacturing')}`}</PageTitle>
      <PageHeader>
        <PageHeaderTitle>{t('my tasks')}</PageHeaderTitle>
      </PageHeader>
      <PageContent>
        <TasksListTable />
      </PageContent>
    </Page>
  );
};

export default TasksList;
