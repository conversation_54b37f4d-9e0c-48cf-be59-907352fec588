import {FC} from 'react';

import {useTranslations} from 'next-intl';

import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from '@/components/ui/Select';
import InventoryItemLink from '@/components/ui/special/InventoryItemLink/InventoryItemLink';
import {Link} from '@/components/ui/special/Link';
import {TableCell, TableRow} from '@/components/ui/Table';
import {useRouter} from '@/hooks/helpers/useRouter';
import useServicingOrderActions from '@/hooks/useServicingOrderActions';
import useTaskActions from '@/hooks/useTaskActions';
import {
  ManufacturingTaskStatus,
  ManufacturingTaskStatusReason,
  ServicingStatus,
  ServicingTaskStatus,
} from '@/types/global';
import {MyTask, MyTaskType} from '@/types/manufacturing';
import {minutesToWorkHoursTimeString} from '@/utils/common';
import {formatTime} from '@/utils/format';

type Props = {
  task: MyTask;
};

const TasksListTableRow: FC<Props> = ({task}) => {
  const t = useTranslations();
  const {getAvailableStatuses: getAvailableTaskStatuses, updateManufacturingTaskStatus} = useTaskActions();
  const {getAvailableStatuses, getOrder, updateServicingOrderStatus, validateServicingOrder} =
    useServicingOrderActions();
  const {push} = useRouter();

  return (
    <TableRow>
      <TableCell>
        <Link
          className='whitespace-nowrap hover:underline'
          href={
            task.type === MyTaskType.MANUFACTURING_TASK
              ? `/manufacturing/orders/${task.parentId}`
              : `/manufacturing/services/${task.id}`
          }
        >
          {task.number}
        </Link>
      </TableCell>
      <TableCell>{task.type === MyTaskType.SERVICE ? task.target.name : task.name}</TableCell>
      <TableCell>{task.type === MyTaskType.MANUFACTURING_TASK && <InventoryItemLink item={task.target} />}</TableCell>
      <TableCell>{t(task.type)}</TableCell>
      <TableCell>{formatTime(task.startTime)}</TableCell>
      <TableCell>
        {task.type === MyTaskType.MANUFACTURING_TASK && minutesToWorkHoursTimeString(task.durationInMinutes)}
      </TableCell>
      <TableCell>
        {task.type === MyTaskType.MANUFACTURING_TASK && (
          <Select
            onValueChange={(value) => {
              if (Object.values(ManufacturingTaskStatusReason).includes(value as ManufacturingTaskStatusReason)) {
                updateManufacturingTaskStatus(task.id, ManufacturingTaskStatus.STOPPED, {
                  name: task.name,
                  orderId: task.parentId || '',
                  reason: value as ManufacturingTaskStatusReason,
                });
              } else {
                updateManufacturingTaskStatus(task.id, value as ManufacturingTaskStatus, {
                  name: task.name,
                  orderId: task.parentId || '',
                });
              }
            }}
            value={task.statusReason || task.status}
          >
            <SelectTrigger
              className='w-fit self-end'
              size='badge-md'
              variant={
                task.status === ServicingTaskStatus.TODO
                  ? 'badge-secondary'
                  : task.status === ServicingTaskStatus.IN_PROGRESS
                    ? 'badge-info'
                    : task.status === ServicingTaskStatus.DONE
                      ? 'badge-success'
                      : task.status === ServicingTaskStatus.STOPPED
                        ? 'badge-error'
                        : 'none'
              }
            >
              <SelectValue>
                {task.status === ServicingTaskStatus.STOPPED ? t(task.statusReason) : t(task.status)}
              </SelectValue>
            </SelectTrigger>
            <SelectContent>
              {getAvailableTaskStatuses(task.status as ServicingTaskStatus).map((status) => (
                <SelectItem key={status} value={status}>
                  {t(status)}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        )}
        {task.type === MyTaskType.SERVICE && (
          <Select
            onValueChange={async (value) => {
              const order = await getOrder(task.id);

              if (order && validateServicingOrder(order)) {
                if (value === ServicingStatus.DONE) {
                  push(`/manufacturing/services/${order.id}/consumption`);
                } else if (value === ServicingStatus.MANUFACTURED) {
                  push(`/manufacturing/services/${order.id}/report`);
                } else {
                  updateServicingOrderStatus(order, value as ServicingStatus);
                }
              }
            }}
            value={task.status}
          >
            <SelectTrigger
              className='w-fit self-end'
              size='badge-md'
              variant={
                task.status === ServicingStatus.SUBMITTED
                  ? 'badge-secondary'
                  : task.status === ServicingStatus.MANUFACTURING
                    ? 'badge-info'
                    : task.status === ServicingStatus.DONE
                      ? 'badge-success'
                      : task.status === ServicingStatus.MANUFACTURED
                        ? 'badge-warning'
                        : 'none'
              }
            >
              <SelectValue>{t(task.status)}</SelectValue>
            </SelectTrigger>
            <SelectContent>
              {getAvailableStatuses(task.status as ServicingStatus).map((status) => (
                <SelectItem key={status} value={status}>
                  {t(status)}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        )}
      </TableCell>
    </TableRow>
  );
};

export default TasksListTableRow;
