import {FC, useEffect, useMemo, useRef, useState} from 'react';

import FullCalendar from '@fullcalendar/react';
import {endOfWeek, format, startOfWeek} from 'date-fns';
import {useAtom} from 'jotai';
import {difference} from 'lodash';
import {CalendarIcon, ChevronLeftIcon, ChevronRightIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';

import {Button} from '@/components/ui/Button';
import {Page, PageHeader, PageHeaderTitle, PageTitle} from '@/components/ui/special/Page';
import {Tabs, TabsList, TabsTrigger} from '@/components/ui/Tabs';
import {useRouter} from '@/hooks/helpers/useRouter';
import useSettings from '@/hooks/useSettings';
import {formatDate, formatDay} from '@/utils/format';

import ManufacturingPlanningEmployees from './ManufacturingPlanningEmployees/ManufacturingPlanningEmployees';
import ManufacturingPlanningOrders from './ManufacturingPlanningOrders/ManufacturingPlanningOrders';
import ManufacturingPlanningOrdersButtons from './ManufacturingPlanningOrders/ManufacturingPlanningOrdersButtons';
import {CalendarView, manufacturingPlanningDateAtom, ManufacturingPlanningTab} from './manufacturingPlanningStore';
import ManufacturingPlanningWorkstations from './ManufacturingPlanningWorkstations/ManufacturingPlanningWorkstations';

type Props = {
  tab: ManufacturingPlanningTab;
};

const ManufacturingPlanning: FC<Props> = ({tab}) => {
  const t = useTranslations();
  const [date, setDate] = useAtom(manufacturingPlanningDateAtom);
  const [view, setView] = useState<CalendarView>(CalendarView.resourceTimelineDay);
  const {settings} = useSettings();
  const ref = useRef<FullCalendar>(null);
  const {push} = useRouter();

  const calendarProps = useMemo(() => {
    const weekDays = [1, 2, 3, 4, 5, 6, 7];

    const diff = difference(weekDays, settings?.manufacturing?.workingDays || []);

    const hiddenDays = diff.length === 7 ? [] : diff.map((day) => (day === 7 ? 0 : day));

    return {
      daysOfWeek: settings?.manufacturing?.workingDays?.map((day) => (day === 7 ? 0 : day)) || [],
      endTime: settings?.manufacturing?.workDayEndTime || '24:00:00',
      hiddenDays,
      startTime: settings?.manufacturing?.workDayStartTime || '00:00:00',
      view,
    };
  }, [
    view,
    settings?.manufacturing?.workDayEndTime,
    settings?.manufacturing?.workDayStartTime,
    settings?.manufacturing?.workingDays,
  ]);

  useEffect(() => {
    const api = ref.current?.getApi();

    if (!api || !calendarProps.hiddenDays.includes(date.getDay())) return;

    const currentDay = date.getDay();
    const nextAvailableDay = calendarProps.daysOfWeek.find((day) => day > currentDay) ?? calendarProps.daysOfWeek[0];

    setDate((prev) => {
      if (!calendarProps.hiddenDays.includes(prev.getDay())) return prev;

      const newDate = new Date(prev);
      const daysToAdd =
        nextAvailableDay > currentDay ? nextAvailableDay - currentDay : 7 - currentDay + nextAvailableDay;
      newDate.setDate(newDate.getDate() + daysToAdd);
      return newDate;
    });
  }, [calendarProps.daysOfWeek, calendarProps.hiddenDays, date, ref, setDate]);

  useEffect(() => {
    const api = ref.current?.getApi();
    if (api) api.changeView(view);
  }, [view]);

  return (
    <Page>
      <PageTitle>{`${t('task management')} - ${t('manufacturing')}`}</PageTitle>
      <PageHeader>
        <PageHeaderTitle>{t('task management')}</PageHeaderTitle>
        <div className='flex items-center'>
          <Button
            className='px-2'
            onClick={() => {
              const api = ref.current?.getApi();
              if (api) {
                api.prev();
                setDate(api.getDate());
              }
            }}
            variant='ghost'
          >
            <ChevronLeftIcon />
          </Button>
          <Button
            className='px-2'
            onClick={() => {
              const api = ref.current?.getApi();
              if (api) {
                api.next();
                setDate(api.getDate());
              }
            }}
            variant='ghost'
          >
            <ChevronRightIcon />
          </Button>
          <PageHeaderTitle className='capitalize'>
            {(tab !== ManufacturingPlanningTab.orders || view === CalendarView.resourceTimelineDay) && (
              <>
                {formatDay(date)}, {formatDate(date) || ''}
              </>
            )}
            {tab === ManufacturingPlanningTab.orders && view === CalendarView.resourceTimelineWeek && (
              <>
                {formatDate(startOfWeek(date, {weekStartsOn: 1})) || ''} -{' '}
                {formatDate(endOfWeek(date, {weekStartsOn: 1})) || ''}
              </>
            )}
            {tab === ManufacturingPlanningTab.orders && view === CalendarView.resourceTimelineMonth && (
              <>{format(date, 'MMMM yyyy')}</>
            )}
          </PageHeaderTitle>
        </div>
        <div className='grow' />
        {tab === ManufacturingPlanningTab.orders && (
          <ManufacturingPlanningOrdersButtons setView={setView} view={view} />
        )}
        <Button
          onClick={() => {
            const api = ref.current?.getApi();
            if (api) {
              api.today();
              setDate(api.getDate());
            }
          }}
          variant='secondary'
        >
          <CalendarIcon /> {t('today')}
        </Button>
        <Tabs onValueChange={(value) => push(`/manufacturing/planning/${value}`)} value={tab} variant='menu'>
          <TabsList variant='menu'>
            <TabsTrigger value={ManufacturingPlanningTab.orders} variant='menu'>
              {t(ManufacturingPlanningTab.orders)}
            </TabsTrigger>
            <TabsTrigger value={ManufacturingPlanningTab.employees} variant='menu'>
              {t(ManufacturingPlanningTab.employees)}
            </TabsTrigger>
            <TabsTrigger value={ManufacturingPlanningTab.workstations} variant='menu'>
              {t(ManufacturingPlanningTab.workstations)}
            </TabsTrigger>
          </TabsList>
        </Tabs>
      </PageHeader>
      <div className='px-6'>
        {tab === ManufacturingPlanningTab.orders && (
          <ManufacturingPlanningOrders calendarRef={ref} date={date} {...calendarProps} />
        )}
        {tab === ManufacturingPlanningTab.employees && (
          <ManufacturingPlanningEmployees calendarRef={ref} date={date} {...calendarProps} />
        )}
        {tab === ManufacturingPlanningTab.workstations && (
          <ManufacturingPlanningWorkstations calendarRef={ref} date={date} {...calendarProps} />
        )}
      </div>
    </Page>
  );
};

export default ManufacturingPlanning;
