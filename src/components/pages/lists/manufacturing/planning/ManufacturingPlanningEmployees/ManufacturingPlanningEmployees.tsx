import {FC, useCallback, useState} from 'react';

import FullCalendar from '@fullcalendar/react';
import {
  differenceInMinutes,
  endOfDay,
  isAfter,
  isBefore,
  isWithinInterval,
  parse,
  parseISO,
  startOfDay,
} from 'date-fns';
import {filter, find, sumBy} from 'lodash';

import useEmployees from '@/hooks/useEmployees';
import useTaskActions from '@/hooks/useTaskActions';
import useTasks from '@/hooks/useTasks';
import {Id} from '@/types/global';
import {EmployeeActiveTimeOff} from '@/types/manufacturing';

import AssignEmployeesModal from './AssignEmployeesModal';
import {CalendarView, defaultCalendarProps} from '../manufacturingPlanningStore';
import ManufacturingPlanningEmployeesEvent from './ManufacturingPlanningEmployeesEvent';
import ManufacturingPlanningEmployeesResource from './ManufacturingPlanningEmployeesResource';

type Props = {
  calendarRef: React.RefObject<FullCalendar | null>;
  date: Date;
  daysOfWeek: number[];
  endTime: string;
  hiddenDays: number[];
  startTime: string;
  view: CalendarView;
};

const ManufacturingPlanningEmployees: FC<Props> = ({calendarRef, date, daysOfWeek, endTime, hiddenDays, startTime}) => {
  const {tasks} = useTasks(date);
  const {updateTaskEmployees} = useTaskActions();
  const {employees} = useEmployees({workersOnly: true});
  const [selectedTask, setSelectedTask] = useState<{
    id: string;
    name: string;
    numberOfAssignees: number;
    orderId: string;
  }>();

  const calculatePercentage = useCallback(
    (id: string) => {
      const workDayStartTime = parse(startTime, 'HH:mm:ss', date);
      const workDayEndTime = parse(endTime, 'HH:mm:ss', date);

      const totalWorkMinutes = sumBy(
        filter(tasks, (task) => task.assignedEmployees.map((resource) => resource.id).includes(id)),
        (task) => {
          let limitedEndTime = parseISO(task.endTime);
          let limitedStartTime = parseISO(task.startTime);

          if (isAfter(limitedEndTime, workDayEndTime)) {
            limitedEndTime = workDayEndTime;
          }
          if (isBefore(limitedStartTime, workDayStartTime)) {
            limitedStartTime = workDayStartTime;
          }

          return Math.abs(differenceInMinutes(limitedEndTime, limitedStartTime));
        },
      );
      if (totalWorkMinutes == undefined) return undefined;

      return (totalWorkMinutes / differenceInMinutes(workDayEndTime, workDayStartTime)) * 100;
    },
    [date, endTime, startTime, tasks],
  );

  const getEmployeeActiveTimeoffs = useCallback(
    (activeTimeoffs: EmployeeActiveTimeOff[]) =>
      activeTimeoffs?.length > 0 &&
      find(activeTimeoffs, (leave) =>
        isWithinInterval(date, {
          end: endOfDay(new Date(leave.endTime)),
          start: startOfDay(new Date(leave.startTime)),
        }),
      ) !== undefined
        ? activeTimeoffs
        : undefined,
    [date],
  );

  return (
    <>
      <FullCalendar
        {...defaultCalendarProps({daysOfWeek, endTime, hiddenDays, initialDate: date, startTime, window})}
        eventContent={({event, isDragging}) => (
          <ManufacturingPlanningEmployeesEvent
            event={event}
            isDragging={isDragging}
            onClick={(value) => setSelectedTask(value)}
          />
        )}
        eventDrop={({event, newResource, oldResource}) => {
          let newTask = {
            ...event.extendedProps.orig,
            assignedEmployees: [
              ...filter(event.extendedProps.orig.assignedEmployees, (employee) => employee.id !== oldResource?.id),
              {id: newResource?.id},
            ],
          };

          updateTaskEmployees(
            newTask.id,
            newTask.assignedEmployees?.map((employee: Id) => employee.id),
            newTask.order?.id,
            newTask.name,
          );
        }}
        eventResourceEditable={true}
        events={tasks.map((task) => ({
          ...task,
          end: task.endTime,
          orig: task,
          resourceEditable: true,
          resourceIds: task.assignedEmployees.map((resource) => resource.id),
          start: task.startTime,
          title: task.name,
        }))}
        ref={calendarRef}
        resourceLabelContent={({resource}) => (
          <ManufacturingPlanningEmployeesResource
            percentage={calculatePercentage(resource.id)}
            resource={resource}
            timeOffs={getEmployeeActiveTimeoffs(resource.extendedProps.activeTimeoffs)}
          />
        )}
        resources={employees.map((employee) => ({...employee, title: employee.name}))}
      />
      {selectedTask && (
        <AssignEmployeesModal
          onClose={() => setSelectedTask(undefined)}
          orderId={selectedTask.orderId}
          task={selectedTask}
        />
      )}
    </>
  );
};

export default ManufacturingPlanningEmployees;
