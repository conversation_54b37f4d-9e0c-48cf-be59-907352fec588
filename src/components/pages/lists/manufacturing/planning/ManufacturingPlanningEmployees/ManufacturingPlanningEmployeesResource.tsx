import {FC} from 'react';

import {ResourceApi} from '@fullcalendar/resource';
import {BanIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';

import {Badge} from '@/components/ui/Badge';
import WithLink from '@/components/ui/special/WithLink';
import {Tooltip, TooltipContent, TooltipTrigger} from '@/components/ui/Tooltip';
import {EmployeeActiveTimeOff} from 'types/manufacturing';
import {formatDate} from 'utils/format';

type ResourceLabelType = {
  percentage: number | undefined;
  resource: ResourceApi;
  timeOffs?: EmployeeActiveTimeOff[];
};

const ManufacturingPlanningEmployeesResource: FC<ResourceLabelType> = ({percentage, resource, timeOffs}) => {
  const t = useTranslations();

  return (
    <div className='flex items-center justify-between gap-2'>
      <WithLink className='shrink truncate hover:underline' href={`/employees/${resource.id}`} target='_blank'>
        <Tooltip>
          <TooltipTrigger asChild>
            <span>{resource.title}</span>
          </TooltipTrigger>
          <TooltipContent>{resource.title}</TooltipContent>
        </Tooltip>
      </WithLink>
      <div className='mr-1 flex shrink-0 items-center gap-1'>
        {!!timeOffs && (
          <Tooltip>
            <TooltipTrigger asChild>
              <BanIcon className='size-5 shrink-0 cursor-pointer text-alert-red' />
            </TooltipTrigger>
            <TooltipContent className='flex flex-col'>
              {t('employee leaves')}:
              {timeOffs.map((leave, index) => (
                <div key={`${leave.id}-${index}`}>
                  {formatDate(leave.startTime)} - {formatDate(leave.endTime)}
                </div>
              ))}
            </TooltipContent>
          </Tooltip>
        )}
        {percentage !== undefined && (
          <Tooltip>
            <TooltipTrigger asChild>
              <Badge className='cursor-pointer bg-alert-light-gray'>{Math.round(percentage)}%</Badge>
            </TooltipTrigger>
            <TooltipContent>
              {Math.round(percentage)}% {t('done')}
            </TooltipContent>
          </Tooltip>
        )}
      </div>
    </div>
  );
};

export default ManufacturingPlanningEmployeesResource;
