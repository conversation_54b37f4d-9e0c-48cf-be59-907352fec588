import {FC, useState} from 'react';

import {CircleAlertIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';

import {Button} from '@/components/ui/Button';
import {Sheet, SheetContent, SheetOverlay, SheetPage, SheetTitle} from '@/components/ui/Sheet';
import {ToggleGroup, ToggleGroupItem} from '@/components/ui/ToggleGroup';
import useTaskActions from '@/hooks/useTaskActions';

type Props = {
  onClose: () => void;
  orderId: string;
  task: {id: string; name: string; numberOfAssignees: number};
};

const AssignEmployeesModal: FC<Props> = ({onClose, orderId, task}) => {
  const t = useTranslations();
  const [numberOfAssignees, setNumberOfAssignees] = useState(task.numberOfAssignees);
  const {updateTaskRequiredEmployees} = useTaskActions();

  return (
    <Sheet onOpenChange={onClose} open>
      <SheetOverlay />
      <SheetPage className='min-h-0'>
        <SheetTitle className='flex items-center gap-2'>
          <CircleAlertIcon className='shrink-0 fill-red-700 text-white' />{' '}
          {t('how many employees do you want to assign to this task?')}
        </SheetTitle>
        <SheetContent className='mx-4 mb-2 flex justify-center'>
          <ToggleGroup
            onValueChange={(value) => setNumberOfAssignees(Number(value))}
            type='single'
            value={numberOfAssignees.toString()}
          >
            {Array.from({length: 10}, (_, i) => (
              <ToggleGroupItem key={i} value={(i + 1).toString()}>
                {i + 1}
              </ToggleGroupItem>
            ))}
          </ToggleGroup>
        </SheetContent>
        <div className='mr-6 flex justify-end'>
          <Button
            onClick={() => updateTaskRequiredEmployees(task.id, numberOfAssignees, orderId, task.name).then(onClose)}
          >
            {t('confirm')}
          </Button>
        </div>
      </SheetPage>
    </Sheet>
  );
};

export default AssignEmployeesModal;
