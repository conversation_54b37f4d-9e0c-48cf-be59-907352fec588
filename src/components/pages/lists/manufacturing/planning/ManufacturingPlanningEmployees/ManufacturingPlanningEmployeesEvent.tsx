import {FC, useEffect, useRef} from 'react';

import {EventImpl} from '@fullcalendar/core/internal';
import {useAtomValue} from 'jotai';
import {EllipsisVerticalIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';

import {Link} from '@/components/ui/special/Link';
import {defaultWorkingHoursPerDayAtom} from 'store/defaults';
import {ManufacturingTaskStatus} from 'types/global';
import {getBgColor} from 'utils/colors';
import {classes, minutesToWorkHoursTimeString} from 'utils/common';

type CalendarEventType = {
  event: EventImpl;
  isDragging?: boolean;
  onClick?: (value: {id: string; name: string; numberOfAssignees: number; orderId: string}) => void;
};

const ManufacturingPlanningEmployeesEvent: FC<CalendarEventType> = ({event, isDragging, onClick = () => {}}) => {
  const ref = useRef<HTMLDivElement | null>(null);
  const t = useTranslations();
  const workingHoursPerDay = useAtomValue(defaultWorkingHoursPerDayAtom);

  useEffect(() => {
    const node = ref.current;
    if (node) {
      const parentElement = node.parentNode?.parentNode?.parentNode as HTMLElement;
      const parentRowElement = node.parentNode?.parentNode?.parentNode?.parentNode?.parentNode
        ?.parentNode as HTMLElement;

      const handleMouseEnter = () => {
        if (parentElement) parentElement.style.zIndex = '500';
        if (parentElement) parentRowElement.style.zIndex = '500';
      };

      const handleMouseLeave = () => {
        if (parentElement) parentElement.style.zIndex = '';
        if (parentRowElement) parentRowElement.style.zIndex = '';
      };

      node.addEventListener('mouseenter', handleMouseEnter);
      node.addEventListener('mouseleave', handleMouseLeave);

      return () => {
        node.removeEventListener('mouseenter', handleMouseEnter);
        node.removeEventListener('mouseleave', handleMouseLeave);
      };
    }
  }, []);

  return (
    <div
      className={classes(
        'group relative overflow-x-hidden rounded-lg border border-transparent hover:min-w-fit hover:border-black',
        isDragging && 'w-fit',
        getBgColor(event.extendedProps.order.id),
      )}
      onClick={() => {
        onClick({
          id: event.id,
          name: event.extendedProps.name,
          numberOfAssignees: event.extendedProps.numberOfAssignees,
          orderId: event.extendedProps.order.id,
        });
      }}
      ref={ref}
    >
      <div className='relative flex items-center justify-between p-2 text-slate-900'>
        <div
          className={classes(
            'flex flex-col',
            event.extendedProps.status === ManufacturingTaskStatus.DONE && 'line-through',
          )}
        >
          <div
            className={classes(
              'whitespace-nowrap text-sm font-semibold',
              isDragging && 'line-clamp-none whitespace-nowrap',
            )}
          >
            {event.extendedProps.orig.product?.id && (
              <>
                <Link
                  className='hover:underline'
                  href={`/inventory/items/${event.extendedProps.orig.product.id}`}
                  onClick={(event) => event.stopPropagation()}
                  target='_blank'
                >
                  {event.extendedProps.orig.product.name}
                </Link>{' '}
                &bull; {event.extendedProps.quantity}{' '}
                {t(`unit.name.${event.extendedProps.measurementUnit.name}` as any)}
              </>
            )}
            &bull; {t(event.extendedProps.statusReason || event.extendedProps.status)}
          </div>
          <div className={classes('whitespace-nowrap text-xs', isDragging && 'line-clamp-none whitespace-nowrap')}>
            <Link
              className='hover:underline'
              href={`/manufacturing/orders/${event.extendedProps.order.id}`}
              onClick={(event) => event.stopPropagation()}
              target='_blank'
            >
              {event.extendedProps.order.number}
            </Link>{' '}
            &bull; {event.title} &bull;{' '}
            {minutesToWorkHoursTimeString(event.extendedProps.orig.durationInMinutes, workingHoursPerDay)}
          </div>
        </div>
        {event.extendedProps.status !== ManufacturingTaskStatus.DONE && <EllipsisVerticalIcon />}
      </div>
    </div>
  );
};

export default ManufacturingPlanningEmployeesEvent;
