import {FC, useCallback} from 'react';

import FullCalendar from '@fullcalendar/react';
import {differenceInMinutes, isAfter, isBefore, parse, parseISO} from 'date-fns';
import {filter, sumBy} from 'lodash';

import useTaskActions from '@/hooks/useTaskActions';
import useTasks from '@/hooks/useTasks';
import useWorkstations from '@/hooks/useWorkstations';
import {Id} from '@/types/global';

import {CalendarView, defaultCalendarProps} from '../manufacturingPlanningStore';
import ManufacturingPlanningWorkstationsEvent from './ManufacturingPlanningWorkstationsEvent';
import ManufacturingPlanningWorkstationsResource from './ManufacturingPlanningWorkstationsResource';

type Props = {
  calendarRef: React.RefObject<FullCalendar | null>;
  date: Date;
  daysOfWeek: number[];
  endTime: string;
  hiddenDays: number[];
  startTime: string;
  view: CalendarView;
};

const ManufacturingPlanningWorkstations: FC<Props> = ({
  calendarRef,
  date,
  daysOfWeek,
  endTime,
  hiddenDays,
  startTime,
}) => {
  const {tasks} = useTasks(date);
  const {updateTaskWorkstations} = useTaskActions();
  const {workstations} = useWorkstations();

  const calculatePercentage = useCallback(
    (id: string) => {
      const workDayStartTime = parse(startTime, 'HH:mm:ss', date);
      const workDayEndTime = parse(endTime, 'HH:mm:ss', date);

      const totalWorkMinutes = sumBy(
        filter(tasks, (task) => task.assignedWorkstations.map((resource) => resource.id).includes(id)),
        (task) => {
          let limitedEndTime = parseISO(task.endTime);
          let limitedStartTime = parseISO(task.startTime);

          if (isAfter(limitedEndTime, workDayEndTime)) {
            limitedEndTime = workDayEndTime;
          }
          if (isBefore(limitedStartTime, workDayStartTime)) {
            limitedStartTime = workDayStartTime;
          }

          return Math.abs(differenceInMinutes(limitedEndTime, limitedStartTime));
        },
      );
      if (totalWorkMinutes == undefined) return undefined;

      return (totalWorkMinutes / differenceInMinutes(workDayEndTime, workDayStartTime)) * 100;
    },
    [date, endTime, startTime, tasks],
  );

  return (
    <FullCalendar
      {...defaultCalendarProps({daysOfWeek, endTime, hiddenDays, initialDate: date, startTime, window})}
      eventContent={({event, isDragging}) => (
        <ManufacturingPlanningWorkstationsEvent event={event} isDragging={isDragging} />
      )}
      eventDrop={({event, newResource, oldResource}) => {
        let newTask = {
          ...event.extendedProps.orig,
          assignedEmployees: [
            ...filter(event.extendedProps.orig.assignedEmployees, (employee) => employee.id !== oldResource?.id),
            {id: newResource?.id},
          ],
          assignedWorkstations: [
            ...filter(
              event.extendedProps.orig.assignedWorkstations,
              (workstation) => workstation.id !== oldResource?.id,
            ),
            {id: newResource?.id},
          ],
        };

        updateTaskWorkstations(
          newTask.id,
          newTask.assignedWorkstations?.map((workstation: Id) => workstation.id),
          newTask.order?.id,
          newTask.name,
        );
      }}
      events={tasks.map((task) => ({
        ...task,
        end: task.endTime,
        orig: task,
        resourceEditable: true,
        resourceIds: task.assignedWorkstations.map((resource) => resource.id),
        start: task.startTime,
        title: task.name,
      }))}
      ref={calendarRef}
      resourceLabelContent={({resource}) => (
        <ManufacturingPlanningWorkstationsResource percentage={calculatePercentage(resource.id)} resource={resource} />
      )}
      resources={workstations.map((workstation) => ({...workstation, title: workstation.name}))}
    />
  );
};

export default ManufacturingPlanningWorkstations;
