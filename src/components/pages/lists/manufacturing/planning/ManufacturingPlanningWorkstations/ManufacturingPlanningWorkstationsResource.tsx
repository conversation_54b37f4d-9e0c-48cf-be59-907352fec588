import {FC} from 'react';

import {ResourceApi} from '@fullcalendar/resource';
import {useTranslations} from 'next-intl';

import {Badge} from '@/components/ui/Badge';
import {Tooltip, TooltipContent, TooltipTrigger} from '@/components/ui/Tooltip';

type ResourceLabelType = {
  percentage?: number;
  resource: ResourceApi;
};

const ManufacturingPlanningWorkstationsResource: FC<ResourceLabelType> = ({percentage, resource}) => {
  const t = useTranslations();

  return (
    <div className='flex items-center justify-between gap-2'>
      <Tooltip>
        <TooltipTrigger asChild>
          <span>{resource.title}</span>
        </TooltipTrigger>
        <TooltipContent>{resource.title}</TooltipContent>
      </Tooltip>

      <div className='mr-1 flex shrink-0 items-center gap-1'>
        {percentage !== undefined && (
          <Tooltip>
            <TooltipTrigger asChild>
              <Badge className='cursor-pointer bg-alert-light-gray'>{Math.round(percentage)}%</Badge>
            </TooltipTrigger>
            <TooltipContent>
              {Math.round(percentage)}% {t('done')}
            </TooltipContent>
          </Tooltip>
        )}
      </div>
    </div>
  );
};

export default ManufacturingPlanningWorkstationsResource;
