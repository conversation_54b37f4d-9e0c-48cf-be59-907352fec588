import {SlotLabelContentArg} from '@fullcalendar/core';
import interactionPlugin from '@fullcalendar/interaction';
import resourceTimelinePlugin from '@fullcalendar/resource-timeline';
import {atom} from 'jotai';

import {formatTime} from '@/utils/format';

export enum CalendarView {
  resourceTimelineDay = 'resourceTimelineDay',
  resourceTimelineMonth = 'resourceTimelineMonth',
  resourceTimelineWeek = 'resourceTimelineWeek',
}

export enum ManufacturingPlanningTab {
  employees = 'employees',
  orders = 'orders',
  workstations = 'workstations',
}

export const manufacturingPlanningTabAtom = atom<ManufacturingPlanningTab>(ManufacturingPlanningTab.orders);
export const manufacturingPlanningDateAtom = atom<Date>(new Date());

export const defaultCalendarProps = ({daysOfWeek, endTime, hiddenDays, initialDate, startTime, window}: any) => ({
  businessHours: {
    daysOfWeek,
    endTime,
    startTime,
  },
  contentHeight: (window?.innerHeight || 800) - 72,
  eventColor: 'transparent',
  eventResourceEditable: false,
  firstDay: 1,
  headerToolbar: {center: '', left: '', right: ''},
  hiddenDays,
  initialDate,
  initialView: 'resourceTimelineDay',
  nowIndicator: true,
  plugins: [resourceTimelinePlugin, interactionPlugin],
  resourceAreaHeaderClassNames: 'hidden',
  resourceAreaHeaderContent: '',
  resourceAreaWidth: 200,
  resourceLabelClassNames: 'border-gray-100!',
  resourceLaneClassNames: 'border-gray-100!',
  resourceOrder: 'title',
  resourcesInitiallyExpanded: false,
  schedulerLicenseKey: 'CC-Attribution-NonCommercial-NoDerivatives',
  slotDuration: {minutes: 30},
  slotLabelClassNames: 'font-normal text-sm',
  slotLabelContent: ({date}: SlotLabelContentArg) => formatTime(date),
  slotMaxTime: endTime,
  slotMinTime: startTime,
  slotMinWidth: 60,
  viewClassNames: '[&>table]:border-none!',
});
