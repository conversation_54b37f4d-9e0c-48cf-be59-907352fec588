import {FC, useMemo} from 'react';

import {ResourceApi} from '@fullcalendar/resource';
import {isAfter, parseISO} from 'date-fns';
import {find, sumBy} from 'lodash';
import {CircleAlertIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';

import {Badge} from '@/components/ui/Badge';
import WithLink from '@/components/ui/special/WithLink';
import {Tooltip, TooltipContent, TooltipTrigger} from '@/components/ui/Tooltip';
import {ManufacturingTaskStatus} from '@/types/global';
import {Task} from 'types/manufacturing';
import {formatDate} from 'utils/format';

type ResourceLabelType = {
  resource: ResourceApi;
};

const ManufacturingPlanningOrdersResource: FC<ResourceLabelType> = ({resource}) => {
  const t = useTranslations();

  const deadline = useMemo(() => {
    const {manufacturingTasks, productionDeadline} = resource.extendedProps;

    if (!productionDeadline) return undefined;

    const isBehindSchedule = find(manufacturingTasks, (task: Task) =>
      isAfter(parseISO(task.endTime), parseISO(productionDeadline)),
    );

    return isBehindSchedule ? productionDeadline : undefined;
  }, [resource.extendedProps]);

  const percentage = useMemo(() => {
    const {durationInMinutes, manufacturingTasks, order} = resource.extendedProps;

    if (!!order) return undefined;

    const totalWorkMinutes = durationInMinutes;
    const doneWorkMinutes = sumBy(manufacturingTasks, (task: Task) =>
      task.status === ManufacturingTaskStatus.DONE ? task.durationInMinutes : 0,
    );

    return totalWorkMinutes ? (doneWorkMinutes / totalWorkMinutes) * 100 : 0;
  }, [resource.extendedProps]);

  return (
    <div className='flex items-center justify-between gap-2'>
      <WithLink
        className='shrink truncate hover:underline'
        href={`/manufacturing/orders/${resource.id}`}
        target='_blank'
      >
        <Tooltip>
          <TooltipTrigger asChild>
            <span>{resource.title}</span>
          </TooltipTrigger>
          <TooltipContent>{resource.title}</TooltipContent>
        </Tooltip>
      </WithLink>
      <div className='mr-1 flex shrink-0 items-center gap-0.5'>
        {deadline && (
          <Tooltip>
            <TooltipTrigger asChild>
              <CircleAlertIcon className='h-6 w-5 fill-red-700 text-white' />
            </TooltipTrigger>
            <TooltipContent className='flex flex-col'>
              {t('production deadline')}:<div>{formatDate(deadline)}</div>
            </TooltipContent>
          </Tooltip>
        )}
        {percentage !== undefined && (
          <Tooltip>
            <TooltipTrigger asChild>
              <Badge className='cursor-pointer bg-alert-light-gray'>{Math.round(percentage)}%</Badge>
            </TooltipTrigger>
            <TooltipContent>
              {Math.round(percentage)}% {t('done')}
            </TooltipContent>
          </Tooltip>
        )}
      </div>
    </div>
  );
};

export default ManufacturingPlanningOrdersResource;
