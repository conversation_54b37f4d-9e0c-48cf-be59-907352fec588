import {FC} from 'react';

import {useTranslations} from 'next-intl';

import {ToggleGroup, ToggleGroupItem} from '@/components/ui/ToggleGroup';

import {CalendarView} from '../manufacturingPlanningStore';

type Props = {
  setView: (view: CalendarView) => void;
  view: CalendarView;
};

const ManufacturingPlanningOrdersButtons: FC<Props> = ({setView, view}) => {
  const t = useTranslations();

  return (
    <ToggleGroup onValueChange={(value) => setView(value as CalendarView)} type='single' value={view}>
      <ToggleGroupItem value={CalendarView.resourceTimelineDay}>{t('days')}</ToggleGroupItem>
      <ToggleGroupItem value={CalendarView.resourceTimelineWeek}>{t('week')}</ToggleGroupItem>
      <ToggleGroupItem value={CalendarView.resourceTimelineMonth}>{t('month')}</ToggleGroupItem>
    </ToggleGroup>
  );
};

export default ManufacturingPlanningOrdersButtons;
