import {FC, useMemo} from 'react';

import FullCalendar from '@fullcalendar/react';
import {format, max, min, parseISO} from 'date-fns';
import {capitalize, flatMap, sumBy} from 'lodash';

import useManufacturingOrders from '@/hooks/useManufacturingOrders';
import {formatDay, formatTime} from '@/utils/format';

import ManufacturingPlanningEvent from './ManufacturingPlanningOrdersEvent';
import ManufacturingPlanningOrdersResource from './ManufacturingPlanningOrdersResource';
import ManufacturingPlanningOrdersResourceChild from './ManufacturingPlanningOrdersResourceChild';
import {CalendarView, defaultCalendarProps} from '../manufacturingPlanningStore';

const calendarViewMap: Record<string, 'day' | 'month' | 'week'> = {
  resourceTimelineDay: 'day',
  resourceTimelineMonth: 'month',
  resourceTimelineWeek: 'week',
};

type Props = {
  calendarRef: React.RefObject<FullCalendar | null>;
  date: Date | undefined;
  daysOfWeek: number[];
  endTime: string;
  hiddenDays: number[];
  startTime: string;
  view: CalendarView;
};

const ManufacturingPlanningOrders: FC<Props> = ({
  calendarRef,
  date,
  daysOfWeek,
  endTime,
  hiddenDays,
  startTime,
  view,
}) => {
  const {manufacturingOrders} = useManufacturingOrders({
    date,
    view: calendarViewMap[view] || 'day',
  });

  const resources = useMemo(
    () =>
      manufacturingOrders.map((order) => ({
        ...order,
        children: order.manufacturingTasks.map((task) => ({
          ...task,
          title: task.name,
        })),
        durationInMinutes: sumBy(order.manufacturingTasks, (task) => task.durationInMinutes),
        endTime: max(order.manufacturingTasks.map((task) => parseISO(task.endTime))).toISOString(),
        ranking: order.ranking || 0,
        startTime: min(order.manufacturingTasks.map((task) => parseISO(task.startTime))).toISOString(),
        title: `${order?.number || ''}`,
      })),
    [manufacturingOrders],
  );

  const events = useMemo(
    () => [
      ...flatMap(manufacturingOrders, 'manufacturingTasks').map((task) => ({
        ...task,
        end: task.endTime,
        orig: task,
        resourceIds: [task.id],
        start: task.startTime,
        title: task.name,
      })),
      ...resources.map((order) => ({
        ...order,
        end: order.endTime,
        id: `custom-${order.id}`,
        name: order.title,
        order: {id: order.id},
        orig: order,
        resourceIds: [order.id],
        start: order.startTime,
      })),
    ],
    [manufacturingOrders, resources],
  );

  return (
    <FullCalendar
      {...defaultCalendarProps({daysOfWeek, endTime, hiddenDays, initialDate: date, startTime, window})}
      eventContent={({event, isDragging}) => <ManufacturingPlanningEvent event={event} isDragging={isDragging} />}
      events={events}
      initialView={view}
      ref={calendarRef}
      resourceLabelContent={({resource}) =>
        !!resource._resource.parentId ? (
          <ManufacturingPlanningOrdersResourceChild resource={resource} />
        ) : (
          <ManufacturingPlanningOrdersResource resource={resource} />
        )
      }
      resourceOrder={'ranking'}
      resources={resources}
      slotDuration={(() => {
        switch (view) {
          case 'resourceTimelineDay':
            return {minutes: 30};
          case 'resourceTimelineMonth':
          case 'resourceTimelineWeek':
            return {days: 1};
        }
      })()}
      slotLabelContent={({date}) => {
        switch (view) {
          case 'resourceTimelineDay':
            return formatTime(date);
          case 'resourceTimelineMonth':
            return format(date, 'dd');
          case 'resourceTimelineWeek':
            return `${capitalize(formatDay(date))}, ${format(date, 'dd')}`;
        }
      }}
    />
  );
};

export default ManufacturingPlanningOrders;
