import {FC} from 'react';

import {ResourceApi} from '@fullcalendar/resource';

import {Tooltip, TooltipContent, TooltipTrigger} from '@/components/ui/Tooltip';

type ResourceLabelType = {
  resource: ResourceApi;
};

const ManufacturingPlanningOrdersResourceChild: FC<ResourceLabelType> = ({resource}) => {
  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <div className='cursor-pointer truncate'>{resource.title}</div>
      </TooltipTrigger>
      <TooltipContent>{resource.title}</TooltipContent>
    </Tooltip>
  );
};

export default ManufacturingPlanningOrdersResourceChild;
