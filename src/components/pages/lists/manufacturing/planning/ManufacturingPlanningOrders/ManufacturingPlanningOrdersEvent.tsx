import {FC, useEffect, useMemo, useRef} from 'react';

import {EventImpl} from '@fullcalendar/core/internal';
import {useAtomValue} from 'jotai';
import {useTranslations} from 'next-intl';

import {Link} from '@/components/ui/special/Link';
import {defaultWorkingHoursPerDayAtom} from 'store/defaults';
import {Id, ManufacturingTaskStatus} from 'types/global';
import {getBgColor} from 'utils/colors';
import {classes, minutesToWorkHoursTimeString} from 'utils/common';

type CalendarEventType = {
  event: EventImpl;
  isDragging?: boolean;
};

const ManufacturingPlanningEvent: FC<CalendarEventType> = ({event, isDragging}) => {
  const ref = useRef<HTMLDivElement | null>(null);
  const t = useTranslations();
  const workingHoursPerDay = useAtomValue(defaultWorkingHoursPerDayAtom);

  const isCustom = useMemo(() => event.id.startsWith('custom-'), [event]);

  useEffect(() => {
    const node = ref.current;
    if (node) {
      const parentElement = node.parentNode?.parentNode?.parentNode as HTMLElement;
      const parentRowElement = node.parentNode?.parentNode?.parentNode?.parentNode?.parentNode
        ?.parentNode as HTMLElement;

      const handleMouseEnter = () => {
        if (parentElement) parentElement.style.zIndex = '500';
        if (parentElement) parentRowElement.style.zIndex = '500';
      };

      const handleMouseLeave = () => {
        if (parentElement) parentElement.style.zIndex = '';
        if (parentRowElement) parentRowElement.style.zIndex = '';
      };

      node.addEventListener('mouseenter', handleMouseEnter);
      node.addEventListener('mouseleave', handleMouseLeave);

      return () => {
        node.removeEventListener('mouseenter', handleMouseEnter);
        node.removeEventListener('mouseleave', handleMouseLeave);
      };
    }
  }, []);

  return (
    <div
      className={classes(
        'group relative cursor-default overflow-x-hidden rounded-lg border border-transparent pr-2 hover:min-w-fit',
        isDragging && 'w-fit',
        getBgColor(event.extendedProps.order.id),
      )}
      ref={ref}
    >
      <div className='relative flex items-center justify-between p-2 text-slate-900'>
        <div
          className={classes(
            'flex flex-col',
            event.extendedProps.status === ManufacturingTaskStatus.DONE && 'line-through',
          )}
        >
          <div
            className={classes(
              'whitespace-nowrap text-sm font-semibold',
              isDragging && 'line-clamp-none whitespace-nowrap',
            )}
          >
            {!isCustom && (
              <>
                {event.extendedProps.assignedEmployees.map((employee: Id) => employee.name).join(', ')} &bull;{' '}
                {event.extendedProps.assignedWorkstations.map((workstation: Id) => workstation.name).join(', ')}
              </>
            )}
            {isCustom && !!event.extendedProps.orig.product?.id && (
              <>
                <Link
                  className='hover:underline'
                  href={`/inventory/items/${event.extendedProps.orig.product.id}`}
                  onClick={(event) => event.stopPropagation()}
                  target='_blank'
                >
                  {event.extendedProps.orig.product.name}
                </Link>{' '}
                &bull; {event.extendedProps.quantity}{' '}
                {t(`unit.id.${event.extendedProps.product.measurementUnit.toLowerCase()}` as any)}
              </>
            )}
          </div>
          <div className={classes('whitespace-nowrap text-xs', isDragging && 'line-clamp-none whitespace-nowrap')}>
            {event.extendedProps.customer && <>{event.extendedProps.customer.name} &bull; </>}
            {minutesToWorkHoursTimeString(event.extendedProps.orig.durationInMinutes, workingHoursPerDay)}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ManufacturingPlanningEvent;
