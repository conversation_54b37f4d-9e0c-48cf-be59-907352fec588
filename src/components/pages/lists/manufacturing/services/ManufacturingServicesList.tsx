import {FC} from 'react';

import {useAtom} from 'jotai';
import {useTranslations} from 'next-intl';

import {Page, PageContent, PageHeader, PageHeaderTitle, PageTitle} from '@/components/ui/special/Page';
import {StoreSearchInput} from '@/components/ui/special/StoreSearchInput';
import {Tabs, TabsList, TabsTrigger} from '@/components/ui/Tabs';

import {
  manufacturingServiceSearchQueryAtom,
  ManufacturingServicesView,
  manufacturingServiceViewAtom,
} from './manufacturingServicesListStore';
import ManufacturingServicesListTable from './ManufacturingServicesListTable';

const ManufacturingServicesList: FC = () => {
  const t = useTranslations();
  const [view, setView] = useAtom(manufacturingServiceViewAtom);

  return (
    <>
      <Page>
        <PageTitle>{`${t(view as any)} - ${t('services')} - ${t('execution')}`}</PageTitle>
        <PageHeader>
          <PageHeaderTitle>{t('services')}</PageHeaderTitle>
          <Tabs onValueChange={(newView) => setView(newView as ManufacturingServicesView)} value={view} variant='menu'>
            <TabsList variant='menu'>
              <TabsTrigger value={ManufacturingServicesView.open} variant='menu'>
                {t('open')}
              </TabsTrigger>
              <TabsTrigger value={ManufacturingServicesView.completed} variant='menu'>
                {t('completed')}
              </TabsTrigger>
            </TabsList>
          </Tabs>
          <div className='grow' />
          <StoreSearchInput
            autoFocus
            div={{className: 'grow'}}
            placeholder={t('search by order number')}
            store={manufacturingServiceSearchQueryAtom}
          />
          {/*<ManufacturingListFiltersButton />*/}
        </PageHeader>
        {/*<PageFilters className='flex justify-between'>*/}
        {/*  <ManufacturingListFilters />*/}
        {/*</PageFilters>*/}
        <PageContent>
          <ManufacturingServicesListTable view={view} />
        </PageContent>
      </Page>
    </>
  );
};

export default ManufacturingServicesList;
