import {FC} from 'react';

import {BookTextIcon, FileChartColumnIcon, ReceiptTextIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';

import {Badge} from '@/components/ui/Badge';
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from '@/components/ui/Select';
import {Link} from '@/components/ui/special/Link';
import TimeAgo from '@/components/ui/special/TimeAgo';
import {WithoutEmpty} from '@/components/ui/special/WithoutEmpty';
import {TableActions, TableCell, TableRow} from '@/components/ui/Table';
import {Tooltip, TooltipContent, TooltipTrigger} from '@/components/ui/Tooltip';
import {useHasPermission} from '@/hooks/helpers/useHasPermission';
import {useRouter} from '@/hooks/helpers/useRouter';
import useServicingOrderActions from '@/hooks/useServicingOrderActions';
import {ServicingStatus} from '@/types/global';
import {ServicingOrder} from '@/types/manufacturing';
import {formatDate, formatNumber} from '@/utils/format';

import {ManufacturingServicesView} from './manufacturingServicesListStore';

type Props = {
  order: ServicingOrder;
  view: ManufacturingServicesView;
};

const statusVariantMap: Record<ServicingStatus, 'badge-info' | 'badge-secondary' | 'badge-success' | 'badge-warning'> =
  {
    [ServicingStatus.DONE]: 'badge-success',
    [ServicingStatus.MANUFACTURED]: 'badge-warning',
    [ServicingStatus.MANUFACTURING]: 'badge-info',
    [ServicingStatus.SUBMITTED]: 'badge-secondary',
  };

const ManufacturingServicesListTableRow: FC<Props> = ({order, view}) => {
  const {hasPermission, isLoading: permissionIsLoading} = useHasPermission();
  const {getAvailableStatuses, updateServicingOrderStatus, validateServicingOrder} = useServicingOrderActions();
  const {push} = useRouter();
  const t = useTranslations();

  if (permissionIsLoading) return null;

  return (
    <TableRow>
      <TableCell>
        <Link className='group flex flex-col whitespace-nowrap' href={`/manufacturing/services/${order.id}`}>
          <div className='group-hover:underline'>{order.number}</div>
        </Link>
      </TableCell>
      <TableCell>
        <WithoutEmpty value={order.customer?.id}>
          <Link className='whitespace-nowrap hover:underline' href={`/customers/${order.customer?.id}`}>
            {order.customer?.name}
          </Link>
        </WithoutEmpty>
      </TableCell>
      <TableCell>{order.service.name}</TableCell>
      <TableCell>
        <WithoutEmpty value={order.assignedTo?.name}>
          <Link className='whitespace-nowrap hover:underline' href={`/employees/${order.assignedTo?.id}`}>
            {order.assignedTo?.name}
          </Link>
        </WithoutEmpty>
      </TableCell>
      <TableCell className='text-right'>
        {formatNumber(order.quantity)}{' '}
        {t(
          order.measurementUnit?.name
            ? (`unit.name.${order.measurementUnit.name.toLowerCase()}` as any)
            : order.service?.measurementUnit
              ? (`unit.id.${order.service.measurementUnit.toLowerCase()}` as any)
              : 'unit.name.pcs',
        )}
      </TableCell>
      <TableCell>
        <WithoutEmpty value={order.productionDeadline}>{formatDate(order.productionDeadline)}</WithoutEmpty>
      </TableCell>
      {view === ManufacturingServicesView.completed && (
        <TableCell>
          <TimeAgo date={order.updateTime} hide={['days', 'week', 'weeks']} useYesterday />
        </TableCell>
      )}
      <TableCell>
        {order.status === ServicingStatus.DONE && <Badge variant='success'>{t(order.status)}</Badge>}
        {order.status !== ServicingStatus.DONE && (
          <div className='flex items-center'>
            <Select
              onValueChange={async (value) => {
                if (validateServicingOrder(order)) {
                  if (value === ServicingStatus.DONE) {
                    push(`/manufacturing/services/${order.id}/consumption`);
                  } else if (value === ServicingStatus.MANUFACTURED) {
                    push(`/manufacturing/services/${order.id}/report/preview`);
                  } else {
                    updateServicingOrderStatus(order, value as ServicingStatus);
                  }
                }
              }}
              value={order.status}
            >
              <SelectTrigger
                className='w-fit self-end'
                size='badge-md'
                variant={statusVariantMap[order.status] || 'none'}
              >
                <SelectValue>{t(order.status)}</SelectValue>
              </SelectTrigger>
              <SelectContent>
                {getAvailableStatuses(order.status).map((status) => (
                  <SelectItem key={status} value={status}>
                    {t(status)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        )}
      </TableCell>

      <TableActions>
        <WithoutEmpty
          value={
            (hasPermission('financial', 'manufacturing')
              ? !!order.materialIssueNote || [ServicingStatus.DONE, ServicingStatus.MANUFACTURED].includes(order.status)
              : false) || !!order.goodsAccompanyingNotes?.length
          }
        >
          <div className='flex flex-wrap justify-end gap-x-0.5 gap-y-1'>
            {order.goodsAccompanyingNotes.map((note, index) => (
              <Tooltip key={`note-${note.id}-${index}`}>
                <TooltipTrigger>
                  <Link href={`/manufacturing/services/${order.id}/notes/${note.id}/preview`}>
                    <BookTextIcon strokeWidth={1} />
                  </Link>
                </TooltipTrigger>
                <TooltipContent className='flex flex-col items-center'>
                  <div>{t('goods accompanying note')}</div>
                  <div>{note.number}</div>
                </TooltipContent>
              </Tooltip>
            ))}
            {order.materialIssueNote && (
              <Tooltip>
                <TooltipTrigger>
                  <Link href={`/inventory/consumptions/${order.materialIssueNote?.id}`}>
                    <ReceiptTextIcon strokeWidth={1} />
                  </Link>
                </TooltipTrigger>
                <TooltipContent className='flex flex-col items-center'>
                  <div>{t('consumption')}</div>
                  <div>{order.materialIssueNote?.number}</div>
                </TooltipContent>
              </Tooltip>
            )}
            {[ServicingStatus.DONE, ServicingStatus.MANUFACTURED].includes(order.status) && (
              <Tooltip>
                <TooltipTrigger>
                  <Link
                    href={`/manufacturing/services/${order.id}/report${[ServicingStatus.MANUFACTURING, ServicingStatus.SUBMITTED].includes(order.status) ? '/preview' : ''}`}
                  >
                    <FileChartColumnIcon strokeWidth={1} />
                  </Link>
                </TooltipTrigger>
                <TooltipContent>{t('service report')}</TooltipContent>
              </Tooltip>
            )}
          </div>
        </WithoutEmpty>
      </TableActions>
    </TableRow>
  );
};

export default ManufacturingServicesListTableRow;
