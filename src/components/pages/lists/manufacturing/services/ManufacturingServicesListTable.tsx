import {FC, useMemo} from 'react';

import {useAtomValue} from 'jotai';
import {useTranslations} from 'next-intl';

import {
  Table,
  TableBody,
  TableContainer,
  TableHead,
  TableHeadActions,
  TableHeader,
  TableRow,
} from '@/components/ui/Table';
import useHeight from '@/hooks/helpers/useHeight';
import useServicingOrders from '@/hooks/useServicingOrders';
import {ServicingStatus} from '@/types/global';

import {manufacturingServiceSearchQueryAtom, ManufacturingServicesView} from './manufacturingServicesListStore';
import ManufacturingServicesListTableRow from './ManufacturingServicesListTableRow';

type Props = {
  view: ManufacturingServicesView;
};

const ManufacturingServicesListTable: FC<Props> = ({view}) => {
  const {elementRef} = useHeight();
  const statuses = useMemo(() => {
    let defaultValue: ServicingStatus[] = [];
    switch (view) {
      case ManufacturingServicesView.completed:
        defaultValue = [ServicingStatus.DONE];
        break;
      case ManufacturingServicesView.open:
        defaultValue = [ServicingStatus.SUBMITTED, ServicingStatus.MANUFACTURING, ServicingStatus.MANUFACTURED];
        break;
    }

    return defaultValue;
  }, [view]);
  const search = useAtomValue(manufacturingServiceSearchQueryAtom);
  const sort = useMemo(() => (view === ManufacturingServicesView.completed ? ['-updateTime'] : []), [view]);
  const {isLoading, servicingOrders} = useServicingOrders({
    search,
    sort: sort as any,
    statuses,
  });
  const t = useTranslations();

  return (
    <TableContainer ref={elementRef}>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>{t('order#')}</TableHead>
            <TableHead>{t('customer')}</TableHead>
            <TableHead>{t('service')}</TableHead>
            <TableHead>{t('assigned to')}</TableHead>
            <TableHead className='text-right'>{t('quantity')}</TableHead>
            <TableHead>{t('production deadline')}</TableHead>
            {view === ManufacturingServicesView.completed && <TableHead>{t('date completed')}</TableHead>}
            <TableHead>{t('order status')}</TableHead>
            <TableHeadActions className='text-right'>{t('documents')}</TableHeadActions>
          </TableRow>
        </TableHeader>
        <TableBody className='overflow-y-hidden' isValidating={isLoading}>
          {servicingOrders?.map((order, index) => (
            <ManufacturingServicesListTableRow key={`${order.id}-${index}`} order={order} view={view} />
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );
};

export default ManufacturingServicesListTable;
