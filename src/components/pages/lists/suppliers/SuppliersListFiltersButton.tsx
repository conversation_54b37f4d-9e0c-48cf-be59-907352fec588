import {FC, useCallback, useMemo, useState} from 'react';

import {Accordion} from '@/components/ui/Accordion';
import {FiltersSheet} from '@/components/ui/special/FiltersSheet';

type OwnFiltersProps = {};

const OwnFilters: FC<OwnFiltersProps> = ({}) => {
  const defaultValue = useMemo(() => [], []);

  return <Accordion defaultValue={defaultValue} type='multiple'></Accordion>;
};

const useSalesFilters = () => {
  // const statusFilter = useMemo(() => {
  //   let defaultValue: SaleStatus[] = [];
  //   switch (view) {
  //     case SalesView.active:
  //       if (saleType === SaleType.ORDER) {
  //         defaultValue = [SaleStatus.SUBMITTED, SaleStatus.SHIPPING, SaleStatus.READY_TO_SHIP];
  //       } else {
  //         defaultValue = [SaleStatus.IN_QUOTATION];
  //       }
  //       break;
  //     case SalesView.delivered:
  //       defaultValue = [SaleStatus.DELIVERED];
  // sort={['-updateTime']}
  //       break;
  //     case SalesView.canceled:
  //       defaultValue = [SaleStatus.CANCELED];
  //       break;
  //   }
  //   return defaultValue;
  // }, [saleType, view]);

  const isApplyEnabled = useMemo(() => {
    return false;
  }, []);

  const handleApply = useCallback(() => {}, []);

  const handleClear = useCallback(() => {}, []);

  const handleReset = useCallback(() => {}, []);

  return {
    handleApply,
    handleClear,
    handleReset,
    isApplyEnabled,
  };
};

const SuppliersListFiltersButton: FC = () => {
  const [open, setOpen] = useState(false);
  const {handleApply, handleClear, handleReset, isApplyEnabled} = useSalesFilters();

  return (
    <FiltersSheet
      isApplyEnabled={isApplyEnabled}
      onApply={handleApply}
      onClear={handleClear}
      onClick={() => setOpen(true)}
      onOpenChange={(isOpen) => {
        if (!isOpen) handleReset();
      }}
    >
      {open && <OwnFilters />}
    </FiltersSheet>
  );
};

export default SuppliersListFiltersButton;
