import {FC} from 'react';

import {useAtomValue} from 'jotai';
import {useTranslations} from 'next-intl';

import {
  Table,
  TableBody,
  TableContainer,
  TableHead,
  TableHeadActions,
  TableHeader,
  TableRow,
} from '@/components/ui/Table';
import useHeight from '@/hooks/helpers/useHeight';
import useSuppliers from '@/hooks/useSuppliers';

import {suppliersSearchQueryAtom} from './suppliersListStore';
import SuppliersListTableRow from './SuppliersListTableRow';

const SuppliersListTable: FC = () => {
  const {elementRef} = useHeight();
  const search = useAtomValue(suppliersSearchQueryAtom);
  const {isLoading, suppliers} = useSuppliers({search});
  const t = useTranslations();

  return (
    <TableContainer ref={elementRef}>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>{t('name')}</TableHead>
            <TableHead>{t('email')}</TableHead>
            <TableHead>{t('phone number')}</TableHead>
            <TableHead>{t('city')}</TableHead>
            <TableHead className='text-right'>{t('last delivery took')}</TableHead>
            <TableHeadActions />
          </TableRow>
        </TableHeader>
        <TableBody className='overflow-y-hidden' isValidating={isLoading}>
          {suppliers?.map((supplier, index) => (
            <SuppliersListTableRow key={`${supplier.id}-row-${index}`} supplier={supplier} />
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );
};

export default SuppliersListTable;
