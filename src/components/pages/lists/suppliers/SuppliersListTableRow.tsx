import {FC} from 'react';

import {Trash2Icon} from 'lucide-react';

import {Button} from '@/components/ui/Button';
import {Link} from '@/components/ui/special/Link';
import {WithoutEmpty} from '@/components/ui/special/WithoutEmpty';
import {TableActions, TableCell, TableRow} from '@/components/ui/Table';
import useSupplierActions from '@/hooks/useSupplierActions';
import {Supplier} from '@/types/sales';

type Props = {
  supplier: Supplier;
};

const SuppliersListTableRow: FC<Props> = ({supplier}) => {
  const {deleteSupplier} = useSupplierActions();

  return (
    <TableRow>
      <TableCell>
        <Link className='whitespace-nowrap hover:underline' href={`/suppliers/${supplier.id}`}>
          {supplier.name}
        </Link>
      </TableCell>
      <TableCell>
        <WithoutEmpty value={supplier.contacts?.[0]?.email}>{supplier.contacts?.[0]?.email}</WithoutEmpty>
      </TableCell>
      <TableCell>
        <WithoutEmpty value={supplier.contacts?.[0]?.phoneNumber}>{supplier.contacts?.[0]?.phoneNumber}</WithoutEmpty>
      </TableCell>
      <TableCell>
        <WithoutEmpty value={supplier.addresses?.[0]?.city}>{supplier.addresses?.[0]?.city}</WithoutEmpty>
      </TableCell>
      <TableCell className='text-right'>
        <WithoutEmpty value={supplier.lastOrderDeliveredIn !== null}>{supplier.lastOrderDeliveredIn}</WithoutEmpty>
      </TableCell>
      <TableActions>
        <Button onClick={() => deleteSupplier(supplier.id, supplier.name)} size='icon' variant='none'>
          <Trash2Icon className='size-5 text-red' strokeWidth={1} />
        </Button>
      </TableActions>
    </TableRow>
  );
};

export default SuppliersListTableRow;
