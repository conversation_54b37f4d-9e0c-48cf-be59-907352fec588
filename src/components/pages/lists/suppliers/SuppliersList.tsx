import {useState} from 'react';

import {PlusIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';

import {Button} from '@/components/ui/Button';
import NewSupplierSheet from '@/components/ui/special/NewSupplierSheet';
import {Page, PageContent, PageHeader, PageHeaderTitle, PageTitle} from '@/components/ui/special/Page';
import {StoreSearchInput} from '@/components/ui/special/StoreSearchInput';
import {useRouter} from '@/hooks/helpers/useRouter';
import {NextPageWithLayout} from '@/types/global';

import {suppliersSearchQueryAtom} from './suppliersListStore';
import SuppliersListTable from './SuppliersListTable';

const SuppliersList: NextPageWithLayout = () => {
  const [showNew, setShowNew] = useState(false);
  const t = useTranslations();
  const {push} = useRouter();

  return (
    <>
      {showNew && (
        <NewSupplierSheet
          onClose={() => {
            setShowNew(false);
          }}
          onCreate={(supplier) => push(`/suppliers/${supplier.id}`)}
        />
      )}
      <Page>
        <PageTitle>{t('suppliers')}</PageTitle>
        <PageHeader>
          <PageHeaderTitle>{t('suppliers')}</PageHeaderTitle>
          <div className='grow' />
          <StoreSearchInput
            autoFocus
            div={{className: 'grow'}}
            placeholder={t(
              'search by name, email, phone number, city, tax identification number, identification number',
            )}
            store={suppliersSearchQueryAtom}
          />
          {/*<SuppliersListFiltersButton />*/}
          <Button onClick={() => setShowNew(true)}>
            <PlusIcon />
            {t('add supplier')}
          </Button>
        </PageHeader>
        {/*<PageFilters className='flex justify-between'>*/}
        {/*  <SuppliersListFilters />*/}
        {/*</PageFilters>*/}
        <PageContent>
          <SuppliersListTable />
        </PageContent>
      </Page>
    </>
  );
};

export default SuppliersList;
