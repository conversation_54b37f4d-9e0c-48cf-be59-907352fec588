import {FC, useMemo} from 'react';

import {useAtom, useSet<PERSON>tom} from 'jotai';
import {PlusIcon, Trash2Icon} from 'lucide-react';
import {useTranslations} from 'next-intl';

import {Button, IconButton} from '@/components/ui/Button';
import {Checkbox} from '@/components/ui/Checkbox';
import {Input, InputLabel, NumberInput} from '@/components/ui/Input';
import {WithLabel} from '@/components/ui/Label';
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from '@/components/ui/Select';
import CountriesCombobox from '@/components/ui/special/CountriesCombobox';
import {ToggleGroup, ToggleGroupItem} from '@/components/ui/ToggleGroup';
import {Tooltip, TooltipContent, TooltipTrigger} from '@/components/ui/Tooltip';
import {availableVATRates} from '@/constants/config';
import useAccount from '@/hooks/useAccount';
import useAccountInformation from '@/hooks/useAccountInformation';
import useSettings from '@/hooks/useSettings';
import useSettingsActions from '@/hooks/useSettingsActions';
import {defaultLocaleAtom} from '@/store/defaults';
import {localeAtom} from '@/store/locale';
import {Locale} from '@/types/global';
import {formatVAT} from '@/utils/format';

const GeneralContent: FC = () => {
  const t = useTranslations();
  const {isLoading, settings} = useSettings();
  const {updateSettings} = useSettingsActions();
  const {accountInformation, isLoading: accountIsLoading, updateAccountInformation} = useAccountInformation();
  const [locale, setLocale] = useAtom(localeAtom);
  const setDefaultLocale = useSetAtom(defaultLocaleAtom);
  const {account} = useAccount();

  const handleLanguageSelect = (value: string) => {
    setLocale(value);
  };

  const languages = useMemo(
    () =>
      [
        {id: 'ro', value: t('romanian')},
        {id: 'en', value: t('english')},
        {id: 'hu', value: t('hungarian')},
      ] as const satisfies readonly {id: Locale; value: string}[],
    [t],
  );

  if (isLoading || accountIsLoading) return null;

  return (
    <div className='m-4 flex flex-wrap gap-4'>
      <div className='max-w-lg rounded-lg border p-4'>
        <div className='flex flex-col gap-4'>
          <div className='flex flex-col text-center'>
            <div>{t('account information')}</div>
            <div className='text-sm text-gray-400'>
              {t(
                'complete the information below and select the data you want to be displayed on the commercial documents generated by the system',
              )}
            </div>
          </div>
          <div className='flex flex-col gap-4'>
            <div className='flex w-full items-start gap-4'>
              <WithLabel className='w-full'>
                <Input defaultValue={account?.name || 'Fabriqon'} disabled />
                <InputLabel>{t('company name')}</InputLabel>
              </WithLabel>
              <WithLabel className='w-full border-b border-muted'>
                <div className='flex items-center gap-2'>
                  <Tooltip>
                    <TooltipTrigger>
                      <Checkbox
                        checked={accountInformation.includeSocialCapitalOnDocuments}
                        onCheckedChange={(checked) =>
                          updateAccountInformation({includeSocialCapitalOnDocuments: !!checked})
                        }
                      />
                    </TooltipTrigger>
                    <TooltipContent>{t('include in documents')}</TooltipContent>
                  </Tooltip>

                  <Input
                    className='border-none'
                    defaultValue={accountInformation.socialCapital?.toString() || 0}
                    onBlur={({currentTarget: {value}}) => updateAccountInformation({socialCapital: Number(value)})}
                    onKeyDown={({currentTarget: {value}, key}) => {
                      if (key === 'Enter') {
                        updateAccountInformation({socialCapital: Number(value)});
                      }
                    }}
                  />
                </div>
                <InputLabel>{t('social capital')}</InputLabel>
              </WithLabel>
            </div>
            <div className='flex w-full gap-4'>
              <WithLabel className='w-full border-b border-muted'>
                <div className='flex items-center gap-2'>
                  <Tooltip>
                    <TooltipTrigger>
                      <Checkbox
                        checked={accountInformation.includeTaxIdentificationNumberOnDocuments}
                        onCheckedChange={(checked) =>
                          updateAccountInformation({includeTaxIdentificationNumberOnDocuments: !!checked})
                        }
                      />
                    </TooltipTrigger>
                    <TooltipContent>{t('include in documents')}</TooltipContent>
                  </Tooltip>

                  <Input
                    className='border-none'
                    defaultValue={accountInformation.taxIdentificationNumber}
                    onBlur={({currentTarget: {value}}) => updateAccountInformation({taxIdentificationNumber: value})}
                    onKeyDown={({currentTarget: {value}, key}) => {
                      if (key === 'Enter') {
                        updateAccountInformation({taxIdentificationNumber: value});
                      }
                    }}
                  />
                </div>
                <InputLabel>{t('tax identification number')}</InputLabel>
              </WithLabel>
              <WithLabel className='w-full border-b border-muted'>
                <div className='flex items-center gap-2'>
                  <Tooltip>
                    <TooltipTrigger>
                      <Checkbox
                        checked={accountInformation.includeIdentificationNumberOnDocuments}
                        onCheckedChange={(checked) =>
                          updateAccountInformation({includeIdentificationNumberOnDocuments: !!checked})
                        }
                      />
                    </TooltipTrigger>
                    <TooltipContent>{t('include in documents')}</TooltipContent>
                  </Tooltip>

                  <Input
                    className='border-none'
                    defaultValue={accountInformation.identificationNumber}
                    onBlur={({currentTarget: {value}}) => updateAccountInformation({identificationNumber: value})}
                    onKeyDown={({currentTarget: {value}, key}) => {
                      if (key === 'Enter') {
                        updateAccountInformation({identificationNumber: value});
                      }
                    }}
                  />
                </div>
                <InputLabel>{t('identification number')}</InputLabel>
              </WithLabel>
            </div>
            <div className='flex w-full gap-4'>
              <WithLabel className='w-full border-b border-muted'>
                <div className='flex items-center gap-2'>
                  <Tooltip>
                    <TooltipTrigger>
                      <Checkbox
                        checked={accountInformation.includeEmailOnDocuments}
                        onCheckedChange={(checked) => updateAccountInformation({includeEmailOnDocuments: !!checked})}
                      />
                    </TooltipTrigger>
                    <TooltipContent>{t('include in documents')}</TooltipContent>
                  </Tooltip>

                  <Input
                    className='border-none'
                    defaultValue={accountInformation.email}
                    onBlur={({currentTarget: {value}}) => updateAccountInformation({email: value})}
                    onKeyDown={({currentTarget: {value}, key}) => {
                      if (key === 'Enter') {
                        updateAccountInformation({email: value});
                      }
                    }}
                  />
                </div>
                <InputLabel>{t('email')}</InputLabel>
              </WithLabel>
              <WithLabel className='w-full border-b border-muted'>
                <div className='flex items-center gap-2'>
                  <Tooltip>
                    <TooltipTrigger>
                      <Checkbox
                        checked={accountInformation.includePhoneOnDocuments}
                        onCheckedChange={(checked) => updateAccountInformation({includePhoneOnDocuments: !!checked})}
                      />
                    </TooltipTrigger>
                    <TooltipContent>{t('include in documents')}</TooltipContent>
                  </Tooltip>

                  <Input
                    className='border-none'
                    defaultValue={accountInformation.phone}
                    onBlur={({currentTarget: {value}}) => updateAccountInformation({phone: value})}
                    onKeyDown={({currentTarget: {value}, key}) => {
                      if (key === 'Enter') {
                        updateAccountInformation({phone: value});
                      }
                    }}
                  />
                </div>
                <InputLabel>{t('phone number')}</InputLabel>
              </WithLabel>
            </div>
            <div className='flex w-full flex-col gap-2 rounded-lg border p-4'>
              <WithLabel className='w-full border-b border-muted'>
                <div className='flex items-center gap-2'>
                  <Tooltip>
                    <TooltipTrigger>
                      <Checkbox
                        checked={accountInformation.includeAddressOnDocuments}
                        onCheckedChange={(checked) => updateAccountInformation({includeAddressOnDocuments: !!checked})}
                      />
                    </TooltipTrigger>
                    <TooltipContent>{t('include in documents')}</TooltipContent>
                  </Tooltip>

                  <Input
                    className='border-none'
                    defaultValue={accountInformation.address?.address1}
                    onBlur={({currentTarget: {value}}) =>
                      updateAccountInformation({address: {...accountInformation.address, address1: value}})
                    }
                    onKeyDown={({currentTarget: {value}, key}) => {
                      if (key === 'Enter') {
                        updateAccountInformation({address: {...accountInformation.address, address1: value}});
                      }
                    }}
                  />
                </div>
                <InputLabel>{t('address (str no bl ap st fl)')}</InputLabel>
              </WithLabel>
              <div className='flex items-center gap-2'>
                <WithLabel className='w-full'>
                  <Input
                    defaultValue={accountInformation.address?.city}
                    onBlur={({currentTarget: {value}}) =>
                      updateAccountInformation({address: {...accountInformation.address, city: value}})
                    }
                    onKeyDown={({currentTarget: {value}, key}) => {
                      if (key === 'Enter') {
                        updateAccountInformation({address: {...accountInformation.address, city: value}});
                      }
                    }}
                  />
                  <InputLabel>{t('city')}</InputLabel>
                </WithLabel>
                <WithLabel className='w-full'>
                  <Input
                    defaultValue={accountInformation.address?.state}
                    onBlur={({currentTarget: {value}}) =>
                      updateAccountInformation({address: {...accountInformation.address, state: value}})
                    }
                    onKeyDown={({currentTarget: {value}, key}) => {
                      if (key === 'Enter') {
                        updateAccountInformation({address: {...accountInformation.address, state: value}});
                      }
                    }}
                  />
                  <InputLabel>{t('state')}</InputLabel>
                </WithLabel>
              </div>
              <div className='flex items-center gap-2'>
                <WithLabel className='w-full'>
                  <CountriesCombobox
                    onChange={(value) =>
                      updateAccountInformation({address: {...accountInformation.address, country: value}})
                    }
                    value={accountInformation.address?.country}
                  />
                  <InputLabel>{t('country')}</InputLabel>
                </WithLabel>
                <WithLabel>
                  <Input
                    defaultValue={accountInformation.address?.zip}
                    onBlur={({currentTarget: {value}}) =>
                      updateAccountInformation({address: {...accountInformation.address, zip: value}})
                    }
                    onKeyDown={({currentTarget: {value}, key}) => {
                      if (key === 'Enter') {
                        updateAccountInformation({address: {...accountInformation.address, zip: value}});
                      }
                    }}
                    size='md'
                  />
                  <InputLabel>{t('zip')}</InputLabel>
                </WithLabel>
              </div>
            </div>
            <div className='flex items-center justify-between'>
              {t('bank accounts')}
              <IconButton
                className='self-start'
                icon={<PlusIcon />}
                onClick={() =>
                  updateAccountInformation({
                    bankAccounts: [...(accountInformation.bankAccounts || []), {}],
                    includeBankAccountOnDocuments: [...(accountInformation.includeBankAccountOnDocuments || []), false],
                  })
                }
              />
            </div>
            {accountInformation.bankAccounts?.map((bankAccount, index) => (
              <div
                className='flex w-full flex-col items-center gap-4 rounded-lg border p-4'
                key={`${JSON.stringify(bankAccount)}-${index}`}
              >
                <div className='flex w-full items-center justify-between'>
                  <Tooltip>
                    <TooltipTrigger>
                      <Checkbox
                        checked={!!accountInformation.includeBankAccountOnDocuments?.[index]}
                        onCheckedChange={(checked) =>
                          updateAccountInformation({
                            includeBankAccountOnDocuments: accountInformation.includeBankAccountOnDocuments?.map(
                              (v, i) => (i === index ? !!checked : v),
                            ),
                          })
                        }
                      />
                    </TooltipTrigger>
                    <TooltipContent>{t('include in documents')}</TooltipContent>
                  </Tooltip>

                  <Button
                    className=''
                    onClick={() => {
                      updateAccountInformation({
                        bankAccounts: accountInformation.bankAccounts?.filter((_, i) => i !== index),
                        includeBankAccountOnDocuments: accountInformation.includeBankAccountOnDocuments?.filter(
                          (_, i) => i !== index,
                        ),
                      });
                    }}
                    size='icon'
                    variant='ghost'
                  >
                    <Trash2Icon className='size-5 text-red' strokeWidth={1} />
                  </Button>
                </div>
                <WithLabel className='w-full border-b border-muted'>
                  <Input
                    className='border-none'
                    defaultValue={bankAccount.bank}
                    onBlur={({currentTarget: {value}}) =>
                      updateAccountInformation({
                        bankAccounts: accountInformation.bankAccounts?.map((bank, i) =>
                          i === index ? {...bank, bank: value} : bank,
                        ),
                      })
                    }
                    onKeyDown={({currentTarget: {value}, key}) => {
                      if (key === 'Enter') {
                        updateAccountInformation({
                          bankAccounts: accountInformation.bankAccounts?.map((bank, i) =>
                            i === index ? {...bank, bank: value} : bank,
                          ),
                        });
                      }
                    }}
                  />
                  <InputLabel>{t('bank')}</InputLabel>
                </WithLabel>
                <div className='flex w-full items-center gap-4'>
                  <WithLabel className='w-full'>
                    <Input
                      defaultValue={bankAccount.number}
                      onBlur={({currentTarget: {value}}) =>
                        updateAccountInformation({
                          bankAccounts: accountInformation.bankAccounts?.map((bank, i) =>
                            i === index ? {...bank, number: value} : bank,
                          ),
                        })
                      }
                      onKeyDown={({currentTarget: {value}, key}) => {
                        if (key === 'Enter') {
                          updateAccountInformation({
                            bankAccounts: accountInformation.bankAccounts?.map((bank, i) =>
                              i === index ? {...bank, number: value} : bank,
                            ),
                          });
                        }
                      }}
                    />
                    <InputLabel>{t('number')}</InputLabel>
                  </WithLabel>
                  <WithLabel>
                    <Input
                      defaultValue={bankAccount.swiftNumber}
                      onBlur={({currentTarget: {value}}) =>
                        updateAccountInformation({
                          bankAccounts: accountInformation.bankAccounts?.map((bank, i) =>
                            i === index ? {...bank, swiftNumber: value} : bank,
                          ),
                        })
                      }
                      onKeyDown={({currentTarget: {value}, key}) => {
                        if (key === 'Enter') {
                          updateAccountInformation({
                            bankAccounts: accountInformation.bankAccounts?.map((bank, i) =>
                              i === index ? {...bank, swiftNumber: value} : bank,
                            ),
                          });
                        }
                      }}
                    />
                    <InputLabel>{t('swift')}</InputLabel>
                  </WithLabel>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
      <div className='h-fit rounded-lg border p-4 text-center'>
        <div className='flex flex-col gap-4'>
          <div className='flex flex-col'>
            <div>{t('tax and order')}</div>
            <div className='text-sm text-gray-400'>{t('tax and order description')}</div>
          </div>
          <div className='flex flex-col gap-4'>
            <div className='flex flex-col items-center gap-1'>
              <InputLabel>{t('default vat value')}</InputLabel>
              <Select
                onValueChange={(value) => {
                  updateSettings(settings, 'taxRates', {defaultVAT: Number(value)});
                }}
                value={formatVAT(settings?.taxRates.defaultVAT)}
              >
                <SelectTrigger size='sm'>
                  <SelectValue>{formatVAT(settings?.taxRates.defaultVAT)}%</SelectValue>
                </SelectTrigger>
                <SelectContent>
                  {availableVATRates.map((vat) => (
                    <SelectItem key={vat} value={vat}>
                      {formatVAT(Number(vat))}%
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className='flex flex-col items-center gap-1'>
              <InputLabel>
                {t('default delivery time for sales orders')} ({t('hours')})
              </InputLabel>
              <NumberInput
                indicator={t('h')}
                min={0}
                onChange={(value) => {
                  if (value && Number(value) !== settings?.general?.defaultDeliveryTimeForSalesOrders)
                    updateSettings(settings, 'general', {defaultDeliveryTimeForSalesOrders: Number(value)});
                }}
                value={settings?.general?.defaultDeliveryTimeForSalesOrders || 7}
              />
            </div>
          </div>
        </div>
      </div>
      <div className='h-fit rounded-lg border p-4 text-center'>
        <div className='flex flex-col gap-4'>
          <div className='flex flex-col'>
            <div>{t('regionals')}</div>
            <div className='text-sm text-gray-400'>{t('locales description')}</div>
          </div>
          <div className='flex flex-col gap-6'>
            <div className='flex flex-col items-center'>
              <InputLabel>{t('language')}</InputLabel>
              <Select
                onValueChange={(value) => {
                  setDefaultLocale(value);
                  updateSettings(settings, 'general', {defaultLanguage: value});
                  handleLanguageSelect(value);
                }}
                value={locale}
              >
                <SelectTrigger size='md'>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {languages.map((language) => (
                    <SelectItem key={language.id} value={language.id}>
                      {language.value}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className='flex flex-col items-center'>
              <InputLabel>{t('the base currency used for all your operations')}</InputLabel>
              <Input disabled size='sm' value={settings?.general.defaultCurrency} />
            </div>
          </div>
        </div>
      </div>
      <div className='h-fit rounded-lg border p-4 text-center'>
        <div className='flex flex-col gap-4'>
          <div className='flex flex-col'>
            <div>{t('inventory')}</div>
            <div className='text-sm text-gray-400'>{t('inventory description')}</div>
          </div>
          <div className='flex flex-col'>
            <InputLabel>{t('stock value calculation based on')}</InputLabel>
            <ToggleGroup type='single' value={settings.general.inventoryAccountingSettings.method || ''}>
              <ToggleGroupItem disabled value={'Weight'}>
                {t('weighted average')}
              </ToggleGroupItem>
              <ToggleGroupItem value={'FIFO'}>FIFO</ToggleGroupItem>
            </ToggleGroup>
          </div>
        </div>
      </div>
    </div>
  );
};

export default GeneralContent;
