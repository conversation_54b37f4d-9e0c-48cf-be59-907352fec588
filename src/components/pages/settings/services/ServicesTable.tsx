import {FC} from 'react';

import {useTranslations} from 'next-intl';

import {
  Table,
  TableBody,
  TableContainer,
  TableHead,
  TableHeadActions,
  TableHeader,
  TableRow,
} from '@/components/ui/Table';
import useHeight from '@/hooks/helpers/useHeight';
import useEnabledUnits from '@/hooks/useEnabledUnits';
import useServiceTemplates from '@/hooks/useServiceTemplates';

import ServicesTableRow from './ServicesTableRow';

const ServicesTable: FC = () => {
  const {elementRef} = useHeight();
  const {isLoading, services} = useServiceTemplates();
  const {enabledUnits, isLoading: unitsIsLoading, units} = useEnabledUnits();
  const t = useTranslations();

  if (unitsIsLoading) return null;

  return (
    <TableContainer ref={elementRef}>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>{t('name')}</TableHead>
            <TableHead>{t('unit of measure')}</TableHead>
            <TableHead className='text-right'>{t('price')}</TableHead>
            <TableHead className='text-right'>{t('cost per hour')}</TableHead>
            <TableHead className='text-right'>{t('vat rate')}</TableHead>
            <TableHeadActions />
          </TableRow>
        </TableHeader>
        <TableBody className='overflow-y-hidden' isValidating={isLoading}>
          {services.map((service) => (
            <ServicesTableRow enabledUnits={enabledUnits} key={service.id} service={service} units={units} />
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );
};

export default ServicesTable;
