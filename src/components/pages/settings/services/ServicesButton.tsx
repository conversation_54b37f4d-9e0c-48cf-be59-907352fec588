import {FC, KeyboardEvent, useCallback, useState} from 'react';

import {useAtomValue} from 'jotai';
import {PlusIcon, XIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';

import {Button, IconButton} from '@/components/ui/Button';
import {Input} from '@/components/ui/Input';
import useServiceTemplateActions from '@/hooks/useServiceTemplateActions';
import {defaultCurrencyAtom, defaultVATAtom} from '@/store/defaults';
import {classes} from '@/utils/common';

type Props = {
  forTable?: boolean;
};

const ServicesButton: FC<Props> = ({forTable}) => {
  const t = useTranslations();
  const [addEnabled, setAddEnabled] = useState(false);
  const {createService} = useServiceTemplateActions();
  const defaultCurrency = useAtomValue(defaultCurrencyAtom);
  const defaultVat = useAtomValue(defaultVATAtom);

  const handleAdd = useCallback(
    (value: string) =>
      createService({
        measurementUnit: 'PIECE',
        name: value,
        sellPrice: {amount: 0, currency: defaultCurrency},
        vatRate: defaultVat,
      }).then(() => setAddEnabled(false)),
    [createService, defaultCurrency, defaultVat],
  );

  return (
    <div className={classes('flex items-center gap-4', forTable && 'mr-4 w-[288px]')}>
      {!addEnabled && forTable && (
        <IconButton icon={<PlusIcon />} onClick={() => setAddEnabled(true)}>
          {t('add service')}
        </IconButton>
      )}
      {!addEnabled && !forTable && (
        <Button onClick={() => setAddEnabled(true)}>
          <PlusIcon /> {t('add service')}
        </Button>
      )}
      {addEnabled && (
        <>
          <Input
            autoFocus
            className={classes(forTable ? 'w-full' : 'w-[300px]')}
            onKeyDown={({key, target}: KeyboardEvent<HTMLInputElement>) => {
              const value = (target as HTMLInputElement).value;
              if (key === 'Enter' && value) handleAdd(value);
            }}
            placeholder={t('name')}
          />
          <Button onClick={() => setAddEnabled(false)} variant='secondary'>
            <XIcon />
            {t('cancel')}
          </Button>
        </>
      )}
    </div>
  );
};

export default ServicesButton;
