import {FC} from 'react';

import Jo<PERSON> from 'joi';
import {useAtomValue} from 'jotai';
import {Trash2Icon} from 'lucide-react';
import {useTranslations} from 'next-intl';

import {Button} from '@/components/ui/Button';
import {PopoverInput} from '@/components/ui/Input';
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from '@/components/ui/Select';
import {TableActions, TableCell, TableRow} from '@/components/ui/Table';
import {availableVATRates} from '@/constants/config';
import useServiceTemplateActions from '@/hooks/useServiceTemplateActions';
import {defaultCurrencyAtom, defaultVATAtom} from '@/store/defaults';
import {Service, Unit} from '@/types/global';
import {formatCurrency, formatVAT} from '@/utils/format';

type Props = {
  enabledUnits: Unit[];
  service: Service;
  units: Unit[];
};

const ServicesTableRow: FC<Props> = ({enabledUnits, service, units}) => {
  const {deleteService, updateService} = useServiceTemplateActions();
  const t = useTranslations();
  const defaultVAT = useAtomValue(defaultVATAtom);
  const defaultCurrency = useAtomValue(defaultCurrencyAtom);

  return (
    <TableRow>
      <TableCell>
        <PopoverInput
          defaultValue={service.name}
          label={t('name')}
          onChange={(value) => updateService(service.id, {...service, name: value})}
          validation={Joi.string().required()}
        >
          {service.name}
        </PopoverInput>
      </TableCell>
      <TableCell>
        <Select
          onValueChange={(value) => updateService(service.id, {...service, measurementUnit: value})}
          value={service.measurementUnit}
        >
          <SelectTrigger size='sm'>
            <SelectValue>
              {t(`unit.name.${units.find(({id}) => id === service.measurementUnit)?.name || 'pcs'}` as any)}
            </SelectValue>
          </SelectTrigger>
          <SelectContent>
            {enabledUnits.map(({id, name}) => (
              <SelectItem key={id} value={id}>
                {t(`unit.name.${name.toLowerCase()}` as any)}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </TableCell>
      <TableCell className='text-right'>
        <PopoverInput
          className='justify-end'
          defaultValue={service.sellPrice?.amount?.toString()}
          error={(service.sellPrice?.amount || 0) < 0}
          label={`${t('unit price')} (${service.sellPrice?.currency || defaultCurrency})`}
          onChange={(value) =>
            updateService(service.id, {...service, sellPrice: {...service.sellPrice, amount: Number(value)}})
          }
          type='number'
          validation={Joi.number().required().positive().allow(0)}
        >
          {formatCurrency(service.sellPrice)}
        </PopoverInput>
      </TableCell>
      <TableCell className='text-right'>
        <PopoverInput
          className='justify-end'
          defaultValue={service.cost?.amount?.toString()}
          error={(service.cost?.amount || 0) < 0}
          label={`${t('cost per hour')} (${service.cost?.currency || defaultCurrency})`}
          onChange={(value) => updateService(service.id, {...service, cost: {...service.cost, amount: Number(value)}})}
          type='number'
          validation={Joi.number().required().positive().allow(0)}
        >
          {formatCurrency(service.cost)}
        </PopoverInput>
      </TableCell>
      <TableCell className='text-right'>
        <div className='flex items-center justify-end'>
          <Select
            onValueChange={(value) => updateService(service.id, {...service, vatRate: Number(value)})}
            value={formatVAT(service.vatRate)}
          >
            <SelectTrigger size='sm'>
              <SelectValue>{formatVAT(service.vatRate, defaultVAT)}%</SelectValue>
            </SelectTrigger>
            <SelectContent>
              {availableVATRates.map((vat) => (
                <SelectItem key={vat} value={vat}>
                  {formatVAT(Number(vat), defaultVAT)}%
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </TableCell>
      <TableActions>
        <Button onClick={() => deleteService(service.id, service.name)} size='icon' variant='none'>
          <Trash2Icon className='size-5 text-red' strokeWidth={1} />
        </Button>
      </TableActions>
    </TableRow>
  );
};

export default ServicesTableRow;
