import {FC, KeyboardEvent, useCallback, useState} from 'react';

import {useAtomValue} from 'jotai';
import {PlusIcon, XIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';

import {Button} from '@/components/ui/Button';
import {Input} from '@/components/ui/Input';
import useWorkstationActions from '@/hooks/useWorkstationActions';
import {defaultCurrencyAtom} from '@/store/defaults';
import {classes} from '@/utils/common';

type Props = {
  className?: string;
};

const WorkstationsButton: FC<Props> = ({className}) => {
  const t = useTranslations();
  const [addEnabled, setAddEnabled] = useState(false);
  const {createWorkstation} = useWorkstationActions();
  const defaultCurrency = useAtomValue(defaultCurrencyAtom);

  const handleAdd = useCallback(
    (value: string) =>
      createWorkstation({costPerHour: {amount: 0, currency: defaultCurrency}, name: value}).then(() =>
        setAddEnabled(false),
      ),
    [createWorkstation, defaultCurrency],
  );

  return (
    <div className={classes('flex items-center gap-4', className)}>
      {!addEnabled && (
        <Button onClick={() => setAddEnabled(true)}>
          <PlusIcon /> {t('add workstation')}
        </Button>
      )}
      {addEnabled && (
        <>
          <Input
            autoFocus
            className='w-[300px]'
            onKeyDown={({key, target}: KeyboardEvent<HTMLInputElement>) => {
              const value = (target as HTMLInputElement).value;
              if (key === 'Enter' && value) handleAdd(value);
            }}
            placeholder={t('name')}
          />
          <Button onClick={() => setAddEnabled(false)} variant='secondary'>
            <XIcon />
            {t('cancel')}
          </Button>
        </>
      )}
    </div>
  );
};

export default WorkstationsButton;
