import {FC} from 'react';

import Jo<PERSON> from 'joi';
import {sortBy} from 'lodash';
import {Trash2Icon} from 'lucide-react';
import {useTranslations} from 'next-intl';

import {Button} from '@/components/ui/Button';
import {PopoverInput} from '@/components/ui/Input';
import {TableMultiSelect} from '@/components/ui/special/TableMultiSelect';
import {TableActions, TableCell, TableRow} from '@/components/ui/Table';
import useWorkstationActions from '@/hooks/useWorkstationActions';
import {Operation, Workstation} from '@/types/manufacturing';
import {formatCurrency} from '@/utils/format';

type Props = {
  operations: Operation[];
  workstation: Workstation;
};

const WorkstationsTableRow: FC<Props> = ({operations, workstation}) => {
  const {deleteWorkstation, updateWorkstation} = useWorkstationActions();
  const t = useTranslations();

  return (
    <TableRow>
      <TableCell>
        <PopoverInput
          defaultValue={workstation.name}
          label={t('name')}
          onChange={(value) => updateWorkstation({...workstation, name: value})}
          validation={Joi.string().required()}
        >
          {workstation.name}
        </PopoverInput>
      </TableCell>
      <TableCell>
        <PopoverInput
          defaultValue={workstation.costPerHour?.amount.toString()}
          label={t('cost per hour')}
          onChange={(value) =>
            updateWorkstation({
              ...workstation,
              costPerHour: {amount: Number(value), currency: workstation.costPerHour.currency},
            })
          }
          validation={Joi.string().required()}
        >
          {formatCurrency(workstation.costPerHour)}
        </PopoverInput>
      </TableCell>
      <TableCell>
        <TableMultiSelect
          defaultValue={workstation.manufacturingOperationTemplates.map((operation) => operation.id)}
          onValueChange={(value) =>
            updateWorkstation({
              ...workstation,
              manufacturingOperationTemplates: value.map((id) => ({
                id,
                name: '',
              })),
            })
          }
          options={sortBy(
            operations.map((operation) => ({id: operation.id, value: operation.name})),
            'name',
          )}
          renderNotFound={() => t('operation not found')}
          searchPlaceholder={t('search operation')}
          side='right'
          summaryLabel={t('selected.female')}
        />
      </TableCell>
      <TableActions>
        <Button onClick={() => deleteWorkstation(workstation.id, workstation.name)} size='icon' variant='none'>
          <Trash2Icon className='size-5 text-red' strokeWidth={1} />
        </Button>
      </TableActions>
    </TableRow>
  );
};

export default WorkstationsTableRow;
