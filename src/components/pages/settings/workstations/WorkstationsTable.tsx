import {FC} from 'react';

import {useTranslations} from 'next-intl';

import {
  Table,
  TableBody,
  TableContainer,
  TableHead,
  TableHeadActions,
  TableHeader,
  TableRow,
} from '@/components/ui/Table';
import useHeight from '@/hooks/helpers/useHeight';
import useOperations from '@/hooks/useOperations';
import useWorkstations from 'hooks/useWorkstations';

import WorkstationsTableRow from './WorkstationsTableRow';

const WorkstationsTable: FC = () => {
  const {elementRef} = useHeight();
  const {isLoading: operationsIsLoading, operations} = useOperations();
  const {isLoading, workstations} = useWorkstations();
  const t = useTranslations();

  return (
    <TableContainer ref={elementRef}>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>{t('name')}</TableHead>
            <TableHead>{t('cost per hour')}</TableHead>
            <TableHead>{t('operations')}</TableHead>
            <TableHeadActions />
          </TableRow>
        </TableHeader>
        <TableBody className='overflow-y-hidden' isValidating={isLoading || operationsIsLoading}>
          {workstations?.map((workstation) => (
            <WorkstationsTableRow key={workstation.id} operations={operations} workstation={workstation} />
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );
};

export default WorkstationsTable;
