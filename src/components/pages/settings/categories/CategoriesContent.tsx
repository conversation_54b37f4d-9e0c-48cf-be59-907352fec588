import {FC} from 'react';

import {Trash2Icon} from 'lucide-react';
import {useTranslations} from 'next-intl';

import {Button} from '@/components/ui/Button';
import {Input} from '@/components/ui/Input';
import {Tooltip, TooltipContent, TooltipTrigger} from '@/components/ui/Tooltip';
import useHeight from '@/hooks/helpers/useHeight';
import useCategories from '@/hooks/useCategories';
import useCategoryActions from '@/hooks/useCategoryActions';

import CategoriesButton from './CategoriesButton';

const CategoriesContent: FC = () => {
  const {elementRef} = useHeight();
  const {categories, isLoading} = useCategories();
  const {deleteCategory, updateCategory} = useCategoryActions();
  const t = useTranslations();

  if (isLoading) return null;

  return (
    <div className='flex flex-col gap-4 divide-y'>
      <div className='flex flex-col px-6 pt-4'>
        <div>{t('categories')}</div>
        <div className='text-sm text-gray-400'>
          {t('the categories can be assigned to items for better organizing and grouping of products and materials')}
        </div>
      </div>
      <div className='overflow-x-auto px-6 pt-4' ref={elementRef}>
        <div className='flex h-full w-fit flex-col flex-wrap gap-2'>
          {categories?.map((category) => (
            <Tooltip key={category.id}>
              <TooltipTrigger asChild>
                <div className='relative mr-4 inline-flex items-center'>
                  <Input
                    className='pr-5'
                    defaultValue={category.details.name}
                    onBlur={({target: {value}}) => {
                      if (value && value !== category.details.name) updateCategory(category.id, value);
                    }}
                    onKeyDown={({key, target}) => {
                      const value = (target as HTMLInputElement).value;
                      if (key === 'Enter' && value && value !== category.details.name)
                        updateCategory(category.id, value);
                    }}
                    size='2xl'
                  />
                  <Button
                    onClick={() => deleteCategory(category.id, category.details.name)}
                    size='none'
                    variant='ghost'
                  >
                    <Trash2Icon className='absolute right-0 size-5' strokeWidth={1} />
                  </Button>
                </div>
              </TooltipTrigger>
              <TooltipContent side='bottom'>{category.details.name}</TooltipContent>
            </Tooltip>
          ))}
          <CategoriesButton forTable />
        </div>
      </div>
    </div>
  );
};

export default CategoriesContent;
