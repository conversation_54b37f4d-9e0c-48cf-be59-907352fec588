import {FC, KeyboardEvent, useCallback, useState} from 'react';

import {PlusIcon, XIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';

import {Button, IconButton} from '@/components/ui/Button';
import {Input} from '@/components/ui/Input';
import useCategoryActions from '@/hooks/useCategoryActions';
import {classes} from '@/utils/common';

type Props = {
  forTable?: boolean;
};

const CategoriesButton: FC<Props> = ({forTable}) => {
  const t = useTranslations();
  const [addEnabled, setAddEnabled] = useState(false);
  const {createCategory} = useCategoryActions();

  const handleAdd = useCallback(
    (value: string) => createCategory(value).then(() => setAddEnabled(false)),
    [createCategory],
  );

  return (
    <div className={classes('flex items-center gap-4', forTable && 'mr-4 w-[288px]')}>
      {!addEnabled && forTable && (
        <IconButton icon={<PlusIcon />} onClick={() => setAddEnabled(true)}>
          {t('add category')}
        </IconButton>
      )}
      {!addEnabled && !forTable && (
        <Button onClick={() => setAddEnabled(true)}>
          <PlusIcon /> {t('add category')}
        </Button>
      )}
      {addEnabled && (
        <>
          <Input
            autoFocus
            className={classes(forTable ? 'w-full' : 'w-[300px]')}
            onKeyDown={({key, target}: KeyboardEvent<HTMLInputElement>) => {
              const value = (target as HTMLInputElement).value;
              if (key === 'Enter' && value) handleAdd(value);
            }}
            placeholder={t('name')}
          />
          <Button onClick={() => setAddEnabled(false)} variant='secondary'>
            <XIcon />
            {t('cancel')}
          </Button>
        </>
      )}
    </div>
  );
};

export default CategoriesButton;
