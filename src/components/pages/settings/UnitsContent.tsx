import {FC, useCallback} from 'react';

import {useAtom} from 'jotai';
import {findIndex} from 'lodash';
import {useTranslations} from 'next-intl';

import {Label, WithLabel} from '@/components/ui/Label';
import {Switch} from '@/components/ui/Switch';
import useHeight from '@/hooks/helpers/useHeight';
import useSettings from '@/hooks/useSettings';
import useSettingsActions from '@/hooks/useSettingsActions';
import useUnits from '@/hooks/useUnits';
import {enabledUnitsAtom} from '@/store/defaults';
import {Unit} from '@/types/global';

const UnitsContent: FC = () => {
  const t = useTranslations();
  const {isLoading, units} = useUnits();
  const {isLoading: settingsIsLoading, settings} = useSettings();
  const [enabledUnitIds, setEnabledUnitIds] = useAtom(enabledUnitsAtom);
  const {updateSettings} = useSettingsActions();
  const {elementRef} = useHeight();

  const handleChange = useCallback(
    (checked: boolean, unit: Unit) => {
      if (!settings) return;

      let settingsValue;

      if (checked) {
        settingsValue = [...settings.enabledMeasurementUnits, unit.id];
      } else {
        const index = findIndex(settings.enabledMeasurementUnits, (label) => label === unit.id);
        settingsValue = [
          ...settings.enabledMeasurementUnits.slice(0, index),
          ...settings.enabledMeasurementUnits.slice(index + 1),
        ];
      }

      setEnabledUnitIds(settingsValue);

      updateSettings(settings, 'enabledMeasurementUnits', settingsValue);
    },
    [setEnabledUnitIds, settings, updateSettings],
  );

  if (isLoading || settingsIsLoading) return null;

  return (
    <div className='flex flex-col gap-4 divide-y'>
      <div className='flex flex-col px-6 pt-4'>
        <div>{t('units of measure')}</div>
        <div className='text-sm text-gray-400'>{t('select the type of units used for your operations')}</div>
      </div>
      <div className='overflow-x-auto px-6 pt-4' ref={elementRef}>
        <div className='flex h-full w-fit flex-col flex-wrap gap-2'>
          {units?.map((unit) => (
            <WithLabel direction='horizontal' key={unit.id}>
              <Switch
                checked={enabledUnitIds.includes(unit.id)}
                onCheckedChange={(checked) => handleChange(checked, unit)}
              />
              <Label>
                {t(`unit.id.${unit.id.toLowerCase()}` as any)} ({t(`unit.name.${unit.name.toLowerCase()}` as any)})
              </Label>
            </WithLabel>
          ))}
        </div>
      </div>
    </div>
  );
};

export default UnitsContent;
