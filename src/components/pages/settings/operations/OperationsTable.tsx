import {FC} from 'react';

import {useTranslations} from 'next-intl';

import {
  Table,
  TableBody,
  TableContainer,
  TableHead,
  TableHeadActions,
  TableHeader,
  TableRow,
} from '@/components/ui/Table';
import useHeight from '@/hooks/helpers/useHeight';
import useOperations from '@/hooks/useOperations';
import useEmployees from 'hooks/useEmployees';
import useWorkstations from 'hooks/useWorkstations';

import OperationsTableRow from './OperationsTableRow';

const OperationsTable: FC = () => {
  const {elementRef} = useHeight();
  const {isLoading, operations} = useOperations();
  const {employees, isLoading: employeesIsLoading} = useEmployees();
  const {isLoading: workstationsIsLoading, workstations} = useWorkstations();
  const t = useTranslations();

  return (
    <TableContainer ref={elementRef}>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>{t('name')}</TableHead>
            <TableHead className='text-right'>{t('cost per hour')}</TableHead>
            <TableHead>{t('qualified employees')}</TableHead>
            <TableHead>{t('workstations')}</TableHead>
            <TableHeadActions />
          </TableRow>
        </TableHeader>
        <TableBody
          className='overflow-y-hidden'
          isValidating={isLoading || employeesIsLoading || workstationsIsLoading}
        >
          {operations.map((operation) => (
            <OperationsTableRow
              employees={employees}
              key={operation.id}
              operation={operation}
              workstations={workstations}
            />
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );
};

export default OperationsTable;
