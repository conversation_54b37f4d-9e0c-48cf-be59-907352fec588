import {FC, KeyboardEvent, useCallback, useState} from 'react';

import {PlusIcon, XIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';

import {Button} from '@/components/ui/Button';
import {Input} from '@/components/ui/Input';
import useOperationActions from '@/hooks/useOperationActions';
import {classes} from '@/utils/common';

type Props = {
  className?: string;
};

const OperationsButton: FC<Props> = ({className}) => {
  const t = useTranslations();
  const [addEnabled, setAddEnabled] = useState(false);
  const {createOperation} = useOperationActions();

  const handleAdd = useCallback(
    (value: string) => createOperation({name: value}).then(() => setAddEnabled(false)),
    [createOperation],
  );

  return (
    <div className={classes('flex items-center gap-4', className)}>
      {!addEnabled && (
        <Button onClick={() => setAddEnabled(true)}>
          <PlusIcon /> {t('add operation')}
        </Button>
      )}
      {addEnabled && (
        <>
          <Input
            autoFocus
            className='w-[300px]'
            onKeyDown={({key, target}: KeyboardEvent<HTMLInputElement>) => {
              const value = (target as HTMLInputElement).value;
              if (key === 'Enter' && value) handleAdd(value);
            }}
            placeholder={t('name')}
          />
          <Button onClick={() => setAddEnabled(false)} variant='secondary'>
            <XIcon />
            {t('cancel')}
          </Button>
        </>
      )}
    </div>
  );
};

export default OperationsButton;
