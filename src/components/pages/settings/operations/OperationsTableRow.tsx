import {FC} from 'react';

import Joi from 'joi';
import {useAtomValue} from 'jotai';
import {sortBy} from 'lodash';
import {StarIcon, Trash2Icon} from 'lucide-react';
import {useTranslations} from 'next-intl';

import {Button} from '@/components/ui/Button';
import {PopoverInput} from '@/components/ui/Input';
import {Popover, PopoverClose, PopoverContent, PopoverTrigger} from '@/components/ui/Popover';
import {TableMultiSelect} from '@/components/ui/special/TableMultiSelect';
import {TableActions, TableCell, TableRow} from '@/components/ui/Table';
import useEmployeeActions from '@/hooks/useEmployeeActions';
import useOperationActions from '@/hooks/useOperationActions';
import useWorkstationActions from '@/hooks/useWorkstationActions';
import {defaultCurrencyAtom} from '@/store/defaults';
import {Employee, Operation, Workstation} from '@/types/manufacturing';
import {classes} from '@/utils/common';
import {formatCurrency} from '@/utils/format';

type Props = {
  employees: Employee[];
  operation: Operation;
  workstations: Workstation[];
};

const OperationsTableRow: FC<Props> = ({employees, operation, workstations}) => {
  const {deleteOperation, updateOperation} = useOperationActions();
  const {createEmployee} = useEmployeeActions();
  const {createWorkstation} = useWorkstationActions();
  const defaultCurrency = useAtomValue(defaultCurrencyAtom);
  const t = useTranslations();

  return (
    <TableRow>
      <TableCell>
        <PopoverInput
          defaultValue={operation.name}
          label={t('name')}
          onChange={(value) => updateOperation({...operation, name: value})}
          validation={Joi.string().required()}
        >
          {operation.name}
        </PopoverInput>
      </TableCell>
      <TableCell className='text-right'>
        <PopoverInput
          defaultValue={operation.costPerHour?.amount.toString()}
          label={t('cost per hour')}
          onChange={(value) =>
            updateOperation({
              ...operation,
              costPerHour: {amount: Number(value), currency: operation.costPerHour?.currency || defaultCurrency},
            })
          }
          validation={Joi.number().positive().allow(0).required()}
        >
          {formatCurrency(operation.costPerHour)}
        </PopoverInput>
      </TableCell>
      <TableCell>
        <TableMultiSelect
          defaultValue={operation.employees.map((employee) => employee.id)}
          notFoundClassName='mx-1'
          onValueChange={(value) =>
            updateOperation({
              ...operation,
              employees: value.map((id) => ({
                id,
                name: '',
                preferential: operation.employees.find((e) => e.id === id)?.preferential ?? false,
              })),
            })
          }
          options={sortBy(
            employees.map((employee) => ({id: employee.id, value: employee.name})),
            'name',
          )}
          renderElement={(option) => (
            <div className='flex items-center gap-1 pr-2 text-sm'>
              <Button
                className={classes(
                  'hover:fill-button-primary-hovered',
                  operation.employees.find((employee) => employee.id === option.id)?.preferential
                    ? 'fill-button-primary'
                    : 'fill-white',
                )}
                onClick={() =>
                  updateOperation({
                    ...operation,
                    employees: operation.employees.map((e) => ({
                      ...e,
                      preferential: e.id === option.id ? !e.preferential : e.preferential,
                    })),
                  })
                }
                size='icon'
                variant='none'
              >
                <StarIcon className='size-4' fill='' strokeWidth={1.5} />
              </Button>
              {option.value}
            </div>
          )}
          renderNotFound={(query) => (
            <Button
              className='w-full justify-start'
              onClick={() => {
                createEmployee({name: query, position: '-'}).then((employee) => {
                  if (employee)
                    updateOperation({
                      ...operation,
                      employees: [...operation.employees, employee],
                    });
                });
              }}
              variant='secondary'
            >
              {t('create employee')}
            </Button>
          )}
          searchPlaceholder={t('search employee')}
          side='right'
          summaryLabel={t('selected.male')}
        />
      </TableCell>
      <TableCell>
        <TableMultiSelect
          defaultValue={operation.workstations.map((workstation) => workstation.id)}
          notFoundClassName='mx-1'
          onValueChange={(value) =>
            updateOperation({
              ...operation,
              workstations: value.map((id) => ({
                costPerHour: {amount: 0, currency: ''},
                id,
                manufacturingOperationTemplates: [],
                name: '',
              })),
            })
          }
          options={sortBy(
            workstations.map((workstation) => ({id: workstation.id, value: workstation.name})),
            'name',
          )}
          renderNotFound={(query) => (
            <Button
              className='w-full justify-start'
              onClick={() => {
                createWorkstation({costPerHour: {amount: 0, currency: defaultCurrency}, name: query}).then(
                  (workstation) => {
                    if (workstation)
                      updateOperation({
                        ...operation,
                        workstations: [...operation.workstations, workstation],
                      });
                  },
                );
              }}
              variant='secondary'
            >
              {t('create workstation')}
            </Button>
          )}
          searchPlaceholder={t('search workstation')}
          side='left'
          summaryLabel={t('selected.female')}
        />
      </TableCell>
      <TableActions>
        <Popover>
          <PopoverTrigger asChild>
            <Button size='icon' variant='none'>
              <Trash2Icon className='size-5 text-red' strokeWidth={1} />
            </Button>
          </PopoverTrigger>
          <PopoverContent align='end' className='flex w-64 max-w-none flex-col items-center gap-4'>
            {t('the operation will be permanently deleted from the system')}
            <div className='flex items-center gap-8'>
              <PopoverClose asChild>
                <Button variant='secondary'>{t('cancel')}</Button>
              </PopoverClose>
              <PopoverClose asChild>
                <Button
                  className='bg-red-light hover:bg-red hover:text-white'
                  onClick={() => deleteOperation(operation.id, operation.name)}
                  variant='secondary'
                >
                  {t('delete')}
                </Button>
              </PopoverClose>
            </div>
          </PopoverContent>
        </Popover>
      </TableActions>
    </TableRow>
  );
};

export default OperationsTableRow;
