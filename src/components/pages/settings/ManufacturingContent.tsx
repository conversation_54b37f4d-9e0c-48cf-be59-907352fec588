import {FC, useMemo} from 'react';

import {useAtomValue} from 'jotai';
import {filter, flatten, padStart} from 'lodash';
import {ClockIcon, InfoIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';

import {HoverCard, HoverCardContent, HoverCardTrigger} from '@/components/ui/HoverCard';
import {InputLabel, NumberInput} from '@/components/ui/Input';
import {Label, WithLabel} from '@/components/ui/Label';
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from '@/components/ui/Select';
import {Switch} from '@/components/ui/Switch';
import useSettings from '@/hooks/useSettings';
import useSettingsActions from '@/hooks/useSettingsActions';
import {defaultCurrencyAtom} from '@/store/defaults';
import {durationStringToDate} from '@/utils/common';
import {formatTime} from '@/utils/format';

const ManufacturingContent: FC = () => {
  const t = useTranslations();
  const {isLoading, settings} = useSettings();
  const {updateSettings} = useSettingsActions();
  const defaultCurrency = useAtomValue(defaultCurrencyAtom);

  const weekDays = [
    {id: 1, name: 'monday'},
    {id: 2, name: 'tuesday'},
    {id: 3, name: 'wednesday'},
    {id: 4, name: 'thursday'},
    {id: 5, name: 'friday'},
    {id: 6, name: 'saturday'},
    {id: 7, name: 'sunday'},
  ];

  const times = useMemo(
    () =>
      flatten(
        [...Array(24)].map((_, index) => [
          `${padStart(index.toString(), 2, '0')}:00`,
          `${padStart(index.toString(), 2, '0')}:30`,
        ]),
      ),
    [],
  );

  if (isLoading) return null;

  return (
    <div className='m-4 flex flex-wrap gap-4'>
      <div className='rounded-lg border p-4 text-center'>
        <div className='flex flex-col gap-4'>
          <div className='flex flex-col'>
            <div>{t('working days')}</div>
            <div className='text-sm text-gray-400'>{t('working days description')}</div>
          </div>
          <div className='flex justify-center'>
            <div className='flex flex-col gap-2'>
              {weekDays.map((weekDay) => (
                <WithLabel direction='horizontal' key={weekDay.name}>
                  <Switch
                    checked={settings.manufacturing.workingDays?.includes(weekDay.id)}
                    onCheckedChange={(checked) => {
                      if (checked) {
                        updateSettings(settings, 'manufacturing', {
                          workingDays: [...(settings.manufacturing.workingDays || []), weekDay.id],
                        });
                      } else {
                        updateSettings(settings, 'manufacturing', {
                          workingDays: filter(settings.manufacturing.workingDays, (day) => day !== weekDay.id),
                        });
                      }
                    }}
                  />
                  <Label>{t(weekDay.name as any)}</Label>
                </WithLabel>
              ))}
            </div>
          </div>
        </div>
      </div>
      <div className='h-fit rounded-lg border p-4 text-center'>
        <div className='flex flex-col gap-4'>
          <div className='flex flex-col'>
            <div>{t('working hours')}</div>
            <div className='text-sm text-gray-400'>{t('working hours description')}</div>
          </div>
          <div className='flex justify-center'>
            <div className='flex items-center gap-4'>
              <WithLabel>
                <Select
                  onValueChange={(value) => updateSettings(settings, 'manufacturing', {workDayStartTime: value})}
                  value={settings.manufacturing?.workDayStartTime}
                >
                  <SelectTrigger size='md'>
                    <ClockIcon className='size-5' />
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {times.map((time) => (
                      <SelectItem key={time} value={`${time}:00`}>
                        {formatTime(durationStringToDate(time))}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <InputLabel>{t('start time')}</InputLabel>
              </WithLabel>
              <WithLabel>
                <Select
                  onValueChange={(value) => updateSettings(settings, 'manufacturing', {workDayEndTime: value})}
                  value={settings.manufacturing?.workDayEndTime}
                >
                  <SelectTrigger size='md'>
                    <ClockIcon className='size-5' />
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {times.map((time) => (
                      <SelectItem key={time} value={`${time}:00`}>
                        {formatTime(durationStringToDate(time))}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <InputLabel>{t('end time')}</InputLabel>
              </WithLabel>
            </div>
          </div>
        </div>
      </div>
      <div className='flex flex-col gap-4'>
        <div className='h-fit rounded-lg border p-4 text-center'>
          <div className='flex flex-col gap-4'>
            <div className='flex flex-col'>
              <div className='flex items-center gap-2'>
                {t('hourly indirect cost of production per worker')} (
                {settings.manufacturing.manufacturingOverheadPerEmployeeHour?.currency || defaultCurrency}/{t('h')})
                <HoverCard>
                  <HoverCardTrigger>
                    <InfoIcon className='size-5 cursor-pointer' />
                  </HoverCardTrigger>
                  <HoverCardContent>
                    <h3 className='mb-2 text-lg font-bold'>{t('texts.indirectCost.title')}</h3>
                    <p className='mb-2'>{t('texts.indirectCost.description')}</p>
                    <ul className='mb-2 list-disc pl-5'>
                      <li>{t('texts.indirectCost.list.1')}</li>
                      <li>{t('texts.indirectCost.list.2')}</li>
                      <li>{t('texts.indirectCost.list.3')}</li>
                      <li>{t('texts.indirectCost.list.4')}</li>
                      <li>{t('texts.indirectCost.list.5')}</li>
                      <li>{t('texts.indirectCost.list.6')}</li>
                    </ul>
                    <p className='mb-1 font-semibold'>{t('texts.indirectCost.calculation.title')}</p>
                    <ul className='mb-2 list-decimal pl-5'>
                      <li>{t('texts.indirectCost.calculation.list.1')}</li>
                      <li>{t('texts.indirectCost.calculation.list.2')}</li>
                      <li>{t('texts.indirectCost.calculation.list.3')}</li>
                    </ul>
                    <p className='mb-1 font-semibold'>{t('texts.indirectCost.example.title')}</p>
                    <p className='mb-2'>
                      <strong>{t('texts.indirectCost.example.list.1')}</strong>
                      <br />
                      <strong>{t('texts.indirectCost.example.list.2')}</strong>
                      <br />
                      <strong>{t('texts.indirectCost.example.list.3')}</strong>
                    </p>
                    <p className='text-xs text-accent-foreground'>{t('texts.indirectCost.note')}</p>
                    <p className='mt-1 text-xs text-accent-foreground'>
                      <strong>{t('texts.indirectCost.updateRecommendation.text')}</strong>{' '}
                      {t('texts.indirectCost.updateRecommendation.frequency')}
                    </p>
                  </HoverCardContent>
                </HoverCard>
              </div>
            </div>
            <div className='flex justify-center'>
              <NumberInput
                min={0}
                onChange={(value) => {
                  if (settings.manufacturing.manufacturingOverheadPerEmployeeHour?.amount !== value * 100)
                    updateSettings(settings, 'manufacturing', {
                      manufacturingOverheadPerEmployeeHour: {
                        amount: value * 100,
                        currency:
                          settings.manufacturing.manufacturingOverheadPerEmployeeHour?.currency || defaultCurrency,
                      },
                    });
                }}
                value={(settings.manufacturing.manufacturingOverheadPerEmployeeHour?.amount || 0) / 100}
              />
            </div>
          </div>
        </div>
        <div className='h-fit rounded-lg border p-4 text-center'>
          <div className='flex flex-col gap-4'>
            <div className='flex flex-col'>
              <div className='flex items-center gap-2'>
                {t('hourly indirect administrative cost per worker')} (
                {settings.manufacturing.administrativeOverheadPerEmployeeHour?.currency || defaultCurrency}/{t('h')})
                <HoverCard>
                  <HoverCardTrigger>
                    <InfoIcon className='size-5 cursor-pointer' />
                  </HoverCardTrigger>
                  <HoverCardContent>
                    <h3 className='mb-2 text-lg font-bold'>{t('texts.administrativeCost.title')}</h3>
                    <p className='mb-2'>{t('texts.administrativeCost.description')}</p>
                    <ul className='mb-2 list-disc pl-5'>
                      <li>{t('texts.administrativeCost.list.1')}</li>
                      <li>{t('texts.administrativeCost.list.2')}</li>
                      <li>{t('texts.administrativeCost.list.3')}</li>
                      <li>{t('texts.administrativeCost.list.4')}</li>
                      <li>{t('texts.administrativeCost.list.5')}</li>
                      <li>{t('texts.administrativeCost.list.6')}</li>
                      <li>{t('texts.administrativeCost.list.7')}</li>
                      <li>{t('texts.administrativeCost.list.8')}</li>
                    </ul>
                    <p className='mb-1 font-semibold'>{t('texts.administrativeCost.calculation.title')}</p>
                    <ul className='mb-2 list-decimal pl-5'>
                      <li>{t('texts.administrativeCost.calculation.list.1')}</li>
                      <li>{t('texts.administrativeCost.calculation.list.2')}</li>
                      <li>{t('texts.administrativeCost.calculation.list.3')}</li>
                    </ul>
                    <p className='mb-1 font-semibold'>{t('texts.administrativeCost.example.title')}</p>
                    <p className='mb-2'>
                      <strong>{t('texts.administrativeCost.example.list.1')}</strong>
                      <br />
                      <strong>{t('texts.administrativeCost.example.list.2')}</strong>
                      <br />
                      <strong>{t('texts.administrativeCost.example.list.3')}</strong>
                      <br />
                    </p>
                    <p className='text-xs text-accent-foreground'>{t('texts.administrativeCost.note')}</p>
                    <p className='mt-1 text-xs text-accent-foreground'>
                      <strong>{t('texts.administrativeCost.updateRecommendation.text')}</strong>{' '}
                      {t('texts.administrativeCost.updateRecommendation.frequency')}
                    </p>
                  </HoverCardContent>
                </HoverCard>
              </div>
            </div>
            <div className='flex justify-center'>
              <NumberInput
                min={0}
                onChange={(value) => {
                  if (settings.manufacturing.administrativeOverheadPerEmployeeHour?.amount !== value * 100)
                    updateSettings(settings, 'manufacturing', {
                      administrativeOverheadPerEmployeeHour: {
                        amount: value * 100,
                        currency:
                          settings.manufacturing.administrativeOverheadPerEmployeeHour?.currency || defaultCurrency,
                      },
                    });
                }}
                value={(settings.manufacturing.administrativeOverheadPerEmployeeHour?.amount || 0) / 100}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ManufacturingContent;
