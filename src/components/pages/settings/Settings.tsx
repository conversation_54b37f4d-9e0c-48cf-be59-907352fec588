import {FC, useEffect} from 'react';

import {useAtom} from 'jotai';
import {useTranslations} from 'next-intl';

import {Page, PageContent, PageHeader, PageHeaderTitle, PageTitle} from '@/components/ui/special/Page';
import {Tabs, TabsList, TabsTrigger} from '@/components/ui/Tabs';
import {useHasPermission} from '@/hooks/helpers/useHasPermission';

import CategoriesButton from './categories/CategoriesButton';
import CategoriesContent from './categories/CategoriesContent';
import GeneralContent from './GeneralContent';
import InventoryUnitsButton from './inventoryUnits/InventoryUnitsButton';
import InventoryUnitsContent from './inventoryUnits/InventoryUnitsContent';
import ManufacturingContent from './ManufacturingContent';
import OperationsButton from './operations/OperationsButton';
import OperationsTable from './operations/OperationsTable';
import ServicesButton from './services/ServicesButton';
import ServicesTable from './services/ServicesTable';
import {SettingsTab, settingsTabAtom} from './settingsStore';
import UnitsContent from './UnitsContent';
import WorkstationsButton from './workstations/WorkstationsButton';
import WorkstationsTable from './workstations/WorkstationsTable';

const Settings: FC = () => {
  const t = useTranslations();
  const {hasPermission, isLoading} = useHasPermission();
  const [tab, setTab] = useAtom(settingsTabAtom);

  useEffect(() => {
    if (['manufacturing', 'operations', 'workstations'].includes(tab) && !hasPermission('update', 'manufacturing')) {
      setTab('general');
    }
  }, [hasPermission, setTab, tab]);

  if (isLoading) return null;

  return (
    <Page>
      <PageTitle>{`${t(tab)} - ${t('settings')}`}</PageTitle>
      <PageHeader>
        <PageHeaderTitle className='mr-10'>{t('settings')}</PageHeaderTitle>
        <Tabs onValueChange={(newTab) => setTab(newTab as SettingsTab)} value={tab} variant='menu'>
          <TabsList variant='menu'>
            <TabsTrigger value='general' variant='menu'>
              {t('general')}
            </TabsTrigger>
            <TabsTrigger value='units of measure' variant='menu'>
              {t('units of measure')}
            </TabsTrigger>
            <TabsTrigger value='categories' variant='menu'>
              {t('categories')}
            </TabsTrigger>
            <TabsTrigger value='inventory units' variant='menu'>
              {t('inventory units')}
            </TabsTrigger>
            {hasPermission('update', 'manufacturing') && (
              <>
                <TabsTrigger value='manufacturing' variant='menu'>
                  {t('manufacturing')}
                </TabsTrigger>
                <TabsTrigger value='operations' variant='menu'>
                  {t('operations')}
                </TabsTrigger>
                <TabsTrigger value='workstations' variant='menu'>
                  {t('workstations')}
                </TabsTrigger>
              </>
            )}
            <TabsTrigger value='services' variant='menu'>
              {t('services')}
            </TabsTrigger>
          </TabsList>
        </Tabs>
        <div className='grow' />
        {tab === 'categories' && <CategoriesButton />}
        {tab === 'inventory units' && <InventoryUnitsButton />}
        {tab === 'operations' && <OperationsButton />}
        {tab === 'workstations' && <WorkstationsButton />}
        {tab === 'services' && <ServicesButton />}
      </PageHeader>
      <PageContent>
        {tab === 'general' && <GeneralContent />}
        {tab === 'units of measure' && <UnitsContent />}
        {tab === 'categories' && <CategoriesContent />}
        {tab === 'inventory units' && <InventoryUnitsContent />}
        {tab === 'manufacturing' && <ManufacturingContent />}
        {tab === 'operations' && <OperationsTable />}
        {tab === 'workstations' && <WorkstationsTable />}
        {tab === 'services' && <ServicesTable />}
      </PageContent>
    </Page>
  );
};

export default Settings;
