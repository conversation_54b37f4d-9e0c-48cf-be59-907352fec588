import {FC, KeyboardEvent, useCallback, useState} from 'react';

import {PlusIcon, XIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';

import {Button, IconButton} from '@/components/ui/Button';
import {Input} from '@/components/ui/Input';
import useInventoryUnitActions from '@/hooks/useInventoryUnitActions';
import {classes} from '@/utils/common';

type Props = {
  forTable?: boolean;
};

const InventoryUnitsButton: FC<Props> = ({forTable}) => {
  const t = useTranslations();
  const [addEnabled, setAddEnabled] = useState(false);
  const {createInventoryUnit} = useInventoryUnitActions();

  const handleAdd = useCallback(
    (value: string) => createInventoryUnit(value).then(() => setAddEnabled(false)),
    [createInventoryUnit],
  );

  return (
    <div className={classes('flex items-center gap-4', forTable && 'mr-4 w-[192px]')}>
      {!addEnabled && forTable && (
        <IconButton icon={<PlusIcon />} onClick={() => setAddEnabled(true)}>
          {t('add inventory unit')}
        </IconButton>
      )}
      {!addEnabled && !forTable && (
        <Button onClick={() => setAddEnabled(true)}>
          <PlusIcon /> {t('add inventory unit')}
        </Button>
      )}
      {addEnabled && (
        <>
          <Input
            autoFocus
            className={classes(forTable ? 'w-full' : 'w-[300px]')}
            onKeyDown={({key, target}: KeyboardEvent<HTMLInputElement>) => {
              const value = (target as HTMLInputElement).value;
              if (key === 'Enter' && value) handleAdd(value);
            }}
            placeholder={t('name')}
          />
          <Button onClick={() => setAddEnabled(false)} variant='secondary'>
            <XIcon />
            {t('cancel')}
          </Button>
        </>
      )}
    </div>
  );
};

export default InventoryUnitsButton;
