import {FC} from 'react';

import {Trash2Icon} from 'lucide-react';
import {useTranslations} from 'next-intl';

import {Button} from '@/components/ui/Button';
import {Combobox, ComboboxLabel} from '@/components/ui/Combobox';
import {Input} from '@/components/ui/Input';
import {WithLabel} from '@/components/ui/Label';
import useHeight from '@/hooks/helpers/useHeight';
import useInventoryUnitActions from '@/hooks/useInventoryUnitActions';
import useInventoryUnits from '@/hooks/useInventoryUnits';
import useSettings from 'hooks/useSettings';
import useSettingsActions from 'hooks/useSettingsActions';

import InventoryUnitsButton from './InventoryUnitsButton';

const InventoryUnitsContent: FC = () => {
  const {elementRef} = useHeight();
  const {inventoryUnits, isLoading} = useInventoryUnits();
  const {deleteInventoryUnit, updatedInventoryUnit} = useInventoryUnitActions();
  const {isLoading: settingsIsLoading, settings} = useSettings();
  const {updateSettings} = useSettingsActions();
  const t = useTranslations();

  if (isLoading || settingsIsLoading) return null;

  return (
    <div className='flex flex-col gap-4 divide-y'>
      <div className='flex items-center gap-8 px-6 pt-4'>
        <div className='flex flex-col'>
          <div>{t('inventory units')}</div>
          <div className='mb-4 text-sm text-gray-400'>{t('inventory unit description')}</div>
        </div>
        <WithLabel>
          <Combobox
            className='w-48'
            notFoundClassName='mx-1'
            onChange={(value) =>
              updateSettings(settings, 'general', {
                inventoryAccountingSettings: {
                  ...settings.general.inventoryAccountingSettings,
                  unitDesignations: {
                    ...settings.general.inventoryAccountingSettings.unitDesignations,
                    defaultInventoryUnit: value,
                  },
                },
              })
            }
            options={inventoryUnits.map((unit) => ({id: unit.id, value: unit.name}))}
            placeholder={t('inventory unit')}
            searchPlaceholder={t('search inventory unit')}
            value={settings?.general?.inventoryAccountingSettings?.unitDesignations?.defaultInventoryUnit}
          />
          <ComboboxLabel>{t('default inventory unit')}</ComboboxLabel>
        </WithLabel>
      </div>
      <div className='overflow-x-auto px-6 pt-4' ref={elementRef}>
        <div className='flex h-full w-fit flex-col flex-wrap gap-2'>
          {inventoryUnits?.map((unit) => (
            <div className='relative mr-4 inline-flex items-center' key={unit.id}>
              <Input
                className='pr-5'
                defaultValue={unit.name}
                onBlur={({target: {value}}) => {
                  if (value && value !== unit.name) updatedInventoryUnit(unit.id, value);
                }}
                onKeyDown={({key, target}) => {
                  const value = (target as HTMLInputElement).value;
                  if (key === 'Enter' && value && value !== unit.name) updatedInventoryUnit(unit.id, value);
                }}
                size='lg'
              />
              {settings.general.inventoryAccountingSettings.unitDesignations?.defaultInventoryUnit !== unit.id && (
                <Button onClick={() => deleteInventoryUnit(unit.id, unit.name)} size='none' variant='ghost'>
                  <Trash2Icon className='absolute right-0 size-5' strokeWidth={1} />
                </Button>
              )}
            </div>
          ))}
          <InventoryUnitsButton forTable />
        </div>
      </div>
    </div>
  );
};

export default InventoryUnitsContent;
