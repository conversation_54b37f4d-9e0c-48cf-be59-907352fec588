import {FC, memo, useMemo, useRef} from 'react';

import {startOfDay} from 'date-fns';
import {useAtom} from 'jotai';
import {useTranslations} from 'next-intl';

import {LoadingSpinner} from '@/components/ui/special/LoadingSpinner';
import {CardTranslations, useCardTranslations} from '@/hooks/useCardTranslations';
import useEmployees from '@/hooks/useEmployees';
import useTasks from '@/hooks/useTasks';
import {dashboardLayoutAtom} from '@/store/ui';
import {CardSize} from '@/types/dashboard';
import {ManufacturingTaskStatus, ManufacturingTaskStatusReason} from '@/types/global';

import {BarChart} from '../shared/BarChart';
import {Legend} from '../shared/Legend';
import {PieChart} from '../shared/PieChart';
import {DashboardCardProps} from '../withDashboardCard';
import withDashboardCard from '../withDashboardCard';

export const cardTranslations: CardTranslations = {
  'completed tasks': {
    en: 'Completed Tasks',
    hu: 'Befe<PERSON><PERSON>tt feladatok',
    ro: 'Sarcini finalizate',
  },
  'in progress': {
    en: 'In Progress',
    hu: 'Folyamatban',
    ro: 'În desfășurare',
  },
  'not started': {
    en: 'Not Started',
    hu: 'Nem kezdődött el',
    ro: 'Neînceput',
  },
  'on hold': {
    en: 'On Hold',
    hu: 'Felfüggesztve',
    ro: 'În așteptare',
  },
  'task completion': {
    en: 'Task Completion',
    hu: 'Feladat teljesítés',
    ro: 'Finalizarea sarcinilor',
  },
  'task distribution': {
    en: 'Task Distribution',
    hu: 'Feladat eloszlás',
    ro: 'Distribuția sarcinilor',
  },
  'tasks per employee': {
    en: 'Tasks per Employee',
    hu: 'Feladatok alkalmazottanként',
    ro: 'Sarcini per angajat',
  },
  'task status': {
    en: 'Task Status',
    hu: 'Feladat állapot',
    ro: 'Starea sarcinilor',
  },
  'task status per employee': {
    en: 'Task Status per Employee',
    hu: 'Feladat állapot alkalmazottanként',
    ro: 'Starea sarcinilor per angajat',
  },
};

type TaskStatusCount = {
  count: number;
  status: ManufacturingTaskStatus;
  statusReason?: ManufacturingTaskStatusReason;
};

const EmployeeTaskCompletionCard: FC<{size: CardSize}> = ({size}) => {
  const ct = useCardTranslations(cardTranslations);
  const t = useTranslations();
  const {employees, isLoading: isEmployeesLoading} = useEmployees();
  const today = useRef(startOfDay(new Date())).current;
  const {isLoading: isTasksLoading, tasks} = useTasks(today);

  const isLoading = isEmployeesLoading || isTasksLoading;

  const {employeeStatusCounts, taskStatusCounts} = useMemo(() => {
    if (tasks.length === 0) {
      return {
        employeeStatusCounts: [] as {name: string; statuses: {count: number; status: ManufacturingTaskStatus}[]}[],
        taskStatusCounts: [] as TaskStatusCount[],
      };
    }

    const statusCounts: Record<string, number> = {};
    const stoppedStatusCounts: Record<string, number> = {};

    Object.values(ManufacturingTaskStatus).forEach((status) => {
      statusCounts[status] = 0;
    });

    const employeeTasks: Record<string, {count: number; name: string}> = {};
    const employeeStatusMap: Record<string, {name: string; statusCounts: Record<ManufacturingTaskStatus, number>}> = {};

    employees.forEach((emp) => {
      employeeTasks[emp.id] = {
        count: 0,
        name: emp.name,
      };

      employeeStatusMap[emp.id] = {
        name: emp.name,
        statusCounts: {
          [ManufacturingTaskStatus.DONE]: 0,
          [ManufacturingTaskStatus.IN_PROGRESS]: 0,
          [ManufacturingTaskStatus.STOPPED]: 0,
          [ManufacturingTaskStatus.TODO]: 0,
        },
      };
    });

    tasks.forEach((task) => {
      if (task.status === ManufacturingTaskStatus.STOPPED && task.statusReason) {
        const key = `${task.status}_${task.statusReason}`;
        stoppedStatusCounts[key] = (stoppedStatusCounts[key] || 0) + 1;
      } else {
        statusCounts[task.status] = (statusCounts[task.status] || 0) + 1;
      }

      task.assignedEmployees.forEach((employee) => {
        if (employeeTasks[employee.id]) {
          employeeTasks[employee.id].count += 1;

          if (employeeStatusMap[employee.id]) {
            employeeStatusMap[employee.id].statusCounts[task.status] += 1;
          }
        }
      });
    });

    let taskStatusCounts = Object.entries(statusCounts)
      .map(([status, count]) => ({
        count,
        status: status as ManufacturingTaskStatus,
      }))
      .filter((item) => item.count > 0);

    const stoppedWithReasonCounts = Object.entries(stoppedStatusCounts).map(([key, count]) => {
      const [status, reason] = key.split('_');
      return {
        count,
        status: status as ManufacturingTaskStatus,
        statusReason: reason as ManufacturingTaskStatusReason,
      };
    });

    taskStatusCounts = [...taskStatusCounts, ...stoppedWithReasonCounts];

    const employeeTaskCounts = Object.values(employeeTasks)
      .filter((item) => item.count > 0)
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    const employeeStatusCounts = Object.values(employeeStatusMap)
      .filter((employee) => {
        const totalTasks = Object.values(employee.statusCounts).reduce((sum, count) => sum + count, 0);
        return totalTasks > 0;
      })
      .map((employee) => ({
        name: employee.name,
        statuses: Object.entries(employee.statusCounts)
          .filter(([_, count]) => count > 0)
          .map(([status, count]) => ({
            count,
            status: status as ManufacturingTaskStatus,
          })),
      }))
      .sort((a, b) => {
        const totalA = a.statuses.reduce((sum, {count}) => sum + count, 0);
        const totalB = b.statuses.reduce((sum, {count}) => sum + count, 0);
        return totalB - totalA;
      })
      .slice(0, 5);

    return {
      employeeStatusCounts,
      employeeTaskCounts,
      taskStatusCounts,
    };
  }, [tasks, employees]);

  const statusSegments = useMemo(() => {
    return taskStatusCounts.map((item) => {
      const {count, status} = item;
      let color;
      switch (status) {
        case ManufacturingTaskStatus.DONE:
          color = 'green';
          break;
        case ManufacturingTaskStatus.IN_PROGRESS:
          color = 'blue';
          break;
        case ManufacturingTaskStatus.STOPPED:
          color = 'red';
          break;
        case ManufacturingTaskStatus.TODO:
          color = 'gray';
          break;
        default:
          color = 'blue';
      }

      const label =
        status === ManufacturingTaskStatus.STOPPED && 'statusReason' in item ? t(item.statusReason as any) : t(status);

      return {
        color,
        label,
        value: count,
      };
    });
  }, [taskStatusCounts, t]);

  if (isLoading) return <LoadingSpinner size='lg' />;
  if (tasks.length === 0) return null;

  if (size === CardSize.SMALL) {
    return (
      <div className='flex size-full flex-col items-center justify-center p-2 text-center'>
        <PieChart centerText={ct('task status')} segments={statusSegments} />
      </div>
    );
  }

  if (size === CardSize.MEDIUM) {
    return (
      <div className='flex size-full flex-col overflow-hidden'>
        <div className='mb-3 flex items-center justify-between gap-2 border-b border-border pb-2'>
          <h2 className='text-lg font-semibold'>{ct('task completion')}</h2>
        </div>
        <div className='flex-1 overflow-y-auto pr-1'>
          <BarChart
            renderValue={({percent, segment}) => `(${segment.value}) ${percent}%`}
            segments={statusSegments}
            title={ct('task status')}
          />
        </div>
      </div>
    );
  }

  return (
    <div className='flex size-full flex-col gap-4 overflow-hidden'>
      <div className='flex h-full gap-4 overflow-hidden'>
        <div className='flex w-1/2 flex-col gap-4'>
          <div className='rounded-lg border border-border p-4'>
            <PieChart centerText={ct('task status')} segments={statusSegments} />
          </div>
          <Legend segments={statusSegments} />
        </div>
        <div className='flex w-1/2 flex-col overflow-hidden'>
          <div className='flex-1 overflow-y-auto rounded-lg border border-border p-4'>
            <div className='flex flex-col gap-2 overflow-y-auto'>
              <h3 className='font-semibold'>{ct('task status per employee')}</h3>
              {employeeStatusCounts.map((employee) => {
                const totalTasks = employee.statuses.reduce((sum, {count}) => sum + count, 0);

                return (
                  <div key={employee.name}>
                    <div className='flex items-center justify-between'>
                      <span className='line-clamp-1 font-medium'>{employee.name}</span>
                      <span className='shrink-0 text-sm text-muted-foreground'>{totalTasks} tasks</span>
                    </div>
                    <div className='mt-1 flex h-2 w-full overflow-hidden rounded-full bg-muted'>
                      {employee.statuses.map(({count, status}) => {
                        const widthPercent = (count / totalTasks) * 100;

                        let bgColorClass;
                        switch (status) {
                          case ManufacturingTaskStatus.DONE:
                            bgColorClass = 'bg-green';
                            break;
                          case ManufacturingTaskStatus.IN_PROGRESS:
                            bgColorClass = 'bg-blue';
                            break;
                          case ManufacturingTaskStatus.STOPPED:
                            bgColorClass = 'bg-red';
                            break;
                          case ManufacturingTaskStatus.TODO:
                            bgColorClass = 'bg-gray';
                            break;
                          default:
                            bgColorClass = 'bg-blue';
                        }

                        return (
                          <div
                            className={`h-full ${bgColorClass}`}
                            key={status}
                            style={{width: `${widthPercent}%`}}
                            title={`${t(status)}: ${count} tasks (${Math.round(widthPercent)}%)`}
                          />
                        );
                      })}
                    </div>
                    <div className='mt-1 flex justify-between text-xs text-muted-foreground'>
                      {employee.statuses.map(({count, status}) => (
                        <div className='flex items-center gap-1' key={status}>
                          <div
                            className={`size-2 rounded-full ${
                              status === ManufacturingTaskStatus.TODO
                                ? 'bg-gray'
                                : status === ManufacturingTaskStatus.IN_PROGRESS
                                  ? 'bg-blue'
                                  : status === ManufacturingTaskStatus.DONE
                                    ? 'bg-green'
                                    : 'bg-red'
                            }`}
                          />
                          <span>
                            {t(status)}: {count}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

const EmployeeTaskCompletionCardWithSize: FC<DashboardCardProps> = memo(({instanceId}) => {
  const [layout] = useAtom(dashboardLayoutAtom);
  const size = useMemo(
    () => (instanceId ? layout.find((item) => item.i === instanceId)?.size || CardSize.MEDIUM : CardSize.MEDIUM),
    [layout, instanceId],
  );

  return <EmployeeTaskCompletionCard size={size} />;
});

export default withDashboardCard(EmployeeTaskCompletionCardWithSize, {
  id: 'employeeTaskCompletion',
  sizes: {
    large: {h: 5, w: 4},
    medium: {h: 4, w: 2},
    small: {h: 3, w: 2},
  },
  title: 'task completion',
});
