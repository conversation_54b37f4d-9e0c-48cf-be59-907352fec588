import {FC, memo, useMemo} from 'react';

import {useAtom} from 'jotai';

import {LoadingSpinner} from '@/components/ui/special/LoadingSpinner';
import {CardTranslations, useCardTranslations} from '@/hooks/useCardTranslations';
import useEmployees from '@/hooks/useEmployees';
import {dashboardLayoutAtom} from '@/store/ui';
import {CardSize} from '@/types/dashboard';
import {getBgColor} from '@/utils/colors';

import {BarChart} from '../shared/BarChart';
import {Legend} from '../shared/Legend';
import {PieChart} from '../shared/PieChart';
import {DashboardCardProps} from '../withDashboardCard';
import withDashboardCard from '../withDashboardCard';

export const cardTranslations: CardTranslations = {
  'employee skills': {
    en: 'Employee Skills',
    hu: 'Alkalmazotti készségek',
    ro: 'Competențele angajaților',
  },
  'skills distribution': {
    en: 'Skills Distribution',
    hu: 'Készségek eloszlása',
    ro: 'Distribuția competențelor',
  },
  total: {
    en: 'Total',
    hu: 'Összesen',
    ro: 'Total',
  },
};

const EmployeeSkillsCard: FC<{size: CardSize}> = ({size}) => {
  const ct = useCardTranslations(cardTranslations);
  const {employees, isLoading} = useEmployees();

  const skillsData = useMemo(() => {
    const skillsMap: {[key: string]: {count: number; name: string}} = {};
    if (employees) {
      employees.forEach((emp) => {
        emp.manufacturingOperationTemplates.forEach((template) => {
          if (!skillsMap[template.id]) {
            skillsMap[template.id] = {count: 0, name: template.name};
          }
          skillsMap[template.id].count += 1;
        });
      });
    }
    return Object.values(skillsMap).sort((a, b) => b.count - a.count);
  }, [employees]);

  const segments = useMemo(() => {
    return skillsData.map((item) => ({
      color: getBgColor(item.name, {solid: true}) || 'blue',
      label: item.name,
      tooltip: `${item.count} ${ct('total')}`,
      value: item.count,
    }));
  }, [skillsData, ct]);

  const totalSkills = useMemo(() => skillsData.length, [skillsData]);

  if (isLoading) return <LoadingSpinner size='lg' />;
  if (skillsData.length === 0) return null;

  if (size === CardSize.SMALL) {
    return (
      <div className='flex size-full flex-col items-center justify-center p-2 text-center'>
        <PieChart centerText={ct('employee skills')} overrideTotal={totalSkills} segments={segments} />
      </div>
    );
  }

  if (size === CardSize.MEDIUM) {
    return (
      <div className='flex size-full flex-col overflow-hidden'>
        <div className='mb-3 flex items-center justify-between gap-2 border-b border-border pb-2'>
          <h2 className='text-lg font-semibold'>{ct('employee skills')}</h2>
          <span className='text-2xl font-bold text-blue'>{totalSkills}</span>
        </div>
        <div className='flex-1 overflow-y-auto pr-1'>
          <BarChart segments={segments} title={ct('skills distribution')} />
        </div>
      </div>
    );
  }

  return (
    <div className='flex size-full flex-col gap-4 overflow-hidden'>
      <div className='flex h-full gap-4 overflow-hidden'>
        <div className='flex w-1/2 flex-col gap-4'>
          <div className='rounded-lg border border-border p-4'>
            <PieChart centerText={ct('employee skills')} overrideTotal={totalSkills} segments={segments} />
          </div>
          <div className='rounded-lg border border-border p-4'>
            <Legend segments={segments} />
          </div>
        </div>
        <div className='flex w-1/2 flex-col overflow-hidden'>
          <div className='flex-1 overflow-y-auto rounded-lg border border-border p-4'>
            <BarChart segments={segments} title={ct('skills distribution')} />
          </div>
        </div>
      </div>
    </div>
  );
};

const EmployeeSkillsCardWithSize: FC<DashboardCardProps> = memo(({instanceId}) => {
  const [layout] = useAtom(dashboardLayoutAtom);
  const size = useMemo(
    () => (instanceId ? layout.find((item) => item.i === instanceId)?.size || CardSize.MEDIUM : CardSize.MEDIUM),
    [layout, instanceId],
  );
  return <EmployeeSkillsCard size={size} />;
});

export default withDashboardCard(EmployeeSkillsCardWithSize, {
  id: 'employeeSkills',
  sizes: {
    large: {h: 5, w: 4},
    medium: {h: 4, w: 2},
    small: {h: 3, w: 2},
  },
  title: 'employee skills',
});
