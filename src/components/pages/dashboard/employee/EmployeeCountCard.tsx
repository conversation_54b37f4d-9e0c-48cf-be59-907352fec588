import {FC, memo, useMemo} from 'react';

import {useAtom} from 'jotai';
import {groupBy, orderBy} from 'lodash';

import {LoadingSpinner} from '@/components/ui/special/LoadingSpinner';
import {CardTranslations, useCardTranslations} from '@/hooks/useCardTranslations';
import useEmployees from '@/hooks/useEmployees';
import {dashboardLayoutAtom} from '@/store/ui';
import {CardSize} from '@/types/dashboard';
import {getBgColor} from '@/utils/colors';

import {BarChart} from '../shared/BarChart';
import {Legend} from '../shared/Legend';
import {PieChart} from '../shared/PieChart';
import {DashboardCardProps} from '../withDashboardCard';
import withDashboardCard from '../withDashboardCard';

export const cardTranslations: CardTranslations = {
  'employee count': {
    en: 'Employee Count',
    hu: 'Alkalmazottak száma',
    ro: 'Număr anga<PERSON>i',
  },
  'position distribution': {
    en: 'Position Distribution',
    hu: 'Pozíció eloszlás',
    ro: 'Distribuția pozițiilor',
  },
  'top positions': {
    en: 'Top Positions',
    hu: 'Top pozíciók',
    ro: 'Poziții de top',
  },
  total: {
    en: 'Total',
    hu: 'Összesen',
    ro: 'Total',
  },
};

type Props = {
  size?: CardSize;
};

const EmployeeCountCard: FC<Props> = memo(({size = CardSize.MEDIUM}) => {
  const ct = useCardTranslations(cardTranslations);
  const {employees, isLoading} = useEmployees();

  const {positionCounts, total} = useMemo(() => {
    const total = employees.length;
    const employeesByPosition = groupBy(employees, (e) => e.position || 'Unknown');
    const positionCounts = orderBy(
      Object.entries(employeesByPosition).map(([position, emps]) => ({
        count: emps.length,
        position,
      })),
      'count',
      'desc',
    );
    return {positionCounts, total};
  }, [employees]);

  const segments = useMemo(
    () =>
      positionCounts.map(({count, position}) => ({
        color: getBgColor(position, {solid: true}) || 'blue',
        label: position,
        value: count,
      })),
    [positionCounts],
  );

  if (isLoading) return <LoadingSpinner size='lg' />;
  if (total === 0) return null;

  if (size === CardSize.SMALL) {
    return (
      <div className='flex size-full flex-col items-center justify-center p-2 text-center'>
        <PieChart centerText={ct('employee count')} segments={segments} />
      </div>
    );
  }

  if (size === CardSize.MEDIUM) {
    return (
      <div className='flex size-full flex-col overflow-hidden'>
        <div className='mb-3 flex items-center justify-between border-b border-border pb-2'>
          <h2 className='text-lg font-semibold'>{ct('employee count')}</h2>
          <span className='text-2xl font-bold text-blue'>{total}</span>
        </div>
        <h4 className='mb-2 text-sm font-medium text-muted-foreground'>{ct('top positions')}</h4>
        <div className='flex-1 space-y-2 overflow-y-auto pr-1'>
          <BarChart
            renderValue={({percent, segment}) => `(${segment.value}) ${percent}%`}
            segments={segments}
            title={undefined}
          />
        </div>
      </div>
    );
  }

  return (
    <div className='flex size-full flex-col gap-4 overflow-hidden'>
      <div className='flex h-full gap-4 overflow-hidden'>
        <div className='flex w-1/2 flex-col gap-4'>
          <div className='rounded-lg border border-border p-4'>
            <PieChart centerText={ct('employee count')} segments={segments} />
          </div>
          <div className='rounded-lg border border-border p-4'>
            <Legend segments={segments} />
          </div>
        </div>
        <div className='flex w-1/2 flex-col overflow-hidden'>
          <div className='flex-1 overflow-y-auto rounded-lg border border-border p-4'>
            <BarChart segments={segments} title={ct('position distribution')} />
          </div>
        </div>
      </div>
    </div>
  );
});

const EmployeeCountCardWithSize: FC<DashboardCardProps> = memo(({instanceId}) => {
  const [layout] = useAtom(dashboardLayoutAtom);
  const size = useMemo(
    () => (instanceId ? layout.find((item) => item.i === instanceId)?.size || CardSize.MEDIUM : CardSize.MEDIUM),
    [layout, instanceId],
  );

  return <EmployeeCountCard size={size} />;
});

export default withDashboardCard(EmployeeCountCardWithSize, {
  id: 'employeeCount',
  sizes: {
    large: {h: 5, w: 4},
    medium: {h: 4, w: 2},
    small: {h: 3, w: 2},
  },
  title: 'employee count',
});
