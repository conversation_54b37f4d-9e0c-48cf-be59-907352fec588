import {FC, memo, useMemo} from 'react';

import {useAtom} from 'jotai';

import {Button} from '@/components/ui/Button';
import {Link} from '@/components/ui/special/Link';
import {LoadingSpinner} from '@/components/ui/special/LoadingSpinner';
import {CardTranslations, useCardTranslations} from '@/hooks/useCardTranslations';
import useEmployees from '@/hooks/useEmployees';
import {dashboardLayoutAtom} from '@/store/ui';
import {CardSize} from '@/types/dashboard';

import {BarChart} from '../shared/BarChart';
import {PieChart} from '../shared/PieChart';
import {DashboardCardProps} from '../withDashboardCard';
import withDashboardCard from '../withDashboardCard';

export const cardTranslations: CardTranslations = {
  'All employees have paired devices': {
    en: 'All employees have paired devices',
    hu: 'Minden alkalmazottnak párosított eszköze van',
    ro: 'Toți angajații au dispozitive asociate',
  },
  'device pairing status': {
    en: 'Device Pairing Status',
    hu: 'Eszköz párosítási állapot',
    ro: 'Starea de asociere a dispozitivului',
  },
  paired: {
    en: 'Paired',
    hu: 'Párosítva',
    ro: 'Asociat',
  },
  'paired devices': {
    en: 'Paired devices',
    hu: 'Párosított eszközök',
    ro: 'Dispozitive asociate',
  },
  unpaired: {
    en: 'Unpaired',
    hu: 'Nincs párosítva',
    ro: 'Neasociat',
  },
};

const EmployeePairingCard: FC<{size: CardSize}> = ({size}) => {
  const ct = useCardTranslations(cardTranslations);
  const {employees, isLoading} = useEmployees();

  const pairedEmployees = useMemo(() => employees.filter((emp) => emp.isDevicePaired), [employees]);
  const unpairedEmployees = useMemo(() => employees.filter((emp) => !emp.isDevicePaired), [employees]);
  const totalEmployees = employees.length;

  const segments = useMemo(
    () => [
      {color: 'green', label: ct('paired'), value: pairedEmployees.length},
      {color: 'red', label: ct('unpaired'), value: totalEmployees - pairedEmployees.length},
    ],
    [pairedEmployees.length, totalEmployees, ct],
  );

  if (isLoading) {
    return <LoadingSpinner size='lg' />;
  }

  if (totalEmployees === 0) {
    return null;
  }

  if (size === CardSize.SMALL) {
    return (
      <div className='flex size-full flex-col items-center justify-center p-2 text-center'>
        <PieChart centerText={ct('paired devices')} segments={segments} />
      </div>
    );
  }

  return (
    <div className='flex size-full flex-col overflow-hidden'>
      <div className='mb-3 flex items-center justify-between border-b border-border pb-2'>
        <h2 className='text-lg font-semibold'>{ct('paired devices')}</h2>
        <span className='whitespace-nowrap text-2xl font-bold text-blue'>
          {pairedEmployees.length}/{totalEmployees}
        </span>
      </div>

      <BarChart
        segments={[
          {
            color: 'red',
            label: `${unpairedEmployees.length} ${ct('unpaired')}`,
            value: unpairedEmployees.length,
          },
        ]}
      />
      <div className='mt-4 flex-1 overflow-y-auto pr-1'>
        <div className='space-y-3'>
          {unpairedEmployees.length > 0 ? (
            <div className='text-xs'>
              {unpairedEmployees.map((employee, index) => (
                <span key={employee.id}>
                  <Button asChild size='none' variant='text'>
                    <Link className='text-muted-foreground' href={`/employees/${employee.id}`} target='_blank'>
                      {employee.name}
                    </Link>
                  </Button>
                  {index < unpairedEmployees.length - 1 ? ', ' : ''}
                </span>
              ))}
            </div>
          ) : (
            <div className='text-sm text-muted-foreground'>{ct('All employees have paired devices')}</div>
          )}
        </div>
      </div>
    </div>
  );
};

const EmployeePairingCardWithSize: FC<DashboardCardProps> = memo(({instanceId}) => {
  const [layout] = useAtom(dashboardLayoutAtom);
  const size = useMemo(
    () => (instanceId ? layout.find((item) => item.i === instanceId)?.size || CardSize.MEDIUM : CardSize.MEDIUM),
    [layout, instanceId],
  );

  return <EmployeePairingCard size={size} />;
});

export default withDashboardCard(EmployeePairingCardWithSize, {
  id: 'devicePairing',
  sizes: {
    medium: {h: 3, w: 2},
    small: {h: 3, w: 2},
  },
  title: 'paired devices',
});
