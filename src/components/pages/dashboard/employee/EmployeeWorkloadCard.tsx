import {FC, memo, useMemo} from 'react';

import {startOfDay} from 'date-fns';
import {useAtom} from 'jotai';

import {LoadingSpinner} from '@/components/ui/special/LoadingSpinner';
import {CardTranslations, useCardTranslations} from '@/hooks/useCardTranslations';
import useEmployees from '@/hooks/useEmployees';
import useTasks from '@/hooks/useTasks';
import {dashboardLayoutAtom} from '@/store/ui';
import {CardSize} from '@/types/dashboard';
import {getBgColor} from '@/utils/colors';

import {BarChart} from '../shared/BarChart';
import {Legend} from '../shared/Legend';
import {PieChart} from '../shared/PieChart';
import {DashboardCardProps} from '../withDashboardCard';
import withDashboardCard from '../withDashboardCard';

export const cardTranslations: CardTranslations = {
  'employee workload': {
    en: 'Employee Workload',
    hu: 'Alkalmazotti munkaterhelés',
    ro: 'Volumul de lucru al angajaților',
  },
  'hour abbreviation': {
    en: 'h',
    hu: 'ó',
    ro: 'o',
  },
  'tasks count': {
    en: 'Tasks Count',
    hu: 'Feladatok száma',
    ro: 'Număr de sarcini',
  },
  total: {
    en: 'Total',
    hu: 'Összesen',
    ro: 'Total',
  },
  'workload distribution': {
    en: 'Workload Distribution',
    hu: 'Munkaterhelés eloszlása',
    ro: 'Distribuția volumului de lucru',
  },
};

const EmployeeWorkloadCard: FC<{size: CardSize}> = ({size}) => {
  const ct = useCardTranslations(cardTranslations);
  const {employees, isLoading: isLoadingEmployees} = useEmployees();
  const {isLoading: isLoadingTasks, tasks} = useTasks(startOfDay(new Date()), {view: 'day'});

  const workloadData = useMemo(() => {
    const workloadMap: {[key: string]: {name: string; tasksCount: number; totalDuration: number}} = {};
    if (employees && tasks) {
      employees.forEach((emp) => {
        workloadMap[emp.id] = {
          name: emp.name,
          tasksCount: 0,
          totalDuration: 0,
        };
      });
      tasks.forEach((task) => {
        task.assignedEmployees.forEach((emp) => {
          if (workloadMap[emp.id]) {
            workloadMap[emp.id].totalDuration += task.durationInMinutes;
            workloadMap[emp.id].tasksCount += 1;
          }
        });
      });
    }
    return Object.values(workloadMap).sort((a, b) => b.totalDuration - a.totalDuration);
  }, [employees, tasks]);

  const segments = useMemo(() => {
    return workloadData.map((item) => ({
      color: getBgColor(item.name, {solid: true}) || 'blue',
      label: item.name,
      tooltip: `${item.tasksCount} ${ct('tasks count')}`,
      value: Number((item.totalDuration / 60).toFixed(1)),
    }));
  }, [workloadData, ct]);

  const totalHours = useMemo(() => {
    return workloadData.reduce((sum, emp) => sum + emp.totalDuration, 0) / 60;
  }, [workloadData]);

  if (isLoadingEmployees || isLoadingTasks) return <LoadingSpinner size='lg' />;
  if (workloadData.length === 0) return null;

  const hourAbbr = ct('hour abbreviation');

  if (size === CardSize.SMALL) {
    return (
      <div className='flex size-full flex-col items-center justify-center p-2 text-center'>
        <PieChart centerText={ct('employee workload')} segments={segments} suffix={hourAbbr} />
      </div>
    );
  }

  if (size === CardSize.MEDIUM) {
    return (
      <div className='flex size-full flex-col overflow-hidden'>
        <div className='mb-3 flex items-center justify-between gap-2 border-b border-border pb-2'>
          <h2 className='text-lg font-semibold'>{ct('employee workload')}</h2>
          <span className='text-2xl font-bold text-blue'>
            {totalHours.toFixed(1)}
            {hourAbbr}
          </span>
        </div>
        <div className='flex-1 overflow-y-auto pr-1'>
          <BarChart
            renderValue={({percent, segment}) => `(${percent}%) ${segment.value}${hourAbbr}`}
            segments={segments}
            title={ct('workload distribution')}
          />
        </div>
      </div>
    );
  }

  return (
    <div className='flex size-full flex-col gap-4 overflow-hidden'>
      <div className='flex h-full gap-4 overflow-hidden'>
        <div className='flex w-1/2 flex-col gap-4'>
          <div className='rounded-lg border border-border p-4'>
            <PieChart centerText={ct('employee workload')} segments={segments} suffix={hourAbbr} />
          </div>
          <div className='rounded-lg border border-border p-4'>
            <Legend segments={segments} suffix={hourAbbr} />
          </div>
        </div>
        <div className='flex w-1/2 flex-col overflow-hidden'>
          <div className='flex-1 overflow-y-auto rounded-lg border border-border p-4'>
            <BarChart
              renderValue={({percent, segment}) => `(${percent}%) ${segment.value}${hourAbbr}`}
              segments={segments}
              title={ct('workload distribution')}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

const EmployeeWorkloadCardWithSize: FC<DashboardCardProps> = memo(({instanceId}) => {
  const [layout] = useAtom(dashboardLayoutAtom);
  const size = useMemo(
    () => (instanceId ? layout.find((item) => item.i === instanceId)?.size || CardSize.MEDIUM : CardSize.MEDIUM),
    [layout, instanceId],
  );
  return <EmployeeWorkloadCard size={size} />;
});

export default withDashboardCard(EmployeeWorkloadCardWithSize, {
  id: 'employeeWorkload',
  sizes: {
    large: {h: 5, w: 4},
    medium: {h: 4, w: 2},
    small: {h: 3, w: 2},
  },
  title: 'employee workload',
});
