import {FC, memo, useMemo} from 'react';

import {addDays, addWeeks, endOfWeek, isWithinInterval, startOfWeek} from 'date-fns';
import {useAtom} from 'jotai';

import {Button} from '@/components/ui/Button';
import {Link} from '@/components/ui/special/Link';
import {LoadingSpinner} from '@/components/ui/special/LoadingSpinner';
import {useDeepMemo} from '@/hooks/helpers/useDeep';
import {CardTranslations, useCardTranslations} from '@/hooks/useCardTranslations';
import useEmployees from '@/hooks/useEmployees';
import {dashboardLayoutAtom} from '@/store/ui';
import {CardSize} from '@/types/dashboard';
import {Employee, EmployeeActiveTimeOffType} from '@/types/manufacturing';

import {BarChart} from '../shared/BarChart';
import {Legend} from '../shared/Legend';
import {PieChart} from '../shared/PieChart';
import {DashboardCardProps} from '../withDashboardCard';
import withDashboardCard from '../withDashboardCard';

const cardTranslations: CardTranslations = {
  active: {
    en: 'Active',
    hu: 'Aktív',
    ro: 'Activi',
  },
  'active employees': {
    en: 'Active Employees',
    hu: 'Aktív alkalmazottak',
    ro: 'Angajați activi',
  },
  business: {
    en: 'Business',
    hu: 'Üzleti út',
    ro: 'Delegație',
  },
  distribution: {
    en: 'Distribution',
    hu: 'Eloszlás',
    ro: 'Distribuție',
  },
  later: {
    en: 'Later',
    hu: 'Később',
    ro: 'Mai târziu',
  },
  'next week': {
    en: 'Next Week',
    hu: 'Jövő héten',
    ro: 'Săptămâna viitoare',
  },
  'on business': {
    en: 'On Business',
    hu: 'Üzleti úton',
    ro: 'În delegație',
  },
  'on pto': {
    en: 'On PTO',
    hu: 'Szabadságon',
    ro: 'În concediu',
  },
  pto: {
    en: 'PTO',
    hu: 'Szabadság',
    ro: 'Concediu',
  },
  'returning from business': {
    en: 'Returning from Business',
    hu: 'Visszatérés üzleti útról',
    ro: 'Se întorc din delegație',
  },
  'returning from pto': {
    en: 'Returning from PTO',
    hu: 'Visszatérés szabadságról',
    ro: 'Se întorc din concediu',
  },
  'this week': {
    en: 'This Week',
    hu: 'Ezen a héten',
    ro: 'Săptămâna aceasta',
  },
  tomorrow: {
    en: 'Tomorrow',
    hu: 'Holnap',
    ro: 'Mâine',
  },
};

type EmployeeStatus = {
  active: number;
  business: number;
  pto: number;
  returning: {
    business: Record<TimeframeKey, Employee[]>;
    pto: Record<TimeframeKey, Employee[]>;
  };
};

type TimeframeKey = 'later' | 'next week' | 'this week' | 'tomorrow';

const getEmployeeStatus = (employees: Employee[]): EmployeeStatus => {
  const now = new Date();
  const tomorrow = addDays(now, 1);
  const thisWeekStart = startOfWeek(now);
  const thisWeekEnd = endOfWeek(now);
  const nextWeekStart = addWeeks(thisWeekStart, 1);
  const nextWeekEnd = endOfWeek(nextWeekStart);

  const status: EmployeeStatus = {
    active: 0,
    business: 0,
    pto: 0,
    returning: {
      business: {later: [], 'next week': [], 'this week': [], tomorrow: []},
      pto: {later: [], 'next week': [], 'this week': [], tomorrow: []},
    },
  };

  employees.forEach((emp) => {
    let isOnLeave = false;

    emp.activeTimeoffs.forEach((timeoff) => {
      const startTime = new Date(timeoff.startTime);
      const endTime = new Date(timeoff.endTime);

      if (isWithinInterval(now, {end: endTime, start: startTime})) {
        isOnLeave = true;
        const category = timeoff.type === EmployeeActiveTimeOffType.BUSINESS ? 'business' : 'pto';
        status[category]++;

        if (isWithinInterval(endTime, {end: tomorrow, start: now})) {
          status.returning[category]['tomorrow'].push(emp);
        } else if (isWithinInterval(endTime, {end: thisWeekEnd, start: thisWeekStart})) {
          status.returning[category]['this week'].push(emp);
        } else if (isWithinInterval(endTime, {end: nextWeekEnd, start: nextWeekStart})) {
          status.returning[category]['next week'].push(emp);
        } else {
          status.returning[category]['later'].push(emp);
        }
      }
    });

    if (!isOnLeave) {
      status.active++;
    }
  });

  return status;
};

const ReturnSchedule: FC<{returning: EmployeeStatus['returning']; type: EmployeeActiveTimeOffType}> = ({
  returning,
  type,
}) => {
  const ct = useCardTranslations(cardTranslations);
  const title = type === EmployeeActiveTimeOffType.BUSINESS ? ct('returning from business') : ct('returning from pto');
  const color = type === EmployeeActiveTimeOffType.BUSINESS ? 'text-blue' : 'text-gray';
  const orderedTimeframes: TimeframeKey[] = ['tomorrow', 'this week', 'next week', 'later'];

  return (
    <div className='flex h-full flex-col overflow-hidden'>
      <h3 className={`text-sm font-medium ${color} mb-3 shrink-0`}>{title}</h3>
      <div className='flex-1 space-y-3 overflow-y-auto pr-1'>
        {orderedTimeframes.map((timeframe) => {
          const employees = returning[type === EmployeeActiveTimeOffType.BUSINESS ? 'business' : 'pto'][timeframe];
          if (employees.length === 0) return null;

          return (
            <div key={timeframe}>
              <div className='mb-1 flex items-center justify-between text-sm'>
                <span>{ct(timeframe)}</span>
                <span className='font-medium'>{employees.length}</span>
              </div>
              <div className='text-xs'>
                {employees.map((emp, index) => (
                  <span key={emp.id}>
                    <Button asChild size='none' variant='text'>
                      <Link className='text-muted-foreground' href={`/employees/${emp.id}`} target='_blank'>
                        {emp.name}
                      </Link>
                    </Button>
                    {index < employees.length - 1 ? ', ' : ''}
                  </span>
                ))}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

const EmployeeActiveStatusCard: FC<{size: CardSize}> = ({size}) => {
  const ct = useCardTranslations(cardTranslations);
  const {employees, isLoading} = useEmployees();

  const status = useDeepMemo(() => getEmployeeStatus(employees || []), [employees]);

  const total = useDeepMemo(() => {
    const active = status?.active || 0;
    const business = status?.business || 0;
    const pto = status?.pto || 0;
    return active + business + pto;
  }, [status]);

  const segments = useDeepMemo(
    () => [
      {color: 'green', label: ct('active'), value: status?.active || 0},
      {color: 'blue', label: ct('on business'), value: status?.business || 0},
      {color: 'gray', label: ct('on pto'), value: status?.pto || 0},
    ],
    [status, ct],
  );

  if (isLoading) return <LoadingSpinner size='lg' />;
  if (!status || total === 0) return null;

  if (size === CardSize.SMALL) {
    return (
      <div className='flex size-full flex-col items-center justify-center p-2 text-center'>
        <PieChart centerText={ct('active employees')} segments={segments} />
      </div>
    );
  }

  if (size === CardSize.MEDIUM) {
    return (
      <div className='flex size-full flex-col overflow-hidden'>
        <div className='mb-3 flex items-center justify-between border-b border-border pb-2'>
          <h2 className='text-lg font-semibold'>{ct('active employees')}</h2>
          <span className='text-2xl font-bold text-blue'>{total}</span>
        </div>
        <h4 className='mb-2 text-sm font-medium text-muted-foreground'>{ct('distribution')}</h4>
        <div className='flex-1 space-y-2 overflow-y-auto pr-1'>
          <BarChart
            renderValue={({percent, segment}) => `(${segment.value}) ${percent}%`}
            segments={segments}
            title={undefined}
          />
        </div>
      </div>
    );
  }

  return (
    <div className='flex size-full flex-col overflow-hidden'>
      <div className='flex shrink-0 gap-4'>
        <div className='flex w-1/2 flex-col items-center gap-4'>
          <PieChart centerText={ct('active employees')} segments={segments} />
        </div>
        <div className='flex w-1/2 flex-col gap-4'>
          <div className='space-y-2 rounded-lg border border-border p-4'>
            <h4 className='text-sm font-medium text-muted-foreground'>{ct('distribution')}</h4>
            <BarChart segments={segments} title={undefined} />
          </div>
          <Legend segments={segments} />
        </div>
      </div>
      <div className='mt-4 grid flex-1 grid-cols-2 gap-4 overflow-hidden'>
        <div className='overflow-hidden rounded-lg border border-border p-4'>
          <ReturnSchedule returning={status.returning} type={EmployeeActiveTimeOffType.BUSINESS} />
        </div>
        <div className='overflow-hidden rounded-lg border border-border p-4'>
          <ReturnSchedule returning={status.returning} type={EmployeeActiveTimeOffType.PTO} />
        </div>
      </div>
    </div>
  );
};

const EmployeeActiveStatusCardWithSize: FC<DashboardCardProps> = memo(({instanceId}) => {
  const [layout] = useAtom(dashboardLayoutAtom);
  const size = useMemo(
    () => (instanceId ? layout.find((item) => item.i === instanceId)?.size || CardSize.MEDIUM : CardSize.MEDIUM),
    [layout, instanceId],
  );

  return <EmployeeActiveStatusCard size={size} />;
});

export default withDashboardCard(EmployeeActiveStatusCardWithSize, {
  id: 'activeEmployeeStatus',
  sizes: {
    large: {h: 5, w: 4},
    medium: {h: 3, w: 2},
    small: {h: 3, w: 2},
  },
  title: 'active employees',
});
