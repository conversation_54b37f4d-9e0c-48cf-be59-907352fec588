import {FC, memo, useMemo} from 'react';

import {useAtom, useAtomValue} from 'jotai';

import {LoadingSpinner} from '@/components/ui/special/LoadingSpinner';
import {CardTranslations, useCardTranslations} from '@/hooks/useCardTranslations';
import useEmployees from '@/hooks/useEmployees';
import {defaultCurrencyAtom} from '@/store/defaults';
import {dashboardLayoutAtom} from '@/store/ui';
import {CardSize} from '@/types/dashboard';
import {getBgColor} from '@/utils/colors';

import {BarChart} from '../shared/BarChart';
import {Legend} from '../shared/Legend';
import {PieChart} from '../shared/PieChart';
import {DashboardCardProps} from '../withDashboardCard';
import withDashboardCard from '../withDashboardCard';

export const cardTranslations: CardTranslations = {
  'average salary': {
    en: 'Average Salary',
    hu: 'Átlagos fizetés',
    ro: 'Salariu mediu',
  },
  'currency abbreviation': {
    en: '$',
    hu: 'Ft',
    ro: 'RON',
  },
  'employee salary': {
    en: 'Employee Salary',
    hu: 'Alkalmazotti fizetés',
    ro: 'Salariu angajat',
  },
  monthly: {
    en: 'Monthly',
    hu: 'Havi',
    ro: 'Lunar',
  },
  'salary distribution': {
    en: 'Salary Distribution',
    hu: 'Fizetés eloszlás',
    ro: 'Distribuția salariilor',
  },
  'salary ranges': {
    en: 'Salary Ranges',
    hu: 'Fizetési sávok',
    ro: 'Intervale salariale',
  },
  'total payroll': {
    en: 'Total Payroll',
    hu: 'Teljes bértömeg',
    ro: 'Fond salarial total',
  },
  unset: {
    en: 'Unset',
    hu: 'Nincs beállítva',
    ro: 'Nesetat',
  },
};

interface SalaryRange {
  count: number;
  label: string;
  max: number;
  min: number;
  percentRange?: number;
}

const EmployeeSalaryCard: FC<{size: CardSize}> = ({size}) => {
  const ct = useCardTranslations(cardTranslations);
  const {employees, isLoading} = useEmployees();
  const defaultCurrency = useAtomValue(defaultCurrencyAtom);
  const currencyAbbr = employees[0]?.monthlyGrossSalary?.currency || defaultCurrency;

  const {averageSalary, salaryRanges, salaryRangesLarge} = useMemo(() => {
    if (employees.length === 0) {
      return {
        averageSalary: 0,
        salaryRanges: [] as SalaryRange[],
        salaryRangesLarge: [] as SalaryRange[],
      };
    }

    const monthlySalaries = employees.map((emp) => emp.monthlyGrossSalary?.amount || 0);
    const validSalaries = monthlySalaries.filter((salary) => salary > 0);
    const unsetCount = monthlySalaries.length - validSalaries.length;

    const totalPayroll = validSalaries.reduce((sum, salary) => sum + salary, 0);
    const validSalaryCount = validSalaries.length;
    const averageSalary = validSalaryCount > 0 ? totalPayroll / validSalaryCount : 0;

    const ranges: Record<string, SalaryRange> = {};
    const rangesLarge: Record<string, SalaryRange> = {};

    ranges['unset'] = {
      count: unsetCount,
      label: ct('unset'),
      max: 0,
      min: 0,
    };

    rangesLarge['unset'] = {
      count: unsetCount,
      label: ct('unset'),
      max: 0,
      min: 0,
    };

    if (validSalaries.length > 0) {
      const minSalary = Math.min(...validSalaries);
      const maxSalary = Math.max(...validSalaries);

      for (let i = 0; i < 5; i++) {
        const percentMin = i * 20;
        const percentMax = (i + 1) * 20;
        const min = Math.round(minSalary + (maxSalary - minSalary) * (percentMin / 100));
        const max = Math.round(minSalary + (maxSalary - minSalary) * (percentMax / 100));

        const key = `range-${i}`;
        ranges[key] = {
          count: 0,
          label: `${min}-${max} ${currencyAbbr}`,
          max,
          min,
          percentRange: percentMin,
        };
      }

      for (let i = 0; i < 10; i++) {
        const percentMin = i * 10;
        const percentMax = (i + 1) * 10;
        const min = Math.round(minSalary + (maxSalary - minSalary) * (percentMin / 100));
        const max = Math.round(minSalary + (maxSalary - minSalary) * (percentMax / 100));

        const key = `range-${i}`;
        rangesLarge[key] = {
          count: 0,
          label: `${min}-${max} ${currencyAbbr}`,
          max,
          min,
          percentRange: percentMin,
        };
      }

      employees.forEach((emp) => {
        const salary = emp.monthlyGrossSalary?.amount || 0;

        if (salary === 0) return;

        for (const range of Object.values(ranges)) {
          if (range.min === 0 && range.max === 0) continue;
          if (salary >= range.min && salary <= range.max) {
            range.count++;
            break;
          }
        }

        for (const range of Object.values(rangesLarge)) {
          if (range.min === 0 && range.max === 0) continue;
          if (salary >= range.min && salary <= range.max) {
            range.count++;
            break;
          }
        }
      });
    }

    const sortRanges = (rangeArray: SalaryRange[]) => {
      return rangeArray.sort((a, b) => {
        if (a.min === 0 && a.max === 0) return -1;
        if (b.min === 0 && b.max === 0) return 1;

        if (a.percentRange !== undefined && b.percentRange !== undefined) {
          return a.percentRange - b.percentRange;
        }

        return a.min - b.min;
      });
    };

    const salaryRanges = sortRanges(Object.values(ranges));
    const salaryRangesLarge = sortRanges(Object.values(rangesLarge));

    return {
      averageSalary,
      salaryRanges,
      salaryRangesLarge,
      totalPayroll,
      unsetCount,
      validSalaryCount,
    };
  }, [currencyAbbr, employees, ct]);

  const getSegments = (ranges: SalaryRange[]) => {
    return ranges.map((range) => ({
      color: getBgColor(range.label, {solid: true}) || 'blue',
      label: range.label,
      value: range.count,
    }));
  };

  const segments = useMemo(() => getSegments(salaryRanges), [salaryRanges]);
  const segmentsLarge = useMemo(() => getSegments(salaryRangesLarge), [salaryRangesLarge]);

  if (isLoading) return <LoadingSpinner size='lg' />;
  if (employees.length === 0) return null;

  if (size === CardSize.SMALL) {
    return (
      <div className='flex size-full flex-col items-center justify-center p-2 text-center'>
        <PieChart
          centerText={ct('average salary')}
          overrideTotal={averageSalary}
          segments={segments}
          suffix={currencyAbbr}
        />
      </div>
    );
  }

  if (size === CardSize.MEDIUM) {
    return (
      <div className='flex size-full flex-col overflow-hidden'>
        <div className='mb-3 flex items-center justify-between gap-2 border-b border-border pb-2'>
          <h2 className='text-lg font-semibold'>{ct('average salary')}</h2>
          <span className='text-2xl font-bold text-blue'>
            {averageSalary.toFixed(0)} {currencyAbbr}
          </span>
        </div>
        <div className='flex-1 overflow-y-auto pr-1'>
          <BarChart
            renderValue={({percent, segment}) => `(${segment.value}) ${percent}%`}
            segments={segments}
            title={ct('salary distribution')}
          />
        </div>
      </div>
    );
  }

  return (
    <div className='flex size-full flex-col gap-4 overflow-hidden'>
      <div className='flex h-full gap-4 overflow-hidden'>
        <div className='flex w-1/2 flex-col gap-4'>
          <div className='rounded-lg border border-border p-4'>
            <PieChart
              centerText={ct('average salary')}
              overrideTotal={averageSalary}
              segments={segmentsLarge}
              suffix={currencyAbbr}
            />
          </div>
          <Legend segments={segmentsLarge} />
        </div>
        <div className='flex w-1/2 flex-col overflow-hidden'>
          <div className='flex-1 overflow-y-auto rounded-lg border border-border p-4'>
            <BarChart
              renderValue={({percent, segment}) => `(${segment.value}) ${percent}%`}
              segments={segmentsLarge}
              title={ct('salary distribution')}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

const EmployeeSalaryCardWithSize: FC<DashboardCardProps> = memo(({instanceId}) => {
  const [layout] = useAtom(dashboardLayoutAtom);
  const size = useMemo(
    () => (instanceId ? layout.find((item) => item.i === instanceId)?.size || CardSize.MEDIUM : CardSize.MEDIUM),
    [layout, instanceId],
  );

  return <EmployeeSalaryCard size={size} />;
});

export default withDashboardCard(EmployeeSalaryCardWithSize, {
  id: 'employeeSalary',
  sizes: {
    large: {h: 5, w: 4},
    medium: {h: 4, w: 2},
    small: {h: 3, w: 2},
  },
  title: 'employee salary',
});
