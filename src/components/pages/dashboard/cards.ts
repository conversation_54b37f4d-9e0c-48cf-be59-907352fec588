import {FC} from 'react';

import {DashboardCardConfig} from '@/types/dashboard';

import * as EmployeeCards from './employee';
import {WithDashboardConfig} from './withDashboardCard';

const isDashboardCard = (component: any): component is FC & WithDashboardConfig => {
  return component && typeof component === 'function' && 'dashboardConfig' in component;
};

const createCardComponentsMap = () => {
  const allCardModules = [EmployeeCards];

  return allCardModules.reduce(
    (acc, cardModule) => {
      Object.values(cardModule).forEach((component) => {
        if (isDashboardCard(component)) {
          acc[component.dashboardConfig.id] = {
            Component: component,
            config: component.dashboardConfig,
          };
        }
      });
      return acc;
    },
    {} as Record<string, {Component: FC & WithDashboardConfig; config: DashboardCardConfig}>,
  );
};

export const CARD_COMPONENTS_MAP = createCardComponentsMap();
