import {FC, ReactNode, useState} from 'react';

import {GripVerticalIcon, LayoutDashboardIcon, MoreVerticalIcon, Trash2Icon} from 'lucide-react';

import {Button} from '@/components/ui/Button';
import {Popover, PopoverContent, PopoverTrigger} from '@/components/ui/Popover';
import {ToggleGroup, ToggleGroupItem} from '@/components/ui/ToggleGroup';
import {Tooltip, TooltipContent, TooltipTrigger} from '@/components/ui/Tooltip';
import {CardSize, SizeRepresentation} from '@/types/dashboard';

type Props = {
  availableSizes: Partial<Record<CardSize, SizeRepresentation>>;
  children: ReactNode;
  currentSizeCategory: CardSize;
  id: string;
  onRemove: (id: string) => void;
  onResize?: (id: string, newSize: CardSize) => void;
  title: string;
};

const DashboardCard: FC<Props> = ({availableSizes, children, currentSizeCategory, id, onRemove, onResize}) => {
  const [isPopoverOpen, setIsPopoverOpen] = useState(false);
  const isResizable = Object.keys(availableSizes).length > 1;

  const handleResize = (newSize: CardSize) => {
    if (onResize) {
      onResize(id, newSize);
    }
    setIsPopoverOpen(false);
  };

  const getSizeIcon = (size: CardSize) => {
    switch (size) {
      case CardSize.LARGE:
        return <LayoutDashboardIcon className='size-5' />;
      case CardSize.MEDIUM:
        return <LayoutDashboardIcon className='size-4' />;
      case CardSize.SMALL:
        return <LayoutDashboardIcon className='size-3' />;
    }
  };

  return (
    <div className='group relative flex size-full flex-col overflow-hidden rounded-lg border border-border  bg-white shadow-sm'>
      <div
        className={`flex w-full items-center justify-between border-b border-border transition-opacity ${isPopoverOpen ? 'opacity-100' : 'opacity-0 group-hover:opacity-100'}`}
      >
        <div className='flex grow items-center'>
          <Button
            className='react-draggable-handle w-full cursor-move justify-start text-muted'
            size='none'
            variant='none'
          >
            <GripVerticalIcon className='size-4' />
          </Button>
        </div>

        <div className='h-4 border-r border-border'></div>

        <Popover onOpenChange={setIsPopoverOpen} open={isPopoverOpen}>
          <PopoverTrigger asChild>
            <Button
              onMouseDown={(e) => {
                e.stopPropagation();
              }}
              size='none'
              variant='ghost'
            >
              <MoreVerticalIcon className='size-4' />
            </Button>
          </PopoverTrigger>
          <PopoverContent align='end' className='max-w-none p-3'>
            <div className='flex flex-col gap-3'>
              {isResizable && (
                <div>
                  <ToggleGroup
                    onValueChange={(value) => {
                      if (value) handleResize(value as CardSize);
                    }}
                    type='single'
                    value={currentSizeCategory}
                  >
                    {[CardSize.SMALL, CardSize.MEDIUM, CardSize.LARGE]
                      .filter((size) => availableSizes[size])
                      .map((size) => (
                        <ToggleGroupItem
                          aria-label={`${size} size`}
                          className={size === currentSizeCategory ? 'bg-accent text-accent-foreground' : ''}
                          key={size}
                          value={size}
                        >
                          <Tooltip key={size}>
                            <TooltipTrigger asChild>{getSizeIcon(size as CardSize)}</TooltipTrigger>
                            <TooltipContent>{size.charAt(0).toUpperCase() + size.slice(1)}</TooltipContent>
                          </Tooltip>
                        </ToggleGroupItem>
                      ))}
                  </ToggleGroup>
                </div>
              )}

              <Button onClick={() => onRemove(id)} size='full' variant='destructive'>
                <Trash2Icon className='mr-1 size-4' />
                Remove
              </Button>
            </div>
          </PopoverContent>
        </Popover>
      </div>
      <div className='flex-1 overflow-auto px-4 pb-4'>{children}</div>
    </div>
  );
};

export default DashboardCard;
