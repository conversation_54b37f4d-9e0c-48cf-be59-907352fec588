import {FC, memo, ReactNode, useMemo} from 'react';

import {Tooltip, TooltipContent, TooltipTrigger} from '@/components/ui/Tooltip';
import {ChartProps} from '@/types/dashboard';

type Props = ChartProps & {
  renderValue?: ({percent, segment}: {percent: number; segment: ChartProps['segments'][0]}) => ReactNode;
  showPercentage?: boolean;
  showValue?: boolean;
};

export const BarChart: FC<Props> = memo(
  ({renderValue, segments, showPercentage = true, showTooltip = true, showValue = false, title}) => {
    const total = useMemo(() => segments.reduce((sum, segment) => sum + segment.value, 0), [segments]);

    const percentages = useMemo(
      () => segments.map((segment) => Math.round((segment.value / total) * 100)),
      [segments, total],
    );

    return (
      <div className='space-y-2'>
        {title && <h4 className='text-sm font-medium text-muted-foreground'>{title}</h4>}
        <div className='space-y-2'>
          {segments.map((segment, index) => {
            const {color, label, value} = segment;
            const percent = percentages[index];
            const bg = color.startsWith('bg-')
              ? color
              : color.startsWith('text-')
                ? `bg-${color.replace('text-', '')}`
                : `bg-${color}`;

            const content = (
              <div className='space-y-1'>
                <div className='flex justify-between text-sm'>
                  <span className='truncate whitespace-nowrap'>{label}</span>
                  <span className='whitespace-nowrap text-muted-foreground'>
                    {renderValue
                      ? renderValue({percent, segment})
                      : `${showValue ? `${value}h ` : ''}${showPercentage ? `${percent}%` : ''}`}
                  </span>
                </div>
                <div className='h-2 w-full rounded bg-gray-100'>
                  <div className={`h-full rounded ${bg}`} style={{width: `${percent}%`}} />
                </div>
              </div>
            );

            if (showTooltip) {
              return (
                <Tooltip key={label}>
                  <TooltipTrigger asChild>
                    <div className='cursor-default'>{content}</div>
                  </TooltipTrigger>
                  <TooltipContent side='top'>{label}</TooltipContent>
                </Tooltip>
              );
            }

            return <div key={label}>{content}</div>;
          })}
        </div>
      </div>
    );
  },
);

BarChart.displayName = 'BarChart';
