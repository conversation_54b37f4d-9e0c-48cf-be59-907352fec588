import {FC, useMemo} from 'react';

import {Tooltip, TooltipContent, TooltipTrigger} from '@/components/ui/Tooltip';
import {ChartProps} from '@/types/dashboard';

type Props = ChartProps & {
  centerText?: string;
  overrideTotal?: number;
  suffix?: string;
};

export const PieChart: FC<Props> = ({centerText, overrideTotal, segments, showTooltip = true, suffix, title}) => {
  const validSegments = useMemo(() => segments.filter((s) => s.value > 0), [segments]);

  const computedTotal = useMemo(() => validSegments.reduce((sum, s) => sum + s.value, 0), [validSegments]);

  const displayedTotal = useMemo(() => {
    const total = overrideTotal !== undefined ? overrideTotal : computedTotal;
    return parseFloat(total.toFixed(1));
  }, [overrideTotal, computedTotal]);

  const measureTextWidth = (text: string) => {
    const tempSpan = document.createElement('span');
    tempSpan.style.visibility = 'hidden';
    tempSpan.style.position = 'absolute';
    tempSpan.style.fontSize = '1.875rem'; // text-3xl
    tempSpan.style.fontWeight = '700'; // font-bold
    tempSpan.innerText = text;
    document.body.appendChild(tempSpan);
    const width = tempSpan.offsetWidth;
    document.body.removeChild(tempSpan);
    return width;
  };

  const totalText = `${displayedTotal}${suffix || ''}`;
  const needsBackground = useMemo(() => {
    if (typeof window === 'undefined') return false;
    return measureTextWidth(totalText) > 100;
  }, [totalText]);

  const pathData = validSegments.map((segment, index) => {
    const startAngle = validSegments.slice(0, index).reduce((sum, s) => sum + (s.value / computedTotal) * 360, 0);
    const endAngle = startAngle + (segment.value / computedTotal) * 360;
    const startRad = (startAngle - 90) * (Math.PI / 180);
    const endRad = (endAngle - 90) * (Math.PI / 180);
    const x1 = 50 + 40 * Math.cos(startRad);
    const y1 = 50 + 40 * Math.sin(startRad);
    const x2 = 50 + 40 * Math.cos(endRad);
    const y2 = 50 + 40 * Math.sin(endRad);
    const largeArcFlag = endAngle - startAngle > 180 ? 1 : 0;

    const color = segment.color || 'gray';
    const textColor = color.startsWith('bg-')
      ? `text-${color.replace('bg-', '')}`
      : color.startsWith('text-')
        ? color
        : `text-${color}`;

    return {
      color: textColor,
      path: `M 50 50 L ${x1} ${y1} A 40 40 0 ${largeArcFlag} 1 ${x2} ${y2} Z`,
    };
  });

  return (
    <div className='relative flex flex-col items-center'>
      {title && <h4 className='mb-2 text-sm font-medium text-muted-foreground'>{title}</h4>}
      <div className='relative size-56'>
        <svg className='size-full' viewBox='0 0 100 100'>
          <circle cx='50' cy='50' fill='none' r='40' stroke='#f3f4f6' strokeWidth='20' />
          {pathData.map(({color, path}, idx) => (
            <path className={`fill-current ${color}`} d={path} key={idx} />
          ))}
          <circle cx='50' cy='50' fill='white' r='25' />
        </svg>
        <div className='absolute inset-0 flex cursor-default flex-col items-center justify-center text-center'>
          <span
            className={`relative z-10 ${needsBackground ? 'rounded-full bg-white/90 px-3 py-1 shadow-xs' : ''} text-3xl font-bold text-blue`}
          >
            {totalText}
          </span>
          {centerText &&
            (showTooltip ? (
              <Tooltip>
                <TooltipTrigger asChild>
                  <span className='mt-1 line-clamp-2 w-[100px] text-xs font-medium leading-tight text-muted-foreground'>
                    {centerText}
                  </span>
                </TooltipTrigger>
                <TooltipContent side='top'>{centerText}</TooltipContent>
              </Tooltip>
            ) : (
              <span className='mt-1 line-clamp-2 w-[100px] text-xs font-medium leading-tight text-muted-foreground'>
                {centerText}
              </span>
            ))}
        </div>
      </div>
    </div>
  );
};

PieChart.displayName = 'PieChart';
