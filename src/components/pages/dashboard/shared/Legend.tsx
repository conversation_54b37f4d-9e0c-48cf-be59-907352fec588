import {FC, memo} from 'react';

import {Tooltip, TooltipContent, TooltipTrigger} from '@/components/ui/Tooltip';
import {ChartProps} from '@/types/dashboard';

type Props = ChartProps & {
  columns?: number;
  suffix?: string;
};

const GRID_MAP: Record<number, string> = {
  1: 'grid-cols-1',
  2: 'grid-cols-2',
  3: 'grid-cols-3',
  4: 'grid-cols-4',
};

export const Legend: FC<Props> = memo(({columns = 2, segments, showTooltip = true, suffix, title}) => {
  return (
    <div className='w-full'>
      {title && <h4 className='mb-2 text-sm font-medium text-muted-foreground'>{title}</h4>}
      <div className='max-h-28 w-full overflow-y-auto pr-1'>
        <div className={`${GRID_MAP[columns]} grid w-full gap-x-4 gap-y-2`}>
          {segments.map(({color, label, value}) => {
            const bgColor = color.startsWith('bg-')
              ? color
              : color.startsWith('text-')
                ? `bg-${color.replace('text-', '')}`
                : `bg-${color}`;
            return showTooltip ? (
              <Tooltip key={label}>
                <TooltipTrigger asChild>
                  <div className='flex cursor-default items-center gap-2 text-sm'>
                    <div className={`size-3 shrink-0 rounded-full ${bgColor}`} />
                    <span className='truncate'>{label}</span>
                    <span className='ml-auto text-muted-foreground'>
                      {value}
                      {suffix ? `${suffix}` : ''}
                    </span>
                  </div>
                </TooltipTrigger>
                <TooltipContent side='top'>{label}</TooltipContent>
              </Tooltip>
            ) : (
              <div className='flex cursor-default items-center gap-2 text-sm' key={label}>
                <div className={`size-3 shrink-0 rounded-full ${bgColor}`} />
                <span className='truncate'>{label}</span>
                <span className='ml-auto text-muted-foreground'>
                  {value}
                  {suffix ? `${suffix}` : ''}
                </span>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
});

Legend.displayName = 'Legend';
