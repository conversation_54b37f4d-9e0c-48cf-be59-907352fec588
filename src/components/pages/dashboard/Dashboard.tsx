import {ComponentType, FC, useCallback, useMemo, useState} from 'react';

import {useAtom} from 'jotai';
import {PlusIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';
import {Responsive, WidthProvider} from 'react-grid-layout';

import {Button} from '@/components/ui/Button';
import {DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger} from '@/components/ui/DropdownMenu';
import {Page, PageContent, PageHeader, PageHeaderTitle, PageTitle} from '@/components/ui/special/Page';
import useHeight from '@/hooks/helpers/useHeight';
import {CardTranslations, useCardTranslations} from '@/hooks/useCardTranslations';
import {dashboardLayoutAtom} from '@/store/ui';
import {CardSize, ReactGridLayoutItem, StoredLayoutItem} from '@/types/dashboard';
import {newId} from '@/utils/common';

import {CARD_COMPONENTS_MAP} from './cards';
import DashboardCard from './DashboardCard';
import {DashboardCardProps} from './withDashboardCard';

import 'react-grid-layout/css/styles.css';

const ResponsiveGridLayout = WidthProvider(Responsive);

const BREAKPOINTS = {lg: 1200, md: 996, sm: 768, xs: 480, xxs: 0};
const COLS = {lg: 12, md: 10, sm: 6, xs: 4, xxs: 2};

const cardTranslations: CardTranslations = {
  'active employees': {
    en: 'Active Employees',
    hu: 'Aktív alkalmazottak',
    ro: 'Angajați activi',
  },
  'employee count': {
    en: 'Employee Count',
    hu: 'Alkalmazottak száma',
    ro: 'Număr angajați',
  },
  'employee salary': {
    en: 'Employee Salary',
    hu: 'Alkalmazotti fizetés',
    ro: 'Salariu angajat',
  },
  'employee skills': {
    en: 'Employee Skills',
    hu: 'Alkalmazotti készségek',
    ro: 'Competențele angajaților',
  },
  'employee workload': {
    en: 'Employee Workload',
    hu: 'Alkalmazotti munkaterhelés',
    ro: 'Volumul de lucru al angajaților',
  },
  'paired devices': {
    en: 'Paired devices',
    hu: 'Párosított eszközök',
    ro: 'Dispozitive asociate',
  },
  'task completion': {
    en: 'Task Completion',
    hu: 'Feladat teljesítés',
    ro: 'Finalizarea sarcinilor',
  },
};

const Dashboard: FC = () => {
  const [layout, setLayout] = useAtom(dashboardLayoutAtom);
  const t = useTranslations();
  const ct = useCardTranslations(cardTranslations);
  const [currentBreakpoint, setCurrentBreakpoint] = useState<string>('lg');
  const {elementRef} = useHeight({dependencies: [layout, currentBreakpoint]});

  const handleBreakpointChange = useCallback((breakpoint: string) => {
    setCurrentBreakpoint(breakpoint);
  }, []);

  const getGridLayoutItems = useCallback((storedItems: StoredLayoutItem[]): ReactGridLayoutItem[] => {
    return storedItems.map((item) => {
      const cardType = item.type || item.i.split('__')[0];
      const cardInfo = CARD_COMPONENTS_MAP[cardType];
      if (!cardInfo) return item as unknown as ReactGridLayoutItem;

      const sizeCategory = item.size || cardInfo.config.defaultSize || CardSize.MEDIUM;
      let dimensions = cardInfo.config.sizes[sizeCategory];

      if (!dimensions) {
        const availableSizes = Object.keys(cardInfo.config.sizes) as CardSize[];
        if (availableSizes.length > 0) {
          dimensions = cardInfo.config.sizes[availableSizes[0]];
        }
      }

      if (!dimensions) dimensions = {h: 3, w: 2};

      return {
        ...dimensions,
        i: item.i,
        size: sizeCategory,
        type: cardType,
        x: item.x,
        y: item.y,
      };
    });
  }, []);

  const gridLayoutItems = useMemo(() => getGridLayoutItems(layout), [getGridLayoutItems, layout]);

  const handleLayoutChange = useCallback(
    (newLayout: ReactGridLayoutItem[]) => {
      const newStoredLayout: StoredLayoutItem[] = newLayout.map((item) => {
        const existingItem = layout.find((layoutItem) => layoutItem.i === item.i);
        return {
          i: item.i,
          size: existingItem?.size || CardSize.MEDIUM,
          type: item.type || existingItem?.type || item.i.split('__')[0],
          x: item.x,
          y: item.y,
        };
      });

      if (JSON.stringify(newStoredLayout) !== JSON.stringify(layout)) {
        setLayout(newStoredLayout);
      }
    },
    [layout, setLayout],
  );

  const findFirstAvailablePosition = useCallback(
    (cardSize: {h: number; w: number}) => {
      const cols = COLS[currentBreakpoint as keyof typeof COLS] || COLS.lg;

      if (gridLayoutItems.length === 0) {
        return {x: 0, y: 0};
      }

      let maxY = 0;
      gridLayoutItems.forEach((item) => {
        const itemBottom = item.y + item.h;
        if (itemBottom > maxY) {
          maxY = itemBottom;
        }
      });

      const grid: boolean[][] = [];

      for (let y = 0; y <= maxY; y++) {
        grid[y] = Array(cols).fill(false);
      }

      gridLayoutItems.forEach((item) => {
        for (let y = item.y; y < item.y + item.h; y++) {
          for (let x = item.x; x < item.x + item.w; x++) {
            if (y < grid.length && x < cols) {
              grid[y][x] = true;
            }
          }
        }
      });

      for (let y = 0; y <= maxY; y++) {
        for (let x = 0; x <= cols - cardSize.w; x++) {
          let canFit = true;

          for (let dy = 0; dy < cardSize.h; dy++) {
            for (let dx = 0; dx < cardSize.w; dx++) {
              const checkY = y + dy;
              const checkX = x + dx;

              if (checkY >= grid.length) {
                grid[checkY] = Array(cols).fill(false);
              }

              if (grid[checkY][checkX]) {
                canFit = false;
                break;
              }
            }
            if (!canFit) break;
          }

          if (canFit) {
            return {x, y};
          }
        }
      }

      return {x: 0, y: maxY + 1};
    },
    [currentBreakpoint, gridLayoutItems],
  );

  const addCard = useCallback(
    (cardType: string) => {
      const cardInfo = CARD_COMPONENTS_MAP[cardType];
      if (!cardInfo) return;

      const uniqueId = `${cardType}__${newId()}`;
      const sizeCategory = cardInfo.config.defaultSize || CardSize.MEDIUM;

      const cardSize = cardInfo.config.sizes[sizeCategory];
      if (!cardSize) {
        const availableSizes = Object.keys(cardInfo.config.sizes) as CardSize[];
        if (availableSizes.length === 0) return;

        const fallbackSize = availableSizes[0];
        const fallbackCardSize = cardInfo.config.sizes[fallbackSize];
        if (!fallbackCardSize) return;

        const position = findFirstAvailablePosition(fallbackCardSize);

        const newItem: StoredLayoutItem = {
          i: uniqueId,
          size: fallbackSize,
          type: cardType,
          x: position.x,
          y: position.y,
        };

        setLayout([...layout, newItem]);
        return;
      }

      const position = findFirstAvailablePosition(cardSize);

      const newItem: StoredLayoutItem = {
        i: uniqueId,
        size: sizeCategory,
        type: cardType,
        x: position.x,
        y: position.y,
      };

      setLayout([...layout, newItem]);
    },
    [findFirstAvailablePosition, layout, setLayout],
  );

  const reorganizeLayout = useCallback((newLayout: ReactGridLayoutItem[]) => {
    if (newLayout.length === 0) return newLayout;

    const sortedLayout = [...newLayout].sort((a, b) => {
      if (a.y !== b.y) return a.y - b.y;
      return a.x - b.x;
    });

    let maxY = 0;
    sortedLayout.forEach((item) => {
      const itemBottom = item.y + item.h;
      if (itemBottom > maxY) {
        maxY = itemBottom;
      }
    });

    const occupiedRows = new Set<number>();

    sortedLayout.forEach((item) => {
      for (let y = item.y; y < item.y + item.h; y++) {
        occupiedRows.add(y);
      }
    });

    const emptyRows: number[] = [];
    for (let y = 0; y < maxY; y++) {
      if (!occupiedRows.has(y)) {
        emptyRows.push(y);
      }
    }

    if (emptyRows.length === 0) {
      return sortedLayout;
    }

    emptyRows.sort((a, b) => a - b);

    return sortedLayout.map((item) => {
      let newItem = {...item};
      let rowsToMoveUp = 0;

      for (const emptyRow of emptyRows) {
        if (newItem.y > emptyRow) {
          rowsToMoveUp++;
        }
      }

      if (rowsToMoveUp > 0) {
        newItem.y -= rowsToMoveUp;
      }

      return newItem;
    });
  }, []);

  const removeCard = useCallback(
    (cardId: string) => {
      const newLayout = layout.filter((item) => item.i !== cardId);
      const gridItems = getGridLayoutItems(newLayout);
      const reorganizedGridItems = reorganizeLayout(gridItems);

      const reorganizedStoredItems: StoredLayoutItem[] = reorganizedGridItems.map((item) => ({
        i: item.i,
        size: item.size || CardSize.MEDIUM,
        type: item.type || item.i.split('__')[0],
        x: item.x,
        y: item.y,
      }));

      setLayout(reorganizedStoredItems);
    },
    [layout, reorganizeLayout, setLayout, getGridLayoutItems],
  );

  const handleCardResize = useCallback(
    (cardId: string, newSizeCategory: CardSize) => {
      const item = layout.find((item) => item.i === cardId);
      if (!item) return;

      const cardType = item.type || cardId.split('__')[0];
      const cardInfo = CARD_COMPONENTS_MAP[cardType];
      if (!cardInfo) return;

      const newSize = cardInfo.config.sizes[newSizeCategory];
      if (!newSize) {
        console.warn(`Size ${newSizeCategory} not available for card ${cardType}`);
        return;
      }

      const updatedLayout = layout.map((item) => {
        if (item.i === cardId) {
          return {
            ...item,
            size: newSizeCategory,
          };
        }
        return item;
      });

      setLayout(updatedLayout);
    },
    [layout, setLayout],
  );

  const visibleCardIds = useMemo(() => layout.map((item) => item.i), [layout]);

  const availableCardOptions = useMemo(() => Object.values(CARD_COMPONENTS_MAP), []);

  return (
    <Page>
      <PageTitle>{t('dashboard')}</PageTitle>
      <PageHeader>
        <PageHeaderTitle>{t('dashboard')}</PageHeaderTitle>
        <div className='grow' />
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button disabled={availableCardOptions.length === 0}>
              <PlusIcon /> {t('add widget')}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            {availableCardOptions.map((card) => (
              <DropdownMenuItem key={card.config.id} onSelect={() => addCard(card.config.id)}>
                {ct(card.config.title as any)}
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
      </PageHeader>
      <PageContent className='overflow-y-auto' ref={elementRef}>
        <ResponsiveGridLayout
          breakpoints={BREAKPOINTS}
          className='w-full'
          cols={COLS}
          compactType={null}
          draggableHandle='.react-draggable-handle'
          isDraggable
          isResizable={false}
          layouts={{
            lg: gridLayoutItems,
            md: gridLayoutItems,
            sm: gridLayoutItems,
            xs: gridLayoutItems,
            xxs: gridLayoutItems,
          }}
          onBreakpointChange={handleBreakpointChange}
          onLayoutChange={handleLayoutChange}
          preventCollision={true}
          rowHeight={80}
        >
          {visibleCardIds.map((cardId) => {
            const storedItem = layout.find((item) => item.i === cardId);
            if (!storedItem) return null;

            const cardType = storedItem.type || cardId.split('__')[0];
            const cardInfo = CARD_COMPONENTS_MAP[cardType];
            if (!cardInfo) return null;

            const {Component: CardComponent, config} = cardInfo;
            const TypedComponent = CardComponent as ComponentType<DashboardCardProps>;
            const layoutItem = gridLayoutItems.find((item) => item.i === cardId);
            if (!layoutItem) return null;

            const currentSizeCategory = storedItem.size || config.defaultSize || CardSize.MEDIUM;

            return (
              <div data-grid={layoutItem} key={cardId}>
                <DashboardCard
                  availableSizes={config.sizes}
                  currentSizeCategory={currentSizeCategory}
                  id={cardId}
                  onRemove={removeCard}
                  onResize={handleCardResize}
                  title={config.title || ''}
                >
                  <div className='h-full'>
                    <TypedComponent instanceId={cardId} />
                  </div>
                </DashboardCard>
              </div>
            );
          })}
        </ResponsiveGridLayout>
      </PageContent>
    </Page>
  );
};

export default Dashboard;
