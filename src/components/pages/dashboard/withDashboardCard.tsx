import {ComponentType, FC} from 'react';

import {CardSize, DashboardCardConfig} from '@/types/dashboard';

export type DashboardCardProps = {
  instanceId?: string;
};

export type WithDashboardConfig = {
  dashboardConfig: DashboardCardConfig;
};

const withDashboardCard = <P extends object>(
  Component: ComponentType<DashboardCardProps & P>,
  config: DashboardCardConfig,
): FC<DashboardCardProps & P> & WithDashboardConfig => {
  let defaultSize: CardSize;

  if (config.sizes.medium) {
    defaultSize = CardSize.MEDIUM;
  } else if (config.sizes.small) {
    defaultSize = CardSize.SMALL;
  } else if (config.sizes.large) {
    defaultSize = CardSize.LARGE;
  } else {
    defaultSize = Object.keys(config.sizes)[0] as CardSize;
  }

  const enhancedConfig: DashboardCardConfig = {
    ...config,
    defaultSize,
  };

  return Object.assign(
    (props: DashboardCardProps & P) => {
      return <Component {...props} />;
    },
    {dashboardConfig: enhancedConfig},
  );
};

export default withDashboardCard;
