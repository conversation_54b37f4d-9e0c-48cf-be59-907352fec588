import {FC} from 'react';

import {useAtomValue} from 'jotai';
import {useTranslations} from 'next-intl';
import {useFormContext} from 'react-hook-form';

import {Input, InputLabel} from '@/components/ui/Input';
import {WithLabel} from '@/components/ui/Label';
import {useHasPermission} from '@/hooks/helpers/useHasPermission';
import {defaultCurrencyAtom} from '@/store/defaults';
import {Employee} from '@/types/manufacturing';

const EmployeeDetailsData: FC = () => {
  const t = useTranslations();
  const {hasPermission, isLoading} = useHasPermission();
  const defaultCurrency = useAtomValue(defaultCurrencyAtom);
  const {
    formState: {errors},
    register,
    watch,
  } = useFormContext<Employee>();

  if (isLoading) return null;

  return (
    <div className='mx-6 mt-6 rounded-lg border border-border p-6'>
      <div className='flex flex-col gap-4'>
        <div className='text-base font-medium'>{t('employee details')}</div>
        <div className='grid grid-cols-6 gap-6'>
          <WithLabel>
            <Input {...register('position')} error={!!errors?.position} />
            <InputLabel>{t('position')}*</InputLabel>
          </WithLabel>
          {hasPermission('financial', 'employees') && (
            <WithLabel>
              <Input type='number' {...register('monthlyGrossSalary.amount')} error={!!errors?.monthlyGrossSalary} />
              <InputLabel>
                {t('monthly gross salary')} ({watch('monthlyGrossSalary.currency') || defaultCurrency})
              </InputLabel>
            </WithLabel>
          )}
        </div>
      </div>
    </div>
  );
};

export default EmployeeDetailsData;
