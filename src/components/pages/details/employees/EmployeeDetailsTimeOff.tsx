import {FC, useState} from 'react';

import {eachDayOfInterval, isWeekend} from 'date-fns';
import {CalendarDaysIcon, PlusIcon, XIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';
import {useFormContext} from 'react-hook-form';

import {Button, IconButton} from '@/components/ui/Button';
import {DatePicker} from '@/components/ui/DatePicker';
import useEmployeeActions from '@/hooks/useEmployeeActions';
import {Employee, EmployeeActiveTimeOffType} from '@/types/manufacturing';
import {formatDate} from '@/utils/format';

const EmployeeDetailsTimeOff: FC = () => {
  const {watch} = useFormContext<Employee>();
  const {addEmployeeLeave, deleteEmployeeLeave} = useEmployeeActions();
  const [addEnabled, setAddEnabled] = useState(false);
  const t = useTranslations();

  return (
    <div className='flex flex-col gap-2'>
      <div className='text-base font-medium'>{t('employee leaves')}</div>
      {watch('activeTimeoffs')
        ?.filter((leave) => leave.type !== EmployeeActiveTimeOffType.BUSINESS)
        ?.map((leave, index) => (
          <div className='flex items-center justify-between border p-2' key={`${leave.id}-${index}`}>
            <div className='flex items-center gap-2'>
              <CalendarDaysIcon className='mx-4 size-8' strokeWidth={1} />
              <div className='flex flex-col'>
                <div>
                  {formatDate(leave.startTime)} - {formatDate(leave.endTime)}
                </div>
                <div className='lowercase'>
                  {
                    eachDayOfInterval({end: leave.endTime, start: leave.startTime}).filter((day) => !isWeekend(day))
                      .length
                  }{' '}
                  {t('days')}
                </div>
              </div>
            </div>
            <Button onClick={() => deleteEmployeeLeave(leave.id, watch('id'))} variant='secondary'>
              <XIcon /> {t('delete')}
            </Button>
          </div>
        ))}
      {!addEnabled && (
        <IconButton className='self-start' icon={<PlusIcon />} onClick={() => setAddEnabled(true)}>
          {t('add leave')}
        </IconButton>
      )}
      {addEnabled && (
        <div className='flex items-center gap-4'>
          <DatePicker
            disablePast
            disableWeekends
            onChange={(date) => {
              if (date.to && date.from) {
                addEmployeeLeave(watch('id'), date.from, date.to);
                setAddEnabled(false);
              }
            }}
            open
            withRange
          />
          <Button onClick={() => setAddEnabled(false)} variant='secondary'>
            <XIcon />
            {t('cancel')}
          </Button>
        </div>
      )}
    </div>
  );
};

export default EmployeeDetailsTimeOff;
