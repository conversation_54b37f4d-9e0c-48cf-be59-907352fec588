import {FC, useState} from 'react';

import Jo<PERSON> from 'joi';
import {ArrowLeftIcon, EllipsisVerticalIcon, QrCodeIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';
import {FormProvider} from 'react-hook-form';

import ConnectMobileAppModal from '@/components/pages/lists/employees/EmployeeConnectMobileAppModal';
import {Button} from '@/components/ui/Button';
import {DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger} from '@/components/ui/DropdownMenu';
import {PopoverInput} from '@/components/ui/Input';
import {Page, PageContent, PageHeader, PageHeaderTitle, PageTitle} from '@/components/ui/special/Page';
import {useHasPermission} from '@/hooks/helpers/useHasPermission';
import {useRouter} from '@/hooks/helpers/useRouter';
import useEmployee from '@/hooks/useEmployee';

import EmployeeDetailsData from './EmployeeDetailsData';
import EmployeeDetailsDelegation from './EmployeeDetailsDelegation';
import EmployeeDetailsOperations from './EmployeeDetailsOperations';
import EmployeeDetailsTimeOff from './EmployeeDetailsTimeOff';

type Props = {
  id: string;
};

const EmployeeDetails: FC<Props> = ({id}) => {
  const t = useTranslations();
  const {
    deleteEmployee,
    isDirty,
    isLoading,
    saveEmployee,
    useFormActions: {
      formState: {errors, ...restUseFormState},
      register,
      resetField,
      setValue,
      watch,
      ...restUseFormActions
    },
  } = useEmployee(id);
  const {hasPermission} = useHasPermission();
  const [showCode, setShowCode] = useState(false);
  const {back} = useRouter();

  if (isLoading) return null;

  return (
    <>
      {showCode && <ConnectMobileAppModal onClose={() => setShowCode(false)} selectedEmployeeId={id} />}
      <Page>
        <PageTitle>{`${watch('name') || t('new employee')} - ${t('employees')}`}</PageTitle>
        <PageHeader>
          <PageHeaderTitle className='inline-flex items-center gap-2'>
            <Button onClick={() => back(`/employees`)} size='icon' variant='none'>
              <ArrowLeftIcon />
            </Button>
            <PopoverInput
              className='mr-2'
              defaultValue={watch('name')}
              error={!!errors?.name}
              label={t('name')}
              onChange={(value) => setValue('name', value)}
              validation={Joi.string().required()}
            >
              {watch('name') || t('new employee name')}
            </PopoverInput>
          </PageHeaderTitle>
          <div className='grow' />
          <Button onClick={() => setShowCode(true)} variant='secondary'>
            <QrCodeIcon className='size-5' /> {t('connect mobile app')}
          </Button>
          {isDirty && <Button onClick={() => saveEmployee()}>{t('save')}</Button>}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button className='px-1.5' variant='ghost'>
                <EllipsisVerticalIcon />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align='end'>
              <DropdownMenuItem onSelect={deleteEmployee}>{t('delete')}</DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </PageHeader>
        <PageContent>
          <FormProvider
            {...{
              formState: {errors, ...restUseFormState},
              register,
              resetField,
              setValue,
              watch,
              ...restUseFormActions,
            }}
          >
            <EmployeeDetailsData />
            <div className='grid grid-cols-3 gap-10 p-6'>
              {hasPermission('update', 'manufacturing') && <EmployeeDetailsOperations />}
              {id && <EmployeeDetailsTimeOff />}
              {id && hasPermission('read', 'manufacturing') && <EmployeeDetailsDelegation />}
            </div>
          </FormProvider>
        </PageContent>
      </Page>
    </>
  );
};

export default EmployeeDetails;
