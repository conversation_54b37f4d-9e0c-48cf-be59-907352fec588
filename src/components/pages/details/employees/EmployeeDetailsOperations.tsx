import {FC, useState} from 'react';

import {PlusIcon, StarIcon, XIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';
import {useFieldArray, useFormContext} from 'react-hook-form';

import {Button, IconButton} from '@/components/ui/Button';
import {Combobox} from '@/components/ui/Combobox';
import useOperationActions from '@/hooks/useOperationActions';
import useOperations from '@/hooks/useOperations';
import {Employee} from '@/types/manufacturing';
import {classes} from '@/utils/common';

const EmployeeDetailsOperations: FC = () => {
  const {control, watch} = useFormContext<Employee>();
  const {append, remove, update} = useFieldArray({control: control, name: 'manufacturingOperationTemplates'});
  const [addEnabled, setAddEnabled] = useState(false);
  const t = useTranslations();
  const {operations} = useOperations();
  const {createOperation} = useOperationActions();

  return (
    <div className='flex flex-col gap-2'>
      <div className='text-base font-medium'>{t('operations')}</div>
      {watch('manufacturingOperationTemplates')?.map((operation, index) => (
        <div className='flex items-center justify-between border-b pb-2' key={`${operation.id}-${index}`}>
          <div className='flex gap-4'>
            <Button
              className={classes(
                'hover:fill-button-primary-hovered',
                operation.preferential ? 'fill-button-primary' : 'fill-white',
              )}
              onClick={() => update(index, {...operation, preferential: !operation.preferential})}
              size='icon'
              variant='none'
            >
              <StarIcon className='size-5' fill='' strokeWidth={1.5} />
            </Button>
            <div>{operation.name}</div>
          </div>
          <Button onClick={() => remove(index)} variant='secondary'>
            <XIcon /> {t('delete')}
          </Button>
        </div>
      ))}
      {!addEnabled && (
        <IconButton className='self-start' icon={<PlusIcon />} onClick={() => setAddEnabled(true)}>
          {t('add operation')}
        </IconButton>
      )}
      {addEnabled && (
        <div className='flex items-center gap-4'>
          <Combobox
            className='w-[300px]'
            onChange={(value) => {
              append({id: value, name: operations.find((op) => op.id === value)?.name || '', preferential: false});
              setAddEnabled(false);
            }}
            open
            options={operations
              .filter((operation) => !watch('manufacturingOperationTemplates')?.some((op) => op.id === operation.id))
              .map((operation) => ({id: operation.id, value: operation.name}))}
            placeholder={t('operation')}
            renderNotFound={(query) => (
              <Button
                className='w-full justify-start'
                onClick={() => {
                  createOperation({name: query}).then((operation) => {
                    if (operation) append({...operation, preferential: false});
                  });
                  setAddEnabled(false);
                }}
                variant='secondary'
              >
                {t('create operation')}
              </Button>
            )}
            searchPlaceholder={t('search operation')}
          />
          <Button onClick={() => setAddEnabled(false)} variant='secondary'>
            <XIcon />
            {t('cancel')}
          </Button>
        </div>
      )}
    </div>
  );
};

export default EmployeeDetailsOperations;
