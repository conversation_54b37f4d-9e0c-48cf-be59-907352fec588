import {FC, useCallback, useState} from 'react';

import {eachDayOfInterval, isWeekend} from 'date-fns';
import {CalendarDaysIcon, PlusIcon, XIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';
import {useFormContext} from 'react-hook-form';

import {Button, IconButton} from '@/components/ui/Button';
import {Combobox} from '@/components/ui/Combobox';
import {DatePicker} from '@/components/ui/DatePicker';
import useEmployeeActions from '@/hooks/useEmployeeActions';
import useServicingOrders from '@/hooks/useServicingOrders';
import {DateRange} from '@/types/global';
import {Employee, EmployeeActiveTimeOffType} from '@/types/manufacturing';
import {formatDate} from '@/utils/format';

type ServicesComboboxProps = {
  onCancel: () => void;
  onChange: (date: DateRange, manufacturingOrderId: string) => void;
};

const ServicesCombobox: FC<ServicesComboboxProps> = ({onCancel, onChange}) => {
  const t = useTranslations();
  const {servicingOrders} = useServicingOrders();
  const [manufacturingOrderId, setManufacturingOrderId] = useState<string>();

  const handleCancel = useCallback(() => {
    onCancel();
    setManufacturingOrderId(undefined);
  }, [onCancel]);

  return (
    <>
      <div className='flex items-center gap-4'>
        <Combobox
          className='w-[300px]'
          onChange={(value) => {
            setManufacturingOrderId(value);
          }}
          open
          options={servicingOrders.map((service) => ({
            id: service.id,
            value: `${service.number} - ${service.customer?.name}`,
          }))}
          placeholder={t('services')}
          renderNotFound={() => t('service not found')}
          searchPlaceholder={t('search service')}
        />
        <Button onClick={handleCancel} variant='secondary'>
          <XIcon />
          {t('cancel')}
        </Button>
      </div>
      {manufacturingOrderId && (
        <div className='flex items-center gap-4'>
          <DatePicker
            disablePast
            disableWeekends
            onChange={(date) => {
              onChange(date, manufacturingOrderId);
            }}
            open
            withRange
          />
        </div>
      )}
    </>
  );
};

const EmployeeDetailsDelegation: FC = () => {
  const {watch} = useFormContext<Employee>();
  const {addEmployeeLeave, deleteEmployeeLeave} = useEmployeeActions();
  const [addEnabled, setAddEnabled] = useState(false);
  const t = useTranslations();

  return (
    <div className='flex flex-col gap-2'>
      <div className='text-base font-medium'>{t('employee delegations')}</div>
      {watch('activeTimeoffs')
        ?.filter((leave) => leave.type === EmployeeActiveTimeOffType.BUSINESS)
        ?.map((leave, index) => (
          <div className='flex items-center justify-between border p-2' key={`${leave.id}-${index}`}>
            <div className='flex items-center gap-2'>
              <CalendarDaysIcon className='mx-4 size-8' strokeWidth={1} />
              <div className='flex flex-col'>
                <div>
                  {formatDate(leave.startTime)} - {formatDate(leave.endTime)}
                </div>
                <div className='lowercase'>
                  {
                    eachDayOfInterval({end: leave.endTime, start: leave.startTime}).filter((day) => !isWeekend(day))
                      .length
                  }{' '}
                  {t('days')}
                </div>
                <div>
                  {leave.servicingOrder?.name} - {leave.customer?.name}
                </div>
              </div>
            </div>
            <Button
              onClick={() => deleteEmployeeLeave(leave.id, watch('id'), {type: EmployeeActiveTimeOffType.BUSINESS})}
              variant='secondary'
            >
              <XIcon /> {t('delete')}
            </Button>
          </div>
        ))}
      {!addEnabled && (
        <IconButton className='self-start' icon={<PlusIcon />} onClick={() => setAddEnabled(true)}>
          {t('add delegation')}
        </IconButton>
      )}
      {addEnabled && (
        <ServicesCombobox
          onCancel={() => setAddEnabled(false)}
          onChange={(date, manufacturingOrderId) => {
            if (date.to && date.from && manufacturingOrderId) {
              addEmployeeLeave(watch('id'), date.from, date.to, {servicingOrderId: manufacturingOrderId});
              setAddEnabled(false);
            }
          }}
        />
      )}
    </div>
  );
};

export default EmployeeDetailsDelegation;
