import {FC, useMemo} from 'react';

import {useAtomValue} from 'jotai';
import {sumBy} from 'lodash';
import {useTranslations} from 'next-intl';
import {useFormContext} from 'react-hook-form';

import {detailsDataShownAtom} from '@/components/pages/details/detailsStore';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableFooter,
  TableHead,
  TableHeadActions,
  TableHeader,
  TableRow,
} from '@/components/ui/Table';
import useHeight from '@/hooks/helpers/useHeight';
import useInventoryUnits from '@/hooks/useInventoryUnits';
import {defaultCurrencyAtom} from '@/store/defaults';
import {Reception} from '@/types/purchases';
import {formatNumber} from '@/utils/format';

import ReceptionCreateTableRow from './ReceptionCreateTableRow';

const ReceptionCreateTable: FC = () => {
  const {watch} = useFormContext<Reception>();
  const detailsDataShown = useAtomValue(detailsDataShownAtom(watch('id')));
  const defaultCurrency = useAtomValue(defaultCurrencyAtom);
  const {inventoryUnits, isLoading} = useInventoryUnits();
  const {elementRef} = useHeight({dependencies: [detailsDataShown]});
  const t = useTranslations();

  const items = watch('goods');
  const totalQuantity = useMemo(() => sumBy(items, (good) => good.receivedQuantity), [items]);
  const totalValue = useMemo(() => sumBy(items, (good) => good.price.amount * good.receivedQuantity), [items]);

  return (
    <TableContainer ref={elementRef}>
      <Table className='h-full'>
        <TableHeader>
          <TableRow>
            <TableHead>{t('name')}</TableHead>
            <TableHead>{t('inventory unit')}</TableHead>
            <TableHead>{t('unit of measure')}</TableHead>
            <TableHead className='text-right'>{t('document quantity')}</TableHead>
            <TableHead className='text-right'>{t('received quantity')}</TableHead>
            <TableHead className='text-right'>{t('purchase price')}</TableHead>
            <TableHead className='text-right'>{t('reception price')}</TableHead>
            <TableHead className='text-right'>{t('reception value')}</TableHead>
            {!watch('id') && <TableHeadActions />}
          </TableRow>
        </TableHeader>
        <TableBody className='overflow-y-hidden' isValidating={!watch() || isLoading}>
          {watch('goods')?.map((item, index) => (
            <ReceptionCreateTableRow
              additionalCosts={watch('additionalCosts')}
              index={index}
              inventoryUnits={inventoryUnits}
              item={item}
              key={`${item.materialGood.id}-${index}`}
              totalQuantity={totalQuantity}
              totalValue={totalValue}
            />
          ))}
        </TableBody>
        <TableFooter className='sticky bottom-0 h-[60px] shadow-[0_-4px_8px_0_rgba(0,0,0,0.07843137255)]'>
          <TableRow>
            <TableCell colSpan={7} />
            <TableCell className='text-right'>
              <div className='flex flex-col'>
                <div className='text-xs font-medium uppercase text-border-foreground'>{t('total value')}</div>
                <div className='font-medium'>
                  {formatNumber(totalValue + sumBy(watch('additionalCosts'), (cost) => cost.price?.amount || 0))}{' '}
                  {watch('currency') || defaultCurrency}
                </div>
              </div>
            </TableCell>
            {!watch('id') && <TableCell />}
          </TableRow>
        </TableFooter>
      </Table>
    </TableContainer>
  );
};

export default ReceptionCreateTable;
