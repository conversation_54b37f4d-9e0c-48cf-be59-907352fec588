import {FC, useMemo} from 'react';

import Joi from 'joi';
import {useAtomValue} from 'jotai';
import {Trash2Icon} from 'lucide-react';
import {useTranslations} from 'next-intl';
import {useFieldArray, useFormContext} from 'react-hook-form';

import {Badge} from '@/components/ui/Badge';
import {Button} from '@/components/ui/Button';
import {PopoverInput} from '@/components/ui/Input';
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from '@/components/ui/Select';
import InventoryItemLink from '@/components/ui/special/InventoryItemLink/InventoryItemLink';
import {TableActions, TableCell, TableRow} from '@/components/ui/Table';
import {defaultCurrencyAtom} from '@/store/defaults';
import {InventoryUnit} from '@/types/inventory';
import {
  Reception,
  ReceptionAdditionalCost,
  ReceptionAdditionalCostAllocationStrategy,
  ReceptionAdditionalCostType,
  ReceptionItem,
} from '@/types/purchases';
import {formatCurrency, formatNumber} from '@/utils/format';

type Props = {
  additionalCosts: ReceptionAdditionalCost[];
  index: number;
  inventoryUnits: InventoryUnit[];
  item: ReceptionItem;
  totalQuantity: number;
  totalValue: number;
};

const ReceptionCreateTableRow: FC<Props> = ({
  additionalCosts,
  index,
  inventoryUnits,
  item,
  totalQuantity,
  totalValue,
}) => {
  const t = useTranslations();
  const defaultCurrency = useAtomValue(defaultCurrencyAtom);
  const {
    control,
    formState: {errors},
    watch,
  } = useFormContext<Reception>();
  const {remove, update} = useFieldArray({control, name: 'goods'});

  const calculatedCost = useMemo(() => {
    let costs = 0;

    const transportation = additionalCosts.find((cost) => cost.type === ReceptionAdditionalCostType.TRANSPORTATION);

    switch (transportation?.allocationStrategy) {
      case ReceptionAdditionalCostAllocationStrategy.BY_QUANTITY:
        costs = ((item.receivedQuantity / totalQuantity) * transportation.price.amount) / item.receivedQuantity;
        break;
      case ReceptionAdditionalCostAllocationStrategy.BY_VALUE:
        costs =
          ((item.price.amount * item.receivedQuantity) / totalValue / item.receivedQuantity) *
          transportation.price.amount;
        break;
      case ReceptionAdditionalCostAllocationStrategy.CUSTOM:
        costs = (item.additionalCostCustomValue?.amount || 0) / item.receivedQuantity;
        break;
    }

    return costs;
  }, [
    additionalCosts,
    item.additionalCostCustomValue?.amount,
    item.price.amount,
    item.receivedQuantity,
    totalQuantity,
    totalValue,
  ]);

  return (
    <TableRow className='h-[61px]'>
      <TableCell>
        <InventoryItemLink item={item.materialGood} />
      </TableCell>
      <TableCell>
        {watch('id') && <Badge>{item.inventoryUnit?.name}</Badge>}
        {!watch('id') && (
          <Select
            onValueChange={async (value) => {
              const unit = inventoryUnits.find((unit) => unit.id === value);
              if (unit) update(index, {...item, inventoryUnit: unit});
            }}
            value={item.inventoryUnit?.id}
          >
            <SelectTrigger className='w-fit' size='badge-md' variant='badge-default'>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {inventoryUnits.map((type) => (
                <SelectItem key={type.id} value={type.id}>
                  {type.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        )}
      </TableCell>
      <TableCell>{t(`unit.name.${item.measurementUnit?.name?.toLowerCase() || 'pcs'}` as any)}</TableCell>
      <TableCell className='text-right'>
        {watch('id') && (
          <>{`${formatNumber(item.orderedQuantity)} ${t(`unit.name.${item.measurementUnit?.name?.toLowerCase() || 'pcs'}` as any)}`}</>
        )}
        {!watch('id') && (
          <PopoverInput
            className='w-fit justify-end'
            defaultValue={item.orderedQuantity.toString()}
            label={t('document quantity')}
            onChange={(value) => {
              update(index, {
                ...item,
                orderedQuantity: Number(value),
              });
            }}
            validation={Joi.number().positive().required()}
          >
            {`${formatNumber(item.orderedQuantity)} ${t(`unit.name.${item.measurementUnit?.name?.toLowerCase() || 'pcs'}` as any)}`}
          </PopoverInput>
        )}
      </TableCell>
      <TableCell className='text-right'>
        {watch('id') && (
          <>{`${formatNumber(item.receivedQuantity)} ${t(`unit.name.${item.measurementUnit?.name?.toLowerCase() || 'pcs'}` as any)}`}</>
        )}
        {!watch('id') && (
          <PopoverInput
            className='w-fit justify-end'
            defaultValue={item.receivedQuantity.toString()}
            label={t('received quantity')}
            onChange={(value) => {
              update(index, {
                ...item,
                receivedQuantity: Number(value),
              });
            }}
            validation={Joi.number().positive().required()}
          >
            {`${formatNumber(item.receivedQuantity)} ${t(`unit.name.${item.measurementUnit?.name?.toLowerCase() || 'pcs'}` as any)}`}
          </PopoverInput>
        )}
      </TableCell>
      <TableCell className='text-right'>
        {watch('id') && <>{formatCurrency(item.price)}</>}
        {!watch('id') && (
          <PopoverInput
            className='w-fit justify-end'
            defaultValue={(item.price?.amount || 0).toString()}
            error={!!errors.goods?.[index]?.price?.amount}
            label={t('unit price')}
            onChange={(value) => {
              update(index, {
                ...item,
                price: {amount: Number(value), currency: item.price?.currency || defaultCurrency},
              });
            }}
            validation={Joi.number().positive().required()}
          >
            {formatCurrency(item.price)}
          </PopoverInput>
        )}
      </TableCell>
      <TableCell className='text-right'>
        {formatCurrency({
          amount: (item.price?.amount || 0) + calculatedCost,
          currency: defaultCurrency,
        })}
      </TableCell>
      <TableCell className='text-right'>
        {formatCurrency({
          amount: ((item.price?.amount || 0) + calculatedCost) * item.receivedQuantity || 0,
          currency: defaultCurrency,
        })}
      </TableCell>
      {!watch('id') && (
        <TableActions>
          <Button onClick={() => remove(index)} size='icon' variant='none'>
            <Trash2Icon className='size-5 text-red' strokeWidth={1} />
          </Button>
        </TableActions>
      )}
    </TableRow>
  );
};

export default ReceptionCreateTableRow;
