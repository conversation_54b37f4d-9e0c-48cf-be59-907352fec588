import {FC, useCallback, useRef, useState} from 'react';

import {first} from 'lodash';
import {ArrowLeftIcon, CloudDownloadIcon, FileUpIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';
import {FormProvider} from 'react-hook-form';

import {detailsDataShownAtom} from '@/components/pages/details/detailsStore';
import {Button} from '@/components/ui/Button';
import {HideableContentToggle} from '@/components/ui/special/HideableContentToggle';
import {Link} from '@/components/ui/special/Link';
import {Page, PageContent, PageHeader, PageHeaderTitle, PageTitle} from '@/components/ui/special/Page';
import {Tabs, TabsList, TabsTrigger} from '@/components/ui/Tabs';
import {useRouter} from '@/hooks/helpers/useRouter';
import useReception from '@/hooks/useReception';
import useReceptionActions from '@/hooks/useReceptionActions';

import ReceptionCreateTransport from './expenses/ReceptionCreateTransport';
import ReceptionCreateButton from './ReceptionCreateButton';
import ReceptionCreateData from './ReceptionCreateData';
import ReceptionCreateFilesButtons from './ReceptionCreateFiles/ReceptionCreateFilesButtons';
import ReceptionCreateFiles from './ReceptionCreateFiles/ReceptionCreateFilesTable';
import ReceptionCreateTable from './ReceptionCreateTable';

type Props = {
  id?: string;
};

type ReceptionTab = 'files' | 'items' | 'transport';

const ReceptionCreate: FC<Props> = ({id}) => {
  const t = useTranslations();
  const [tab, setTab] = useState<ReceptionTab>('items');
  const importButton = useRef<HTMLInputElement>(null);
  const {importWinMENTORReception} = useReceptionActions();
  const [files, setFiles] = useState<File[]>([]);
  const {
    resetToDefault,
    saveReception,
    uploadReceptionFile,
    useFormActions: {
      formState: {dirtyFields, errors, ...restUseFormState},
      register,
      reset,
      resetField,
      watch,
      ...restUseFormActions
    },
  } = useReception(id);
  const {back} = useRouter();

  const handleImport = useCallback(
    (file?: File) => {
      if (!file) return;
      resetToDefault();
      importWinMENTORReception(file).then((value) => reset(value));
    },
    [importWinMENTORReception, reset, resetToDefault],
  );

  return (
    <Page>
      <PageTitle>{`${watch('number') || t('reception')} - ${t('reception')} - ${t('receptions')}`}</PageTitle>
      <PageHeader>
        <PageHeaderTitle className='inline-flex items-center gap-2'>
          <Button onClick={() => back('/inventory/receptions')} size='icon' variant='none'>
            <ArrowLeftIcon />
          </Button>
          {watch('number') || t('reception')}
        </PageHeaderTitle>
        <HideableContentToggle store={detailsDataShownAtom(watch('id'))} />
        <div className='grow' />
        {!id && (
          <>
            <div className='flex items-center gap-4'>
              <Button
                onClick={() => {
                  let proceed = true; // Default to true

                  if (Object.keys(dirtyFields).length > 0)
                    proceed = window.confirm(
                      `${t('information you’ve entered will be lost')}\n${t('please confirm that you want to import')}`,
                    );

                  if (proceed) importButton.current?.click();
                }}
                variant='secondary'
              >
                <FileUpIcon /> {t('import')}
              </Button>
              <input
                accept='.xls,.xlsx'
                className='hidden'
                onChange={({target: {files}}) => handleImport(first(files))}
                ref={importButton}
                type='file'
              />
            </div>
            <Button onClick={() => saveReception(files)}>{t('add reception')}</Button>
          </>
        )}
        {id && (
          <Button asChild variant='secondary'>
            <Link href={`/api/inventory/receptions/${id}/details?mediaType=application/pdf`}>
              <CloudDownloadIcon />
              {t('download')}
            </Link>
          </Button>
        )}
      </PageHeader>
      <PageContent>
        <FormProvider
          {...{
            formState: {dirtyFields, errors, ...restUseFormState},
            register,
            reset,
            resetField,
            watch,
            ...restUseFormActions,
          }}
        >
          <ReceptionCreateData />
          <div className='mx-6 flex items-center justify-between'>
            <div className='inline-flex items-center'>
              <Tabs onValueChange={(value) => setTab(value as ReceptionTab)} value={tab} variant='menu'>
                <TabsList variant='menu'>
                  <TabsTrigger
                    badge={watch('goods')?.length || 0}
                    error={!!errors.goods}
                    value={'items'}
                    variant='menu'
                  >
                    {t('items')}
                  </TabsTrigger>
                  <TabsTrigger
                    badge={watch('additionalCosts')?.length || 0}
                    error={!!errors.additionalCosts || !!(errors as any)?.['']}
                    value={'transport'}
                    variant='menu'
                  >
                    {t('transport')}
                  </TabsTrigger>
                  <TabsTrigger
                    badge={watch('id') ? watch('files')?.length || 0 : files.length}
                    value={'files'}
                    variant='menu'
                  >
                    {t('files')}
                  </TabsTrigger>
                </TabsList>
              </Tabs>
            </div>
            <div>
              {tab === 'items' && !id && <ReceptionCreateButton />}
              {tab === 'files' && <ReceptionCreateFilesButtons setFiles={setFiles} uploadFile={uploadReceptionFile} />}
            </div>
          </div>
          {tab === 'items' && <ReceptionCreateTable />}
          {tab === 'transport' && <ReceptionCreateTransport />}
          {tab === 'files' && <ReceptionCreateFiles files={files} setFiles={setFiles} />}
        </FormProvider>
      </PageContent>
    </Page>
  );
};

export default ReceptionCreate;
