import {FC, useCallback, useState} from 'react';

import {useAtomValue} from 'jotai';
import {PlusIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';
import {useFieldArray, UseFieldArrayAppend, useFormContext} from 'react-hook-form';

import {Button} from '@/components/ui/Button';
import AddItemModal from '@/components/ui/special/AddItemModal/AddItemModal';
import useItemActions from '@/hooks/useItemActions';
import useSettings from '@/hooks/useSettings';
import {defaultCurrencyAtom} from '@/store/defaults';
import {Reception} from '@/types/purchases';
import {classes} from '@/utils/common';

type AddItemProps = {
  excludeIds: string[];
  onClose: () => void;
  onSelect: UseFieldArrayAppend<Reception, 'goods'>;
};

const AddItem: FC<AddItemProps> = ({excludeIds, onClose, onSelect}) => {
  const defaultCurrency = useAtomValue(defaultCurrencyAtom);
  const {settings} = useSettings();
  const {getItem} = useItemActions();

  const handleAdd = useCallback(
    (values: {id: string; quantity: number}[]) => {
      values.forEach(async ({id, quantity}) => {
        const item = await getItem(id);

        if (!item) return;
        onSelect({
          additionalCostCustomValue: {amount: 0, currency: item.inventoryCostPerItem?.currency || defaultCurrency},
          calculatedAdditionalCost: {amount: 0, currency: item.inventoryCostPerItem?.currency || defaultCurrency},
          inventoryUnit: {
            id: settings.general.inventoryAccountingSettings.unitDesignations?.defaultInventoryUnit,
            name: '',
          },
          materialGood: {code: item.code, id: item.id, name: item.name},
          measurementUnit: item.measurementUnit,
          orderedQuantity: 0,
          price: {
            amount: item.inventoryCostPerItem?.amount || 0,
            currency: item.inventoryCostPerItem?.currency || defaultCurrency,
          },
          receivedQuantity: quantity,
          totalValue: {amount: 0, currency: item.inventoryCostPerItem?.currency || defaultCurrency},
          vat: 0,
        });
      });

      onClose();
    },
    [
      defaultCurrency,
      getItem,
      onClose,
      onSelect,
      settings?.general?.inventoryAccountingSettings?.unitDesignations?.defaultInventoryUnit,
    ],
  );

  return <AddItemModal create='none' excludeIds={excludeIds} onAdd={handleAdd} onClose={onClose} />;
};

type Props = {
  className?: string;
};

const ReceptionCreateButton: FC<Props> = ({className}) => {
  const t = useTranslations();
  const [addEnabled, setAddEnabled] = useState(false);
  const {control, watch} = useFormContext<Reception>();
  const {append} = useFieldArray({control, name: 'goods'});

  return (
    <div className={classes('flex items-center gap-4', className)}>
      {!addEnabled && (
        <Button onClick={() => setAddEnabled(true)} variant='secondary'>
          <PlusIcon /> {t('add item')}
        </Button>
      )}
      {addEnabled && (
        <AddItem
          excludeIds={watch('goods')?.map((item) => item.materialGood.id) ?? []}
          onClose={() => setAddEnabled(false)}
          onSelect={append}
        />
      )}
    </div>
  );
};

export default ReceptionCreateButton;
