import {FC} from 'react';

import {ArrowLeftIcon, CloudDownloadIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';

import {Button} from '@/components/ui/Button';
import {DocumentPreview} from '@/components/ui/special/DocumentPreview';
import {Link} from '@/components/ui/special/Link';
import {Page, PageContent, PageHeader, PageHeaderTitle, PageTitle} from '@/components/ui/special/Page';
import useReceptionDocument from '@/hooks/documents/useReceptionDocument';
import useHeight from '@/hooks/helpers/useHeight';
import {useRouter} from '@/hooks/helpers/useRouter';

type Props = {
  id: string;
};

const ReceptionPreview: FC<Props> = ({id}) => {
  const {elementRef} = useHeight();
  const {receptionDocument} = useReceptionDocument(id);
  const {back} = useRouter();
  const t = useTranslations();

  return (
    <Page>
      <PageTitle>{`${t('reception')} - ${t('receptions')}`}</PageTitle>
      <PageHeader>
        <PageHeaderTitle className='inline-flex items-center gap-2'>
          <Button onClick={() => back('/inventory/receptions')} size='icon' variant='none'>
            <ArrowLeftIcon />
          </Button>
          {t('reception')}
        </PageHeaderTitle>
        <div className='grow' />
        <Button asChild variant='secondary'>
          <Link href={`/api/inventory/receptions/${id}/details?mediaType=application/pdf`}>
            <CloudDownloadIcon />
            {t('download')}
          </Link>
        </Button>
      </PageHeader>
      <PageContent>
        <div className='flex' ref={elementRef}>
          <div className='flex size-full justify-center bg-gray-100 p-4'>
            <DocumentPreview content={receptionDocument} />
          </div>
        </div>
      </PageContent>
    </Page>
  );
};

export default ReceptionPreview;
