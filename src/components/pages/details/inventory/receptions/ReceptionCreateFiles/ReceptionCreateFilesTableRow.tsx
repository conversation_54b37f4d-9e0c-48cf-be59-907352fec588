import {FC} from 'react';

import {CloudDownloadIcon, Trash2Icon} from 'lucide-react';
import {useFieldArray, useFormContext} from 'react-hook-form';

import {Button} from '@/components/ui/Button';
import {Link} from '@/components/ui/special/Link';
import {TableActions, TableCell, TableRow} from '@/components/ui/Table';
import {useRouter} from '@/hooks/helpers/useRouter';
import {CustomFile} from '@/types/global';
import {Reception} from '@/types/purchases';

type Props = {
  file: CustomFile;
  index: number;
};

const ReceptionCreateFilesTableRow: FC<Props> = ({file, index}) => {
  const {push} = useRouter();
  const {control, watch} = useFormContext<Reception>();
  const {remove} = useFieldArray({control, name: 'files'});

  return (
    <TableRow>
      <TableCell>
        <Link href={`/api${file.previewLink}`}>{file.name}</Link>
      </TableCell>
      <TableCell className='w-full'>
        <Button
          onClick={() => {
            push(`/api${file.downloadLink}`);
          }}
          size='icon'
          variant='none'
        >
          <CloudDownloadIcon />
        </Button>
      </TableCell>
      {!watch('id') && (
        <TableActions>
          <Button onClick={() => remove(index)} size='icon' variant='none'>
            <Trash2Icon className='size-5 text-red' strokeWidth={1} />
          </Button>
        </TableActions>
      )}
    </TableRow>
  );
};

export default ReceptionCreateFilesTableRow;
