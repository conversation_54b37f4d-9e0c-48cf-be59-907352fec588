import {Dispatch, FC, useRef} from 'react';

import {SetStateAction} from 'jotai';
import {first} from 'lodash';
import {PlusIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';
import {useFormContext} from 'react-hook-form';

import {Button} from '@/components/ui/Button';
import {Reception} from '@/types/purchases';
import {classes} from '@/utils/common';

type Props = {
  className?: string;
  setFiles: Dispatch<SetStateAction<File[]>>;
  uploadFile?: (file?: File) => void;
};

const ReceptionCreateFilesButtons: FC<Props> = ({className, setFiles, uploadFile}) => {
  const uploadBtn = useRef<HTMLInputElement>(null);
  const t = useTranslations();
  const {watch} = useFormContext<Reception>();

  return (
    <div className={classes('flex items-center gap-4', className)}>
      <Button onClick={() => uploadBtn.current?.click()} variant='secondary'>
        <PlusIcon /> {t('add file')}
      </Button>
      <input
        className='hidden'
        onChange={({target: {files}}) => {
          if (files) {
            if (watch('id') && uploadFile) {
              uploadFile(first(files));
            } else {
              setFiles((prev) => [...prev, ...files]);
            }
          }
        }}
        ref={uploadBtn}
        type='file'
      />
    </div>
  );
};

export default ReceptionCreateFilesButtons;
