import {Dispatch, FC} from 'react';

import {SetStateAction} from 'jotai';
import {Trash2Icon} from 'lucide-react';

import {Button} from '@/components/ui/Button';
import {TableActions, TableCell, TableRow} from '@/components/ui/Table';

type Props = {
  file: File;
  index: number;
  setFiles: Dispatch<SetStateAction<File[]>>;
};

const ReceptionCreateTempFilesTableRow: FC<Props> = ({file, index, setFiles}) => {
  return (
    <TableRow>
      <TableCell>{file.name}</TableCell>
      <TableCell />
      <TableActions>
        <Button onClick={() => setFiles((prev) => prev.filter((_, i) => i !== index))} size='icon' variant='none'>
          <Trash2Icon className='size-5 text-red' strokeWidth={1} />
        </Button>
      </TableActions>
    </TableRow>
  );
};

export default ReceptionCreateTempFilesTableRow;
