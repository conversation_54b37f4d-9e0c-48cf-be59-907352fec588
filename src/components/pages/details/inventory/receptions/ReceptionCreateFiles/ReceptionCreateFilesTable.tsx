import {Dispatch, FC} from 'react';

import {useAtomValue} from 'jotai';
import {SetStateAction} from 'jotai';
import {useTranslations} from 'next-intl';
import {useFormContext} from 'react-hook-form';

import {detailsDataShownAtom} from '@/components/pages/details/detailsStore';
import ReceptionCreateTempFilesTableRow from '@/components/pages/details/inventory/receptions/ReceptionCreateFiles/ReceptionCreateTempFilesTableRow';
import {
  Table,
  TableBody,
  TableContainer,
  TableHead,
  TableHeadActions,
  TableHeader,
  TableRow,
} from '@/components/ui/Table';
import useHeight from '@/hooks/helpers/useHeight';
import {Reception} from '@/types/purchases';

import ReceptionCreateFilesTableRow from './ReceptionCreateFilesTableRow';

type Props = {
  files: File[];
  setFiles: Dispatch<SetStateAction<File[]>>;
};

const ReceptionCreateFilesTable: FC<Props> = ({files, setFiles}) => {
  const {watch} = useFormContext<Reception>();
  const detailsDataShown = useAtomValue(detailsDataShownAtom(watch('id')));
  const {elementRef} = useHeight({dependencies: [detailsDataShown]});
  const t = useTranslations();

  return (
    <TableContainer ref={elementRef}>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>{t('name')}</TableHead>
            <TableHead />
            {!watch('id') && <TableHeadActions />}
          </TableRow>
        </TableHeader>
        <TableBody className='overflow-y-hidden' isValidating={!watch()}>
          {watch('id') &&
            watch('files')?.map((file, index) => (
              <ReceptionCreateFilesTableRow file={file} index={index} key={`${file.id}-${index}`} />
            ))}
          {!watch('id') &&
            files?.map((file, index) => (
              <ReceptionCreateTempFilesTableRow file={file} index={index} key={`file-${index}`} setFiles={setFiles} />
            ))}
        </TableBody>
      </Table>
    </TableContainer>
  );
};

export default ReceptionCreateFilesTable;
