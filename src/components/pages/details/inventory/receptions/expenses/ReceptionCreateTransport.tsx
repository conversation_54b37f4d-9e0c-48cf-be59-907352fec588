import {FC, useMemo} from 'react';

import {useAtomValue} from 'jotai';
import {useFormContext} from 'react-hook-form';

import {defaultCurrencyAtom} from '@/store/defaults';
import {Reception} from '@/types/purchases';

import ReceptionCreateTransportData from './ReceptionCreateTransportData';
import ReceptionCreateTransportTable from './ReceptionCreateTransportTable';

const ReceptionCreateTransport: FC = () => {
  const {watch} = useFormContext<Reception>();
  const defaultCurrency = useAtomValue(defaultCurrencyAtom);

  const costs = watch('additionalCosts');
  const items = watch('goods');
  const index = useMemo(() => costs.findIndex((cost) => cost.type === 'TRANSPORTATION'), [costs]);

  return (
    <>
      <ReceptionCreateTransportData
        currency={watch('currency') || defaultCurrency}
        index={index}
        transportation={costs[index]}
      />
      <ReceptionCreateTransportTable
        currency={watch('currency') || defaultCurrency}
        items={items}
        transportation={costs[index]}
      />
    </>
  );
};

export default ReceptionCreateTransport;
