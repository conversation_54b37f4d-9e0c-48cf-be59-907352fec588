import {FC, useState} from 'react';

import {useTranslations} from 'next-intl';
import {useFieldArray, useFormContext} from 'react-hook-form';

import {Input, InputLabel} from '@/components/ui/Input';
import {WithLabel} from '@/components/ui/Label';
import {TextareaAutosize} from '@/components/ui/Textarea';
import {ToggleGroup, ToggleGroupItem} from '@/components/ui/ToggleGroup';
import {
  Reception,
  ReceptionAdditionalCost,
  ReceptionAdditionalCostAllocationStrategy,
  ReceptionAdditionalCostType,
} from '@/types/purchases';

type Props = {
  currency: string;
  index: number;
  transportation: ReceptionAdditionalCost | undefined;
};

const ReceptionCreateTransportData: FC<Props> = ({currency, index, transportation}) => {
  const t = useTranslations();
  const {control, watch} = useFormContext<Reception>();
  const {append, remove, update} = useFieldArray({control, name: 'additionalCosts'});
  const [inputValue, setInputValue] = useState((transportation?.price?.amount || 0).toString());

  const updatePrice = (value: string) => {
    const amount = Number(value.replace(',', '.'));
    if (amount === 0) {
      remove(index);
    } else {
      if (index === -1) {
        append({
          allocationStrategy: ReceptionAdditionalCostAllocationStrategy.BY_VALUE,
          code: '',
          description: '',
          price: {amount, currency},
          quantity: 1,
          type: ReceptionAdditionalCostType.TRANSPORTATION,
          vat: 0,
        });
      } else {
        update(index, {
          ...(transportation as ReceptionAdditionalCost),
          price: {
            amount,
            currency,
          },
        });
      }
    }

    setInputValue(amount.toString());
  };

  return (
    <div className='mx-6 mb-6 flex flex-col gap-4 rounded-lg border border-border p-6'>
      <div className='flex flex-wrap items-center gap-4'>
        <WithLabel>
          <Input
            disabled={!!watch('id')}
            onBlur={({target: {value}}) => updatePrice(value)}
            onChange={({target: {value}}) => setInputValue(value.replace(',', '.'))}
            onKeyDown={({currentTarget: {value}, key}) => {
              if (key === 'Enter') updatePrice(value);
            }}
            value={inputValue}
          />
          <InputLabel className='mb-2 mt-1'>
            {t('price')} ({transportation?.price?.currency || currency})
          </InputLabel>
        </WithLabel>
        <WithLabel>
          <ToggleGroup
            disabled={index === -1 || !!watch('id')}
            onValueChange={(value) =>
              update(index, {
                ...(transportation as ReceptionAdditionalCost),
                allocationStrategy: value as ReceptionAdditionalCostAllocationStrategy,
              })
            }
            type='single'
            value={transportation?.allocationStrategy || ReceptionAdditionalCostAllocationStrategy.BY_VALUE}
          >
            <ToggleGroupItem value={ReceptionAdditionalCostAllocationStrategy.BY_VALUE}>
              {t(ReceptionAdditionalCostAllocationStrategy.BY_VALUE)}
            </ToggleGroupItem>
            <ToggleGroupItem value={ReceptionAdditionalCostAllocationStrategy.BY_QUANTITY}>
              {t(ReceptionAdditionalCostAllocationStrategy.BY_QUANTITY)}
            </ToggleGroupItem>
            <ToggleGroupItem value={ReceptionAdditionalCostAllocationStrategy.CUSTOM}>
              {t(ReceptionAdditionalCostAllocationStrategy.CUSTOM)}
            </ToggleGroupItem>
          </ToggleGroup>
          <InputLabel className='mb-3'>{t('stock value calculation based on')}</InputLabel>
        </WithLabel>

        <WithLabel>
          <Input
            disabled={index === -1 || !!watch('id')}
            onChange={({target: {value}}) =>
              update(index, {...(transportation as ReceptionAdditionalCost), code: value})
            }
            value={transportation?.code || ''}
          />
          <InputLabel className='mb-2 mt-1'>{t('code')}</InputLabel>
        </WithLabel>
      </div>
      <WithLabel>
        <TextareaAutosize
          disabled={index === -1 || !!watch('id')}
          maxRows={3}
          minRows={1}
          onChange={({target: {value}}) =>
            update(index, {...(transportation as ReceptionAdditionalCost), description: value})
          }
          value={transportation?.description || ''}
        />
        <InputLabel className='mb-2 mt-1'>{t('description')}</InputLabel>
      </WithLabel>
    </div>
  );
};

export default ReceptionCreateTransportData;
