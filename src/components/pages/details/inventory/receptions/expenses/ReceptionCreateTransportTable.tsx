import {FC, useMemo} from 'react';

import {useAtomValue} from 'jotai';
import {sumBy} from 'lodash';
import {useTranslations} from 'next-intl';
import {useFormContext} from 'react-hook-form';

import {detailsDataShownAtom} from '@/components/pages/details/detailsStore';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableFooter,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/Table';
import useHeight from '@/hooks/helpers/useHeight';
import {
  Reception,
  ReceptionAdditionalCost,
  ReceptionAdditionalCostAllocationStrategy,
  ReceptionItem,
} from '@/types/purchases';
import {classes} from '@/utils/common';
import {formatNumber} from '@/utils/format';

import ReceptionCreateTransportTableRow from './ReceptionCreateTransportTableRow';

type Props = {
  currency: string;
  items: ReceptionItem[];
  transportation: ReceptionAdditionalCost | undefined;
};

const ReceptionCreateTransportTable: FC<Props> = ({currency, items, transportation}) => {
  const {
    formState: {errors},
    watch,
  } = useFormContext<Reception>();
  const detailsDataShown = useAtomValue(detailsDataShownAtom(watch('id')));
  const {elementRef} = useHeight({dependencies: [detailsDataShown]});
  const t = useTranslations();

  const totalQuantity = useMemo(() => sumBy(items, (good) => good.receivedQuantity), [items]);
  const totalValue = useMemo(() => sumBy(items, (good) => good.price.amount * good.receivedQuantity), [items]);

  return (
    <TableContainer ref={elementRef}>
      <Table className={classes(!!transportation?.allocationStrategy && 'h-full')}>
        <TableHeader>
          <TableRow>
            <TableHead>{t('name')}</TableHead>
            <TableHead className='text-right'>{t('percent distribution')}</TableHead>
            <TableHead className='text-right'>{t('unit value distribution')}</TableHead>
            <TableHead className='text-right'>{t('value distribution')}</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody className='overflow-y-hidden' isValidating={false}>
          {items.map((item, index) => (
            <ReceptionCreateTransportTableRow
              currency={currency}
              index={index}
              item={item}
              key={`${item.materialGood.id}-${index}`}
              strategy={transportation?.allocationStrategy}
              totalQuantity={totalQuantity}
              totalValue={totalValue}
              transportPrice={transportation?.price?.amount || 0}
            />
          ))}
        </TableBody>
        {!!transportation?.allocationStrategy && (
          <TableFooter className='sticky bottom-0 h-[60px] shadow-[0_-4px_8px_0_rgba(0,0,0,0.07843137255)]'>
            <TableRow>
              <TableCell />
              <TableCell className='text-right'>
                <div
                  className={classes(
                    'flex flex-col',
                    (!!errors.additionalCosts || !!(errors as any)?.['']) && 'text-red',
                  )}
                >
                  <div
                    className={classes(
                      'text-xs font-medium uppercase',
                      !!errors.additionalCosts || !!(errors as any)?.[''] ? 'text-red' : 'text-border-foreground',
                    )}
                  >
                    {t('total percent distribution')}
                  </div>
                  <div className='font-medium'>
                    {formatNumber(
                      transportation?.allocationStrategy === ReceptionAdditionalCostAllocationStrategy.CUSTOM
                        ? sumBy(
                            items,
                            (item) =>
                              ((item?.additionalCostCustomValue?.amount || 0) / (transportation?.price?.amount || 0)) *
                              100,
                          )
                        : 100,
                    )}{' '}
                    %
                  </div>
                </div>
              </TableCell>
              <TableCell />
              <TableCell className='text-right'>
                <div
                  className={classes(
                    'flex flex-col',
                    (!!errors.additionalCosts || !!(errors as any)?.['']) && 'text-red',
                  )}
                >
                  <div
                    className={classes(
                      'text-xs font-medium uppercase',
                      !!errors.additionalCosts || !!(errors as any)?.[''] ? 'text-red' : 'text-border-foreground',
                    )}
                  >
                    {t('total value distribution')}
                  </div>
                  <div className='font-medium'>
                    {formatNumber(
                      transportation?.allocationStrategy === ReceptionAdditionalCostAllocationStrategy.CUSTOM
                        ? sumBy(items, (item) => item?.additionalCostCustomValue?.amount || 0)
                        : transportation?.price?.amount,
                    )}{' '}
                    {currency}
                  </div>
                </div>
              </TableCell>
            </TableRow>
          </TableFooter>
        )}
      </Table>
    </TableContainer>
  );
};

export default ReceptionCreateTransportTable;
