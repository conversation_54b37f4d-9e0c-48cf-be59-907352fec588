import {FC} from 'react';

import Jo<PERSON> from 'joi';
import {useTranslations} from 'next-intl';
import {useFieldArray, useFormContext} from 'react-hook-form';

import {PopoverInput} from '@/components/ui/Input';
import InventoryItemLink from '@/components/ui/special/InventoryItemLink/InventoryItemLink';
import {TableCell, TableRow} from '@/components/ui/Table';
import {Reception, ReceptionAdditionalCostAllocationStrategy, ReceptionItem} from '@/types/purchases';
import {formatCurrency, formatNumber} from '@/utils/format';

type Props = {
  currency: string;
  index: number;
  item: ReceptionItem;
  strategy: ReceptionAdditionalCostAllocationStrategy | undefined;
  totalQuantity: number;
  totalValue: number;
  transportPrice: number;
};

const ReceptionCreateTransportTableRow: FC<Props> = ({
  currency,
  index,
  item,
  strategy,
  totalQuantity,
  totalValue,
  transportPrice,
}) => {
  const t = useTranslations();
  const {control, watch} = useFormContext<Reception>();
  const {update} = useFieldArray({control, name: 'goods'});

  return (
    <TableRow className='h-[61px]'>
      <TableCell>
        <InventoryItemLink item={item.materialGood} />
      </TableCell>
      <TableCell className='text-right'>
        {strategy === ReceptionAdditionalCostAllocationStrategy.BY_VALUE &&
          `${formatNumber(((item.price.amount * item.receivedQuantity) / totalValue) * 100)} %`}
        {strategy === ReceptionAdditionalCostAllocationStrategy.BY_QUANTITY &&
          `${formatNumber((item.receivedQuantity / totalQuantity) * 100)} %`}
        {strategy === ReceptionAdditionalCostAllocationStrategy.CUSTOM && (
          <>
            {watch('id') && (
              <>{formatNumber(((item?.additionalCostCustomValue?.amount || 0) / transportPrice) * 100)} %</>
            )}
            {!watch('id') && (
              <PopoverInput
                className='justify-end'
                defaultValue={(((item?.additionalCostCustomValue?.amount || 0) / transportPrice) * 100).toString()}
                label={`${t('unit price')} (${item.price.currency})`}
                onChange={(value) =>
                  update(index, {
                    ...item,
                    additionalCostCustomValue: {amount: transportPrice * (Number(value) / 100), currency},
                  })
                }
                type={'number'}
                validation={Joi.number().positive().allow(0).required()}
              >
                {formatNumber(((item?.additionalCostCustomValue?.amount || 0) / transportPrice) * 100)} %
              </PopoverInput>
            )}
          </>
        )}
      </TableCell>
      <TableCell className='text-right'>
        {strategy === ReceptionAdditionalCostAllocationStrategy.BY_VALUE &&
          formatCurrency({
            amount: ((item.price.amount * item.receivedQuantity) / totalValue / item.receivedQuantity) * transportPrice,
            currency,
          })}
        {strategy === ReceptionAdditionalCostAllocationStrategy.BY_QUANTITY &&
          formatCurrency({
            amount: ((item.receivedQuantity / totalQuantity) * transportPrice) / item.receivedQuantity,
            currency,
          })}
        {strategy === ReceptionAdditionalCostAllocationStrategy.CUSTOM && (
          <>
            {transportPrice !== 0 &&
              formatCurrency({
                ...item.additionalCostCustomValue,
                amount: (item.additionalCostCustomValue?.amount || 0) / item.receivedQuantity,
              })}
            {transportPrice === 0 && formatCurrency({...item.additionalCostCustomValue, amount: 0})}
          </>
        )}
      </TableCell>
      <TableCell className='text-right'>
        {strategy === ReceptionAdditionalCostAllocationStrategy.BY_VALUE &&
          formatCurrency({
            amount: ((item.price.amount * item.receivedQuantity) / totalValue) * transportPrice,
            currency,
          })}
        {strategy === ReceptionAdditionalCostAllocationStrategy.BY_QUANTITY &&
          formatCurrency({
            amount: (item.receivedQuantity / totalQuantity) * transportPrice,
            currency,
          })}
        {strategy === ReceptionAdditionalCostAllocationStrategy.CUSTOM && (
          <>
            {watch('id') && (
              <>
                {formatCurrency({
                  amount: item?.additionalCostCustomValue?.amount || 0,
                  currency,
                })}
              </>
            )}
            {!watch('id') && (
              <PopoverInput
                className='justify-end'
                defaultValue={(item?.additionalCostCustomValue?.amount || 0).toString()}
                label={`${t('unit price')} (${item.price.currency})`}
                onChange={(value) =>
                  update(index, {
                    ...item,
                    additionalCostCustomValue: {amount: Number(value), currency},
                  })
                }
                type={'number'}
                validation={Joi.number().positive().allow(0).required()}
              >
                {formatCurrency({
                  amount: item?.additionalCostCustomValue?.amount || 0,
                  currency,
                })}
              </PopoverInput>
            )}
          </>
        )}
      </TableCell>
    </TableRow>
  );
};

export default ReceptionCreateTransportTableRow;
