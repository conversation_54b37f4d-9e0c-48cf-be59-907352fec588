import {FC} from 'react';

import {useAtomValue} from 'jotai';
import {useTranslations} from 'next-intl';
import {useFormContext} from 'react-hook-form';

import {detailsDataShownAtom} from '@/components/pages/details/detailsStore';
import {But<PERSON>} from '@/components/ui/Button';
import {Combobox, ComboboxLabel} from '@/components/ui/Combobox';
import {ControlledDatePickerInput} from '@/components/ui/DatePicker';
import {Input, InputLabel} from '@/components/ui/Input';
import {WithLabel} from '@/components/ui/Label';
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from '@/components/ui/Select';
import {HideableContent} from '@/components/ui/special/HideableContent';
import {TextareaAutosize} from '@/components/ui/Textarea';
import useSupplierActions from '@/hooks/useSupplierActions';
import useSuppliers from '@/hooks/useSuppliers';
import {Id} from '@/types/global';
import {Reception, SupportingDocumentType} from '@/types/purchases';

type Props = {
  saleId?: string;
};

const ReceptionCreateData: FC<Props> = ({saleId}) => {
  const t = useTranslations();
  const {suppliers} = useSuppliers();
  const {createSupplier} = useSupplierActions();
  const {
    control,
    formState: {errors},
    register,
    setValue,
    watch,
  } = useFormContext<Reception>();
  const detailsDataShown = useAtomValue(detailsDataShownAtom(watch('id')));

  return (
    <HideableContent show={detailsDataShown}>
      <div className='mx-6 mt-6 rounded-lg border border-border p-6'>
        <div className='flex flex-col gap-4'>
          <div className='text-base font-medium'>{t('reception details')}</div>
          <div className='flex gap-8'>
            <WithLabel>
              <ControlledDatePickerInput control={control} controlName='receptionDate' disabled={!!watch('id')} />
              <InputLabel>{t('reception date')}*</InputLabel>
            </WithLabel>
            {saleId && (
              <WithLabel>
                <Input disabled value={watch('supplier.name')} />
                <InputLabel>{t('supplier')}</InputLabel>
              </WithLabel>
            )}
            {!saleId && (
              <WithLabel>
                <Combobox
                  className='w-[250px]'
                  disabled={!!watch('id')}
                  error={!!errors.supplier}
                  onChange={(value) => setValue('supplier', suppliers.find((supplier) => supplier.id === value) as Id)}
                  options={suppliers.map((supplier) => ({id: supplier.id, value: supplier.name}))}
                  placeholder={t('supplier')}
                  renderNotFound={(query) => (
                    <Button
                      className='w-full justify-start'
                      onClick={() => {
                        createSupplier({name: query}).then((supplier) => {
                          if (supplier) setValue('supplier', supplier);
                        });
                      }}
                      variant='secondary'
                    >
                      {t('create supplier')}
                    </Button>
                  )}
                  searchPlaceholder={t('search supplier')}
                  value={watch('supplier.id')}
                />
                <ComboboxLabel>{t('supplier')}*</ComboboxLabel>
              </WithLabel>
            )}
            <WithLabel>
              <Select
                disabled={!!watch('id')}
                onValueChange={(value) => {
                  setValue('supportingDocument.type', value as SupportingDocumentType);
                  setValue('supportingDocument.number', '');
                  setValue('supportingDocument.date', '');
                }}
                value={watch('supportingDocument.type')}
              >
                <SelectTrigger size='xl'>
                  <SelectValue>{t(watch('supportingDocument.type') as any)}</SelectValue>
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value={SupportingDocumentType.NONE}>{t(SupportingDocumentType.NONE)}</SelectItem>
                  <SelectItem value={SupportingDocumentType.INVOICE}>{t(SupportingDocumentType.INVOICE)}</SelectItem>
                  <SelectItem value={SupportingDocumentType.ACCOMPANYING_LETTER}>
                    {t(SupportingDocumentType.ACCOMPANYING_LETTER)}
                  </SelectItem>
                </SelectContent>
              </Select>
              <InputLabel>{t('supporting document type')}*</InputLabel>
            </WithLabel>
            <WithLabel>
              <Input
                {...register('supportingDocument.number')}
                disabled={watch('supportingDocument.type') === SupportingDocumentType.NONE || !!watch('id')}
                error={!!errors?.supportingDocument?.number}
              />
              <InputLabel>
                {t('document number')}
                {watch('supportingDocument.type') !== SupportingDocumentType.NONE && '*'}
              </InputLabel>
            </WithLabel>
            <WithLabel>
              <ControlledDatePickerInput
                control={control}
                controlName='supportingDocument.date'
                disabled={watch('supportingDocument.type') === SupportingDocumentType.NONE || !!watch('id')}
                error={!!errors?.supportingDocument?.date}
              />
              <InputLabel>
                {t('document date')}
                {watch('supportingDocument.type') !== SupportingDocumentType.NONE && '*'}
              </InputLabel>
            </WithLabel>
          </div>
          <div className='flex gap-8'>
            <WithLabel>
              <Input {...register('receivedBy')} disabled={!!watch('id')} />
              <InputLabel>{t('received by')}</InputLabel>
            </WithLabel>
            <WithLabel>
              <Input {...register('transportedBy')} disabled={!!watch('id')} />
              <InputLabel>{t('transported by')}</InputLabel>
            </WithLabel>
            <WithLabel>
              <Input {...register('transportedWith')} disabled={!!watch('id')} />
              <InputLabel>{t('transported with')}</InputLabel>
            </WithLabel>
          </div>
          <div className='flex gap-8'>
            <WithLabel className='w-full'>
              <TextareaAutosize {...register('notes')} disabled={!!watch('id')} maxRows={8} minRows={2} />
              <InputLabel>{t('observations')}</InputLabel>
            </WithLabel>
          </div>
        </div>
      </div>
    </HideableContent>
  );
};

export default ReceptionCreateData;
