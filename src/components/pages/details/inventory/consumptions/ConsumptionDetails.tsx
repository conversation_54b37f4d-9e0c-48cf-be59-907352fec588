import {FC} from 'react';

import {ArrowLeftIcon, CloudDownloadIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';

import {Button} from '@/components/ui/Button';
import {DocumentPreview} from '@/components/ui/special/DocumentPreview';
import {Link} from '@/components/ui/special/Link';
import {Page, PageContent, PageHeader, PageHeaderTitle, PageTitle} from '@/components/ui/special/Page';
import useConsumptionDocument from '@/hooks/documents/useConsumptionDocument';
import useHeight from '@/hooks/helpers/useHeight';
import {useRouter} from '@/hooks/helpers/useRouter';

type Props = {
  id: string;
};

const ConsumptionDetails: FC<Props> = ({id}) => {
  const {elementRef} = useHeight();
  const {consumptionDocument} = useConsumptionDocument(id);
  const {back} = useRouter();
  const t = useTranslations();

  return (
    <Page>
      <PageTitle>{`${t('consumption')} - ${t('consumptions')}`}</PageTitle>
      <PageHeader>
        <PageHeaderTitle className='inline-flex items-center gap-2'>
          <Button onClick={() => back('/inventory/consumptions')} size='icon' variant='none'>
            <ArrowLeftIcon />
          </Button>
          {t('consumption')}
        </PageHeaderTitle>
        <div className='grow' />
        <Button asChild>
          <Link href={`/api/manufacturing/material-issue-notes/${id}/details?mediaType=application/pdf`}>
            <CloudDownloadIcon />
            {t('download')}
          </Link>
        </Button>
      </PageHeader>
      <PageContent>
        <div className='flex' ref={elementRef}>
          <div className='flex size-full justify-center bg-gray-100 p-4'>
            <DocumentPreview content={consumptionDocument} />
          </div>
        </div>
      </PageContent>
    </Page>
  );
};

export default ConsumptionDetails;
