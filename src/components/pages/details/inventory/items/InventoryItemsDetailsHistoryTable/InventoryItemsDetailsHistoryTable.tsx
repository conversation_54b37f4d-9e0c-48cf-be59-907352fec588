import {FC} from 'react';

import {useAtomValue} from 'jotai';
import {useFormContext} from 'react-hook-form';

import {
  ItemHistoryCommitedTable,
  ItemHistoryIncomingTable,
  ItemHistoryStockTable,
} from '@/components/ui/special/ItemHistoryModal';
import {useHasPermission} from '@/hooks/helpers/useHasPermission';
import useItemHistory from '@/hooks/useItemHistory';
import {InventoryItem} from '@/types/inventory';

import {
  inventoryItemsDetailsHistoryTableStoreDateFilterAtom,
  inventoryItemsDetailsHistoryTableStoreDirectionFilterAtom,
  inventoryItemsDetailsHistoryTableStoreViewAtom,
} from './inventoryItemsDetailsHistoryTableStore';

const InventoryItemsDetailsHistoryTable: FC = () => {
  const {watch} = useFormContext<InventoryItem>();
  const dateRange = useAtomValue(inventoryItemsDetailsHistoryTableStoreDateFilterAtom);
  const entryTypes = useAtomValue(inventoryItemsDetailsHistoryTableStoreDirectionFilterAtom);
  const {isValidating, itemHistory} = useItemHistory(watch('id'), {
    dateRange,
    entryType: entryTypes?.length > 1 ? undefined : entryTypes[0],
  });
  const view = useAtomValue(inventoryItemsDetailsHistoryTableStoreViewAtom);
  const {hasPermission, isLoading} = useHasPermission();

  if (view === 'committed')
    return (
      <ItemHistoryCommitedTable
        isValidating={isValidating || isLoading}
        items={itemHistory?.committedTo || []}
        measurementUnit={watch('measurementUnit')}
      />
    );
  if (view === 'incoming')
    return (
      <ItemHistoryIncomingTable
        isValidating={isValidating || isLoading}
        items={itemHistory?.incomingOrders || []}
        measurementUnit={watch('measurementUnit')}
      />
    );

  return (
    <ItemHistoryStockTable
      hasFinancialPermission={hasPermission('financial', 'inventory')}
      isValidating={isValidating || isLoading}
      items={itemHistory?.stockHistory || []}
      measurementUnit={watch('measurementUnit')}
    />
  );
};

export default InventoryItemsDetailsHistoryTable;
