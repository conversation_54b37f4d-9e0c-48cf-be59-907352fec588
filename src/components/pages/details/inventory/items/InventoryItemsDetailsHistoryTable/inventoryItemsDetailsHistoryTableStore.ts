import {atom} from 'jotai';

import {DateRange} from '@/types/global';
import {StockHistoryEntryType} from '@/types/inventory';

export type HistoryTableView = 'committed' | 'incoming' | 'in stock';

export const inventoryItemsDetailsHistoryTableStoreViewAtom = atom<HistoryTableView>('in stock');
export const inventoryItemsDetailsHistoryTableStoreDirectionFilterAtom = atom<StockHistoryEntryType[]>([]);
export const inventoryItemsDetailsHistoryTableStoreDateFilterAtom = atom<DateRange | undefined>(undefined);
