import {FC} from 'react';

import {useAtom} from 'jotai';
import {useTranslations} from 'next-intl';

import {DatePicker} from '@/components/ui/DatePicker';
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from '@/components/ui/Select';
import {ToggleGroup, ToggleGroupItem} from '@/components/ui/ToggleGroup';
import {StockHistoryEntryType} from '@/types/inventory';

import {
  HistoryTableView,
  inventoryItemsDetailsHistoryTableStoreDateFilterAtom,
  inventoryItemsDetailsHistoryTableStoreDirectionFilterAtom,
  inventoryItemsDetailsHistoryTableStoreViewAtom,
} from './inventoryItemsDetailsHistoryTableStore';

const InventoryItemsDetailsHistoryButtons: FC = () => {
  const t = useTranslations();
  const [date, setDate] = useAtom(inventoryItemsDetailsHistoryTableStoreDateFilterAtom);
  const [view, setView] = useAtom(inventoryItemsDetailsHistoryTableStoreViewAtom);
  const [direction, setDirection] = useAtom(inventoryItemsDetailsHistoryTableStoreDirectionFilterAtom);

  return (
    <div className='flex items-center gap-4'>
      {view === 'in stock' && (
        <ToggleGroup
          allowEmpty
          onValueChange={(value) => setDirection(value as StockHistoryEntryType[])}
          type='multiple'
          value={direction}
        >
          <ToggleGroupItem value={StockHistoryEntryType.INCOMING}>
            {t(StockHistoryEntryType.INCOMING as any)}
          </ToggleGroupItem>
          <ToggleGroupItem value={StockHistoryEntryType.OUTGOING}>
            {t(StockHistoryEntryType.OUTGOING as any)}
          </ToggleGroupItem>
        </ToggleGroup>
      )}
      <Select onValueChange={(value) => setView(value as HistoryTableView)} value={view}>
        <SelectTrigger size='lg'>
          <SelectValue>{t(view)}</SelectValue>
        </SelectTrigger>
        <SelectContent>
          <SelectItem value={'in stock'}>{t('in stock')}</SelectItem>
          <SelectItem value={'committed'}>{t('committed')}</SelectItem>
          <SelectItem value={'incoming'}>{t('incoming')}</SelectItem>
        </SelectContent>
      </Select>
      <DatePicker modal onChange={setDate} value={date} withRange />
    </div>
  );
};

export default InventoryItemsDetailsHistoryButtons;
