import {FC} from 'react';

import {useAtomValue} from 'jotai';
import {useTranslations} from 'next-intl';
import {useFormContext} from 'react-hook-form';

import {Input, InputLabel} from '@/components/ui/Input';
import {WithLabel} from '@/components/ui/Label';
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from '@/components/ui/Select';
import {DetailsDataGrid, DetailsDataGridContent} from '@/components/ui/special/DetailsData';
import {availableVATRates} from '@/constants/config';
import {defaultCurrencyAtom, defaultVATAtom} from '@/store/defaults';
import {InventoryItem} from '@/types/inventory';
import {withoutUnset} from '@/utils/common';
import {formatVAT} from '@/utils/format';

type Props = {
  className?: string;
  readonly?: boolean;
};

const InventoryItemsDetailsDataSale: FC<Props> = ({className = 'grid-cols-2', readonly}) => {
  const t = useTranslations();
  const defaultCurrency = useAtomValue(defaultCurrencyAtom);
  const defaultVAT = useAtomValue(defaultVATAtom);
  const {
    formState: {errors},
    register,
    setValue,
    watch,
  } = useFormContext<InventoryItem>();

  return (
    <DetailsDataGrid title={`${t('sale')} (${watch('sellPrice.currency') || defaultCurrency})`}>
      <DetailsDataGridContent className={className}>
        <WithLabel className='col-span-2'>
          <Input type='number' {...register('sellPrice.amount')} disabled={readonly} error={!!errors?.sellPrice} />
          <InputLabel>
            {t('selling price')}
            {!readonly && '*'}
          </InputLabel>
        </WithLabel>
        <WithLabel>
          <Input disabled value={withoutUnset(watch('margin'))} />
          <InputLabel>{t('markup')} (%)</InputLabel>
        </WithLabel>
        <WithLabel>
          <Select
            disabled={readonly}
            onValueChange={(value) => {
              setValue('vatRate', Number(value));
            }}
            value={formatVAT(watch('vatRate'))}
          >
            <SelectTrigger>
              <SelectValue>{formatVAT(watch('vatRate'), defaultVAT)}%</SelectValue>
            </SelectTrigger>
            <SelectContent>
              {availableVATRates.map((vat) => (
                <SelectItem key={vat} value={vat}>
                  {formatVAT(Number(vat), defaultVAT)}%
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <InputLabel>{t('vat rate')}</InputLabel>
        </WithLabel>
      </DetailsDataGridContent>
    </DetailsDataGrid>
  );
};

export default InventoryItemsDetailsDataSale;
