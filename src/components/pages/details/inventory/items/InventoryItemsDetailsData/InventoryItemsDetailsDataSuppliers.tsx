import {FC} from 'react';

import {useAtomValue} from 'jotai';
import {useTranslations} from 'next-intl';
import {useFormContext} from 'react-hook-form';

import {Input, InputLabel} from '@/components/ui/Input';
import {WithLabel} from '@/components/ui/Label';
import {DetailsDataGrid, DetailsDataGridContent} from '@/components/ui/special/DetailsData';
import {useHasPermission} from '@/hooks/helpers/useHasPermission';
import {defaultCurrencyAtom} from '@/store/defaults';
import {InventoryItem} from '@/types/inventory';
import {withoutUnset} from '@/utils/common';

type Props = {
  className?: string;
};

const InventoryItemsDetailsDataSuppliers: FC<Props> = ({className = 'grid-cols-1'}) => {
  const t = useTranslations();
  const defaultCurrency = useAtomValue(defaultCurrencyAtom);
  const {watch} = useFormContext<InventoryItem>();
  const {hasPermission, isLoading} = useHasPermission();

  if (isLoading) return null;

  return (
    <DetailsDataGrid title={`${t('supplier')} (${watch('lastPurchase.price.currency') || defaultCurrency})`}>
      <DetailsDataGridContent className={className}>
        <WithLabel>
          <Input disabled value={withoutUnset(watch('lastPurchase.supplierName'))} />
          <InputLabel>{t('supplier')}</InputLabel>
        </WithLabel>
        {hasPermission('financial', 'inventory') && (
          <WithLabel>
            <Input disabled value={withoutUnset(watch('lastPurchase.price.amount'))} />
            <InputLabel>{t('last purchase price')}</InputLabel>
          </WithLabel>
        )}
      </DetailsDataGridContent>
    </DetailsDataGrid>
  );
};

export default InventoryItemsDetailsDataSuppliers;
