import {FC, useMemo} from 'react';

import {useAtomValue} from 'jotai';
import {useTranslations} from 'next-intl';
import {useFormContext} from 'react-hook-form';

import {Costs} from '@/components/ui/special/Cost';
import {DetailsDataGrid, DetailsDataGridContent} from '@/components/ui/special/DetailsData';
import {defaultCurrencyAtom} from '@/store/defaults';
import {InventoryItem} from '@/types/inventory';

type Props = {
  className?: string;
};

const InventoryItemsDetailsDataCosts: FC<Props> = ({className = 'grid-cols-1'}) => {
  const t = useTranslations();
  const defaultCurrency = useAtomValue(defaultCurrencyAtom);
  const {watch} = useFormContext<InventoryItem>();

  const {
    estimatedAdministrativeCost: {amount: adminCost = 0} = {},
    estimatedEmployeeAndWorkstationCost: {amount: laborCost = 0} = {},
    estimatedMaterialCost: {amount: materialCost = 0} = {},
    estimatedProductionOverheadCost: {amount: overheadCost = 0} = {},
  } = watch();

  const productionCost = useMemo(
    () => materialCost + laborCost + overheadCost,
    [materialCost, laborCost, overheadCost],
  );
  const totalCost = useMemo(() => productionCost + adminCost, [productionCost, adminCost]);

  return (
    <DetailsDataGrid
      title={`
          ${t('costs')} (${
            watch('estimatedProductionCost.currency') ||
            watch('estimatedMaterialCost.currency') ||
            watch('estimatedEmployeeAndWorkstationCost.currency') ||
            watch('estimatedProductionOverheadCost.currency') ||
            watch('estimatedAdministrativeCost.currency') ||
            defaultCurrency
          })`}
    >
      <DetailsDataGridContent className={className}>
        <Costs
          cost={productionCost}
          items={[
            {color: 'blue', cost: materialCost, label: t('materials')},
            {color: 'green', cost: laborCost, label: t('labor')},
            {color: 'orange', cost: overheadCost, label: t('indirect production')},
          ]}
          label={t('production')}
        />
        <Costs
          cost={totalCost}
          items={[
            {color: 'gray', cost: productionCost, label: t('production')},
            {color: 'yellow', cost: adminCost, label: t('indirect administrative')},
          ]}
          label={t('total')}
        />
      </DetailsDataGridContent>
    </DetailsDataGrid>
  );
};

export default InventoryItemsDetailsDataCosts;
