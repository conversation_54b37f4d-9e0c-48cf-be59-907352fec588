import {FC} from 'react';

import {useAtomValue} from 'jotai';
import {useFormContext} from 'react-hook-form';

import {detailsDataShownAtom} from '@/components/pages/details/detailsStore';
import {DetailsData} from '@/components/ui/special/DetailsData';
import {useHasPermission} from '@/hooks/helpers/useHasPermission';
import {InventoryItem} from '@/types/inventory';

import InventoryItemsDetailsDataCosts from './InventoryItemsDetailsDataCosts';
import InventoryItemsDetailsDataDetails from './InventoryItemsDetailsDataDetails';
import InventoryItemsDetailsDataSale from './InventoryItemsDetailsDataSale';
import InventoryItemsDetailsDataSuppliers from './InventoryItemsDetailsDataSuppliers';

const getInventoryGridClass = (hasFinancialPermission: boolean, isProduced: boolean, hasSupplier: boolean) => {
  if (hasFinancialPermission) {
    if (isProduced) {
      return hasSupplier ? 'grid-cols-[6fr_2fr_6fr_2fr]' : 'grid-cols-[6fr_2fr_8fr]';
    }
    return 'grid-cols-[6fr_2fr_8fr]';
  }
  return 'grid-cols-[8fr_8fr]';
};

export const canShowSection = (hasPermission: boolean, produced: boolean, hasSupplier: boolean) => ({
  showCosts: hasPermission && produced,
  showSale: hasPermission,
  showSuppliers: !produced || hasSupplier,
});

const InventoryItemsDetailsData: FC = () => {
  const {hasPermission, isLoading} = useHasPermission();
  const {watch} = useFormContext<InventoryItem>();
  const detailsDataShown = useAtomValue(detailsDataShownAtom(watch('id')));

  if (isLoading) return null;

  return (
    <DetailsData
      className={getInventoryGridClass(
        hasPermission('financial', 'inventory'),
        watch('produced'),
        !!watch('lastPurchase.supplierName'),
      )}
      show={detailsDataShown}
    >
      <InventoryItemsDetailsDataDetails />
      {hasPermission('financial', 'inventory') && <InventoryItemsDetailsDataSale />}
      {hasPermission('financial', 'inventory') && watch('produced') && <InventoryItemsDetailsDataCosts />}
      {(!watch('produced') || watch('lastPurchase.supplierName')) && <InventoryItemsDetailsDataSuppliers />}
    </DetailsData>
  );
};

export default InventoryItemsDetailsData;
