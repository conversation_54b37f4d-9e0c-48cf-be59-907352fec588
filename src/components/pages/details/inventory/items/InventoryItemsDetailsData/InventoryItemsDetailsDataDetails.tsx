import {FC, useEffect, useState} from 'react';

import {first} from 'lodash';
import {PencilRulerIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';
import {useFormContext} from 'react-hook-form';

import {Button} from '@/components/ui/Button';
import {ComboboxLabel, ControlledCombobox} from '@/components/ui/Combobox';
import {Input, InputLabel} from '@/components/ui/Input';
import {Label, WithLabel} from '@/components/ui/Label';
import {ControlledSelect, SelectContent, SelectItem, SelectTrigger, SelectValue} from '@/components/ui/Select';
import {DetailsDataGrid, DetailsDataGridContent} from '@/components/ui/special/DetailsData';
import {MaterialDimensionsModal} from '@/components/ui/special/MaterialDimensionsModal';
import {Switch} from '@/components/ui/Switch';
import {TextareaAutosize} from '@/components/ui/Textarea';
import {Tooltip, TooltipContent, TooltipTrigger} from '@/components/ui/Tooltip';
import {useHasPermission} from '@/hooks/helpers/useHasPermission';
import {useRouter} from '@/hooks/helpers/useRouter';
import useCategories from '@/hooks/useCategories';
import useCategoryActions from '@/hooks/useCategoryActions';
import useEnabledUnits from '@/hooks/useEnabledUnits';
import {InventoryItem} from '@/types/inventory';
import {classes, withoutUnset} from '@/utils/common';
type Props = {
  className?: string;
  readonly?: boolean;
};

const InventoryItemsDetailsDataDetails: FC<Props> = ({className, readonly}) => {
  const t = useTranslations();
  const {categories} = useCategories();
  const {enabledUnits, units} = useEnabledUnits();
  const {hasPermission, isLoading} = useHasPermission();
  const {createCategory} = useCategoryActions();
  const [showDimensions, setShowDimensions] = useState(false);
  const {
    control,
    formState: {errors},
    register,
    setValue,
    watch,
  } = useFormContext<InventoryItem>();
  const {
    query: {name},
  } = useRouter();

  useEffect(() => {
    if (!isLoading && name && !watch('name')) setValue('name', name as string);
  }, [isLoading, name, setValue, watch]);

  if (isLoading) return null;

  return (
    <>
      <DetailsDataGrid
        className={className}
        title={
          <div className='mb-4 flex items-center justify-between'>
            <div className='text-base font-medium'>{t('item details')}</div>
            {hasPermission('read', 'manufacturing') && (
              <WithLabel direction='horizontal'>
                <Label htmlFor='made'>{t('made')}</Label>
                <Switch
                  checked={watch('produced')}
                  disabled={readonly}
                  id='made'
                  onCheckedChange={(checked) => {
                    setValue('produced', checked);
                  }}
                />
              </WithLabel>
            )}
          </div>
        }
      >
        <DetailsDataGridContent>
          <WithLabel>
            <ControlledCombobox
              className='h-10'
              containerClassName='max-w-none'
              control={control}
              controlName='category.id'
              disabled={readonly}
              error={!!errors?.category}
              notFoundClassName='mx-1'
              options={categories.map((category) => ({id: category.id, value: category.details.name}))}
              renderNotFound={(query) => (
                <Button
                  className='w-full justify-start'
                  onClick={() => {
                    createCategory(query).then((category) => {
                      if (category) setValue('category.id', category.id);
                    });
                  }}
                  variant='secondary'
                >
                  {t('create category')}
                </Button>
              )}
              searchPlaceholder={t('search category')}
            />
            <ComboboxLabel htmlFor='category'>
              {t('category')}
              {!readonly && '*'}
            </ComboboxLabel>
          </WithLabel>
          <div className='flex items-end gap-1'>
            <WithLabel className='w-full'>
              <ControlledSelect control={control} controlName='measurementUnit.id' disabled={readonly}>
                <SelectTrigger error={!!errors?.measurementUnit}>
                  <SelectValue>
                    {t(
                      `unit.name.${units.find(({id}) => id === watch('measurementUnit.id'))?.name || first(enabledUnits)?.name || 'pcs'}` as any,
                    )}
                  </SelectValue>
                </SelectTrigger>
                <SelectContent>
                  {enabledUnits.map(({id, name}) => (
                    <SelectItem key={id} value={id}>
                      {t(`unit.name.${name.toLowerCase()}` as any)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </ControlledSelect>
              <InputLabel>
                {t('unit of measure')}
                {!readonly && '*'}
              </InputLabel>
            </WithLabel>
            {!readonly && (
              <Tooltip>
                <TooltipTrigger>
                  <Button className='px-0 cursor-pointer' onClick={() => setShowDimensions(true)} variant='ghost'>
                    <PencilRulerIcon
                      className={classes('size-5', watch('material') && 'fill-border')}
                      strokeWidth={1}
                    />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>{t('set material dimensions')}</TooltipContent>
              </Tooltip>
            )}
          </div>
          <WithLabel>
            <Input disabled value={withoutUnset(watch('stock'))} />
            <InputLabel>{t('actual quantity')}</InputLabel>
          </WithLabel>
          <WithLabel>
            <Input type='number' {...register('criticalOnHand')} disabled={readonly} error={!!errors?.criticalOnHand} />
            <InputLabel>
              {t('critical on hand')}
              {!readonly && '*'}
            </InputLabel>
          </WithLabel>
          <WithLabel className='col-span-4'>
            <TextareaAutosize
              maxRows={2}
              minRows={1}
              placeholder={t('description')}
              {...register('description')}
              disabled={readonly}
            />
            <InputLabel>{t('description')}</InputLabel>
          </WithLabel>
        </DetailsDataGridContent>
      </DetailsDataGrid>
      {showDimensions && (
        <MaterialDimensionsModal
          onChange={(value) => setValue('material', value)}
          onClose={() => setShowDimensions(false)}
          value={watch('material')}
        />
      )}
    </>
  );
};

export default InventoryItemsDetailsDataDetails;
