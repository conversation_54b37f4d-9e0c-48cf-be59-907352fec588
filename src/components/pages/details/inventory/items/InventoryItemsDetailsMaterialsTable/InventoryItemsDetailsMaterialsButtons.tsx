import {FC} from 'react';

import {useTranslations} from 'next-intl';
import {useFormContext} from 'react-hook-form';

import {InputLabel, NumberInput} from '@/components/ui/Input';
import {WithLabel} from '@/components/ui/Label';
import {StoreSearchInput} from '@/components/ui/special/StoreSearchInput';
import {InventoryItem} from '@/types/inventory';
import {classes} from '@/utils/common';

import {inventoryItemMaterialsSearchQueryAtom} from '../inventoryItemsDetailsStore';

type Props = {
  className?: string;
};

const InventoryItemsDetailsMaterialsButtons: FC<Props> = ({className}) => {
  const t = useTranslations();
  const {setValue, watch} = useFormContext<InventoryItem>();

  return (
    <div className={classes('flex items-center gap-4', className)}>
      <WithLabel direction='horizontal'>
        <InputLabel className='text-nowrap'>{t('recipe for')}</InputLabel>
        <NumberInput onChange={(value) => setValue('unitOfProduction', value)} value={watch('unitOfProduction')} />
      </WithLabel>
      <StoreSearchInput
        placeholder={t('search materials')}
        store={inventoryItemMaterialsSearchQueryAtom(watch('id'))}
      />
    </div>
  );
};

export default InventoryItemsDetailsMaterialsButtons;
