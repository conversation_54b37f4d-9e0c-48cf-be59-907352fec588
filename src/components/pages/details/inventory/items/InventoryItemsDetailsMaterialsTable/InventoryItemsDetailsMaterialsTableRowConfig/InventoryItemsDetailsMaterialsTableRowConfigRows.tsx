import {FC} from 'react';

import {Checkbox} from '@/components/ui/Checkbox';
import {TableCell, TableRow} from '@/components/ui/Table';
import useItems from '@/hooks/useItems';
import {Id} from '@/types/global';
import {InventoryItemOption} from '@/types/inventory';

type Props = {
  categoryId: string | undefined;
  disabled: boolean;
  excludeIds: string[];
  onChange: (options: Id[]) => void;
  options: InventoryItemOption[];
  primaryOption: InventoryItemOption;
};

const InventoryItemsDetailsMaterialsTableRowConfigRows: FC<Props> = ({
  categoryId = '',
  disabled,
  excludeIds,
  onChange,
  options,
  primaryOption,
}) => {
  const {items} = useItems({categoryIds: [categoryId]});

  return items
    .filter(
      (item) =>
        item.id !== primaryOption.id &&
        !excludeIds.filter((id) => !options.map((option) => option.id).includes(id)).includes(item.id),
    )
    .map((item) => (
      <TableRow key={item.id}>
        <TableCell className='text-wrap'>{item.name}</TableCell>
        <TableCell>
          <div className='flex'>
            <Checkbox
              checked={options.some((option) => option.id === item.id)}
              className='mr-6 justify-end'
              disabled={disabled}
              onCheckedChange={(val) =>
                onChange(val ? [...options, {...item}] : options.filter((option) => option.id !== item.id))
              }
            />
          </div>
        </TableCell>
      </TableRow>
    ));
};

export default InventoryItemsDetailsMaterialsTableRowConfigRows;
