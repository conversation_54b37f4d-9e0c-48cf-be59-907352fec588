import {FC, useState} from 'react';

import {CheckIcon, ChevronRightIcon, XIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';

import {Badge} from '@/components/ui/Badge';
import {Button} from '@/components/ui/Button';
import InventoryItemLink from '@/components/ui/special/InventoryItemLink/InventoryItemLink';
import {ItemHistoryModal} from '@/components/ui/special/ItemHistoryModal';
import {OrderButton} from '@/components/ui/special/OrderButton';
import {TableActions, TableCell, TableRow} from '@/components/ui/Table';
import useItem from '@/hooks/useItem';
import {classes} from '@/utils/common';
import {formatCurrency} from '@/utils/format';

type Props = {
  embedded?: boolean;
  id: string;
  level?: number;
};

const InventoryItemsDetailsMaterialsTableSubRow: FC<Props> = ({embedded, id, level = 1}) => {
  const t = useTranslations();
  const [open, setOpen] = useState<{[key: string]: boolean}>({});
  const {item} = useItem(id);

  if (item.requiredMaterials?.length === 0)
    return (
      <TableRow autoFocus={false} className='h-[61px] bg-input/50 border-b-black'>
        <TableCell colSpan={7} style={{paddingLeft: `${level * 16 + 24}px`}}>
          {t('the item has no materials added')}
        </TableCell>
      </TableRow>
    );

  return (
    <>
      {item.requiredMaterials?.map((requiredItem) => {
        const material = requiredItem.options?.[0];

        if (!material) return null;

        return (
          <>
            <TableRow className='h-[61px] bg-input/50' key={material.id}>
              <TableCell
                className='inline-flex w-full items-center justify-between gap-2'
                style={{paddingLeft: `${level * 16 + 24}px`}}
              >
                <div className='inline-flex items-start gap-2'>
                  <InventoryItemLink item={material} />
                </div>
                {material.produced && (
                  <Button
                    onClick={() => setOpen((prev) => ({...prev, [material.id]: !prev[material.id]}))}
                    variant='ghost'
                  >
                    <ChevronRightIcon className={classes('transition-transform', open[material.id] && '-rotate-90')} />
                  </Button>
                )}
              </TableCell>
              {embedded && (
                <>
                  <TableCell />
                  <TableCell />
                </>
              )}
              <TableCell className='text-right'>
                {requiredItem.quantity} {t(`unit.name.${material.measurementUnit?.name || 'pcs'}` as any)}
              </TableCell>
              <TableCell />
              <TableCell className='text-right'>{formatCurrency(material.cost)}</TableCell>
              <TableCell className='text-right'>
                <ItemHistoryModal item={material}>
                  <Badge className='cursor-pointer gap-1' variant={material.available ? 'success' : 'error'}>
                    {material.available && <CheckIcon className='size-4 text-green' />}
                    {!material.available && <XIcon className='size-4 text-red' />}
                    {t(material.available ? 'available' : 'unavailable', {isPlural: 'true'})}
                  </Badge>
                </ItemHistoryModal>
              </TableCell>
              <TableActions>
                <OrderButton forTable item={material} />
                <div className='w-6' />
              </TableActions>
            </TableRow>
            {open[material.id] && (
              <InventoryItemsDetailsMaterialsTableSubRow embedded={embedded} id={material.id} level={level + 1} />
            )}
          </>
        );
      })}
    </>
  );
};

export default InventoryItemsDetailsMaterialsTableSubRow;
