import {FC, useMemo} from 'react';

import {useAtomValue} from 'jotai';
import {sumBy} from 'lodash';
import {useTranslations} from 'next-intl';
import {useFormContext} from 'react-hook-form';

import {detailsDataShownAtom} from '@/components/pages/details/detailsStore';
import {Dot} from '@/components/ui/special/Dot';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableFooter,
  TableHead,
  TableHeadActions,
  TableHeader,
  TableRow,
} from '@/components/ui/Table';
import {useHasPermission} from '@/hooks/helpers/useHasPermission';
import useHeight from '@/hooks/helpers/useHeight';
import {defaultCurrencyAtom} from '@/store/defaults';
import {InventoryItem, InventoryItemOption, InventoryItemRequiredMaterial} from '@/types/inventory';
import {formatCurrency, formatNumber} from '@/utils/format';

import InventoryItemsDetailsMaterialsTableRow from './InventoryItemsDetailsMaterialsTableRow';
import {inventoryItemMaterialsSearchQueryAtom} from '../inventoryItemsDetailsStore';

type Props = {
  embedded?: boolean;
  materials: InventoryItemRequiredMaterial[];
  operationIndex?: number;
  saveItem: () => void;
};

const InventoryItemsDetailsMaterialsTable: FC<Props> = ({embedded, materials, operationIndex, saveItem}) => {
  const {watch} = useFormContext<InventoryItem>();
  const detailsDataShown = useAtomValue(detailsDataShownAtom(watch('id')));
  const {elementRef} = useHeight({dependencies: [detailsDataShown]});
  const searchQuery = useAtomValue(inventoryItemMaterialsSearchQueryAtom(watch('id')));
  const defaultCurrency = useAtomValue(defaultCurrencyAtom);
  const {hasPermission, isLoading} = useHasPermission();
  const t = useTranslations();

  const filteredRequiredMaterials = useMemo(() => {
    if (!searchQuery) return materials;

    return (materials || []).filter((material) =>
      material.options.some(
        (option: InventoryItemOption) =>
          option.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          option.code.toLowerCase().includes(searchQuery.toLowerCase()),
      ),
    );
  }, [materials, searchQuery]);

  const totalCost = useMemo(
    () => sumBy(filteredRequiredMaterials, (material) => material.options?.[0]?.cost?.amount || 0),
    [filteredRequiredMaterials],
  );

  const unavailableMaterialsCount = useMemo(
    () => filteredRequiredMaterials.filter((material) => !material.options?.[0]?.available).length,
    [filteredRequiredMaterials],
  );

  if (isLoading) return null;

  return (
    <TableContainer ref={embedded ? null : elementRef}>
      <Table className='h-full'>
        <TableHeader>
          <TableRow>
            <TableHead>{t('material')}</TableHead>
            {embedded && (
              <>
                <TableHead>
                  <Dot variant='orange'>{t('configurable')}</Dot> / <Dot variant='purple'>{t('alternative')}</Dot>
                </TableHead>
                <TableHead>{t('optional')}</TableHead>
              </>
            )}
            <TableHead className='text-right'>{t('quantity')}</TableHead>
            <TableHead className='text-right'>{t('waste')}</TableHead>
            {hasPermission('financial', 'inventory') && <TableHead className='text-right'>{t('cost')}</TableHead>}
            <TableHead className='text-right'>{t('available', {isPlural: 'true'})}</TableHead>
            <TableHeadActions className='text-right'>{t('order')}</TableHeadActions>
          </TableRow>
        </TableHeader>
        <TableBody className='overflow-y-hidden' isValidating={!watch()}>
          {filteredRequiredMaterials?.map((requiredMaterial, index) => (
            <InventoryItemsDetailsMaterialsTableRow
              embedded={embedded}
              excludeIds={materials.flatMap((material) => material.options?.map((option) => option.id) || [])}
              index={index}
              key={`${requiredMaterial.options?.[0]?.id}-${index}`}
              operationIndex={operationIndex}
              requiredMaterial={requiredMaterial}
              saveItem={saveItem}
            />
          ))}
        </TableBody>
        {!embedded && (
          <TableFooter className='sticky bottom-0 h-[60px] shadow-[0_-4px_8px_0_rgba(0,0,0,0.07843137255)]'>
            <TableRow>
              <TableCell>
                <div className='flex flex-col'>
                  <div className='text-xs font-medium uppercase text-border-foreground'>{t('total materials')}</div>
                  <div className='font-medium'>{formatNumber(filteredRequiredMaterials?.length)}</div>
                </div>
              </TableCell>
              <TableCell />
              <TableCell />
              {hasPermission('financial', 'inventory') && (
                <TableCell className='text-right'>
                  <div className='flex flex-col'>
                    <div className='text-xs font-medium uppercase text-border-foreground'>
                      {t('total material cost')}
                    </div>
                    <div className='font-medium'>
                      {formatCurrency({
                        amount: totalCost,
                        currency: watch('inventoryCostPerItem.currency') || defaultCurrency,
                      })}
                    </div>
                  </div>
                </TableCell>
              )}
              <TableCell className='text-right'>
                <div className='flex flex-col'>
                  <div className='text-xs font-medium uppercase text-border-foreground'>
                    {t('unavailable', {isPlural: 'true'})}
                  </div>
                  <div className='font-medium'>{formatNumber(unavailableMaterialsCount)}</div>
                </div>
              </TableCell>
              <TableCell />
            </TableRow>
          </TableFooter>
        )}
      </Table>
    </TableContainer>
  );
};

export default InventoryItemsDetailsMaterialsTable;
