import {FC, useEffect} from 'react';

import {useAtom} from 'jotai';
import {useTranslations} from 'next-intl';
import {useFormContext} from 'react-hook-form';

import {Tabs, TabsList, TabsTrigger} from '@/components/ui/Tabs';
import {useHasPermission} from '@/hooks/helpers/useHasPermission';
import {InventoryItem} from '@/types/inventory';
import {formatNumber} from '@/utils/format';

import InventoryDetailsFilesButtons from './InventoryItemsDetailsFilesTable/InventoryItemsDetailsFilesButtons';
import InventoryItemsDetailsFilesTable from './InventoryItemsDetailsFilesTable/InventoryItemsDetailsFilesTable';
import InventoryItemsDetailsHistoryButtons from './InventoryItemsDetailsHistoryTable/InventoryItemsDetailsHistoryButtons';
import InventoryItemsDetailsHistoryTable from './InventoryItemsDetailsHistoryTable/InventoryItemsDetailsHistoryTable';
import InventoryItemsDetailsMaterialsButtons from './InventoryItemsDetailsMaterialsTable/InventoryItemsDetailsMaterialsButtons';
import InventoryItemsDetailsMaterialsTable from './InventoryItemsDetailsMaterialsTable/InventoryItemsDetailsMaterialsTable';
import InventoryItemsDetailsOperationsButtons from './InventoryItemsDetailsOperationsTable/InventoryItemsDetailsOperationsButtons/InventoryItemsDetailsOperationsButtons';
import InventoryItemsDetailsOperationsTable from './InventoryItemsDetailsOperationsTable/InventoryItemsDetailsOperationsTable';
import {InventoryItemTab, inventoryItemTabAtom} from './inventoryItemsDetailsStore';
import InventoryItemsDetailsVariantsButtons from './InventoryItemsDetailsVariantsTable/InventoryItemsDetailsVariantsButtons';
import InventoryItemsDetailsVariantsTable from './InventoryItemsDetailsVariantsTable/InventoryItemsDetailsVariantsTable';

type Props = {
  deleteFile: (id: string) => Promise<number | string | void>;
  saveItem: () => Promise<void>;
  uploadFile: () => void;
};

const InventoryItemsDetailsTables: FC<Props> = ({deleteFile, saveItem, uploadFile}) => {
  const {
    formState: {errors},
    watch,
  } = useFormContext<InventoryItem>();
  const [tab, setTab] = useAtom(inventoryItemTabAtom(watch('id')));
  const t = useTranslations();
  const {hasPermission} = useHasPermission();

  const produced = watch('produced');
  const id = watch('id');
  useEffect(() => {
    if (!produced && ['materials', 'operations'].includes(tab)) setTab('history');
  }, [setTab, tab, produced, id]);

  return (
    <>
      <div className='mx-6 flex items-center justify-between'>
        <div className='inline-flex items-center'>
          <Tabs onValueChange={(value) => setTab(value as InventoryItemTab)} value={tab} variant='menu'>
            <TabsList variant='menu'>
              <TabsTrigger value='history' variant='menu'>
                {t('history')}
              </TabsTrigger>
              {hasPermission('update', 'manufacturing') && watch('produced') && (
                <>
                  <TabsTrigger
                    badge={formatNumber(watch('manufacturingOperations')?.length)}
                    error={!!errors?.manufacturingOperations || !!errors?.requiredMaterials}
                    value='operations'
                    variant='menu'
                  >
                    {t('bill of materials')}
                  </TabsTrigger>
                  <TabsTrigger
                    badge={formatNumber(watch('requiredMaterials')?.length)}
                    value='materials'
                    variant='menu'
                  >
                    {t('materials summary')}
                  </TabsTrigger>
                </>
              )}
              <TabsTrigger badge={formatNumber(watch('variantCount'))} value='variants' variant='menu'>
                {t('variants')}
              </TabsTrigger>
              <TabsTrigger
                badge={formatNumber(watch('files')?.length)}
                disabled={!watch('id')}
                value='files'
                variant='menu'
              >
                {t('files')}
              </TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
        {tab === 'history' && <InventoryItemsDetailsHistoryButtons />}
        {tab === 'files' && <InventoryDetailsFilesButtons uploadFile={uploadFile} />}
        {tab === 'materials' && <InventoryItemsDetailsMaterialsButtons />}
        {tab === 'operations' && <InventoryItemsDetailsOperationsButtons />}
        {tab === 'variants' && (
          <InventoryItemsDetailsVariantsButtons id={watch('id')} variantOptions={watch('variantOptions') || []} />
        )}
      </div>
      {tab === 'history' && <InventoryItemsDetailsHistoryTable />}
      {tab === 'materials' && (
        <InventoryItemsDetailsMaterialsTable materials={watch('requiredMaterials') || []} saveItem={saveItem} />
      )}
      {tab === 'operations' && <InventoryItemsDetailsOperationsTable saveItem={saveItem} />}
      {tab === 'files' && <InventoryItemsDetailsFilesTable deleteFile={deleteFile} />}

      {tab === 'variants' && <InventoryItemsDetailsVariantsTable />}
    </>
  );
};

export default InventoryItemsDetailsTables;
