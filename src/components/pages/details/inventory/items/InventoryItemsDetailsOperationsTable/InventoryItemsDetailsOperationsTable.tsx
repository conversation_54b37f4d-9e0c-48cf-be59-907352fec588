import {FC} from 'react';

import {closestCorners, DndContext, DragEndEvent, PointerSensor, useSensor, useSensors} from '@dnd-kit/core';
import {SortableContext, verticalListSortingStrategy} from '@dnd-kit/sortable';
import {useAtomValue} from 'jotai';
import {useTranslations} from 'next-intl';
import {useFieldArray, useFormContext} from 'react-hook-form';

import {detailsDataShownAtom} from '@/components/pages/details/detailsStore';
import {
  Table,
  TableBody,
  TableContainer,
  TableHead,
  TableHeadActions,
  TableHeader,
  TableRow,
} from '@/components/ui/Table';
import useHeight from '@/hooks/helpers/useHeight';
import {InventoryItem} from '@/types/inventory';

import InventoryItemsDetailsOperationsTableRow from './InventoryItemsDetailsOperationsTableRow';

type Props = {
  saveItem: () => void;
};

const InventoryItemsDetailsOperationsTable: FC<Props> = ({saveItem}) => {
  const t = useTranslations();
  const {control, watch} = useFormContext<InventoryItem>();
  const detailsDataShown = useAtomValue(detailsDataShownAtom(watch('id')));
  const {elementRef} = useHeight({dependencies: [detailsDataShown]});
  const {move} = useFieldArray({control, name: 'manufacturingOperations'});

  const sensors = useSensors(useSensor(PointerSensor));

  const handleDragEnd = ({active, over}: DragEndEvent) => {
    if (active.id !== over?.id) {
      const oldIndex = watch('manufacturingOperations')?.findIndex((operation) => operation.id === active.id);
      const newIndex = over
        ? watch('manufacturingOperations')?.findIndex((operation) => operation.id === over?.id)
        : -1;
      if (oldIndex !== undefined && newIndex !== undefined && oldIndex !== -1 && newIndex !== -1) {
        move(oldIndex, newIndex);
      }
    }
  };

  return (
    <TableContainer ref={elementRef}>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>{t('name')}</TableHead>
            <TableHead className='text-right'>{t('duration')}</TableHead>
            <TableHead className='text-right'>{t('cost per hour')}</TableHead>
            <TableHead>{t('materials')}</TableHead>
            <TableHead>{t('qualified employees')}</TableHead>
            <TableHead>{t('workstations')}</TableHead>
            <TableHead>{t('parallelization')}</TableHead>
            <TableHeadActions />
          </TableRow>
        </TableHeader>
        <TableBody className='overflow-y-hidden' isValidating={!watch()}>
          <DndContext collisionDetection={closestCorners} onDragEnd={handleDragEnd} sensors={sensors}>
            <SortableContext items={watch('manufacturingOperations') || []} strategy={verticalListSortingStrategy}>
              {watch('manufacturingOperations')?.map((operation, index) => (
                <InventoryItemsDetailsOperationsTableRow
                  index={index}
                  key={`${operation.id}-${index}`}
                  operation={operation}
                  saveItem={saveItem}
                />
              ))}
            </SortableContext>
          </DndContext>
        </TableBody>
      </Table>
    </TableContainer>
  );
};

export default InventoryItemsDetailsOperationsTable;
