import {FC, useCallback} from 'react';

import {useAtomValue} from 'jotai/index';
import {useFieldArray, useFormContext} from 'react-hook-form';

import AddItemModal from '@/components/ui/special/AddItemModal/AddItemModal';
import useItemActions from '@/hooks/useItemActions';
import useItemStockActions from '@/hooks/useItemStockActions';
import useSettings from '@/hooks/useSettings';
import {defaultCurrencyAtom} from '@/store/defaults';
import {InventoryItem, InventoryItemRequiredMaterial} from '@/types/inventory';

type Props = {
  index: number;
  onClose: () => void;
};

const InventoryItemsDetailsOperationsMaterialsButton: FC<Props> = ({index, onClose}) => {
  const {control, setValue, watch} = useFormContext<InventoryItem>();
  const {append} = useFieldArray({control, name: `manufacturingOperations.${index}.materials`});
  const {getItemAvailable} = useItemStockActions();
  const {settings} = useSettings();
  const {getItem} = useItemActions();
  const defaultCurrency = useAtomValue(defaultCurrencyAtom);
  const {processMaterials} = useItemActions();

  const handleAdd = useCallback(
    async (values: {id: string; quantity: number}[]) => {
      const entries = await Promise.all(
        values.map(async ({id, quantity}) => {
          const item = await getItem(id);

          if (!item) return;

          const available = await getItemAvailable(id, 1, {
            inventoryUnitIds: [settings.general?.inventoryAccountingSettings?.unitDesignations?.defaultInventoryUnit],
          });

          return {
            category: item.category,
            configurableWithOptions: false,
            optional: false,
            options: [
              {
                ...item,
                available,
                cost: item.inventoryCostPerItem?.amount
                  ? item.inventoryCostPerItem
                  : item.estimatedProductionCost?.amount && item.estimatedProductionOverheadCost?.amount
                    ? {
                        amount: item.estimatedProductionCost.amount + item.estimatedProductionOverheadCost.amount,
                        currency: item.inventoryCostPerItem?.currency || defaultCurrency,
                      }
                    : {
                        amount: 0,
                        currency: item.inventoryCostPerItem?.currency || defaultCurrency,
                      },
                lastOrderedFrom: item.lastPurchase
                  ? {id: item.lastPurchase.supplierId, name: item.lastPurchase.supplierName}
                  : null,
              },
            ],
            quantity,
            replaceableWithOptions: false,
            requiredDimensions: null,
            wastePercentage: 0,
          };
        }),
      );

      const validEntries = entries.filter(Boolean);

      append(validEntries as InventoryItemRequiredMaterial[]);
      setTimeout(() => {
        setValue('requiredMaterials', processMaterials(watch()));
        onClose();
      }, 200);
    },
    [
      append,
      defaultCurrency,
      getItem,
      getItemAvailable,
      onClose,
      processMaterials,
      setValue,
      settings.general?.inventoryAccountingSettings?.unitDesignations?.defaultInventoryUnit,
      watch,
    ],
  );

  return <AddItemModal create='full' onAdd={handleAdd} onClose={onClose} />;
};

export default InventoryItemsDetailsOperationsMaterialsButton;
