import {FC, useRef, useState} from 'react';

import {ChevronUpIcon, PlusIcon, XIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';
import {useFieldArray, UseFieldArrayAppend, useFormContext} from 'react-hook-form';

import {Button} from '@/components/ui/Button';
import {InputLabel, NumberInput} from '@/components/ui/Input';
import {WithLabel} from '@/components/ui/Label';
import {Popover, PopoverContent, PopoverTrigger} from '@/components/ui/Popover';
import {Tabs, TabsList, TabsTrigger} from '@/components/ui/Tabs';
import useClickOutside from '@/hooks/helpers/useClickOutside';
import {InventoryItem} from '@/types/inventory';
import {classes} from '@/utils/common';

import InventoryItemsDetailsOperationsButtonsItemsTab from './InventoryItemsDetailsOperationsButtonsItemsTab';
import InventoryItemsDetailsOperationsButtonsTasksTab from './InventoryItemsDetailsOperationsButtonsTasksTab';

type AddOperationTab = 'items' | 'tasks';

type AddProps = {
  onAdd: UseFieldArrayAppend<InventoryItem, 'manufacturingOperations'>;
  onClose: () => void;
};

const AddOperation: FC<AddProps> = ({onAdd, onClose}) => {
  const t = useTranslations();
  const [tab, setTab] = useState<AddOperationTab>('tasks');
  const ref = useRef<HTMLDivElement>(null);

  useClickOutside([ref], () => onClose());

  return (
    <>
      <Popover open>
        <PopoverTrigger asChild>
          <Button
            className={classes('w-[300px] justify-between border-b border-muted px-0 hover:bg-background')}
            variant='dropdown'
          >
            <div className='truncate text-muted'>{t('tasks')}</div>
            <ChevronUpIcon className='size-4 shrink-0 opacity-50' />
          </Button>
        </PopoverTrigger>
        <PopoverContent autoFocus className='max-w-none p-2' ref={ref}>
          <Tabs className='mx-1 mb-2' onValueChange={(value) => setTab(value as AddOperationTab)} value={tab}>
            <TabsList className='w-full'>
              <TabsTrigger value='tasks'>{t('tasks')}</TabsTrigger>
              <TabsTrigger value='items'>{t('copy from items')}</TabsTrigger>
            </TabsList>
          </Tabs>
          {tab === 'tasks' && <InventoryItemsDetailsOperationsButtonsTasksTab onAdd={onAdd} onClose={onClose} />}
          {tab === 'items' && <InventoryItemsDetailsOperationsButtonsItemsTab onAdd={onAdd} onClose={onClose} />}
        </PopoverContent>
      </Popover>
      <Button onClick={onClose} variant='secondary'>
        <XIcon />
        {t('cancel')}
      </Button>
    </>
  );
};

type Props = {
  className?: string;
};

const InventoryItemsDetailsOperationsButtons: FC<Props> = ({className}) => {
  const t = useTranslations();
  const [addEnabled, setAddEnabled] = useState(false);
  const {control, setValue, watch} = useFormContext<InventoryItem>();
  const {append} = useFieldArray({control, name: 'manufacturingOperations'});

  return (
    <div className={classes('flex items-center gap-4', className)}>
      <WithLabel direction='horizontal'>
        <InputLabel className='text-nowrap'>{t('recipe for')}</InputLabel>
        <NumberInput onChange={(value) => setValue('unitOfProduction', value)} value={watch('unitOfProduction')} />
      </WithLabel>
      {!addEnabled && (
        <Button onClick={() => setAddEnabled(true)} variant='secondary'>
          <PlusIcon /> {t('add operations')}
        </Button>
      )}
      {addEnabled && <AddOperation onAdd={append} onClose={() => setAddEnabled(false)} />}
    </div>
  );
};

export default InventoryItemsDetailsOperationsButtons;
