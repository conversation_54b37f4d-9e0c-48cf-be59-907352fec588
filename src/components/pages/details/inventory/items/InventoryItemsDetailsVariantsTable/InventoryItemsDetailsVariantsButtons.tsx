import {FC, useCallback, useRef, useState} from 'react';

import {PencilIcon, PlusIcon, XIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';

import {Button} from '@/components/ui/Button';
import {InlineInput, Input} from '@/components/ui/Input';
import {Sheet, SheetClose, SheetContent, SheetFooter, SheetPage, SheetTitle, SheetTrigger} from '@/components/ui/Sheet';
import {
  Table,
  TableActions,
  TableBody,
  TableCell,
  TableHead,
  TableHeadActions,
  TableHeader,
  TableRow,
} from '@/components/ui/Table';
import useItemActions from '@/hooks/useItemActions';
import useVariants from '@/hooks/useVariants';
import {InventoryItemVariantOption} from '@/types/inventory';
import {classes} from '@/utils/common';

type Props = {
  id: string;
  variantOptions: InventoryItemVariantOption[];
};

const InventoryItemsDetailsVariantsButtons: FC<Props> = ({id, variantOptions}) => {
  const [options, setOptions] = useState(variantOptions);
  const t = useTranslations();
  const contentRef = useRef<HTMLDivElement>(null);
  const {variants} = useVariants(id);
  const {updateVariants} = useItemActions();
  const [option, setOption] = useState('');

  const addOption = useCallback(() => {
    if (!option) return;
    setOptions((prev) => [...prev, {name: option, values: ['']}]);
    setOption('');
    setTimeout(() => {
      if (contentRef.current) contentRef.current.scrollTo(0, contentRef.current.scrollHeight);
    }, 100);
  }, [option]);

  const isValueUsed = useCallback(
    (optionName: string, value?: string) => {
      return variants.some((variant) =>
        (variant.appliedVariantOptions || [])?.some((appliedOption) =>
          value !== undefined
            ? appliedOption.name === optionName && appliedOption.value === value
            : appliedOption.name === optionName,
        ),
      );
    },
    [variants],
  );

  return (
    <Sheet>
      <div className='inline-flex gap-1'>
        <SheetTrigger asChild>
          <Button variant='secondary'>
            <PencilIcon strokeWidth={1.5} /> {variantOptions.length > 0 ? t('edit variants') : t('add variants')}
          </Button>
        </SheetTrigger>
      </div>
      <SheetPage side='right' size='ms'>
        <SheetTitle className='text-2xl'>
          {variantOptions.length > 0 ? t('edit variants') : t('add variants')}
        </SheetTitle>
        <div className='mx-4 flex items-center gap-4'>
          <Input
            onChange={({target: {value}}) => setOption(value)}
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                addOption();
              }
            }}
            placeholder={t('new option')}
            value={option}
          />
          <Button disabled={!option} onClick={addOption}>
            <PlusIcon />
          </Button>
        </div>
        <SheetContent className='flex flex-col gap-6' ref={contentRef}>
          {options?.map((option, index) => (
            <div className='flex flex-col' key={`${option.name}-${index}`}>
              <div className='ml-6 mr-4 flex items-center justify-between'>
                <InlineInput
                  defaultValue={option.name}
                  disabled={isValueUsed(option.name)}
                  displayValue={option.name}
                  inlineClassName={classes('w-fit text-lg font-medium', isValueUsed(option.name) && 'mb-2')}
                  onBlur={({target: {value}}) => {
                    setOptions((prev) => {
                      const newOptions = [...prev];
                      newOptions[index].name = value;
                      return newOptions;
                    });
                  }}
                />
                {!isValueUsed(option.name) && (
                  <Button
                    onClick={() => setOptions((prev) => prev.filter((_, i) => i !== index))}
                    size='none'
                    variant='none'
                  >
                    <XIcon />
                  </Button>
                )}
              </div>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>{t('value')}</TableHead>
                    <TableHeadActions />
                  </TableRow>
                </TableHeader>
                <TableBody className='overflow-y-hidden' isValidating={false}>
                  {option.values.map((value, valueIndex) => (
                    <TableRow key={`${value}-${valueIndex}`}>
                      <TableCell>
                        <InlineInput
                          defaultValue={value}
                          disabled={isValueUsed(option.name, value)}
                          displayValue={value}
                          inlineClassName='w-fit'
                          onBlur={({target: {value}}) =>
                            setOptions((prev) => {
                              const newOptions = [...prev];
                              newOptions[index].values[valueIndex] = value;
                              return newOptions;
                            })
                          }
                        />
                      </TableCell>
                      <TableActions className='p-0!'>
                        {!isValueUsed(option.name, value) && (
                          <Button
                            onClick={() =>
                              setOptions((prev) => {
                                const newOptions = [...prev];
                                newOptions[index].values.splice(valueIndex, 1);
                                return newOptions;
                              })
                            }
                            variant='ghost'
                          >
                            <XIcon />
                          </Button>
                        )}
                      </TableActions>
                    </TableRow>
                  ))}
                  <TableRow>
                    <TableCell colSpan={2}>
                      <Button
                        className='w-full'
                        onClick={() =>
                          setOptions((prev) =>
                            prev.map((option, i) =>
                              i === index ? {...option, values: [...option.values, '']} : option,
                            ),
                          )
                        }
                        variant='secondary'
                      >
                        <PlusIcon />
                        {t('new value')}
                      </Button>
                    </TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </div>
          ))}
        </SheetContent>
        <SheetFooter className='mt-4 px-6'>
          <SheetClose
            asChild
            disabled={
              !!options.find((option) => option.values.length === 0 || options.length === 0) || options.length === 0
            }
            onClick={() => updateVariants(id, options)}
          >
            <Button>{t('save and generate variants')}</Button>
          </SheetClose>
        </SheetFooter>
      </SheetPage>
    </Sheet>
  );
};

export default InventoryItemsDetailsVariantsButtons;
