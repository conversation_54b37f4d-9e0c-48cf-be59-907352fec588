import {FC} from 'react';

import Jo<PERSON> from 'joi';
import {find} from 'lodash';
import {CheckIcon, Trash2Icon, XIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';
import {useFormContext} from 'react-hook-form';

import {Badge} from '@/components/ui/Badge';
import {Button} from '@/components/ui/Button';
import {PopoverInput} from '@/components/ui/Input';
import {Link} from '@/components/ui/special/Link';
import {TableActions, TableCell, TableRow} from '@/components/ui/Table';
import {useHasPermission} from '@/hooks/helpers/useHasPermission';
import useItemActions from '@/hooks/useItemActions';
import {InventoryItem} from '@/types/inventory';
import {formatCurrency} from '@/utils/format';

type Props = {
  variant: InventoryItem;
};

const InventoryItemsDetailsVariantsTableRow: FC<Props> = ({variant}) => {
  const t = useTranslations();
  const {watch} = useFormContext<InventoryItem>();
  const {deleteItem, updateItem} = useItemActions();
  const {hasPermission} = useHasPermission();

  return (
    <TableRow>
      <TableCell className='inline-flex w-full items-center justify-between gap-2'>
        <Link className='group flex flex-col whitespace-nowrap' href={`/inventory/items/${variant.id}`}>
          <div className='group-hover:underline'>{variant.name}</div>
          <div className='text-xs text-border-foreground'>{`${t('sku')}: ${variant.code}`}</div>
        </Link>
      </TableCell>
      {watch('variantOptions')?.map((option, index) => (
        <TableCell key={`${option.name}-${index}`}>
          {find(variant.appliedVariantOptions, (applied) => applied.name === option.name)?.value}
        </TableCell>
      ))}
      {hasPermission('financial', 'inventory') && (
        <>
          <TableCell className='text-right'>
            <PopoverInput
              className='justify-end'
              defaultValue={variant.sellPrice.amount.toString()}
              label={`${t('selling price')} (${variant.sellPrice.currency})`}
              onChange={(value) =>
                updateItem(variant.id, {
                  ...variant,
                  sellPrice: {amount: Number(value), currency: variant.sellPrice.currency},
                })
              }
              validation={Joi.number().required().positive().allow(0)}
            >
              {formatCurrency(variant.sellPrice)}
            </PopoverInput>
          </TableCell>
          <TableCell className='text-right'>{formatCurrency(variant.estimatedMaterialCost)}</TableCell>
          <TableCell className='text-right'>{formatCurrency(variant.estimatedProductionCost)}</TableCell>
        </>
      )}
      <TableCell className='text-right'>
        <Badge className='gap-1' variant={variant.stock >= variant.criticalOnHand ? 'success' : 'error'}>
          {variant.stock >= variant.criticalOnHand && <CheckIcon className='size-4 text-green' />}
          {variant.stock < variant.criticalOnHand && <XIcon className='size-4 text-red' />}
          {t(variant.stock >= variant.criticalOnHand ? 'available' : 'unavailable', {isPlural: 'false'})}
        </Badge>
      </TableCell>
      <TableCell className='text-right'>{variant.stock}</TableCell>
      <TableActions>
        <Button onClick={() => deleteItem(variant.id, variant.name, variant.parentId)} size='icon' variant='none'>
          <Trash2Icon className='size-5 text-red' strokeWidth={1} />
        </Button>
      </TableActions>
    </TableRow>
  );
};

export default InventoryItemsDetailsVariantsTableRow;
