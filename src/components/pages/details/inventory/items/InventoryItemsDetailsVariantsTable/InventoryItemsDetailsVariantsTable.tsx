import {FC} from 'react';

import {useAtomValue} from 'jotai';
import {useTranslations} from 'next-intl';
import {useFormContext} from 'react-hook-form';

import {detailsDataShownAtom} from '@/components/pages/details/detailsStore';
import {
  Table,
  TableBody,
  TableContainer,
  TableHead,
  TableHeadActions,
  TableHeader,
  TableRow,
} from '@/components/ui/Table';
import {useHasPermission} from '@/hooks/helpers/useHasPermission';
import useHeight from '@/hooks/helpers/useHeight';
import useVariants from '@/hooks/useVariants';
import {InventoryItem} from '@/types/inventory';

import InventoryItemsDetailsVariantsTableRow from './InventoryItemsDetailsVariantsTableRow';

const InventoryItemsDetailsVariantsTable: FC = () => {
  const t = useTranslations();
  const {watch} = useFormContext<InventoryItem>();
  const detailsDataShown = useAtomValue(detailsDataShownAtom(watch('id')));
  const {elementRef} = useHeight({dependencies: [detailsDataShown]});
  const {isLoading, variants} = useVariants(watch('id'));
  const {hasPermission} = useHasPermission();

  return (
    <TableContainer ref={elementRef}>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>{t('name')}</TableHead>
            {watch('variantOptions')?.map((option, index) => (
              <TableHead key={`${option.name}-${index}`}>{option.name}</TableHead>
            ))}
            {hasPermission('financial', 'inventory') && (
              <>
                <TableHead className='text-right'>{t('selling price')}</TableHead>
                <TableHead className='text-right'>{t('material cost')}</TableHead>
                <TableHead className='text-right'>{t('production cost')}</TableHead>
              </>
            )}
            <TableHead className='text-right'>{t('available', {isPlural: 'false'})}</TableHead>
            <TableHead className='text-right'>{t('quantity')}</TableHead>
            <TableHeadActions />
          </TableRow>
        </TableHeader>
        <TableBody className='overflow-y-hidden' isValidating={isLoading}>
          {(variants || [])?.map((variant, index) => (
            <InventoryItemsDetailsVariantsTableRow key={`${variant.id}-${index}`} variant={variant} />
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );
};

export default InventoryItemsDetailsVariantsTable;
