import {FC, useCallback, useState} from 'react';

import {PlusIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';
import {useFieldArray, UseFieldArrayAppend, useFormContext} from 'react-hook-form';

import {Button} from '@/components/ui/Button';
import AddItemModal from '@/components/ui/special/AddItemModal/AddItemModal';
import useItemActions from '@/hooks/useItemActions';
import {GoodsAccompanyingNote} from '@/types/global';
import {classes} from '@/utils/common';

type AddProps = {
  excludeIds: string[];
  onAdd: UseFieldArrayAppend<GoodsAccompanyingNote, 'items'>;
  onClose: () => void;
};

const AddItem: FC<AddProps> = ({excludeIds, onAdd, onClose}) => {
  const {getItem} = useItemActions();

  const handleAdd = useCallback(
    (values: {id: string; quantity: number}[]) => {
      values.forEach(async ({id, quantity}) => {
        const item = await getItem(id);

        onAdd({
          ...item,
          discount: 0,
          quantity,
          unitPrice: item.sellPrice,
        });
      });

      onClose();
    },
    [getItem, onAdd, onClose],
  );

  return <AddItemModal create='none' excludeIds={excludeIds} onAdd={handleAdd} onClose={onClose} />;
};

type Props = {
  className?: string;
};

const AccompanyingNotesCreateEditButtons: FC<Props> = ({className}) => {
  const t = useTranslations();
  const [addEnabled, setAddEnabled] = useState(false);
  const {control, watch} = useFormContext<GoodsAccompanyingNote>();
  const {append} = useFieldArray({control, name: 'items'});

  return (
    <div className={classes('flex items-center gap-4', className)}>
      <Button onClick={() => setAddEnabled(true)} variant='secondary'>
        <PlusIcon /> {t('add material')}
      </Button>
      {addEnabled && (
        <AddItem
          excludeIds={watch('items')?.map((items) => items.id) ?? []}
          onAdd={append}
          onClose={() => setAddEnabled(false)}
        />
      )}
    </div>
  );
};

export default AccompanyingNotesCreateEditButtons;
