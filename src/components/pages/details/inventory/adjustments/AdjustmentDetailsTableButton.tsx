import {FC, useCallback, useMemo, useState} from 'react';

import {find} from 'lodash';
import {PlusIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';
import {useFieldArray, useFormContext} from 'react-hook-form';

import {Button} from '@/components/ui/Button';
import AddItemModal from '@/components/ui/special/AddItemModal/AddItemModal';
import useInventoryUnits from '@/hooks/useInventoryUnits';
import useItemActions from '@/hooks/useItemActions';
import useSettings from '@/hooks/useSettings';
import {Adjustment, InventoryEntry} from '@/types/inventory';
import {classes} from '@/utils/common';
import useItemStockActions from 'hooks/useItemStockActions';

type AddProps = {
  excludeIds: (null | string | undefined)[];
  onAdd: (value: InventoryEntry) => void;
  onClose: () => void;
};

const AddItem: FC<AddProps> = ({excludeIds, onAdd, onClose}) => {
  const {getItemStocks} = useItemStockActions();
  const {settings} = useSettings();
  const {inventoryUnits} = useInventoryUnits();
  const {getItem} = useItemActions();

  const unit = useMemo(
    () =>
      find(
        inventoryUnits,
        (unit) => unit.id === settings.general?.inventoryAccountingSettings?.unitDesignations?.defaultInventoryUnit,
      ),
    [inventoryUnits, settings.general?.inventoryAccountingSettings?.unitDesignations?.defaultInventoryUnit],
  );

  const handleAdd = useCallback(
    (values: {id: string}[]) => {
      values.forEach(async ({id}) => {
        const item = await getItem(id);

        const stocks = await getItemStocks(id, {
          inventoryUnitIds: [settings.general?.inventoryAccountingSettings?.unitDesignations?.defaultInventoryUnit],
          withoutCommited: true,
        });

        if (!unit || !item) return;

        onAdd({
          expiryDate: null,
          fromUnit: null,
          locationId: null,
          markup: 0,
          materialGood: {
            code: item.code,
            id: item.id,
            measurementUnit: item.measurementUnit,
            name: item.name,
          },
          originalFromQuantity: stocks,
          originalQuantity: stocks,
          price: item.inventoryCostPerItem,
          purchaseDate: null,
          quantity: 0,
          supplier: null,
          unit,
        });
      });

      onClose();
    },
    [
      getItem,
      getItemStocks,
      onAdd,
      onClose,
      settings.general?.inventoryAccountingSettings?.unitDesignations?.defaultInventoryUnit,
      unit,
    ],
  );

  return <AddItemModal create='none' excludeIds={excludeIds} onAdd={handleAdd} onClose={onClose} withoutQuantity />;
};

type Props = {
  className?: string;
};

const AdjustmentDetailsTableButton: FC<Props> = ({className}) => {
  const t = useTranslations();
  const [addEnabled, setAddEnabled] = useState(false);
  const {control, watch} = useFormContext<Adjustment>();
  const {append} = useFieldArray({control, name: 'inventoryEntries'});

  return (
    <div className={classes('flex items-center gap-4', className)}>
      <Button
        onClick={() => {
          setAddEnabled(true);
        }}
        variant='secondary'
      >
        <PlusIcon /> {t('add item')}
      </Button>
      {addEnabled && (
        <AddItem
          excludeIds={watch('inventoryEntries')?.map((item) => item.materialGood.id) ?? []}
          onAdd={append}
          onClose={() => {
            setAddEnabled(false);
          }}
        />
      )}
    </div>
  );
};

export default AdjustmentDetailsTableButton;
