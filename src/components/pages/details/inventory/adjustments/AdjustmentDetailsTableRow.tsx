import {FC} from 'react';

import Joi from 'joi';
import {Trash2Icon} from 'lucide-react';
import {useTranslations} from 'next-intl';
import {useFieldArray, useFormContext} from 'react-hook-form';

import {Button} from '@/components/ui/Button';
import {PopoverInput} from '@/components/ui/Input';
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from '@/components/ui/Select';
import InventoryItemLink from '@/components/ui/special/InventoryItemLink/InventoryItemLink';
import {TableActions, TableCell, TableRow} from '@/components/ui/Table';
import {useHasPermission} from '@/hooks/helpers/useHasPermission';
import useInventoryUnits from '@/hooks/useInventoryUnits';
import useItemStockActions from '@/hooks/useItemStockActions';
import {Adjustment, InventoryEntry} from '@/types/inventory';
import {formatCurrency, formatNumber} from '@/utils/format';

type Props = {
  index: number;
  item: InventoryEntry;
};

const AdjustmentDetailsTableRow: FC<Props> = ({index, item}) => {
  const t = useTranslations();
  const {hasPermission, isLoading} = useHasPermission();
  const {getItemStocks} = useItemStockActions();
  const {inventoryUnits, isValidating: inventoryUnitsIsLoading} = useInventoryUnits();
  const {
    control,
    formState: {errors},
    watch,
  } = useFormContext<Adjustment>();
  const {remove, update} = useFieldArray({control, name: 'inventoryEntries'});

  if (isLoading || inventoryUnitsIsLoading) return null;

  return (
    <>
      <TableRow>
        <TableCell className='inline-flex w-full items-center justify-between gap-2'>
          <InventoryItemLink item={item.materialGood} />
        </TableCell>
        <TableCell className='text-right'>
          {formatNumber(item.quantity || 0, true)} {t(`unit.name.${item.materialGood.measurementUnit.name}` as any)}
        </TableCell>
        <TableCell>
          {watch('id') && item.unit?.name}
          {!watch('id') && (
            <Select
              onValueChange={async (value) => {
                const unit = inventoryUnits.find((unit) => unit.id === value);
                const newItem = item;

                if (unit) {
                  const stocks = await getItemStocks(item.materialGood.id, {
                    inventoryUnitIds: [unit?.id],
                    withoutCommited: true,
                  });

                  newItem.originalQuantity = stocks;
                  newItem.originalFromQuantity = stocks;
                  newItem.quantity = 0;

                  update(index, {...newItem, unit});
                }
              }}
              value={item.unit?.id}
            >
              <SelectTrigger className='w-fit' size='badge-md' variant='badge-default'>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {inventoryUnits.map((type) => (
                  <SelectItem key={type.id} value={type.id}>
                    {type.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          )}
        </TableCell>
        {!watch('id') && (
          <>
            <TableCell className='text-right'>
              <PopoverInput
                className='w-fit justify-end'
                defaultValue={((item.originalQuantity || 0) + item.quantity).toFixed(0)}
                error={!!errors.inventoryEntries?.[index]?.quantity}
                label={t('current stock')}
                onChange={(value) => update(index, {...item, quantity: Number(value) - (item.originalQuantity || 0)})}
                validation={Joi.number().min(0).required()}
              >
                {((item.originalQuantity || 0) + item.quantity).toFixed(0)}{' '}
                {t(`unit.name.${item.materialGood.measurementUnit.name.toLowerCase()}` as any)}
              </PopoverInput>
            </TableCell>
            <TableCell className='text-right'>
              {item.originalQuantity} {t(`unit.name.${item.materialGood.measurementUnit.name.toLowerCase()}` as any)}
            </TableCell>
          </>
        )}
        {hasPermission('financial', 'inventory') && (
          <>
            <TableCell className='text-right'>
              <PopoverInput
                className='w-fit justify-end'
                defaultValue={item.price.amount.toString()}
                disabled={!!watch('id')}
                error={!!errors.inventoryEntries?.[index]?.price}
                label={t('cost per unit')}
                onChange={(value) =>
                  update(index, {...item, price: {amount: Number(value), currency: item.price.currency}})
                }
                validation={Joi.number().positive().required()}
              >
                {formatCurrency(item.price)}
              </PopoverInput>
            </TableCell>
            <TableCell className='text-right'>
              {formatCurrency({
                amount: item.quantity * item.price.amount,
                currency: item.price.currency,
              })}
            </TableCell>
          </>
        )}
        {!watch('id') && (
          <TableActions>
            <Button onClick={() => remove(index)} size='icon' variant='none'>
              <Trash2Icon className='size-5 text-red' strokeWidth={1} />
            </Button>
          </TableActions>
        )}
      </TableRow>
    </>
  );
};

export default AdjustmentDetailsTableRow;
