import {FC, useEffect} from 'react';

import {useAtom} from 'jotai';
import {ArrowLeftIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';
import {FormProvider, useFieldArray} from 'react-hook-form';

import {Button} from '@/components/ui/Button';
import {Page, PageContent, PageHeader, PageHeaderTitle, PageTitle} from '@/components/ui/special/Page';
import {useRouter} from '@/hooks/helpers/useRouter';
import useInventoryUnits from '@/hooks/useInventoryUnits';
import useItemStockActions from '@/hooks/useItemStockActions';
import useInventoryAdjustment from 'hooks/useInventoryAdjustment';

import AdjustmentDetailsData from './AdjustmentDetailsData';
import AdjustmentDetailsTable from './AdjustmentDetailsTable';
import {adjustmentItemsAtom} from './adjustmentStore';

type Props = {
  id?: string;
};

const AdjustmentDetails: FC<Props> = ({id}) => {
  const t = useTranslations();
  const {
    isDirty,
    isLoading,
    saveAdjustment,
    useFormActions: {
      control,
      formState: {errors, ...restUseFormState},
      register,
      resetField,
      setValue,
      watch,
      ...restUseFormActions
    },
  } = useInventoryAdjustment(id);
  const {append} = useFieldArray({control, name: 'inventoryEntries'});

  const {back} = useRouter();
  const [adjustmentItems, setAdjustmentItems] = useAtom(adjustmentItemsAtom);
  const {inventoryUnits} = useInventoryUnits();
  const {getItemStocks} = useItemStockActions();
  const {
    query: {inventory},
  } = useRouter();

  useEffect(() => {
    if (
      !id &&
      inventory &&
      adjustmentItems &&
      adjustmentItems.length > 0 &&
      watch('inventoryEntries') &&
      watch('inventoryEntries').length === 0
    ) {
      adjustmentItems.forEach(async (item) => {
        append({
          expiryDate: null,
          fromUnit: null,
          locationId: null,
          markup: 0,
          materialGood: {
            code: item.code,
            id: item.id,
            measurementUnit: item.measurementUnit,
            name: item.name,
          },
          originalFromQuantity: item.quantity,
          originalQuantity: item.available,
          price: item.cost,
          purchaseDate: null,
          quantity: item.quantity - item.available,
          supplier: null,
          unit: item.unit,
        });
      });
      setAdjustmentItems([]);
    }
  }, [adjustmentItems, append, getItemStocks, id, inventory, inventoryUnits, setAdjustmentItems, watch]);

  if (isLoading) return null;

  return (
    <Page>
      <PageTitle>{`${watch('number') || t('new adjustment')} - ${t('adjustments')}`}</PageTitle>
      <PageHeader>
        <PageHeaderTitle className='inline-flex items-center gap-2'>
          <Button onClick={() => back(`/inventory/adjustments`)} size='icon' variant='none'>
            <ArrowLeftIcon />
          </Button>
          <div className='mr-2'>{watch('number') || t('new adjustment')}</div>
        </PageHeaderTitle>
        <div className='grow' />
        {isDirty && <Button onClick={() => saveAdjustment()}>{t('save')}</Button>}
      </PageHeader>
      <PageContent>
        <FormProvider
          {...{
            control,
            formState: {errors, ...restUseFormState},
            register,
            resetField,
            setValue,
            watch,
            ...restUseFormActions,
          }}
        >
          <AdjustmentDetailsData />
          <AdjustmentDetailsTable />
        </FormProvider>
      </PageContent>
    </Page>
  );
};

export default AdjustmentDetails;
