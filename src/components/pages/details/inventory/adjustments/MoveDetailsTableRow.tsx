import {FC} from 'react';

import Jo<PERSON> from 'joi';
import {MoveRightIcon, Trash2Icon} from 'lucide-react';
import {useTranslations} from 'next-intl';
import {useFieldArray, useFormContext} from 'react-hook-form';

import {Button} from '@/components/ui/Button';
import {PopoverInput} from '@/components/ui/Input';
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from '@/components/ui/Select';
import InventoryItemLink from '@/components/ui/special/InventoryItemLink/InventoryItemLink';
import {TableActions, TableCell, TableRow} from '@/components/ui/Table';
import {Tooltip, TooltipContent, TooltipTrigger} from '@/components/ui/Tooltip';
import {useHasPermission} from '@/hooks/helpers/useHasPermission';
import useInventoryUnits from '@/hooks/useInventoryUnits';
import {Adjustment, InventoryEntry} from '@/types/inventory';
import {formatCurrency, formatNumber} from '@/utils/format';
import useItemStockActions from 'hooks/useItemStockActions';

type Props = {
  index: number;
  item: InventoryEntry;
};

const MoveDetailsTableRow: FC<Props> = ({index, item}) => {
  const t = useTranslations();
  const {hasPermission, isLoading} = useHasPermission();
  const {inventoryUnits, isValidating: inventoryUnitsIsLoading} = useInventoryUnits();
  const {getItemStocks} = useItemStockActions();
  const {
    control,
    formState: {errors},
    watch,
  } = useFormContext<Adjustment>();
  const {remove, update} = useFieldArray({control, name: 'inventoryEntries'});

  if (isLoading || inventoryUnitsIsLoading) return null;

  return (
    <>
      <TableRow>
        <TableCell className='inline-flex w-full items-center justify-between gap-2'>
          <InventoryItemLink item={item.materialGood} />
        </TableCell>
        <TableCell className='text-right'>
          {formatNumber(item.quantity || 0, true)} {t(`unit.name.${item.materialGood.measurementUnit.name}` as any)}
        </TableCell>
        <TableCell>
          <div className='flex w-fit flex-col items-center gap-1.5'>
            {watch('id') && item.fromUnit?.name}
            {!watch('id') && (
              <Select
                onValueChange={async (value) => {
                  const unit = inventoryUnits.find((unit) => unit.id === value);

                  if (!unit) return;

                  const stocks = await getItemStocks(item.materialGood.id, {
                    inventoryUnitIds: [value],
                    withoutCommited: true,
                  });

                  update(index, {...item, fromUnit: unit, originalFromQuantity: stocks, quantity: 0});
                }}
                value={item.fromUnit?.id}
              >
                <SelectTrigger
                  className='w-fit'
                  error={item.fromUnit?.id === item.unit.id}
                  size='badge-md'
                  variant='badge-default'
                >
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {inventoryUnits.map((type) => (
                    <SelectItem key={type.id} value={type.id}>
                      {type.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
            <div className='flex items-center'>
              <div className='flex items-center gap-1'>
                <Tooltip>
                  <TooltipTrigger>{formatNumber(item.originalFromQuantity)}</TooltipTrigger>
                  <TooltipContent>{t('before stock')}</TooltipContent>
                </Tooltip>

                <MoveRightIcon className='size-5 fill-black' strokeWidth={1.5} />

                <Tooltip>
                  <TooltipTrigger>{formatNumber((item.originalFromQuantity || 0) - item.quantity)}</TooltipTrigger>
                  <TooltipContent>{t('after stock')}</TooltipContent>
                </Tooltip>
              </div>
            </div>
          </div>
        </TableCell>
        <TableCell>
          <div className='flex w-fit flex-col items-center gap-1.5'>
            {watch('id') && item.unit.name}
            {!watch('id') && (
              <Select
                onValueChange={async (value) => {
                  const unit = inventoryUnits.find((unit) => unit.id === value);

                  if (!unit) return;

                  const stocks = await getItemStocks(item.materialGood.id, {
                    inventoryUnitIds: [value],
                    withoutCommited: true,
                  });

                  update(index, {...item, originalQuantity: stocks, quantity: 0, unit});
                }}
                value={item.unit.id}
              >
                <SelectTrigger
                  className='w-fit'
                  error={item.fromUnit?.id === item.unit.id}
                  size='badge-md'
                  variant='badge-default'
                >
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {inventoryUnits.map((type) => (
                    <SelectItem key={type.id} value={type.id}>
                      {type.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
            <div className='flex items-center'>
              <div className='flex items-center gap-1'>
                <Tooltip>
                  <TooltipTrigger>{formatNumber(item.originalQuantity)}</TooltipTrigger>
                  <TooltipContent>{t('before stock')}</TooltipContent>
                </Tooltip>

                <MoveRightIcon className='size-5 fill-black' strokeWidth={1.5} />

                <Tooltip>
                  <TooltipTrigger>{formatNumber((item.originalQuantity || 0) + item.quantity)}</TooltipTrigger>
                  <TooltipContent>{t('after stock')}</TooltipContent>
                </Tooltip>
              </div>
            </div>
          </div>
        </TableCell>
        {!watch('id') && (
          <>
            <TableCell className='text-right'>
              <PopoverInput
                className='w-fit justify-end'
                defaultValue={((item.originalQuantity || 0) + item.quantity).toFixed(0)}
                error={!!errors.inventoryEntries?.[index]?.quantity}
                label={t('current stock')}
                onChange={(value) => update(index, {...item, quantity: Number(value) - (item.originalQuantity || 0)})}
                validation={Joi.number()
                  .min(item.originalQuantity || 0)
                  .required()
                  .max((item.originalFromQuantity || 0) + (item.originalQuantity || 0))}
              >
                {((item.originalQuantity || 0) + item.quantity).toFixed(0)}{' '}
                {t(`unit.name.${item.materialGood.measurementUnit.name.toLowerCase()}` as any)}
              </PopoverInput>
            </TableCell>
            <TableCell className='text-right'>
              {item.originalFromQuantity}{' '}
              {t(`unit.name.${item.materialGood.measurementUnit.name.toLowerCase()}` as any)}
            </TableCell>
            <TableCell className='text-right'>
              {item.originalQuantity} {t(`unit.name.${item.materialGood.measurementUnit.name.toLowerCase()}` as any)}
            </TableCell>
          </>
        )}
        {hasPermission('financial', 'inventory') && (
          <>
            <TableCell className='text-right'>
              <PopoverInput
                className='w-fit justify-end'
                defaultValue={item.price.amount.toString()}
                disabled={!!watch('id')}
                error={!!errors.inventoryEntries?.[index]?.price}
                label={t('cost per unit')}
                onChange={(value) =>
                  update(index, {...item, price: {amount: Number(value), currency: item.price.currency}})
                }
                validation={Joi.number().positive().required()}
              >
                {formatCurrency(item.price)}
              </PopoverInput>
            </TableCell>
            <TableCell className='text-right'>
              {formatCurrency({
                amount: item.quantity * item.price.amount,
                currency: item.price.currency,
              })}
            </TableCell>
          </>
        )}
        {!watch('id') && (
          <TableActions>
            <Button onClick={() => remove(index)} size='icon' variant='none'>
              <Trash2Icon className='size-5 text-red' strokeWidth={1} />
            </Button>
          </TableActions>
        )}
      </TableRow>
    </>
  );
};

export default MoveDetailsTableRow;
