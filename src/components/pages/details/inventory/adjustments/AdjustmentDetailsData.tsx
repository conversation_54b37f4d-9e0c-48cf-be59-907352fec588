import {FC} from 'react';

import {useTranslations} from 'next-intl';
import {useFormContext} from 'react-hook-form';

import {InputLabel} from '@/components/ui/Input';
import {WithLabel} from '@/components/ui/Label';
import {TextareaAutosize} from '@/components/ui/Textarea';
import {Adjustment} from '@/types/inventory';

const AdjustmentDetailsData: FC = () => {
  const t = useTranslations();
  const {
    formState: {errors},
    register,
    watch,
  } = useFormContext<Adjustment>();

  return (
    <div className='mx-6 mt-6 rounded-lg border border-border p-6'>
      <WithLabel className='w-full'>
        <TextareaAutosize
          className='w-full'
          disabled={!!watch('id')}
          {...register('reason')}
          error={!!errors.reason}
          maxRows={8}
          minRows={2}
        />
        <InputLabel>{t('adjustment reason')}</InputLabel>
      </WithLabel>
    </div>
  );
};

export default AdjustmentDetailsData;
