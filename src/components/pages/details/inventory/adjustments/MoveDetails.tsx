import {FC} from 'react';

import {ArrowLeftIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';
import {FormProvider} from 'react-hook-form';

import {Button} from '@/components/ui/Button';
import {Page, PageContent, PageHeader, PageHeaderTitle, PageTitle} from '@/components/ui/special/Page';
import {useRouter} from '@/hooks/helpers/useRouter';
import useInventoryAdjustment from 'hooks/useInventoryAdjustment';

import MoveDetailsData from './MoveDetailsData';
import MoveDetailsTable from './MoveDetailsTable';

type Props = {
  id?: string;
};

const MoveDetails: FC<Props> = ({id}) => {
  const t = useTranslations();
  const {
    isDirty,
    isLoading,
    saveMove,
    useFormActions: {
      formState: {errors, ...restUseFormState},
      register,
      resetField,
      setValue,
      watch,
      ...restUseFormActions
    },
  } = useInventoryAdjustment(id);
  const {back} = useRouter();

  if (isLoading) return null;

  return (
    <Page>
      <PageTitle>{`${id ? watch('number') : t('transfer between units')} - ${t('adjustments')}`}</PageTitle>
      <PageHeader>
        <PageHeaderTitle className='inline-flex items-center gap-2'>
          <Button onClick={() => back(`/inventory/adjustments`)} size='icon' variant='none'>
            <ArrowLeftIcon />
          </Button>
          <div className='mr-2'>{watch('number') || t('transfer between units')}</div>
        </PageHeaderTitle>
        <div className='grow' />
        {isDirty && <Button onClick={() => saveMove()}>{t('save')}</Button>}
      </PageHeader>
      <PageContent>
        <FormProvider
          {...{formState: {errors, ...restUseFormState}, register, resetField, setValue, watch, ...restUseFormActions}}
        >
          <MoveDetailsData />
          <MoveDetailsTable />
        </FormProvider>
      </PageContent>
    </Page>
  );
};

export default MoveDetails;
