import {FC} from 'react';

import {useTranslations} from 'next-intl';
import {useFormContext} from 'react-hook-form';

import {Input, InputLabel} from '@/components/ui/Input';
import {WithLabel} from '@/components/ui/Label';
import {TextareaAutosize} from '@/components/ui/Textarea';
import {Adjustment} from '@/types/inventory';

const MoveDetailsData: FC = () => {
  const t = useTranslations();
  const {
    formState: {errors},
    register,
    watch,
  } = useFormContext<Adjustment>();

  return (
    <div className='mx-6 mt-6 flex items-start gap-4'>
      <div className='flex flex-col gap-4 rounded-lg border border-border p-6'>
        <WithLabel className='mb-[3px]'>
          <Input disabled={!!watch('id')} value={watch('deliveredBy')} />
          <InputLabel>{t('delivered by')}</InputLabel>
        </WithLabel>
        <WithLabel>
          <Input disabled={!!watch('id')} value={watch('receivedBy')} />
          <InputLabel>{t('received by')}</InputLabel>
        </WithLabel>
      </div>
      <WithLabel className='w-full rounded-lg border border-border p-6'>
        <TextareaAutosize
          className='w-full'
          disabled={!!watch('id')}
          {...register('reason')}
          error={!!errors.reason}
          maxRows={8}
          minRows={5}
        />
        <InputLabel>{t('transfer reason')}</InputLabel>
      </WithLabel>
    </div>
  );
};

export default MoveDetailsData;
