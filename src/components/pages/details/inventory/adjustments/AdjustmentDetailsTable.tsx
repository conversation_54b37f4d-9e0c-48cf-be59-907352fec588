import {FC} from 'react';

import {useTranslations} from 'next-intl';
import {useFormContext} from 'react-hook-form';

import {
  Table,
  TableBody,
  TableContainer,
  TableHead,
  TableHeadActions,
  TableHeader,
  TableRow,
} from '@/components/ui/Table';
import {TabsMenuItem} from '@/components/ui/Tabs';
import {useHasPermission} from '@/hooks/helpers/useHasPermission';
import useHeight from '@/hooks/helpers/useHeight';
import {Adjustment} from '@/types/inventory';
import {formatDate} from '@/utils/format';

import AdjustmentDetailsTableButton from './AdjustmentDetailsTableButton';
import AdjustmentDetailsTableRow from './AdjustmentDetailsTableRow';

const AdjustmentDetailsTable: FC = () => {
  const {elementRef} = useHeight();
  const {
    formState: {errors},
    watch,
  } = useFormContext<Adjustment>();
  const {hasPermission, isLoading} = useHasPermission();
  const t = useTranslations();

  return (
    <>
      <div className='mr-6 flex items-center justify-between'>
        <TabsMenuItem
          badge={watch('inventoryEntries')?.length || 0}
          disabled
          error={!!errors?.inventoryEntries && watch('inventoryEntries').length === 0}
        >
          {t('items')}
        </TabsMenuItem>
        {!watch('id') && <AdjustmentDetailsTableButton />}
        {watch('id') && (
          <>
            {t('adjusted on')} {formatDate(watch('createTime'))}
          </>
        )}
      </div>
      <TableContainer ref={elementRef}>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>{t('item')}</TableHead>
              <TableHead className='text-right'>{t('adjustment')}</TableHead>
              <TableHead>{t('inventory unit')}</TableHead>
              {!watch('id') && (
                <>
                  <TableHead className='text-right'>{t('current stock')}</TableHead>
                  <TableHead className='text-right'>{t('previous stock')}</TableHead>
                </>
              )}
              {hasPermission('financial', 'inventory') && (
                <>
                  <TableHead className='text-right'>{t('cost per unit')}</TableHead>
                  <TableHead className='text-right'>{t('adjustment value')}</TableHead>
                </>
              )}
              {!watch('id') && <TableHeadActions />}
            </TableRow>
          </TableHeader>
          <TableBody className='overflow-y-hidden' isValidating={isLoading}>
            {watch('inventoryEntries')?.map((item, index) => (
              <AdjustmentDetailsTableRow index={index} item={item} key={`${item.materialGood.id}-row-${index}`} />
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </>
  );
};

export default AdjustmentDetailsTable;
