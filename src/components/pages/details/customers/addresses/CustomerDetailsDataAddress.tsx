import {FC, useState} from 'react';

import {PlusIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';
import {useFormContext} from 'react-hook-form';

import {IconButton} from '@/components/ui/Button';
import {Customer} from '@/types/sales';

import CustomerDetailsDataAddressRow from './CustomerDetailsDataAddressRow';

type Props = {
  onSave: () => void;
};

const CustomerDetailsDataAddress: FC<Props> = ({onSave}) => {
  const t = useTranslations();
  const [addEnabled, setAddEnabled] = useState(false);
  const {watch} = useFormContext<Customer>();

  return (
    <div className='flex flex-col gap-4'>
      <div className='text-base font-medium'>{t('addresses')}</div>
      {(watch('addresses') || []).map((address, index) => (
        <CustomerDetailsDataAddressRow
          address={address}
          index={index}
          key={`${address.name}-${index}`}
          onSave={onSave}
        />
      ))}
      {!addEnabled && (
        <IconButton className='self-start' icon={<PlusIcon />} onClick={() => setAddEnabled(true)}>
          {t('add address')}
        </IconButton>
      )}
      {addEnabled && <CustomerDetailsDataAddressRow onCancel={() => setAddEnabled(false)} onSave={onSave} />}
    </div>
  );
};

export default CustomerDetailsDataAddress;
