import {FC} from 'react';

import {useTranslations} from 'next-intl';
import {useFormContext} from 'react-hook-form';

import {
  Table,
  TableBody,
  TableContainer,
  TableHead,
  TableHeadActions,
  TableHeader,
  TableRow,
} from '@/components/ui/Table';
import {Customer} from '@/types/sales';

import CustomerDetailsFilesTableRow from './CustomerDetailsFilesTableRow';

type Props = {
  deleteFile: (id: string) => void;
};

const CustomerDetailsFilesTable: FC<Props> = ({deleteFile}) => {
  const {watch} = useFormContext<Customer>();
  const t = useTranslations();

  return (
    <TableContainer>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>{t('name')}</TableHead>
            <TableHead />
            <TableHeadActions />
          </TableRow>
        </TableHeader>
        <TableBody className='overflow-y-hidden' isValidating={!watch()}>
          {watch('files')?.map((file, index) => (
            <CustomerDetailsFilesTableRow deleteFile={deleteFile} file={file} key={`${file.id}-${index}`} />
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );
};

export default CustomerDetailsFilesTable;
