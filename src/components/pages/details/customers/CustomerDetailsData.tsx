import {FC} from 'react';

import CustomerDetailsDataAddress from './addresses/CustomerDetailsDataAddress';
import CustomerDetailsDataBank from './bankAccounts/CustomerDetailsDataBank';
import CustomerDetailsDataContacts from './contacts/CustomerDetailsDataContacts';

type Props = {
  onSave: () => void;
};

const CustomerDetailsData: FC<Props> = ({onSave}) => {
  return (
    <div className='my-6 ml-6 grid h-fit grid-cols-2 gap-x-20 gap-y-10'>
      <CustomerDetailsDataAddress onSave={onSave} />
      <CustomerDetailsDataContacts onSave={onSave} />
      <CustomerDetailsDataBank onSave={onSave} />
    </div>
  );
};

export default CustomerDetailsData;
