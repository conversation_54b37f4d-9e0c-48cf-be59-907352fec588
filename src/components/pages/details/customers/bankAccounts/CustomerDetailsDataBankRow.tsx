import {FC, useState} from 'react';

import {CheckIcon, PencilIcon, Trash2Icon, XIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';
import {useFieldArray, useFormContext} from 'react-hook-form';

import {Button} from '@/components/ui/Button';
import {Input, InputLabel} from '@/components/ui/Input';
import {WithLabel} from '@/components/ui/Label';
import {BankAccount} from '@/types/global';
import {Customer} from '@/types/sales';
import {classes} from '@/utils/common';

type Props = {
  account?: BankAccount;
  index?: number;
  onCancel?: () => void;
  onSave: () => void;
};

const CustomerDetailsDataBankRow: FC<Props> = ({account, index, onCancel, onSave}) => {
  const t = useTranslations();
  const [editableAccount, setEditableAccount] = useState<BankAccount | undefined>(
    account ? undefined : {bank: '', name: '', number: '', swiftNumber: ''},
  );
  const {control} = useFormContext<Customer>();
  const {append, remove, update} = useFieldArray({control, name: `bankAccounts`});

  const handleClose = () => {
    setEditableAccount(undefined);
    if (index === undefined) onCancel?.();
  };

  return (
    <div
      className={classes(
        'flex items-center justify-between rounded-lg border p-6',
        account?.name && account.bank && account.number ? 'border-border' : 'border-red',
      )}
    >
      {!editableAccount && (
        <>
          <div className='flex flex-col'>
            <div className='text-sm font-medium'>{account?.name}</div>
            <div className='text-sm'>{account?.bank}</div>
            <div className='text-sm'>{account?.number}</div>
            <div className='text-sm'>{account?.swiftNumber}</div>
          </div>
          <Button onClick={() => setEditableAccount(account)} variant='secondary'>
            <PencilIcon className='size-5' /> {t('edit')}
          </Button>
        </>
      )}
      {editableAccount && (
        <div className='flex w-full flex-col gap-4'>
          {!account && <div>{t('add new bank account')}</div>}
          <WithLabel className='w-full'>
            <Input
              defaultValue={editableAccount?.bank}
              error={!editableAccount?.bank}
              onChange={({target: {value}}) => setEditableAccount((prev) => ({...(prev as BankAccount), bank: value}))}
            />
            <InputLabel>{t('bank')}*</InputLabel>
          </WithLabel>
          <div className='flex items-center gap-6'>
            <WithLabel className='w-full'>
              <Input
                defaultValue={editableAccount?.number}
                error={!editableAccount?.number}
                onChange={({target: {value}}) =>
                  setEditableAccount((prev) => ({...(prev as BankAccount), number: value}))
                }
              />
              <InputLabel>{t('number')}*</InputLabel>
            </WithLabel>
            <WithLabel>
              <Input
                defaultValue={editableAccount?.swiftNumber}
                onChange={({target: {value}}) =>
                  setEditableAccount((prev) => ({...(prev as BankAccount), swiftNumber: value}))
                }
              />
              <InputLabel>{t('swift')}</InputLabel>
            </WithLabel>
          </div>
          <WithLabel className='w-full'>
            <Input
              defaultValue={editableAccount?.name}
              error={!editableAccount?.name}
              onChange={({target: {value}}) => setEditableAccount((prev) => ({...(prev as BankAccount), name: value}))}
            />
            <InputLabel>{t('preferential name')}*</InputLabel>
          </WithLabel>
          <div className='flex justify-between'>
            {index !== undefined && (
              <Button
                onClick={() => {
                  remove(index);
                  handleClose();
                }}
                variant='destructive'
              >
                <Trash2Icon className='size-5 text-red' strokeWidth={1} /> {t('delete')}
              </Button>
            )}
            <div className='flex w-full justify-end gap-4'>
              <Button
                onClick={() => {
                  handleClose();
                }}
                variant='secondary'
              >
                <XIcon /> {t('cancel')}
              </Button>
              <Button
                disabled={!editableAccount?.name || !editableAccount.bank || !editableAccount.number}
                onClick={() => {
                  if (index === undefined) {
                    append(editableAccount);
                  } else {
                    update(index, editableAccount);
                  }
                  handleClose();
                  onSave();
                }}
              >
                <CheckIcon /> {t(index === undefined ? 'add' : 'save')}
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CustomerDetailsDataBankRow;
