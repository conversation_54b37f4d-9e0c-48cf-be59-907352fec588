import {FC, useState} from 'react';

import {PlusIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';
import {useFormContext} from 'react-hook-form';

import {IconButton} from '@/components/ui/Button';
import {Customer} from '@/types/sales';

import CustomerDetailsDataContactsRow from './CustomerDetailsDataContactsRow';

type Props = {
  onSave: () => void;
};

const CustomerDetailsDataContacts: FC<Props> = ({onSave}) => {
  const t = useTranslations();
  const [addEnabled, setAddEnabled] = useState(false);
  const {watch} = useFormContext<Customer>();

  return (
    <div className='flex flex-col gap-4'>
      <div className='text-base font-medium'>{t('contacts')}</div>
      {(watch('contacts') || []).map((contact, index) => (
        <CustomerDetailsDataContactsRow
          contact={contact}
          index={index}
          key={`${contact.name}-${index}`}
          onSave={onSave}
        />
      ))}
      {!addEnabled && (
        <IconButton className='self-start' icon={<PlusIcon />} onClick={() => setAddEnabled(true)}>
          {t('add contact')}
        </IconButton>
      )}
      {addEnabled && <CustomerDetailsDataContactsRow onCancel={() => setAddEnabled(false)} onSave={onSave} />}
    </div>
  );
};

export default CustomerDetailsDataContacts;
