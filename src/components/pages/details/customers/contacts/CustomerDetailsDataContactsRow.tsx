import {FC, useState} from 'react';

import {CheckIcon, PencilIcon, Trash2Icon, XIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';
import {useFieldArray, useFormContext} from 'react-hook-form';

import {Button} from '@/components/ui/Button';
import {Input, InputLabel} from '@/components/ui/Input';
import {WithLabel} from '@/components/ui/Label';
import {Contact} from '@/types/global';
import {Customer} from '@/types/sales';
import {classes} from '@/utils/common';

type Props = {
  contact?: Contact;
  index?: number;
  onCancel?: () => void;
  onSave: () => void;
};

const CustomerDetailsDataContactsRow: FC<Props> = ({contact, index, onCancel, onSave}) => {
  const t = useTranslations();
  const [editableContact, setEditableContact] = useState<Contact | undefined>(
    contact ? undefined : {email: '', name: '', phoneNumber: ''},
  );
  const {control} = useFormContext<Customer>();
  const {append, remove, update} = useFieldArray({control, name: `contacts`});

  const handleClose = () => {
    setEditableContact(undefined);
    if (index === undefined) onCancel?.();
  };

  return (
    <div
      className={classes(
        'flex items-center justify-between rounded-lg border p-6',
        contact?.name ? 'border-border' : 'border-red',
      )}
    >
      {!editableContact && (
        <>
          <div className='flex flex-col'>
            <div className='text-sm font-medium'>{contact?.name}</div>
            <div className='text-sm'>{contact?.phoneNumber}</div>
            <div className='text-sm'>{contact?.email}</div>
          </div>
          <Button onClick={() => setEditableContact(contact)} variant='secondary'>
            <PencilIcon className='size-5' /> {t('edit')}
          </Button>
        </>
      )}
      {editableContact && (
        <div className='flex w-full flex-col gap-4'>
          {!contact && <div>{t('add new contact')}</div>}
          <WithLabel>
            <Input
              defaultValue={editableContact?.name}
              error={!editableContact?.name}
              onChange={({target: {value}}) =>
                setEditableContact((prev) => ({
                  ...(prev as Contact),
                  name: value,
                }))
              }
            />
            <InputLabel>{t('name')}*</InputLabel>
          </WithLabel>
          <div className='flex items-center gap-6'>
            <WithLabel className='w-full'>
              <Input
                defaultValue={editableContact?.phoneNumber}
                onChange={({target: {value}}) =>
                  setEditableContact((prev) => ({...(prev as Contact), phoneNumber: value}))
                }
              />
              <InputLabel>{t('phone number')}</InputLabel>
            </WithLabel>
            <WithLabel className='w-full'>
              <Input
                defaultValue={editableContact?.email}
                onChange={({target: {value}}) =>
                  setEditableContact((prev) => ({
                    ...(prev as Contact),
                    email: value,
                  }))
                }
              />
              <InputLabel>{t('email')}</InputLabel>
            </WithLabel>
          </div>
          <div className='flex justify-between'>
            {index !== undefined && (
              <Button
                onClick={() => {
                  remove(index);
                  handleClose();
                }}
                variant='destructive'
              >
                <Trash2Icon className='size-5 text-red' strokeWidth={1} /> {t('delete')}
              </Button>
            )}
            <div className='flex w-full justify-end gap-4'>
              <Button
                onClick={() => {
                  handleClose();
                }}
                variant='secondary'
              >
                <XIcon /> {t('cancel')}
              </Button>
              <Button
                disabled={!editableContact?.name}
                onClick={() => {
                  if (index === undefined) {
                    append(editableContact);
                  } else {
                    update(index, editableContact);
                  }
                  handleClose();
                  onSave();
                }}
              >
                <CheckIcon /> {t(index === undefined ? 'add' : 'save')}
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CustomerDetailsDataContactsRow;
