import {FC, useEffect} from 'react';

import Joi from 'joi';
import {useAtom} from 'jotai';
import {ArrowLeftIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';
import {FormProvider} from 'react-hook-form';

import {Button} from '@/components/ui/Button';
import {InputLabel, PopoverInput} from '@/components/ui/Input';
import {WithLabel} from '@/components/ui/Label';
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from '@/components/ui/Select';
import ActivityButton from '@/components/ui/special/ActivityButton';
import {Page, PageContent, PageHeader, PageHeaderTitle, PageTitle} from '@/components/ui/special/Page';
import {Tabs, TabsList, TabsTrigger} from '@/components/ui/Tabs';
import {useHasPermission} from '@/hooks/helpers/useHasPermission';
import {useRouter} from '@/hooks/helpers/useRouter';
import useActivities from '@/hooks/useActivities';
import useCustomer from '@/hooks/useCustomer';
import {ActivityType} from '@/types/global';
import {CustomerType, SaleType} from '@/types/sales';
import {formatCurrency, formatNumber} from '@/utils/format';

import CustomerDetailsData from './CustomerDetailsData';
import CustomerDetailsFilesButtons from './CustomerDetailsFilesTable/CustomerDetailsFilesButtons';
import CustomerDetailsFilesTable from './CustomerDetailsFilesTable/CustomerDetailsFilesTable';
import {CustomerTab, customerTabAtom} from './customerDetailsStore';
import {SalesView} from '../../lists/sales/salesListStore';
import SalesListTable from '../../lists/sales/SalesListTable';

type Props = {
  id: string;
};

const CustomerDetails: FC<Props> = ({id}) => {
  const t = useTranslations();
  const {
    deleteCustomerFile,
    isDirty,
    isLoading,
    saveCustomer,
    uploadCustomerFile,
    useFormActions: {
      formState: {errors, ...restUseFormState},
      register,
      resetField,
      setValue,
      watch,
      ...restUseFormActions
    },
  } = useCustomer(id);
  const {activities, createActivity} = useActivities(id, ActivityType.CUSTOMER);
  const [tab, setTab] = useAtom(customerTabAtom(watch('id')));
  const {back} = useRouter();
  const {hasPermission} = useHasPermission();

  useEffect(() => {
    if (!hasPermission('update', 'sales') && tab === 'orders') setTab('files');
  }, [hasPermission, setTab, tab]);

  if (isLoading) return null;

  return (
    <Page>
      <PageTitle>{`${watch('name') || t('new customer name')} - ${t('customers')}`}</PageTitle>
      <PageHeader>
        <PageHeaderTitle className='inline-flex items-center gap-2'>
          <Button onClick={() => back(`/customers`)} size='icon' variant='none'>
            <ArrowLeftIcon />
          </Button>
          <PopoverInput
            className='mr-2'
            defaultValue={watch('name')}
            error={!!errors?.name}
            label={t('name')}
            onChange={(value) => setValue('name', value)}
            validation={Joi.string().required()}
          >
            {watch('name') || t('new customer name')}
          </PopoverInput>
        </PageHeaderTitle>
        <WithLabel>
          <PopoverInput
            className='mr-2 h-fit'
            defaultValue={watch('taxIdentificationNumber')}
            error={!!errors?.taxIdentificationNumber}
            onChange={(value) => setValue('taxIdentificationNumber', value)}
            validation={Joi.string().required()}
          >
            {watch('taxIdentificationNumber') || t('new tax identification number')}
          </PopoverInput>
          <InputLabel>
            {t(
              watch('type') === CustomerType.LOCAL_LEGAL_ENTITY
                ? 'tax identification number'
                : watch('type') === CustomerType.FOREIGN_LEGAL_ENTITY
                  ? 'foreign tax identification number'
                  : 'individual tax identification number',
            )}
          </InputLabel>
        </WithLabel>
        {watch('type') === CustomerType.LOCAL_LEGAL_ENTITY && (
          <WithLabel>
            <PopoverInput
              className='mr-2 h-fit'
              defaultValue={watch('identificationNumber')}
              error={!!errors?.identificationNumber}
              onChange={(value) => setValue('identificationNumber', value)}
              validation={Joi.string().required()}
            >
              {watch('identificationNumber') || t('new identification number')}
            </PopoverInput>
            <InputLabel>{t('identification number')}</InputLabel>
          </WithLabel>
        )}
        {hasPermission('financial', 'customers') && (
          <WithLabel>
            {formatCurrency(watch('lifetimeValue'))}
            <InputLabel>{t('total revenue from the customer')}</InputLabel>
          </WithLabel>
        )}
        <Select
          onValueChange={(value) => {
            setValue('type', value as CustomerType);
            if (value !== CustomerType.LOCAL_LEGAL_ENTITY) setValue('identificationNumber', '');
          }}
          value={watch('type') || CustomerType.LOCAL_LEGAL_ENTITY}
        >
          <SelectTrigger className='ml-8' size='xl'>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value={CustomerType.LOCAL_LEGAL_ENTITY}>{t(CustomerType.LOCAL_LEGAL_ENTITY)}</SelectItem>
            <SelectItem value={CustomerType.FOREIGN_LEGAL_ENTITY}>{t(CustomerType.FOREIGN_LEGAL_ENTITY)}</SelectItem>
            <SelectItem value={CustomerType.INDIVIDUAL}>{t(CustomerType.INDIVIDUAL)}</SelectItem>
          </SelectContent>
        </Select>
        <div className='grow' />
        {isDirty && <Button onClick={saveCustomer}>{t('save')}</Button>}
        <ActivityButton activities={activities} createActivity={createActivity} />
      </PageHeader>
      <PageContent>
        <FormProvider
          {...{formState: {errors, ...restUseFormState}, register, resetField, setValue, watch, ...restUseFormActions}}
        >
          <CustomerDetailsData onSave={saveCustomer} />
          <div className='mx-6 flex items-center justify-between'>
            <div className='inline-flex items-center'>
              <Tabs onValueChange={(value) => setTab(value as CustomerTab)} value={tab} variant='menu'>
                <TabsList variant='menu'>
                  {hasPermission('update', 'sales') && (
                    <TabsTrigger value='orders' variant='menu'>
                      {t('orders')}
                    </TabsTrigger>
                  )}
                  <TabsTrigger
                    badge={formatNumber(watch('files')?.length)}
                    disabled={!watch('id')}
                    value='files'
                    variant='menu'
                  >
                    {t('files')}
                  </TabsTrigger>
                </TabsList>
              </Tabs>
            </div>
            {tab === 'files' && <CustomerDetailsFilesButtons uploadFile={uploadCustomerFile} />}
          </div>
          {hasPermission('update', 'sales') && tab === 'orders' && (
            <SalesListTable customerId={id} disableHeight preview saleType={SaleType.ORDER} view={SalesView.all} />
          )}
          {tab === 'files' && <CustomerDetailsFilesTable deleteFile={deleteCustomerFile} />}
        </FormProvider>
      </PageContent>
    </Page>
  );
};

export default CustomerDetails;
