import {FC, useCallback} from 'react';

import {ArrowLeftIcon, CheckIcon, CloudDownloadIcon, PlusIcon, Trash2Icon, XIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';
import {FormProvider, useFieldArray} from 'react-hook-form';

import {Button} from '@/components/ui/Button';
import {ComboboxLabel, ControlledCombobox} from '@/components/ui/Combobox';
import {ControlledDatePickerInput} from '@/components/ui/DatePicker';
import {DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger} from '@/components/ui/DropdownMenu';
import {Input, InputLabel, NumberInput} from '@/components/ui/Input';
import {WithLabel} from '@/components/ui/Label';
import CountriesCombobox from '@/components/ui/special/CountriesCombobox';
import {DocumentPreview} from '@/components/ui/special/DocumentPreview';
import {<PERSON>, PageContent, PageHeader, PageHeaderTitle, PageTitle} from '@/components/ui/special/Page';
import {TextareaAutosize} from '@/components/ui/Textarea';
import useAccompanyingNoteDocumentPreview from '@/hooks/documents/useAccompanyingNoteDocumentPreview';
import useHeight from '@/hooks/helpers/useHeight';
import {useRouter} from '@/hooks/helpers/useRouter';
import useAccompanyingNote from '@/hooks/useAccompanyingNote';
import useEmployees from '@/hooks/useEmployees';
import {classes} from '@/utils/common';

import ManufacturingServicesNoteCreateEditButtons from './ManufacturingServicesNoteCreateEditButtons';

type Props = {
  id?: string;
  serviceId: string;
};

const ManufacturingServicesNoteCreate: FC<Props> = ({id, serviceId}) => {
  const {elementRef} = useHeight();
  const {elementRef: containerRef} = useHeight();
  const {back, push} = useRouter();
  const {
    accompanyingNote,
    isDirty,
    saveAccompanyingNote,
    useFormActions: {
      control,
      formState: {errors, ...restUseFormState},
      register,
      setValue,
      watch,
      ...restUseFormActions
    },
  } = useAccompanyingNote(id, {serviceId});
  const t = useTranslations();
  const {accompanyingNoteDocumentPreview} = useAccompanyingNoteDocumentPreview(accompanyingNote, {serviceId});
  const {remove, update} = useFieldArray({control, name: 'items'});
  const {employees} = useEmployees();

  const handleDownload = useCallback(
    (mediaType: string) => {
      push(`/api/goods-accompanying-notes/${id}/details?mediaType=${mediaType}`);
    },
    [push, id],
  );

  return (
    <Page>
      <PageTitle>{`${accompanyingNote.number ? `${accompanyingNote.number} - ` : ''}${t('goods accompanying note')} - ${t('services')} - ${t('manufacturing')}`}</PageTitle>
      <PageHeader>
        <PageHeaderTitle className='inline-flex items-center gap-2'>
          <Button onClick={() => back(`/manufacturing/services/${serviceId}`)} size='icon' variant='none'>
            <ArrowLeftIcon />
          </Button>
          {accompanyingNote.number ? accompanyingNote.number : t('order')}
        </PageHeaderTitle>
        <div className='grow' />
        {id && (
          <DropdownMenu>
            <DropdownMenuTrigger>
              <Button disabled={isDirty} variant='secondary'>
                <CloudDownloadIcon /> {t('download')}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onClick={() => handleDownload('application/pdf')}>{t('pdf')}</DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleDownload('application/factura-saga%2Bxml')}>
                {t('saga')}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        )}
        <Button onClick={saveAccompanyingNote}>
          <CheckIcon />
          {t(id ? 'update goods accompanying note' : 'add goods accompanying note')}
        </Button>
      </PageHeader>
      <PageContent>
        <div className='flex' ref={elementRef}>
          <div className='flex h-full w-3/4 justify-center bg-gray-100 p-4'>
            <DocumentPreview content={accompanyingNoteDocumentPreview} />
          </div>
          <div className='flex w-1/3 flex-col gap-4 overflow-y-auto p-6' ref={containerRef}>
            <div className='flex flex-col gap-2 rounded-lg border p-4'>
              <div className='flex gap-4'>
                <WithLabel>
                  <ControlledDatePickerInput
                    control={control}
                    controlName='deliveryDate'
                    error={!!errors.deliveryDate}
                    size='md'
                  />
                  <InputLabel>{t('delivery date')}*</InputLabel>
                </WithLabel>
                <WithLabel className='w-full'>
                  <Input {...register('transportRegistrationNumber')} error={!!errors.transportRegistrationNumber} />
                  <InputLabel>{t('transport registration number')}*</InputLabel>
                </WithLabel>
              </div>
              <WithLabel>
                <ControlledCombobox
                  control={control}
                  controlName='delegate.id'
                  error={!!errors.delegate?.id}
                  options={employees.map((employee) => ({id: employee.id, value: employee.name}))}
                  placeholder={t('delegate')}
                  searchPlaceholder={t('search delegate')}
                />
                <ComboboxLabel>{t('delegate')}*</ComboboxLabel>
              </WithLabel>
              {(!watch('from') || !watch('to')) && (
                <div className='flex gap-4'>
                  {!watch('from') && (
                    <Button
                      className='grow'
                      onClick={() =>
                        setValue('from', {
                          address1: '',
                          address2: '',
                          city: '',
                          country: '',
                          name: '',
                          state: '',
                          types: [],
                          zip: '',
                        })
                      }
                      variant='secondary'
                    >
                      <PlusIcon />
                      {t('loading point')}
                    </Button>
                  )}
                  {!watch('to') && (
                    <Button
                      className='grow'
                      onClick={() =>
                        setValue('to', {
                          address1: '',
                          address2: '',
                          city: '',
                          country: '',
                          name: '',
                          state: '',
                          types: [],
                          zip: '',
                        })
                      }
                      variant='secondary'
                    >
                      <PlusIcon />
                      {t('unloading point')}
                    </Button>
                  )}
                </div>
              )}
            </div>
            {watch('from') && (
              <div className='flex w-full flex-col gap-2 rounded-lg border p-4'>
                <div className='flex items-center justify-between'>
                  {t('loading point')}
                  {!id && (
                    <Button
                      onClick={() => {
                        setValue('from', null);
                      }}
                      size='icon'
                      variant='ghost'
                    >
                      <XIcon className='size-5' strokeWidth={1} />
                    </Button>
                  )}
                </div>
                <WithLabel>
                  <Input {...register('from.address1')} error={!!errors.from?.address1} />
                  <InputLabel>{t('address (str no bl ap st fl)')}</InputLabel>
                </WithLabel>
                <div className='flex items-center gap-2'>
                  <WithLabel className='w-full'>
                    <Input {...register('from.city')} error={!!errors.from?.city} />
                    <InputLabel>{t('city')}</InputLabel>
                  </WithLabel>
                  <WithLabel className='w-full'>
                    <Input {...register('from.state')} error={!!errors.from?.state} />
                    <InputLabel>{t('state')}</InputLabel>
                  </WithLabel>
                </div>
                <div className='flex items-center gap-2'>
                  <WithLabel className='w-full'>
                    <CountriesCombobox
                      error={!!errors.from?.country}
                      onChange={(value) => setValue('from.country', value)}
                      value={watch('from.country')}
                    />
                    <InputLabel>{t('country')}</InputLabel>
                  </WithLabel>
                  <WithLabel>
                    <Input {...register('from.zip')} error={!!errors.from?.zip} size='md' />
                    <InputLabel>{t('zip')}</InputLabel>
                  </WithLabel>
                </div>
              </div>
            )}
            {watch('to') && (
              <div className='flex w-full flex-col gap-2 rounded-lg border p-4'>
                <div className='flex items-center justify-between'>
                  {t('unloading point')}
                  {!id && (
                    <Button
                      onClick={() => {
                        setValue('to', null);
                      }}
                      size='icon'
                      variant='ghost'
                    >
                      <XIcon className='size-5' strokeWidth={1} />
                    </Button>
                  )}
                </div>
                <WithLabel>
                  <Input {...register('to.address1')} error={!!errors.to?.address1} />
                  <InputLabel>{t('address (str no bl ap st fl)')}</InputLabel>
                </WithLabel>
                <div className='flex items-center gap-2'>
                  <WithLabel className='w-full'>
                    <Input {...register('to.city')} error={!!errors.to?.city} />
                    <InputLabel>{t('city')}</InputLabel>
                  </WithLabel>
                  <WithLabel className='w-full'>
                    <Input {...register('to.state')} error={!!errors.to?.state} />
                    <InputLabel>{t('state')}</InputLabel>
                  </WithLabel>
                </div>
                <div className='flex items-center gap-2'>
                  <WithLabel className='w-full'>
                    <CountriesCombobox
                      error={!!errors.to?.country}
                      onChange={(value) => setValue('to.country', value)}
                      value={watch('to.country')}
                    />
                    <InputLabel>{t('country')}</InputLabel>
                  </WithLabel>
                  <WithLabel>
                    <Input {...register('to.zip')} error={!!errors.to?.zip} size='md' />
                    <InputLabel>{t('zip')}</InputLabel>
                  </WithLabel>
                </div>
              </div>
            )}
            <div className='w-full rounded-lg border p-4'>
              <TextareaAutosize
                className='w-full'
                placeholder={t('mentions')}
                {...register('notes')}
                error={!!errors.notes}
                maxRows={5}
                minRows={3}
              />
            </div>
            <div className='flex items-center justify-between'>
              <div className={classes('text-base font-medium', !!errors.items && 'text-red')}>{t('items')}</div>
              <FormProvider
                {...{
                  control,
                  formState: {errors, ...restUseFormState},
                  register,
                  setValue,
                  watch,
                  ...restUseFormActions,
                }}
              >
                <ManufacturingServicesNoteCreateEditButtons />
              </FormProvider>
            </div>
            <div className='flex h-full flex-col gap-2 rounded-lg border p-4'>
              {watch('items')?.map((item, index) => (
                <div className='flex items-center justify-between border-b pb-2' key={item.id || index}>
                  {item.name}
                  <div className='flex items-center gap-4'>
                    <NumberInput
                      onChange={(value) => update(index, {...item, quantity: value})}
                      value={item.quantity}
                    />
                    <Button onClick={() => remove(index)} size='icon' variant='none'>
                      <Trash2Icon className='size-5 text-red cursor-pointer' strokeWidth={1} />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </PageContent>
    </Page>
  );
};

export default ManufacturingServicesNoteCreate;
