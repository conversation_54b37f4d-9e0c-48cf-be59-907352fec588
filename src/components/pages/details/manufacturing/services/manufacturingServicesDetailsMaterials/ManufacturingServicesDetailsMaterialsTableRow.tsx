import {FC} from 'react';

import Jo<PERSON> from 'joi';
import {Trash2Icon} from 'lucide-react';
import {useTranslations} from 'next-intl';
import {useFieldArray, useFormContext} from 'react-hook-form';

import {Button} from '@/components/ui/Button';
import {PopoverInput} from '@/components/ui/Input';
import InventoryItemLink from '@/components/ui/special/InventoryItemLink/InventoryItemLink';
import {OrderButton} from '@/components/ui/special/OrderButton';
import {TableActions, TableCell, TableRow} from '@/components/ui/Table';
import {useHasPermission} from '@/hooks/helpers/useHasPermission';
import {ServicingStatus} from '@/types/global';
import {ManufacturingServiceMaterial, ServicingOrder} from '@/types/manufacturing';
import {formatCurrency, formatNumber} from '@/utils/format';

type Props = {
  index: number;
  item: ManufacturingServiceMaterial;
};

const ManufacturingServicesDetailsMaterialsTableRow: FC<Props> = ({index, item}) => {
  const t = useTranslations();
  const {hasPermission, isLoading} = useHasPermission();
  const {control, watch} = useFormContext<ServicingOrder>();
  const {remove, update} = useFieldArray({control, name: 'materials'});

  if (isLoading) return null;

  return (
    <TableRow>
      <TableCell>
        <InventoryItemLink item={item.materialGoods?.[0]} />
      </TableCell>
      <TableCell className='text-right'>
        <PopoverInput
          className='w-fit justify-end'
          defaultValue={(item.usedQuantity || 0).toFixed(2)}
          disabled={[ServicingStatus.DONE, ServicingStatus.MANUFACTURED].includes(watch('status'))}
          label={t('used')}
          onChange={async (value) => update(index, {...item, usedQuantity: Number(value)})}
          validation={Joi.string().required().allow(0)}
        >
          {formatNumber(item.usedQuantity || 0)} {t(`unit.name.${item.measurementUnit.name.toLowerCase()}` as any)}
        </PopoverInput>
      </TableCell>
      <TableCell className='text-right'>
        {formatNumber(item.required)} {t(`unit.name.${item.measurementUnit.name.toLowerCase()}` as any)}
      </TableCell>
      {hasPermission('financial', 'manufacturing') && (
        <TableCell className='text-right'>{formatCurrency(item.cost)}</TableCell>
      )}
      {watch('status') !== ServicingStatus.DONE && (
        <TableActions>
          <OrderButton forTable item={{...item.materialGoods?.[0], produced: item.materialGoods?.[0]?.produced}} />
          {item.isNew && (
            <Button onClick={() => remove(index)} size='icon' variant='none'>
              <Trash2Icon className='size-5 text-red' strokeWidth={1} />
            </Button>
          )}
        </TableActions>
      )}
    </TableRow>
  );
};

export default ManufacturingServicesDetailsMaterialsTableRow;
