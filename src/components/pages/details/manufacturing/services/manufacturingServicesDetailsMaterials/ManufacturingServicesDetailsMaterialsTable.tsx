import {FC} from 'react';

import {useAtomValue} from 'jotai';
import {useTranslations} from 'next-intl';
import {useFormContext} from 'react-hook-form';

import {detailsDataShownAtom} from '@/components/pages/details/detailsStore';
import {
  Table,
  TableBody,
  TableContainer,
  TableHead,
  TableHeadActions,
  TableHeader,
  TableRow,
} from '@/components/ui/Table';
import {useHasPermission} from '@/hooks/helpers/useHasPermission';
import useHeight from '@/hooks/helpers/useHeight';
import {ServicingStatus} from '@/types/global';
import {ServicingOrder} from '@/types/manufacturing';

import ManufacturingServicesDetailsMaterialsTableRow from './ManufacturingServicesDetailsMaterialsTableRow';

const ManufacturingServicesDetailsMaterialsTable: FC = () => {
  const {hasPermission, isLoading} = useHasPermission();
  const {watch} = useFormContext<ServicingOrder>();
  const detailsDataShown = useAtomValue(detailsDataShownAtom(watch('id')));
  const {elementRef} = useHeight({dependencies: [detailsDataShown]});
  const t = useTranslations();

  if (isLoading) return null;

  return (
    <TableContainer ref={elementRef}>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>{t('name')}</TableHead>
            <TableHead className='text-right'>{t('used')}</TableHead>
            <TableHead className='text-right'>{t('available', {isPlural: 'false'})}</TableHead>
            {hasPermission('financial', 'manufacturing') && <TableHead className='text-right'>{t('cost')}</TableHead>}
            {![ServicingStatus.CLOSED, ServicingStatus.BLOCKED].includes(watch('status')) && <TableHeadActions />}
          </TableRow>
        </TableHeader>
        <TableBody className='overflow-y-hidden' isValidating={!watch()}>
          {watch('materials')?.map((item, index) => (
            <ManufacturingServicesDetailsMaterialsTableRow
              index={index}
              item={item}
              key={`${item.materialGoods?.[0]?.name}-${index}`}
            />
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );
};

export default ManufacturingServicesDetailsMaterialsTable;
