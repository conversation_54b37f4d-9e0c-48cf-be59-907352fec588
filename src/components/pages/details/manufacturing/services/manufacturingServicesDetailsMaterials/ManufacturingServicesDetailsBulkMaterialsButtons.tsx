import {FC} from 'react';

import {BookTextIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';
import {useFormContext} from 'react-hook-form';

import {Button} from '@/components/ui/Button';
import {useRouter} from '@/hooks/helpers/useRouter';
import {ServicingOrder} from '@/types/manufacturing';

type Props = {
  className?: string;
};

const ManufacturingServicesDetailsBulkMaterialsButtons: FC<Props> = () => {
  const {watch} = useFormContext<ServicingOrder>();
  const {push} = useRouter();
  const t = useTranslations();

  return (
    <Button onClick={() => push(`/manufacturing/services/${watch('id')}/notes`)} variant='secondary'>
      <BookTextIcon strokeWidth={1} />
      {t('generate goods accompanying note')}
    </Button>
  );
};

export default ManufacturingServicesDetailsBulkMaterialsButtons;
