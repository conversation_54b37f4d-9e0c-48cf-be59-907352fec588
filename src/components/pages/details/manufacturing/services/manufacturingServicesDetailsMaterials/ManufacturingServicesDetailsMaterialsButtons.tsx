import {FC, useCallback, useState} from 'react';

import {PlusIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';
import {useFieldArray, UseFieldArrayAppend, useFormContext} from 'react-hook-form';

import {Button} from '@/components/ui/Button';
import AddItemModal from '@/components/ui/special/AddItemModal/AddItemModal';
import useItemActions from '@/hooks/useItemActions';
import {ServicingOrder} from '@/types/manufacturing';
import {classes} from '@/utils/common';

type AddProps = {
  excludeIds: string[];
  onAdd: UseFieldArrayAppend<ServicingOrder, 'materials'>;
  onClose: () => void;
};

const AddItem: FC<AddProps> = ({excludeIds, onAdd, onClose}) => {
  const {getItem} = useItemActions();
  const {watch} = useFormContext<ServicingOrder>();

  const handleAdd = useCallback(
    (values: {id: string; quantity: number}[]) => {
      values.forEach(async ({id, quantity}) => {
        const item = await getItem(id);

        if (!item) return;

        onAdd({
          cost: {
            amount: item.inventoryCostPerItem.amount * watch('quantity'),
            currency: item.inventoryCostPerItem.currency,
          },
          isNew: true,
          materialGoods: [
            {
              ...item,
              measurementUnit: item.measurementUnit.id,
            },
          ],
          measurementUnit: item.measurementUnit,
          required: quantity,
          usedQuantity: 0,
        });
      });

      onClose();
    },
    [getItem, onAdd, onClose, watch],
  );

  return <AddItemModal create='none' excludeIds={excludeIds} onAdd={handleAdd} onClose={onClose} />;
};

type Props = {
  className?: string;
};

const ManufacturingServicesDetailsMaterialsButtons: FC<Props> = ({className}) => {
  const t = useTranslations();
  const [addEnabled, setAddEnabled] = useState(false);
  const {control, watch} = useFormContext<ServicingOrder>();
  const {append} = useFieldArray({control, name: 'materials'});

  return (
    <div className={classes('flex items-center gap-4', className)}>
      <Button onClick={() => setAddEnabled(true)} variant='secondary'>
        <PlusIcon /> {t('add material')}
      </Button>
      {addEnabled && (
        <AddItem
          excludeIds={watch('materials')?.map((material) => material.materialGoods?.[0]?.id) ?? []}
          onAdd={append}
          onClose={() => setAddEnabled(false)}
        />
      )}
    </div>
  );
};

export default ManufacturingServicesDetailsMaterialsButtons;
