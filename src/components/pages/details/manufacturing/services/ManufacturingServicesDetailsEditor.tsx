import {FC} from 'react';

import {useAtomValue} from 'jotai';
import {useTranslations} from 'next-intl';
import {useFormContext} from 'react-hook-form';

import {detailsDataShownAtom} from '@/components/pages/details/detailsStore';
import HTMLEditor from '@/components/ui/HTMLEditor';
import useHeight from '@/hooks/helpers/useHeight';
import {ServicingStatus} from '@/types/global';
import {ServicingOrder} from '@/types/manufacturing';

const DEFAULT_TEXT = `
<ul style="margin-block: 0; padding-inline-start: 12px; list-style-type: disc;">
  <li><b>Utilaj</b>: </li>
  <li><b>Se<PERSON>zare</b>: </li>
  <li><b>Cauza</b>: </li>
  <li><b>Solutie</b>: </li>
  <li><b>Observatii</b>: </li>
</ul>
<br>
<table style="border-collapse: collapse;">
  <tbody>
    <tr>
      <td style=" border: 1px solid #ccc; padding: 8px; text-align: center; vertical-align: center;">&nbsp;</td>
      <td style=" border: 1px solid #ccc; padding: 8px; text-align: center; vertical-align: center;">Ore lucrate</td>
      <td style=" border: 1px solid #ccc; padding: 8px; text-align: center; vertical-align: center;">Deplasare (km)</td>
      <td style=" border: 1px solid #ccc; padding: 8px; text-align: center; vertical-align: center;">Cazare</td>
    </tr>
    <tr>
      <td style=" border: 1px solid #ccc; padding: 8px; text-align: center; vertical-align: center;">Ore normale L-V<br>08:00-16:30</td>
      <td style=" border: 1px solid #ccc; padding: 8px; text-align: center; vertical-align: center;">&nbsp;</td>
      <td style=" border: 1px solid #ccc; padding: 8px; text-align: center; vertical-align: center;">&nbsp;</td>
      <td style=" border: 1px solid #ccc; padding: 8px; text-align: center; vertical-align: center;">&nbsp;</td>
    </tr>
    <tr>
      <td style=" border: 1px solid #ccc; padding: 8px; text-align: center; vertical-align: center;">Ore suplimentare<br>L-V</td>
      <td style=" border: 1px solid #ccc; padding: 8px; text-align: center; vertical-align: center;">&nbsp;</td>
      <td style=" border: 1px solid #ccc; padding: 8px; text-align: center; vertical-align: center;">&nbsp;</td>
      <td style=" border: 1px solid #ccc; padding: 8px; text-align: center; vertical-align: center;">&nbsp;</td>
    </tr>
    <tr>
      <td style=" border: 1px solid #ccc; padding: 8px; text-align: center; vertical-align: center;">Weekend/Sărb.<br>Legale</td>
      <td style=" border: 1px solid #ccc; padding: 8px; text-align: center; vertical-align: center;">&nbsp;</td>
      <td style=" border: 1px solid #ccc; padding: 8px; text-align: center; vertical-align: center;">&nbsp;</td>
      <td style=" border: 1px solid #ccc; padding: 8px; text-align: center; vertical-align: center;">&nbsp;</td>
    </tr>
  </tbody>
</table>
`;

const ManufacturingServicesDetailsEditor: FC = () => {
  const {setValue, watch} = useFormContext<ServicingOrder>();
  const detailsDataShown = useAtomValue(detailsDataShownAtom(watch('id')));
  const {elementRef} = useHeight({dependencies: [detailsDataShown]});
  const t = useTranslations();

  return (
    <HTMLEditor
      disabled={[ServicingStatus.DONE, ServicingStatus.MANUFACTURED].includes(watch('status'))}
      extraButtons={[
        {
          menu: [
            {
              onAction: (reset) => reset(DEFAULT_TEXT),
              text: t('default template'),
            },
          ],
          name: 'templates',
          text: t('templates'),
          tooltip: t('insert template'),
        },
      ]}
      onChange={(value) => setValue('notes', value)}
      ref={elementRef}
      value={watch('notes') || ''}
    />
  );
};

export default ManufacturingServicesDetailsEditor;
