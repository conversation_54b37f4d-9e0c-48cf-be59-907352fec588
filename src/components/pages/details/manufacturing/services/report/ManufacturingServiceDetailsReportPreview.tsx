import {FC, useState} from 'react';

import {ArrowLeftIcon, CheckIcon, TriangleAlertIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';

import {Button} from '@/components/ui/Button';
import {Input} from '@/components/ui/Input';
import {Label, WithLabel} from '@/components/ui/Label';
import {Popover, PopoverContent, PopoverTrigger} from '@/components/ui/Popover';
import SignatureCanvas from '@/components/ui/SignatureCanvas';
import {DocumentPreview} from '@/components/ui/special/DocumentPreview';
import {Page, PageContent, PageHeader, PageHeaderTitle, PageTitle} from '@/components/ui/special/Page';
import useServiceReportDocumentPreview from '@/hooks/documents/useServiceReportDocumentPreview';
import useHeight from '@/hooks/helpers/useHeight';
import {useRouter} from '@/hooks/helpers/useRouter';
import useServicingOrder from '@/hooks/useServicingOrder';
import useServicingOrderActions from '@/hooks/useServicingOrderActions';
import {ServicingStatus} from '@/types/global';
import {classes} from '@/utils/common';

type Props = {
  serviceId: string;
};

const ManufacturingServiceDetailsReportPreview: FC<Props> = ({serviceId}) => {
  const t = useTranslations();
  const {elementRef} = useHeight();
  const {elementRef: containerRef} = useHeight();
  const {back} = useRouter();
  const [workerSignature, setWorkerSignature] = useState('');
  const [clientSignature, setClientSignature] = useState('');
  const [clientRepresentative, setClientRepresentative] = useState('');
  const {serviceReportDocumentPreview} = useServiceReportDocumentPreview(serviceId, {
    clientRepresentative,
    clientSignature,
    workerSignature,
  });
  const {updateServicingOrderStatus} = useServicingOrderActions();
  const {servicingOrder} = useServicingOrder(serviceId);
  const [popoverOpen, setPopoverOpen] = useState(false);
  const [showSignatures, setShowSignatures] = useState(false);

  return (
    <Page>
      <PageTitle>{`${t('service report')} — ${t('manufacturing')}`}</PageTitle>
      <PageHeader>
        <PageHeaderTitle className='inline-flex items-center gap-2'>
          <Button onClick={() => back(`/manufacturing/services/${serviceId}`)} size='icon' variant='none'>
            <ArrowLeftIcon />
          </Button>
          {t('service report')}
        </PageHeaderTitle>
        <div className='grow' />
        {servicingOrder.manufacturingOperations?.length > 0 &&
          ![ServicingStatus.DONE, ServicingStatus.MANUFACTURED].includes(servicingOrder.status) && (
            <>
              {!showSignatures && (
                <Button onClick={() => setShowSignatures(true)}>
                  <CheckIcon />
                  {t('finish work and service report')}
                </Button>
              )}
              {showSignatures && (
                <>
                  {workerSignature && clientSignature && clientRepresentative && (
                    <Button
                      onClick={() =>
                        updateServicingOrderStatus(servicingOrder, ServicingStatus.MANUFACTURED, {
                          clientRepresentative,
                          clientSignature,
                          workerSignature,
                        }).then(() => {
                          back(`/manufacturing/services/${serviceId}`);
                        })
                      }
                    >
                      <CheckIcon />
                      {t('finish work and service report')}
                    </Button>
                  )}
                  {!(workerSignature && clientSignature && clientRepresentative) && (
                    <Popover
                      onOpenChange={(open) => {
                        setPopoverOpen(open);
                      }}
                      open={popoverOpen}
                    >
                      <PopoverTrigger asChild>
                        <Button
                          onClick={() => {
                            if (popoverOpen) {
                              updateServicingOrderStatus(servicingOrder, ServicingStatus.MANUFACTURED).then(() => {
                                back(`/manufacturing/services/${serviceId}`);
                              });
                              setPopoverOpen(false);
                              return;
                            }
                          }}
                        >
                          <CheckIcon />
                          {t('finish work and service report')}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className='max-w-96'>
                        <div className='flex flex-col gap-4'>
                          <div className='flex items-center justify-between'>
                            <div className='flex items-center gap-2'>
                              <TriangleAlertIcon className='text-dot-orange' />
                              <p className='text-lg font-semibold'>{t('texts.signature.document is not signed')}</p>
                            </div>
                          </div>
                          {t(
                            'texts.signature.click again to confirm finishing the work and service report without a signature',
                          )}
                        </div>
                      </PopoverContent>
                    </Popover>
                  )}
                </>
              )}
            </>
          )}
      </PageHeader>
      <PageContent>
        <div className='flex' ref={elementRef}>
          <div className={classes('flex h-full justify-center bg-gray-100 p-4', showSignatures ? 'w-3/4' : 'w-full')}>
            <DocumentPreview content={serviceReportDocumentPreview} />
          </div>
          {showSignatures && (
            <div className='flex w-1/3 flex-col gap-4 overflow-y-auto p-6' ref={containerRef}>
              <div className='flex flex-col gap-4 border rounded-md p-3'>
                <WithLabel>
                  <Input
                    onChange={({target: {value}}) => setClientRepresentative(value)}
                    value={clientRepresentative}
                  />
                  <Label>{t('client name')}</Label>
                </WithLabel>

                <Label>{t('client signature')}</Label>
                <SignatureCanvas onConfirm={setClientSignature} />
              </div>
              <div className='flex flex-col gap-2 border rounded-md p-3'>
                <Label>{t('worker signature')}</Label>
                <SignatureCanvas onConfirm={setWorkerSignature} />
              </div>
            </div>
          )}
        </div>
      </PageContent>
    </Page>
  );
};

export default ManufacturingServiceDetailsReportPreview;
