import {FC} from 'react';

import {ArrowLeftIcon, CloudDownloadIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';

import {Button} from '@/components/ui/Button';
import {DocumentPreview} from '@/components/ui/special/DocumentPreview';
import {Link} from '@/components/ui/special/Link';
import {Page, PageContent, PageHeader, PageHeaderTitle, PageTitle} from '@/components/ui/special/Page';
import useServiceReportDocument from '@/hooks/documents/useServiceReportDocument';
import useHeight from '@/hooks/helpers/useHeight';
import {useRouter} from '@/hooks/helpers/useRouter';

type Props = {
  serviceId: string;
};

const ManufacturingServiceDetailsReport: FC<Props> = ({serviceId}) => {
  const {elementRef} = useHeight();
  const {serviceReportDocument} = useServiceReportDocument(serviceId);
  const {back} = useRouter();
  const t = useTranslations();

  return (
    <Page>
      <PageTitle>{`${t('service report')} - ${t('manufacturing')}`}</PageTitle>
      <PageHeader>
        <PageHeaderTitle className='inline-flex items-center gap-2'>
          <Button onClick={() => back(`/manufacturing/services/${serviceId}`)} size='icon' variant='none'>
            <ArrowLeftIcon />
          </Button>
          {t('service report')}
        </PageHeaderTitle>
        <div className='grow' />
        <Button asChild variant='secondary'>
          <Link href={`/api/servicing/orders/${serviceId}/service-report?mediaType=application/pdf`}>
            <CloudDownloadIcon />
            {t('download')}
          </Link>
        </Button>
      </PageHeader>
      <PageContent>
        <div className='flex h-full justify-center bg-gray-100 p-4' ref={elementRef}>
          <DocumentPreview content={serviceReportDocument} />
        </div>
      </PageContent>
    </Page>
  );
};

export default ManufacturingServiceDetailsReport;
