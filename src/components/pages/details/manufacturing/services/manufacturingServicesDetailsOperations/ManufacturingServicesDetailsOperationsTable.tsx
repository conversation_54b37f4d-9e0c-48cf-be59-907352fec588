import {FC, useEffect, useState} from 'react';

import {closestCorners, DndContext, DragEndEvent, PointerSensor, useSensor, useSensors} from '@dnd-kit/core';
import {arrayMove, SortableContext, verticalListSortingStrategy} from '@dnd-kit/sortable';
import {useAtomValue} from 'jotai';
import {useTranslations} from 'next-intl';
import {useFieldArray, useFormContext} from 'react-hook-form';

import {detailsDataShownAtom} from '@/components/pages/details/detailsStore';
import {
  Table,
  TableBody,
  TableContainer,
  TableHead,
  TableHeadActions,
  TableHeader,
  TableRow,
} from '@/components/ui/Table';
import {useHasPermission} from '@/hooks/helpers/useHasPermission';
import useHeight from '@/hooks/helpers/useHeight';
import {ServicingStatus} from '@/types/global';
import {OperationStep, ServicingOrder} from '@/types/manufacturing';
import {newId} from '@/utils/common';

import ManufacturingServicesDetailsOperationsTableRow from './ManufacturingServicesDetailsOperationsTableRow';

const ensureValidIds = (operations: OperationStep[]) =>
  operations.map((op) => (op.id?.trim() ? op : {...op, id: op.id || newId()}));

const ManufacturingServicesDetailsOperationsTable: FC = () => {
  const {control, watch} = useFormContext<ServicingOrder>();
  const detailsDataShown = useAtomValue(detailsDataShownAtom(watch('id')));
  const {move} = useFieldArray({control, name: 'manufacturingOperations'});
  const {elementRef} = useHeight({dependencies: [detailsDataShown]});
  const [operations, setOperations] = useState(watch('manufacturingOperations') || []);
  const t = useTranslations();
  const {hasPermission, isLoading} = useHasPermission();

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const watchedOperations = watch('manufacturingOperations') || [];

  useEffect(() => {
    setOperations(ensureValidIds(watchedOperations));
  }, [watchedOperations]);

  const sensors = useSensors(useSensor(PointerSensor));

  const handleDragEnd = ({active, over}: DragEndEvent) => {
    if (active.id !== over?.id) {
      const oldIndex = operations.findIndex((order) => order.id === active.id);
      const newIndex = over ? operations.findIndex((order) => order.id === over.id) : -1;
      if (oldIndex !== -1 && newIndex !== -1) {
        setOperations(arrayMove(operations, oldIndex, newIndex));
        move(oldIndex, newIndex);
      }
    }
  };

  return (
    <TableContainer ref={elementRef}>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>{t('name')}</TableHead>
            <TableHead className='text-right'>{t('duration')}</TableHead>
            {hasPermission('financial', 'manufacturing') && (
              <TableHead className='text-right'>{t('cost per hour')}</TableHead>
            )}
            <TableHead>{t('employees')}</TableHead>
            <TableHead>{t('workstations')}</TableHead>
            <TableHead>{t('parallelization')}</TableHead>
            {![ServicingStatus.CLOSED, ServicingStatus.BLOCKED].includes(watch('status')) && <TableHeadActions />}
          </TableRow>
        </TableHeader>
        <TableBody className='overflow-y-hidden' isValidating={!watch() || isLoading}>
          <DndContext collisionDetection={closestCorners} onDragEnd={handleDragEnd} sensors={sensors}>
            <SortableContext items={operations} strategy={verticalListSortingStrategy}>
              {operations.map((operation, index) => (
                <ManufacturingServicesDetailsOperationsTableRow
                  index={index}
                  key={`${operation.id}-${index}`}
                  operation={operation}
                />
              ))}
            </SortableContext>
          </DndContext>
        </TableBody>
      </Table>
    </TableContainer>
  );
};

export default ManufacturingServicesDetailsOperationsTable;
