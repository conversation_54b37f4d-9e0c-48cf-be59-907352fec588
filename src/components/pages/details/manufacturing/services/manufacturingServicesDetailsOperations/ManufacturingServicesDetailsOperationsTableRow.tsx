import {FC} from 'react';

import {useSortable} from '@dnd-kit/sortable';
import {CSS} from '@dnd-kit/utilities';
import {useAtomValue} from 'jotai';
import {sortBy} from 'lodash';
import {GripHorizontalIcon, Trash2Icon} from 'lucide-react';
import {useTranslations} from 'next-intl';
import {useFieldArray, useFormContext} from 'react-hook-form';

import {Button} from '@/components/ui/Button';
import {PopoverDurationInput} from '@/components/ui/Input';
import {TableMultiSelect} from '@/components/ui/special/TableMultiSelect';
import {Switch} from '@/components/ui/Switch';
import {TableActions, TableCell, TableRow} from '@/components/ui/Table';
import {useHasPermission} from '@/hooks/helpers/useHasPermission';
import useEmployeeActions from '@/hooks/useEmployeeActions';
import useEmployees from '@/hooks/useEmployees';
import useWorkstationActions from '@/hooks/useWorkstationActions';
import useWorkstations from '@/hooks/useWorkstations';
import {defaultCurrencyAtom} from '@/store/defaults';
import {ServicingStatus} from '@/types/global';
import {OperationStep, ServicingOrder} from '@/types/manufacturing';
import {classes, minutesToWorkHoursTimeString} from '@/utils/common';
import {formatCurrency} from '@/utils/format';

type Props = {
  index: number;
  operation: OperationStep;
};

const ManufacturingServicesDetailsOperationsTableRow: FC<Props> = ({index, operation}) => {
  const {attributes, isDragging, listeners, setNodeRef, transform, transition} = useSortable({
    id: operation.id,
  });
  const {employees} = useEmployees();
  const {createEmployee} = useEmployeeActions();
  const {workstations} = useWorkstations();
  const {createWorkstation} = useWorkstationActions();
  const defaultCurrency = useAtomValue(defaultCurrencyAtom);
  const t = useTranslations();
  const {
    control,
    formState: {errors},
    watch,
  } = useFormContext<ServicingOrder>();
  const {remove, update} = useFieldArray({control, name: 'manufacturingOperations'});
  const {hasPermission, isLoading} = useHasPermission();

  if (isLoading) return null;

  return (
    <TableRow
      className={classes(isDragging && 'bg-background shadow-xl hover:bg-background')}
      ref={setNodeRef}
      style={{
        ...(transform ? {transform: CSS.Transform.toString({...transform, scaleX: 1, scaleY: 1})} : {}),
        transition,
      }}
    >
      <TableCell className='mt-0.5 inline-flex h-14 items-center gap-2 align-top'>
        {![ServicingStatus.DONE, ServicingStatus.MANUFACTURED].includes(watch('status')) && (
          <span {...listeners} {...attributes}>
            <GripHorizontalIcon className='size-5 text-slate-900/40' />
          </span>
        )}
        {operation.name}
      </TableCell>
      <TableCell className='text-right'>
        <PopoverDurationInput
          className='w-fit justify-end'
          disabled={[ServicingStatus.DONE, ServicingStatus.MANUFACTURED].includes(watch('status'))}
          error={!!errors.manufacturingOperations?.[index]?.durationInMinutes}
          minutes={operation.durationInMinutes}
          onChange={(value) => {
            update(index, {
              ...operation,
              durationInMinutes: value,
            });
          }}
        >
          {minutesToWorkHoursTimeString(operation.durationInMinutes)}
        </PopoverDurationInput>
      </TableCell>
      {hasPermission('financial', 'manufacturing') && (
        <TableCell className='text-right'>{formatCurrency(operation.costPerHour)}</TableCell>
      )}
      <TableCell>
        <TableMultiSelect
          defaultValue={operation.candidateEmployees.map((employee) => employee.id)}
          disabled={[ServicingStatus.DONE, ServicingStatus.MANUFACTURED].includes(watch('status'))}
          onValueChange={(value) =>
            update(index, {
              ...operation,
              candidateEmployees: value.map((id) => ({
                id,
                name: '',
              })),
            })
          }
          options={sortBy(
            employees.map((employee) => ({id: employee.id, value: employee.name})),
            'name',
          )}
          renderNotFound={(query) => (
            <Button
              className='w-full justify-start'
              onClick={() => {
                createEmployee({name: query, position: '-'}).then((employee) => {
                  update(index, {
                    ...operation,
                    candidateEmployees: [...operation.candidateEmployees, employee],
                  });
                });
              }}
              variant='secondary'
            >
              {t('create employee')}
            </Button>
          )}
          searchPlaceholder={t('search employee')}
          side='right'
          summaryLabel={t('selected.male')}
        />
      </TableCell>
      <TableCell>
        <TableMultiSelect
          defaultValue={operation.candidateWorkstations.map((workstation) => workstation.id)}
          disabled={[ServicingStatus.DONE, ServicingStatus.MANUFACTURED].includes(watch('status'))}
          onValueChange={(value) =>
            update(index, {
              ...operation,
              candidateWorkstations: value.map((id) => ({
                id,
                name: '',
              })),
            })
          }
          options={sortBy(
            workstations.map((workstation) => ({id: workstation.id, value: workstation.name})),
            'name',
          )}
          renderNotFound={(query) => (
            <Button
              className='w-full justify-start'
              onClick={() => {
                createWorkstation({costPerHour: {amount: 0, currency: defaultCurrency}, name: query}).then(
                  (workstation) => {
                    update(index, {
                      ...operation,
                      candidateWorkstations: [...operation.candidateWorkstations, workstation],
                    });
                  },
                );
              }}
              variant='secondary'
            >
              {t('create workstation')}
            </Button>
          )}
          searchPlaceholder={t('search workstation')}
          side='left'
          summaryLabel={t('selected.female')}
        />
      </TableCell>
      <TableCell>
        <Switch
          checked={operation.parallelizable}
          className='mt-2'
          disabled={[ServicingStatus.DONE, ServicingStatus.MANUFACTURED].includes(watch('status'))}
          onCheckedChange={(value) =>
            update(index, {
              ...operation,
              parallelizable: value,
            })
          }
        />
      </TableCell>
      {![ServicingStatus.DONE, ServicingStatus.MANUFACTURED].includes(watch('status')) && (
        <TableActions>
          <Button className='mt-1.5' onClick={() => remove(index)} size='icon' variant='none'>
            <Trash2Icon className='size-5 text-red' strokeWidth={1} />
          </Button>
        </TableActions>
      )}
    </TableRow>
  );
};

export default ManufacturingServicesDetailsOperationsTableRow;
