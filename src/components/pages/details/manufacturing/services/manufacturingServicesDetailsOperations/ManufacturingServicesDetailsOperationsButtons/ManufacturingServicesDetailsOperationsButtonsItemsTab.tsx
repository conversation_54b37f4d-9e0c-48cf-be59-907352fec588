import {FC, useCallback, useState} from 'react';

import {first} from 'lodash';
import {PlusIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';
import {UseFieldArrayAppend} from 'react-hook-form';

import {Button} from '@/components/ui/Button';
import {MultiSelect} from '@/components/ui/special/Multiselect';
import useItemActions from '@/hooks/useItemActions';
import useItems from '@/hooks/useItems';
import {InventoryItemManufacturingOperation} from '@/types/inventory';
import {ServicingOrder} from '@/types/manufacturing';
import {newId} from '@/utils/common';

type Props = {
  onAdd: UseFieldArrayAppend<ServicingOrder, 'manufacturingOperations'>;
  onClose: () => void;
};

const ManufacturingServicesDetailsOperationsButtonsItemsTab: FC<Props> = ({onAdd, onClose}) => {
  const {items} = useItems({onlyProduced: true});
  const {getItem} = useItemActions();
  const [values, setValues] = useState<InventoryItemManufacturingOperation[]>([]);
  const t = useTranslations();

  const handleAdd = useCallback(() => {
    if (values.length > 0) {
      values.map((operation) => onAdd(operation));
    }
    onClose();
  }, [onAdd, onClose, values]);

  const handleSelect = useCallback(
    async (values: string[]) => {
      const value = first(values);
      if (!value) return setValues([]);
      const item = await getItem(value);
      if (!item) return setValues([]);
      setValues(item.manufacturingOperations.map((operation) => ({...operation, id: newId()})));
    },
    [getItem],
  );

  return (
    <>
      <MultiSelect
        autoFocus
        onValueChange={handleSelect}
        options={items.map((item) => ({id: item.id, value: item.name}))}
        renderNotFound={() => t('item not found')}
        searchPlaceholder={t('items')}
        single
      />
      <Button className='w-full' onClick={handleAdd}>
        <PlusIcon className='size-5' /> {t('add number of tasks', {number: values.length})}
      </Button>
    </>
  );
};

export default ManufacturingServicesDetailsOperationsButtonsItemsTab;
