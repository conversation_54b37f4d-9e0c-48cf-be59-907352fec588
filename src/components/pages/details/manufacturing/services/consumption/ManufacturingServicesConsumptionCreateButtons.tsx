import {FC, useCallback, useState} from 'react';

import {PlusIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';
import {useFieldArray, UseFieldArrayAppend, useFormContext} from 'react-hook-form';

import {Button} from '@/components/ui/Button';
import AddItemModal from '@/components/ui/special/AddItemModal/AddItemModal';
import useItemActions from '@/hooks/useItemActions';
import {Consumption} from '@/types/manufacturing';
import {classes} from '@/utils/common';

type AddProps = {
  excludeIds: string[];
  onClose: () => void;
  onSelect: UseFieldArrayAppend<Consumption, 'materials'>;
};

const AddMaterial: FC<AddProps> = ({excludeIds, onClose, onSelect}) => {
  const {getItem} = useItemActions();

  const handleAdd = useCallback(
    (values: {id: string; quantity: number}[]) => {
      values.forEach(async ({id, quantity}) => {
        const item = await getItem(id);

        if (!item) return;

        onSelect({
          ...item,
          quantity,
        });
      });
      onClose();
    },
    [getItem, onClose, onSelect],
  );

  return <AddItemModal create='none' excludeIds={excludeIds} onAdd={handleAdd} onClose={onClose} />;
};

type Props = {
  className?: string;
};

const ManufacturingServicesConsumptionCreateButtons: FC<Props> = ({className}) => {
  const t = useTranslations();
  const [addEnabled, setAddEnabled] = useState(false);
  const {control, watch} = useFormContext<Consumption>();
  const {append} = useFieldArray({control, name: 'materials'});

  return (
    <div className={classes('flex items-center gap-4', className)}>
      {!addEnabled && (
        <Button
          onClick={() => {
            setAddEnabled(true);
          }}
          variant='secondary'
        >
          <PlusIcon /> {t('add material')}
        </Button>
      )}
      {addEnabled && (
        <AddMaterial
          excludeIds={watch('materials')?.map((material) => material.id) ?? []}
          onClose={() => {
            setAddEnabled(false);
          }}
          onSelect={append}
        />
      )}
    </div>
  );
};

export default ManufacturingServicesConsumptionCreateButtons;
