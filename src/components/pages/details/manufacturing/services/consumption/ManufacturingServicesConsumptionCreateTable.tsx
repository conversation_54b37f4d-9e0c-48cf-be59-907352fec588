import {FC} from 'react';

import {useTranslations} from 'next-intl';
import {useFormContext} from 'react-hook-form';

import {
  Table,
  TableBody,
  TableContainer,
  TableHead,
  TableHeadActions,
  TableHeader,
  TableRow,
} from '@/components/ui/Table';
import {TabsMenuItem} from '@/components/ui/Tabs';
import useHeight from '@/hooks/helpers/useHeight';
import {Consumption} from '@/types/manufacturing';

import ManufacturingServicesConsumptionCreateButtons from './ManufacturingServicesConsumptionCreateButtons';
import ManufacturingServicesConsumptionCreateTableRow from './ManufacturingServicesConsumptionCreateTableRow';

const ManufacturingServicesConsumptionCreateTable: FC = () => {
  const {
    formState: {errors},
    watch,
  } = useFormContext<Consumption>();
  const {elementRef} = useHeight();
  const t = useTranslations();

  return (
    <>
      <div className='mr-6 flex items-center justify-between'>
        <TabsMenuItem badge={watch('materials')?.length || 0} error={!!errors?.materials}>
          {t('items')}
        </TabsMenuItem>
        <ManufacturingServicesConsumptionCreateButtons />
      </div>
      <TableContainer ref={elementRef}>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>{t('name')}</TableHead>
              <TableHead className='text-right'>{t('quantity')}</TableHead>
              <TableHeadActions />
            </TableRow>
          </TableHeader>
          <TableBody className='overflow-y-hidden' isValidating={!watch()}>
            {watch('materials')?.map((item, index) => (
              <ManufacturingServicesConsumptionCreateTableRow index={index} item={item} key={`${item.id}-${index}`} />
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </>
  );
};

export default ManufacturingServicesConsumptionCreateTable;
