import {FC, useEffect, useState} from 'react';

import {joiResolver} from '@hookform/resolvers/joi';
import {ArrowLeftIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';
import {FormProvider, useForm} from 'react-hook-form';

import {Button} from '@/components/ui/Button';
import {Page, PageContent, PageHeader, PageHeaderTitle, PageTitle} from '@/components/ui/special/Page';
import {toast} from '@/components/ui/Toast';
import useLeaveConfirm from '@/hooks/helpers/useLeaveConfirm';
import {useRouter} from '@/hooks/helpers/useRouter';
import {consumptionSchema} from '@/hooks/useConsumptions';
import useServicingOrder from '@/hooks/useServicingOrder';
import useServicingOrderActions from '@/hooks/useServicingOrderActions';
import {Consumption} from '@/types/manufacturing';

import ManufacturingServicesConsumptionCreateData from './ManufacturingServicesConsumptionCreateData';
import ManufacturingServicesConsumptionCreateTable from './ManufacturingServicesConsumptionCreateTable';

type Props = {
  isService?: boolean;
  serviceId: string;
};

const ManufacturingServicesConsumptionCreate: FC<Props> = ({isService, serviceId}) => {
  const {isLoading, servicingOrder} = useServicingOrder(serviceId);
  const {finishServicingOrder} = useServicingOrderActions();
  const {back, replace} = useRouter();
  const [created, setCreated] = useState(false);
  const t = useTranslations();

  const {
    formState: {dirtyFields, errors, ...restUseFormState},
    handleSubmit,
    setValue,
    watch,
    ...restUseFormActions
  } = useForm<Consumption>({
    defaultValues: {
      date: new Date().toISOString(),
      materials: [],
    },
    mode: 'onSubmit',
    resolver: joiResolver(consumptionSchema),
  });

  const isDirty = !!Object.keys(dirtyFields).length;

  useLeaveConfirm(created ? false : isDirty, async () => await finishServicingOrder(serviceId, watch()));

  useEffect(() => {
    if (!isLoading && servicingOrder.materials) {
      setValue(
        'materials',
        servicingOrder.materials.map((material) => ({
          ...material,
          code: material.materialGoods?.[0]?.code,
          id: material.materialGoods?.[0]?.id,
          name: material.materialGoods?.[0]?.name,
          quantity: material.usedQuantity || 0,
        })),
      );
    }
  }, [isLoading, servicingOrder.materials, setValue]);

  useEffect(() => {
    if (created) back(`/manufacturing/${isService ? 'services' : 'orders'}`);
  }, [back, created, isService, serviceId, replace]);

  if (isLoading) return null;

  return (
    <Page>
      <PageTitle>{`${t('new consumption')} - ${isService ? t('services') : t('orders')} - ${t('manufacturing')}`}</PageTitle>
      <PageHeader>
        <PageHeaderTitle className='inline-flex items-center gap-2'>
          <Button
            onClick={() => back(`/manufacturing/${isService ? 'services' : 'orders'}/${serviceId}`)}
            size='icon'
            variant='none'
          >
            <ArrowLeftIcon />
          </Button>
          {t('new consumption')}
        </PageHeaderTitle>
        <div className='grow' />
        <Button
          onClick={() =>
            handleSubmit(
              (values) =>
                finishServicingOrder(serviceId, values).then((item) => {
                  if (item) setCreated(true);
                }),
              () => toast.warning(t('please fill in all mandatory fields before saving')),
            )()
          }
        >
          {t('create consumption')}
        </Button>
      </PageHeader>
      <PageContent>
        <FormProvider
          {...{
            formState: {
              dirtyFields,
              errors,
              ...restUseFormState,
            },
            handleSubmit,
            setValue,
            watch,
            ...restUseFormActions,
          }}
        >
          <ManufacturingServicesConsumptionCreateData order={servicingOrder} />
          <ManufacturingServicesConsumptionCreateTable />
        </FormProvider>
      </PageContent>
    </Page>
  );
};

export default ManufacturingServicesConsumptionCreate;
