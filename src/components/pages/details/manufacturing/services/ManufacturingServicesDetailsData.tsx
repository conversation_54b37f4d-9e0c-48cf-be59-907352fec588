import {FC} from 'react';

import {useAtomValue} from 'jotai';
import {useTranslations} from 'next-intl';
import {useFormContext} from 'react-hook-form';

import {detailsDataShownAtom} from '@/components/pages/details/detailsStore';
import {ControlledCombobox} from '@/components/ui/Combobox';
import {ControlledDatePickerInput} from '@/components/ui/DatePicker';
import {Input, InputLabel} from '@/components/ui/Input';
import {WithLabel} from '@/components/ui/Label';
import {DetailsData, DetailsDataGrid, DetailsDataGridContent} from '@/components/ui/special/DetailsData';
import useEmployees from '@/hooks/useEmployees';
import {ServicingStatus} from '@/types/global';
import {ServicingOrder} from '@/types/manufacturing';

const ManufacturingServicesDetailsData: FC = () => {
  const t = useTranslations();
  const {
    control,
    formState: {errors},
    register,
    setValue,
    watch,
  } = useFormContext<ServicingOrder>();
  const detailsDataShown = useAtomValue(detailsDataShownAtom(watch('id')));
  const {employees} = useEmployees();

  return (
    <DetailsData className='grid-cols-1' show={detailsDataShown}>
      <DetailsDataGrid title={t('order details')}>
        <DetailsDataGridContent>
          <WithLabel className='w-full'>
            <Input {...register('service.name')} disabled />
            <InputLabel>{t('service')}</InputLabel>
          </WithLabel>
          <WithLabel className='w-full'>
            <Input
              disabled={[ServicingStatus.DONE, ServicingStatus.MANUFACTURED].includes(watch('status'))}
              error={!!errors.quantity}
              onChange={({target: {value}}) => {
                setValue('quantity', Number(value));
              }}
              value={watch('quantity')}
            />
            <InputLabel>
              {t('quantity')} (
              {t(
                watch('measurementUnit.name')
                  ? (`unit.name.${watch('measurementUnit.name')}` as any)
                  : watch('service.measurementUnit')
                    ? (`unit.id.${watch('service.measurementUnit').toLowerCase()}` as any)
                    : 'unit.name.pcs',
              )}
              ){![ServicingStatus.DONE, ServicingStatus.MANUFACTURED].includes(watch('status')) && '*'}
            </InputLabel>
          </WithLabel>
          <WithLabel className='w-full'>
            <ControlledDatePickerInput control={control} controlName='createTime' disabled />
            <InputLabel>{t('created on')}</InputLabel>
          </WithLabel>
          <WithLabel className='w-full'>
            <ControlledDatePickerInput control={control} controlName='productionDeadline' disabled />
            <InputLabel>{t('production deadline')}</InputLabel>
          </WithLabel>
          {watch('customer.name') && (
            <WithLabel className='w-full'>
              <Input {...register('customer.name')} disabled />
              <InputLabel>{t('customer')}</InputLabel>
            </WithLabel>
          )}
          <WithLabel className='w-full'>
            <ControlledCombobox
              control={control}
              controlName={'assignedTo.id'}
              disabled={[ServicingStatus.DONE, ServicingStatus.MANUFACTURED].includes(watch('status'))}
              options={employees.map((employee) => ({
                id: employee.id,
                value: employee.name,
              }))}
            />
            <InputLabel>{t('assigned to')}</InputLabel>
          </WithLabel>
        </DetailsDataGridContent>
      </DetailsDataGrid>
    </DetailsData>
  );
};

export default ManufacturingServicesDetailsData;
