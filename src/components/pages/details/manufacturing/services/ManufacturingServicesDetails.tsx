import {FC, useEffect} from 'react';

import {useAtom} from 'jotai';
import {ArrowLeftIcon, BookTextIcon, EyeIcon, FileChartColumnIcon, ReceiptTextIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';
import {FormProvider} from 'react-hook-form';

import {detailsDataShownAtom} from '@/components/pages/details/detailsStore';
import {Button} from '@/components/ui/Button';
import {DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger} from '@/components/ui/DropdownMenu';
import ActivityButton from '@/components/ui/special/ActivityButton';
import {HideableContentToggle} from '@/components/ui/special/HideableContentToggle';
import {Link} from '@/components/ui/special/Link';
import {Page, PageContent, PageHeader, PageHeaderTitle, PageTitle} from '@/components/ui/special/Page';
import {<PERSON><PERSON>, <PERSON><PERSON>List, TabsTrigger} from '@/components/ui/Tabs';
import {toast} from '@/components/ui/Toast';
import {useHasPermission} from '@/hooks/helpers/useHasPermission';
import {useRouter} from '@/hooks/helpers/useRouter';
import useActivities from '@/hooks/useActivities';
import useServicingOrder from '@/hooks/useServicingOrder';
import useServicingOrderActions from '@/hooks/useServicingOrderActions';
import {ActivityType, ServicingStatus} from '@/types/global';
import {formatNumber} from '@/utils/format';
import ManufacturingServicesDetailsEditor from 'components/pages/details/manufacturing/services/ManufacturingServicesDetailsEditor';
import ManufacturingServicesDetailsBulkMaterialsButtons from 'components/pages/details/manufacturing/services/manufacturingServicesDetailsMaterials/ManufacturingServicesDetailsBulkMaterialsButtons';

import ManufacturingServicesDetailsData from './ManufacturingServicesDetailsData';
import ManufacturingServicesDetailsFilesButtons from './manufacturingServicesDetailsFiles/ManufacturingServicesDetailsFilesButtons';
import ManufacturingServicesDetailsFilesTable from './manufacturingServicesDetailsFiles/ManufacturingServicesDetailsFilesTable';
import ManufacturingServicesDetailsMaterialsButtons from './manufacturingServicesDetailsMaterials/ManufacturingServicesDetailsMaterialsButtons';
import ManufacturingServicesDetailsMaterialsTable from './manufacturingServicesDetailsMaterials/ManufacturingServicesDetailsMaterialsTable';
import ManufacturingServicesDetailsOperationsButtons from './manufacturingServicesDetailsOperations/ManufacturingServicesDetailsOperationsButtons/ManufacturingServicesDetailsOperationsButtons';
import ManufacturingServicesDetailsOperationsTable from './manufacturingServicesDetailsOperations/ManufacturingServicesDetailsOperationsTable';
import {ManufacturingServiceTab, manufacturingServiceTabAtom} from './manufacturingServicesDetailsStore';

type Props = {
  id: string;
};

const ManufacturingServicesDetails: FC<Props> = ({id}) => {
  const t = useTranslations();
  const {
    deleteManufacturingOrderFile,
    isDirty,
    isLoading,
    saveServicingOrder,
    uploadServicingOrderFile,
    useFormActions: {
      formState: {errors, ...restUseFormState},
      handleSubmit,
      register,
      resetField,
      setError,
      watch,
      ...restUseFormActions
    },
  } = useServicingOrder(id);
  const {activities, createActivity} = useActivities(id, ActivityType.SERVICING);
  const [view, setView] = useAtom(manufacturingServiceTabAtom(id));
  const {updateServicingOrderStatus} = useServicingOrderActions();
  const {hasPermission, isLoading: permissionIsLoading} = useHasPermission();
  const {
    back,
    push,
    query: {tab},
  } = useRouter();

  useEffect(() => {
    if (tab) setView(tab as ManufacturingServiceTab);
  }, [setView, tab]);

  if (isLoading || permissionIsLoading) return null;

  return (
    <Page>
      <PageTitle>{`${watch('number')} - ${t('manufacturing')}`}</PageTitle>
      <PageHeader>
        <PageHeaderTitle className='inline-flex items-center gap-2'>
          <Button onClick={() => back('/manufacturing/services')} size='icon' variant='none'>
            <ArrowLeftIcon />
          </Button>
          <div className='flex flex-col'>
            {watch('number') || t('new manufacturing order')}
            <div className='text-sm font-normal'>{t(watch('status'))}</div>
          </div>
        </PageHeaderTitle>
        <HideableContentToggle store={detailsDataShownAtom(watch('id'))} />
        <div className='grow' />
        <ActivityButton activities={activities} createActivity={createActivity} />
        {isDirty && <Button onClick={saveServicingOrder}>{t('save')}</Button>}
        {!isDirty && (
          <>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant='secondary'>
                  <EyeIcon /> {t('documents')}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                {watch('goodsAccompanyingNotes').map((note) => (
                  <DropdownMenuItem asChild key={note.id}>
                    <Link href={`/manufacturing/services/${id}/notes/${note.id}/preview`}>
                      <BookTextIcon strokeWidth={1} /> {note.number}
                    </Link>
                  </DropdownMenuItem>
                ))}
                {watch('materialIssueNote') && (
                  <DropdownMenuItem asChild>
                    <Link href={`/inventory/consumptions/${watch('materialIssueNote.id')}`}>
                      <ReceiptTextIcon strokeWidth={1} /> {t('consumption')}
                    </Link>
                  </DropdownMenuItem>
                )}
                {hasPermission('financial', 'manufacturing') && (
                  <DropdownMenuItem asChild>
                    <Link
                      href={`/manufacturing/services/${id}/report${[ServicingStatus.MANUFACTURING, ServicingStatus.SUBMITTED].includes(watch('status')) ? '/preview' : ''}`}
                    >
                      <FileChartColumnIcon strokeWidth={1} /> {t('service report')}
                    </Link>
                  </DropdownMenuItem>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
            {watch('status') === ServicingStatus.SUBMITTED && (
              <Button onClick={() => updateServicingOrderStatus(watch(), ServicingStatus.MANUFACTURING)}>
                {t('start work')}
              </Button>
            )}
            {watch('status') === ServicingStatus.MANUFACTURING && (
              <Button
                onClick={() => {
                  if (!watch('manufacturingOperations')?.length) {
                    setError('manufacturingOperations', {type: 'minLength'});
                    return toast.warning(t('please fill in all mandatory fields before saving'));
                  }

                  push(`/manufacturing/services/${id}/report/preview`);
                }}
              >
                {t('finish work and create service report')}
              </Button>
            )}
            {watch('status') === ServicingStatus.MANUFACTURED && (
              <Button onClick={() => push(`/manufacturing/services/${id}/consumption`)}>
                {t('create consumption')}
              </Button>
            )}
          </>
        )}
      </PageHeader>
      <PageContent>
        <FormProvider
          {...{
            formState: {errors, ...restUseFormState},
            handleSubmit,
            register,
            resetField,
            setError,
            watch,
            ...restUseFormActions,
          }}
        >
          <ManufacturingServicesDetailsData />
          <div className='mx-6 flex items-center justify-between'>
            <div className='inline-flex items-center'>
              <Tabs onValueChange={(value) => setView(value as ManufacturingServiceTab)} value={view} variant='menu'>
                <TabsList variant='menu'>
                  <TabsTrigger value='details' variant='menu'>
                    {t('service details')}
                  </TabsTrigger>
                  <TabsTrigger badge={formatNumber(watch('materials')?.length)} value='materials' variant='menu'>
                    {t('materials')}
                  </TabsTrigger>
                  <TabsTrigger
                    badge={formatNumber(watch('manufacturingOperations')?.length)}
                    error={!!errors?.manufacturingOperations}
                    value='workmanship'
                    variant='menu'
                  >
                    {t('workmanship')}
                  </TabsTrigger>
                  <TabsTrigger badge={formatNumber(watch('files')?.length)} value='files' variant='menu'>
                    {t('files')}
                  </TabsTrigger>
                </TabsList>
              </Tabs>
            </div>
            <div className='flex gap-4 items-center'>
              {![ServicingStatus.DONE, ServicingStatus.MANUFACTURED].includes(watch('status')) && (
                <>
                  {view === 'workmanship' && <ManufacturingServicesDetailsOperationsButtons />}
                  {view === 'materials' && <ManufacturingServicesDetailsMaterialsButtons />}
                  {['details', 'materials'].includes(view) && <ManufacturingServicesDetailsBulkMaterialsButtons />}
                </>
              )}
              {view === 'files' && <ManufacturingServicesDetailsFilesButtons uploadFile={uploadServicingOrderFile} />}
            </div>
          </div>
          {view === 'workmanship' && <ManufacturingServicesDetailsOperationsTable />}
          {view === 'materials' && <ManufacturingServicesDetailsMaterialsTable />}
          {view === 'files' && <ManufacturingServicesDetailsFilesTable deleteFile={deleteManufacturingOrderFile} />}
          {view === 'details' && <ManufacturingServicesDetailsEditor />}
        </FormProvider>
      </PageContent>
    </Page>
  );
};

export default ManufacturingServicesDetails;
