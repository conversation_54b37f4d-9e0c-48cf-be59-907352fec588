import {FC, useCallback, useState} from 'react';

import {useAtomValue} from 'jotai';
import {first} from 'lodash';
import {PlusIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';
import {UseFieldArrayAppend, useFormContext} from 'react-hook-form';

import {Button} from '@/components/ui/Button';
import {MultiSelect} from '@/components/ui/special/Multiselect';
import useItemActions from '@/hooks/useItemActions';
import useItems from '@/hooks/useItems';
import useManufacturingOrderActions from '@/hooks/useManufacturingOrderActions';
import {defaultCurrencyAtom} from '@/store/defaults';
import {ManufacturingOrder, ManufacturingOrderOperationStep} from '@/types/manufacturing';
import {newId} from '@/utils/common';

type Props = {
  onAdd: UseFieldArrayAppend<ManufacturingOrder, 'manufacturingOperations'>;
  onClose: () => void;
};

const ManufacturingOrderDetailsOperationsButtonsItemsTab: FC<Props> = ({onAdd, onClose}) => {
  const {items} = useItems({onlyProduced: true});
  const {getItem} = useItemActions();
  const {setValue, watch} = useFormContext<ManufacturingOrder>();
  const defaultCurrency = useAtomValue(defaultCurrencyAtom);
  const [newOperations, setNewOperations] = useState<ManufacturingOrderOperationStep[]>([]);
  const t = useTranslations();
  const {processMaterials} = useManufacturingOrderActions();

  const handleAdd = useCallback(() => {
    newOperations.map((operation) => onAdd(operation));
    setTimeout(() => {
      setValue('materials', processMaterials(watch()));
      onClose();
    }, 200);
  }, [newOperations, onAdd, setValue, processMaterials, watch, onClose]);

  const handleSelect = useCallback(
    async (values: string[]) => {
      const value = first(values);
      if (!value) return setNewOperations([]);
      const item = await getItem(value);
      if (!item) return setNewOperations([]);
      setNewOperations(
        item.manufacturingOperations.map((operation) => ({
          ...operation,
          id: operation.id || newId(),
          materials: (operation.materials || [])
            .filter((material) => material.options?.[0])
            .map((material) => {
              const firstOption = material.options[0];
              const safeQuantity = material.quantity || 1;
              const totalOrderQuantity = watch('quantity') || 1;
              const cost = firstOption.cost ?? {amount: 0, currency: defaultCurrency};

              return {
                ...material,
                allAvailable: firstOption.available ?? false,
                cost,
                materialGoods: [
                  {
                    ...firstOption,
                    available: firstOption.available ?? false,
                    cost,
                    inventoryCostPerItem: {
                      ...cost,
                      amount: safeQuantity && totalOrderQuantity ? cost.amount / safeQuantity / totalOrderQuantity : 0,
                    },
                    lastOrderedFrom: null,
                    measurementUnit: firstOption.measurementUnit,
                  },
                ],
                onStock: 0,
                required: safeQuantity,
                requiredDimensions: material.requiredDimensions ?? null,
                requiredTotal: safeQuantity,
                reservedTotal: 0,
                subassemblyManufacturingOrder: null,
                totalRequiredDimensions: null,
                wastePercentage: material.wastePercentage,
              };
            }),
        })),
      );
    },
    [defaultCurrency, getItem, watch],
  );

  return (
    <>
      <MultiSelect
        autoFocus
        onValueChange={handleSelect}
        options={items.map((item) => ({id: item.id, value: item.name}))}
        renderNotFound={() => t('item not found')}
        searchPlaceholder={t('items')}
        single
      />
      <Button className='w-full' onClick={handleAdd}>
        <PlusIcon className='size-5' /> {t('add number of tasks', {number: newOperations.length})}
      </Button>
    </>
  );
};

export default ManufacturingOrderDetailsOperationsButtonsItemsTab;
