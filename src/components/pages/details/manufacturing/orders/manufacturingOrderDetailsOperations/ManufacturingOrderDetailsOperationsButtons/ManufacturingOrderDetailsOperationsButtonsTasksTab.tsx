import {FC, useCallback, useState} from 'react';

import {PlusIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';
import {UseFieldArrayAppend} from 'react-hook-form';

import {Button} from '@/components/ui/Button';
import {MultiSelect} from '@/components/ui/special/Multiselect';
import useOperationActions from '@/hooks/useOperationActions';
import useOperations from '@/hooks/useOperations';
import {ManufacturingOrder} from '@/types/manufacturing';
import {newId} from '@/utils/common';

type Props = {
  onAdd: UseFieldArrayAppend<ManufacturingOrder, 'manufacturingOperations'>;
  onClose: () => void;
};

const ManufacturingOrderDetailsOperationsButtonsTasksTab: FC<Props> = ({onAdd, onClose}) => {
  const {operations} = useOperations();
  const [values, setValues] = useState<string[]>([]);
  const t = useTranslations();
  const {createOperation} = useOperationActions();

  const handleAdd = useCallback(() => {
    if (values.length > 0) {
      const newOperations = operations.filter((operation) => values.includes(operation.id));

      newOperations.map((operation) =>
        onAdd({
          ...operation,
          candidateEmployees: operation.employees,
          candidateWorkstations: operation.workstations,
          durationInMinutes: 0,
          id: newId(),
          materials: [],
          parallelizable: false,
        }),
      );
    }
    onClose();
  }, [values, onClose, operations, onAdd]);

  return (
    <>
      <MultiSelect
        autoFocus
        defaultValue={values}
        onValueChange={setValues}
        options={operations.map((operation) => ({id: operation.id, value: operation.name}))}
        renderNotFound={(query) => (
          <Button
            className='w-full justify-start'
            onClick={() => {
              createOperation({name: query}).then((operation) => {
                if (operation) setValues([...values, operation.id]);
              });
            }}
            variant='secondary'
          >
            {t('create operation')}
          </Button>
        )}
        searchPlaceholder={t('tasks')}
      />
      <Button className='w-full' onClick={handleAdd}>
        <PlusIcon className='size-5' /> {t('add number of tasks', {number: values.length})}
      </Button>
    </>
  );
};

export default ManufacturingOrderDetailsOperationsButtonsTasksTab;
