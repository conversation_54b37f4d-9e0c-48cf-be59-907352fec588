import {FC} from 'react';

import {closestCorners, DndContext, DragEndEvent, PointerSensor, useSensor, useSensors} from '@dnd-kit/core';
import {SortableContext, verticalListSortingStrategy} from '@dnd-kit/sortable';
import {useAtomValue} from 'jotai';
import {useTranslations} from 'next-intl';
import {useFieldArray, useFormContext} from 'react-hook-form';

import {detailsDataShownAtom} from '@/components/pages/details/detailsStore';
import {
  Table,
  TableBody,
  TableContainer,
  TableHead,
  TableHeadActions,
  TableHeader,
  TableRow,
} from '@/components/ui/Table';
import {useHasPermission} from '@/hooks/helpers/useHasPermission';
import useHeight from '@/hooks/helpers/useHeight';
import {ManufacturingOrder} from '@/types/manufacturing';

import ManufacturingOrderDetailsOperationsTableRow from './ManufacturingOrderDetailsOperationsTableRow';

const ManufacturingOrderDetailsOperationsTable: FC = () => {
  const {control, watch} = useFormContext<ManufacturingOrder>();
  const detailsDataShown = useAtomValue(detailsDataShownAtom(watch('id')));
  const {move} = useFieldArray({control, name: 'manufacturingOperations'});
  const {elementRef} = useHeight({dependencies: [detailsDataShown]});
  const t = useTranslations();
  const {hasPermission, isLoading} = useHasPermission();

  const sensors = useSensors(useSensor(PointerSensor));

  const handleDragEnd = ({active, over}: DragEndEvent) => {
    if (active.id !== over?.id) {
      const oldIndex = watch('manufacturingOperations').findIndex((order) => order.id === active.id);
      const newIndex = over ? watch('manufacturingOperations').findIndex((order) => order.id === over.id) : -1;
      if (oldIndex !== -1 && newIndex !== -1) {
        move(oldIndex, newIndex);
      }
    }
  };

  return (
    <TableContainer ref={elementRef}>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>{t('name')}</TableHead>
            <TableHead className='text-right'>{t('duration')}</TableHead>
            {hasPermission('financial', 'manufacturing') && (
              <TableHead className='text-right'>{t('cost per hour')}</TableHead>
            )}
            <TableHead>{t('materials')}</TableHead>
            <TableHead>{t('employees')}</TableHead>
            <TableHead>{t('workstations')}</TableHead>
            <TableHead>{t('parallelization')}</TableHead>
            <TableHeadActions />
          </TableRow>
        </TableHeader>
        <TableBody className='overflow-y-hidden' isValidating={!watch() || isLoading}>
          <DndContext collisionDetection={closestCorners} onDragEnd={handleDragEnd} sensors={sensors}>
            <SortableContext items={watch('manufacturingOperations')} strategy={verticalListSortingStrategy}>
              {watch('manufacturingOperations').map((operation, index) => (
                <ManufacturingOrderDetailsOperationsTableRow
                  index={index}
                  key={`${operation.id}-${index}`}
                  operation={operation}
                />
              ))}
            </SortableContext>
          </DndContext>
        </TableBody>
      </Table>
    </TableContainer>
  );
};

export default ManufacturingOrderDetailsOperationsTable;
