import {FC, useState} from 'react';

import {useSortable} from '@dnd-kit/sortable';
import {CSS} from '@dnd-kit/utilities';
import {useAtomValue} from 'jotai';
import {sortBy} from 'lodash';
import {ChevronDownIcon, ChevronUpIcon, GripHorizontalIcon, PlusIcon, Trash2Icon} from 'lucide-react';
import {useTranslations} from 'next-intl';
import {useFieldArray, useFormContext} from 'react-hook-form';

import {Badge} from '@/components/ui/Badge';
import {Button, IconButton} from '@/components/ui/Button';
import {PopoverDurationInput} from '@/components/ui/Input';
import {TableMultiSelect} from '@/components/ui/special/TableMultiSelect';
import {Switch} from '@/components/ui/Switch';
import {TableActions, TableCell, TableRow} from '@/components/ui/Table';
import {useHasPermission} from '@/hooks/helpers/useHasPermission';
import useEmployeeActions from '@/hooks/useEmployeeActions';
import useEmployees from '@/hooks/useEmployees';
import useWorkstationActions from '@/hooks/useWorkstationActions';
import useWorkstations from '@/hooks/useWorkstations';
import {defaultCurrencyAtom} from '@/store/defaults';
import {ManufacturingOrder, ManufacturingOrderOperationStep} from '@/types/manufacturing';
import {classes, minutesToWorkHoursTimeString} from '@/utils/common';
import {formatCurrency} from '@/utils/format';

import ManufacturingOrderDetailsMaterialsTable from '../manufacturingOrderDetailsMaterials/ManufacturingOrderDetailsMaterialsTable';
import ManufacturingOrderDetailsOperationsMaterialsButton from '../manufacturingOrderDetailsOperations/ManufacturingOrderDetailsOperationsButtons/ManufacturingOrderDetailsOperationsMaterialsButton';

type Props = {
  index: number;
  operation: ManufacturingOrderOperationStep;
};

const ManufacturingOrderDetailsOperationsTableRow: FC<Props> = ({index, operation}) => {
  const {attributes, isDragging, listeners, setNodeRef, transform, transition} = useSortable({
    id: operation.id,
  });
  const {employees} = useEmployees();
  const {createEmployee} = useEmployeeActions();
  const {workstations} = useWorkstations();
  const {createWorkstation} = useWorkstationActions();
  const defaultCurrency = useAtomValue(defaultCurrencyAtom);
  const t = useTranslations();
  const {
    control,
    formState: {errors},
    watch,
  } = useFormContext<ManufacturingOrder>();
  const {remove, update} = useFieldArray({control, name: 'manufacturingOperations'});
  const {hasPermission, isLoading} = useHasPermission();
  const [open, setOpen] = useState(false);
  const [addMaterials, setAddMaterials] = useState(false);

  if (isLoading) return null;

  return (
    <>
      <TableRow
        className={classes(isDragging && 'bg-background shadow-xl hover:bg-background')}
        ref={setNodeRef}
        style={{
          ...(transform ? {transform: CSS.Transform.toString({...transform, scaleX: 1, scaleY: 1})} : {}),
          transition,
        }}
      >
        <TableCell className='mt-0.5 inline-flex h-14 items-center gap-2 align-top'>
          <span {...listeners} {...attributes}>
            <GripHorizontalIcon className='size-5 text-slate-900/40' />
          </span>
          {operation.name}
        </TableCell>
        <TableCell className='text-right'>
          <PopoverDurationInput
            className='w-fit justify-end'
            error={!!errors.manufacturingOperations?.[index]?.durationInMinutes}
            minutes={operation.durationInMinutes}
            onChange={(value) => {
              update(index, {
                ...operation,
                durationInMinutes: value,
              });
            }}
          >
            {minutesToWorkHoursTimeString(operation.durationInMinutes)}
          </PopoverDurationInput>
        </TableCell>
        {hasPermission('financial', 'manufacturing') && (
          <TableCell className='text-right'>{formatCurrency(operation.costPerHour)}</TableCell>
        )}
        <TableCell>
          <div className='flex items-center gap-2'>
            <IconButton icon={<PlusIcon className='size-6' />} onClick={() => setAddMaterials(true)} />
            {operation.materials?.length > 0 && (
              <Badge className='cursor-pointer' onClick={() => setOpen(!open)} size='sm' variant='info'>
                {operation.materials?.length} {t('materials')}
                {!open && <ChevronDownIcon className='size-5' />}
                {open && <ChevronUpIcon className='size-5' />}
              </Badge>
            )}
          </div>
        </TableCell>
        <TableCell>
          <TableMultiSelect
            defaultValue={operation.candidateEmployees.map((employee) => employee.id)}
            onValueChange={(value) =>
              update(index, {
                ...operation,
                candidateEmployees: value.map((id) => ({
                  id,
                  name: '',
                })),
              })
            }
            options={sortBy(
              employees.map((employee) => ({id: employee.id, value: employee.name})),
              'name',
            )}
            renderNotFound={(query) => (
              <Button
                className='w-full justify-start'
                onClick={() => {
                  createEmployee({name: query, position: '-'}).then((employee) => {
                    update(index, {
                      ...operation,
                      candidateEmployees: [...operation.candidateEmployees, employee],
                    });
                  });
                }}
                variant='secondary'
              >
                {t('create employee')}
              </Button>
            )}
            searchPlaceholder={t('search employee')}
            side='right'
            summaryLabel={t('selected.male')}
          />
        </TableCell>
        <TableCell>
          <TableMultiSelect
            defaultValue={operation.candidateWorkstations.map((workstation) => workstation.id)}
            onValueChange={(value) =>
              update(index, {
                ...operation,
                candidateWorkstations: value.map((id) => ({
                  id,
                  name: '',
                })),
              })
            }
            options={sortBy(
              workstations.map((workstation) => ({id: workstation.id, value: workstation.name})),
              'name',
            )}
            renderNotFound={(query) => (
              <Button
                className='w-full justify-start'
                onClick={() => {
                  createWorkstation({costPerHour: {amount: 0, currency: defaultCurrency}, name: query}).then(
                    (workstation) => {
                      update(index, {
                        ...operation,
                        candidateWorkstations: [...operation.candidateWorkstations, workstation],
                      });
                    },
                  );
                }}
                variant='secondary'
              >
                {t('create workstation')}
              </Button>
            )}
            searchPlaceholder={t('search workstation')}
            side='left'
            summaryLabel={t('selected.female')}
          />
        </TableCell>
        <TableCell>
          <Switch
            checked={operation.parallelizable}
            className='mt-2'
            onCheckedChange={(value) =>
              update(index, {
                ...operation,
                parallelizable: value,
              })
            }
          />
        </TableCell>
        <TableActions>
          <Button className='mt-1.5' onClick={() => remove(index)} size='icon' variant='none'>
            <Trash2Icon className='size-5 text-red' strokeWidth={1} />
          </Button>
        </TableActions>
      </TableRow>
      {open && (
        <TableRow className='bg-input/50 border-b-black'>
          <TableCell className='p-0' colSpan={99}>
            <ManufacturingOrderDetailsMaterialsTable
              embedded
              materials={watch(`manufacturingOperations.${index}.materials`)}
              operationIndex={index}
            />
          </TableCell>
        </TableRow>
      )}
      {addMaterials && (
        <ManufacturingOrderDetailsOperationsMaterialsButton index={index} onClose={() => setAddMaterials(false)} />
      )}
    </>
  );
};

export default ManufacturingOrderDetailsOperationsTableRow;
