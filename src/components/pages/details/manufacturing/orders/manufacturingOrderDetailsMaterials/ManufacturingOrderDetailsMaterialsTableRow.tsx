import {FC, useCallback} from 'react';

import Joi from 'joi';
import {min} from 'lodash';
import {Trash2Icon} from 'lucide-react';
import {useTranslations} from 'next-intl';
import {useFieldArray, useFormContext} from 'react-hook-form';

import {Badge} from '@/components/ui/Badge';
import {Button} from '@/components/ui/Button';
import {PopoverInput} from '@/components/ui/Input';
import InventoryItemLink from '@/components/ui/special/InventoryItemLink/InventoryItemLink';
import {ItemHistoryModal} from '@/components/ui/special/ItemHistoryModal';
import {OrderButton} from '@/components/ui/special/OrderButton';
import SetDimensionPopover from '@/components/ui/special/SetDimensionPopover';
import {TableActions, TableCell, TableRow} from '@/components/ui/Table';
import {useHasPermission} from '@/hooks/helpers/useHasPermission';
import useItemStockActions from '@/hooks/useItemStockActions';
import useManufacturingOrderActions from '@/hooks/useManufacturingOrderActions';
import {ManufacturingStatus} from '@/types/global';
import {ManufacturingOrder, ManufacturingOrderMaterial} from '@/types/manufacturing';
import {formatCurrency, formatNumber} from '@/utils/format';

type Props = {
  embedded?: boolean;
  index: number;
  item: ManufacturingOrderMaterial;
  operationIndex?: number;
};

const ManufacturingOrderDetailsMaterialsTableRow: FC<Props> = ({index, item, operationIndex}) => {
  const t = useTranslations();
  const {hasPermission, isLoading} = useHasPermission();
  const {control, setValue, watch} = useFormContext<ManufacturingOrder>();
  const {getItemStocks} = useItemStockActions();
  const {remove, update} = useFieldArray({control, name: `manufacturingOperations.${operationIndex || 0}.materials`});
  const {processMaterials} = useManufacturingOrderActions();

  const primaryGood = item?.materialGoods?.[0];

  const updateMaterial = useCallback(
    (material: Partial<ManufacturingOrderMaterial>) => {
      update(index, {
        ...item,
        ...material,
      });
      setTimeout(() => {
        setValue('materials', processMaterials(watch()));
      }, 200);
    },
    [index, item, processMaterials, setValue, update, watch],
  );

  if (isLoading) return null;

  return (
    <TableRow>
      <TableCell>
        <InventoryItemLink item={primaryGood} />
      </TableCell>
      <TableCell className='text-right'>
        <div className='flex items-center justify-end gap-1'>
          {operationIndex !== undefined && primaryGood?.material && (
            <SetDimensionPopover
              itemId={primaryGood.id}
              onChange={(value, dimensions) =>
                updateMaterial({
                  required: value,
                  requiredDimensions: dimensions,
                  requiredTotal: value * watch('quantity'),
                  totalRequiredDimensions: null,
                })
              }
              shape={primaryGood.material.shape.shape}
              value={item.requiredDimensions}
            />
          )}
          <PopoverInput
            className='w-fit justify-end'
            defaultValue={item.required?.toString()}
            disabled={watch('status') !== ManufacturingStatus.CUSTOMIZATION_NEEDED || operationIndex === undefined}
            label={t('required')}
            onChange={async (value) => {
              const stocks = await getItemStocks(primaryGood?.id, {
                id: watch('id'),
                type: 'manufacturing',
              });
              updateMaterial({
                allAvailable: stocks >= Number(value) * watch('quantity'),
                cost: {
                  ...item.cost,
                  amount: (primaryGood.inventoryCostPerItem?.amount || 0) * (watch('quantity') * Number(value)),
                },
                onStock: stocks,
                required: Number(value),
                requiredDimensions: null,
                requiredTotal: Number(value) * watch('quantity'),
                reservedTotal: min([item.onStock, Number(value) * watch('quantity')]) || 0,
              });
            }}
            validation={Joi.string().required()}
          >
            {formatNumber(item.required)} {t(`unit.name.${primaryGood.measurementUnit.name.toLowerCase()}` as any)}
          </PopoverInput>
        </div>
      </TableCell>
      <TableCell className='text-right'>
        <div className='flex items-center justify-end gap-1'>
          {operationIndex !== undefined && primaryGood?.material && (
            <SetDimensionPopover
              itemId={primaryGood.id}
              onChange={(value, dimensions) => {
                updateMaterial({
                  required: value / watch('quantity'),
                  requiredDimensions: null,
                  requiredTotal: value,
                  totalRequiredDimensions: {
                    length: dimensions.length,
                    width: dimensions.width || undefined,
                  },
                });
              }}
              shape={primaryGood.material.shape.shape}
              value={
                item.totalRequiredDimensions
                  ? {
                      length: item.totalRequiredDimensions.length,
                      width: item.totalRequiredDimensions.width || undefined,
                    }
                  : null
              }
            />
          )}
          <PopoverInput
            className='w-fit justify-end'
            defaultValue={item.requiredTotal.toString()}
            disabled={watch('status') !== ManufacturingStatus.CUSTOMIZATION_NEEDED || operationIndex === undefined}
            label={t('required total')}
            onChange={async (value) => {
              const stocks = await getItemStocks(primaryGood?.id, {
                id: watch('id'),
                type: 'manufacturing',
              });

              updateMaterial({
                allAvailable: stocks >= Number(value),
                cost: {...item.cost, amount: (primaryGood.inventoryCostPerItem?.amount || 0) * Number(value)},
                onStock: stocks,
                required: Number((Number(value) / watch('quantity')).toFixed(4)),
                requiredDimensions: null,
                requiredTotal: Number(value),
                reservedTotal: min([item.onStock, Number(value)]) || 0,
              });
            }}
            validation={Joi.string().required()}
          >
            {formatNumber(item.requiredTotal)} {t(`unit.name.${primaryGood.measurementUnit.name.toLowerCase()}` as any)}
          </PopoverInput>
        </div>
      </TableCell>
      <TableCell className='text-right'>
        {formatNumber(item.onStock)} {t(`unit.name.${primaryGood.measurementUnit.name.toLowerCase()}` as any)}
      </TableCell>
      <TableCell className='text-right'>
        <PopoverInput
          className='w-fit justify-end'
          defaultValue={item.wastePercentage.toString()}
          disabled={watch('status') !== ManufacturingStatus.CUSTOMIZATION_NEEDED || operationIndex === undefined}
          label={`${t('waste')} (%)`}
          onChange={(value) => {
            updateMaterial({
              wastePercentage: Number(value),
            });
          }}
          validation={Joi.number().required().positive().allow(0)}
        >
          {formatNumber(item.wastePercentage)}%
        </PopoverInput>
      </TableCell>
      {hasPermission('financial', 'manufacturing') && (
        <TableCell className='text-right'>{formatCurrency(item.cost)}</TableCell>
      )}
      <TableCell>
        <ItemHistoryModal item={{...primaryGood, measurementUnit: primaryGood.measurementUnit}}>
          <Badge
            className='cursor-pointer'
            variant={item.allAvailable === null ? 'secondary' : item.allAvailable ? 'success' : 'error'}
          >
            {t(item.allAvailable === null ? 'unknown' : item.allAvailable ? 'available' : 'unavailable', {
              isPlural: 'false',
            })}
          </Badge>
        </ItemHistoryModal>
      </TableCell>
      {![ManufacturingStatus.DONE, ManufacturingStatus.MANUFACTURED].includes(watch('status')) && (
        <TableActions>
          <OrderButton forTable item={{...primaryGood, produced: primaryGood.produced}} quantity={item.requiredTotal} />
          {operationIndex !== undefined && watch('status') === ManufacturingStatus.CUSTOMIZATION_NEEDED && (
            <Button
              className='mt-1.5'
              onClick={() => {
                remove(index);
                setTimeout(() => {
                  setValue('materials', processMaterials(watch()));
                }, 200);
              }}
              size='icon'
              variant='none'
            >
              <Trash2Icon className='size-5 text-red' strokeWidth={1} />
            </Button>
          )}
        </TableActions>
      )}
    </TableRow>
  );
};

export default ManufacturingOrderDetailsMaterialsTableRow;
