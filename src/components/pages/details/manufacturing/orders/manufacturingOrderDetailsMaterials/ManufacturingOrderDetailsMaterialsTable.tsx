import {FC} from 'react';

import {useAtomValue} from 'jotai';
import {useTranslations} from 'next-intl';
import {useFormContext} from 'react-hook-form';

import {detailsDataShownAtom} from '@/components/pages/details/detailsStore';
import {
  Table,
  TableBody,
  TableContainer,
  TableHead,
  TableHeadActions,
  TableHeader,
  TableRow,
} from '@/components/ui/Table';
import {useHasPermission} from '@/hooks/helpers/useHasPermission';
import useHeight from '@/hooks/helpers/useHeight';
import {ManufacturingStatus} from '@/types/global';
import {ManufacturingOrder, ManufacturingOrderMaterial} from '@/types/manufacturing';
import {classes} from '@/utils/common';

import ManufacturingOrderDetailsMaterialsTableRow from './ManufacturingOrderDetailsMaterialsTableRow';

type Props = {
  embedded?: boolean;
  materials: ManufacturingOrderMaterial[];
  operationIndex?: number;
};

const ManufacturingOrderDetailsMaterialsTable: FC<Props> = ({embedded, materials, operationIndex}) => {
  const {hasPermission, isLoading} = useHasPermission();
  const {watch} = useFormContext<ManufacturingOrder>();
  const detailsDataShown = useAtomValue(detailsDataShownAtom(watch('id')));
  const {elementRef} = useHeight({dependencies: [detailsDataShown]});
  const t = useTranslations();

  return (
    <TableContainer ref={embedded ? null : elementRef}>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className={classes(embedded && 'h-auto py-1')}>{t('name')}</TableHead>
            <TableHead className={classes('text-right', embedded && 'h-auto py-1')}>{t('required')}</TableHead>
            <TableHead className={classes('text-right', embedded && 'h-auto py-1')}>{t('required total')}</TableHead>
            <TableHead className={classes('text-right', embedded && 'h-auto py-1')}>{t('available total')}</TableHead>
            <TableHead className={classes('text-right', embedded && 'h-auto py-1')}>{t('waste')}</TableHead>
            {hasPermission('financial', 'manufacturing') && (
              <TableHead className={classes('text-right', embedded && 'h-auto py-1')}>{t('cost')}</TableHead>
            )}
            <TableHead className={classes(embedded && 'h-auto py-1')}>{t('availability')}</TableHead>
            {![ManufacturingStatus.CONSUMPTION_RECORDED, ManufacturingStatus.ACCOUNTED, ManufacturingStatus.MANUFACTURED].includes(watch('status')) && (
              <TableHeadActions className={classes(embedded && 'h-auto py-1')} />
            )}
          </TableRow>
        </TableHeader>
        <TableBody className='overflow-y-hidden' isValidating={isLoading}>
          {materials?.map((item, materialIndex) => (
            <ManufacturingOrderDetailsMaterialsTableRow
              embedded={embedded}
              index={materialIndex}
              item={item}
              key={`${item.materialGoods?.[0]?.name}-${materialIndex}`}
              operationIndex={operationIndex}
            />
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );
};

export default ManufacturingOrderDetailsMaterialsTable;
