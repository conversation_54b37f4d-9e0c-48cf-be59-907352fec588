import {FC} from 'react';

import {useAtomValue} from 'jotai';
import {useTranslations} from 'next-intl';
import {useFieldArray, useFormContext} from 'react-hook-form';

import {detailsDataShownAtom} from '@/components/pages/details/detailsStore';
import {ControlledDatePickerInput} from '@/components/ui/DatePicker';
import {Input, InputLabel} from '@/components/ui/Input';
import {WithLabel} from '@/components/ui/Label';
import {DetailsData, DetailsDataGrid, DetailsDataGridContent} from '@/components/ui/special/DetailsData';
import {useHasPermission} from '@/hooks/helpers/useHasPermission';
import {ManufacturingStatus} from '@/types/global';
import {ManufacturingOrder} from '@/types/manufacturing';
import {classes} from '@/utils/common';

import ManufacturingOrderDetailsDataCosts from './ManufacturingOrderDetailsDataCosts';

const ManufacturingOrderDetailsData: FC = () => {
  const t = useTranslations();
  const {hasPermission, isLoading} = useHasPermission();
  const {
    control,
    formState: {errors},
    register,
    setValue,
    watch,
  } = useFormContext<ManufacturingOrder>();
  const detailsDataShown = useAtomValue(detailsDataShownAtom(watch('id')));
  const {update} = useFieldArray({control, name: 'materials'});

  if (isLoading) return null;

  return (
    <DetailsData
      className={classes(hasPermission('financial', 'manufacturing') ? 'grid-cols-[7fr_7fr]' : 'grid-cols-1')}
      show={detailsDataShown}
    >
      <DetailsDataGrid title={t('manufacturing details')}>
        <DetailsDataGridContent>
          {!watch('product.code') && (
            <WithLabel className='w-full'>
              <Input {...register('product.name')} disabled />
              <InputLabel>{t('service')}</InputLabel>
            </WithLabel>
          )}
          {watch('product.code') && (
            <WithLabel className='w-full'>
              <Input {...register('product.name')} disabled />
              <InputLabel>{t('item')}</InputLabel>
            </WithLabel>
          )}
          <WithLabel className='w-full'>
            <Input
              disabled={[ManufacturingStatus.CONSUMPTION_RECORDED, ManufacturingStatus.ACCOUNTED, ManufacturingStatus.MANUFACTURED].includes(watch('status'))}
              error={!!errors.quantity}
              onChange={({target: {value}}) => {
                setValue('quantity', Number(value));
                watch('materials')?.forEach((item, index) => {
                  update(index, {
                    ...item,
                    allAvailable: null,
                    cost: {
                      ...item.cost,
                      amount: item.materialGoods?.[0]?.inventoryCostPerItem?.amount * (Number(value) * item.required),
                    },
                    requiredTotal: Number(value) * item.required,
                  });
                });
              }}
              value={watch('quantity')}
            />
            <InputLabel>
              {t('quantity')} (
              {t(
                watch('measurementUnit.name')
                  ? (`unit.name.${watch('measurementUnit.name')}` as any)
                  : watch('product.measurementUnit')
                    ? (`unit.id.${watch('product.measurementUnit').toLowerCase()}` as any)
                    : 'unit.name.pcs',
              )}
              ){![ManufacturingStatus.CONSUMPTION_RECORDED, ManufacturingStatus.ACCOUNTED, ManufacturingStatus.MANUFACTURED].includes(watch('status')) && '*'}
            </InputLabel>
          </WithLabel>
          <WithLabel className='w-full'>
            <ControlledDatePickerInput control={control} controlName='createTime' disabled />
            <InputLabel>{t('created on')}</InputLabel>
          </WithLabel>
          <WithLabel className='w-full'>
            <ControlledDatePickerInput control={control} controlName='productionDeadline' disabled />
            <InputLabel>{t('production deadline')}</InputLabel>
          </WithLabel>
          <WithLabel className='w-full'>
            <ControlledDatePickerInput control={control} controlName='estimatedCompletionDate' disabled />
            <InputLabel>{t('expected by')}</InputLabel>
          </WithLabel>
          {watch('customer.name') && (
            <WithLabel className='w-full'>
              <Input {...register('customer.name')} disabled />
              <InputLabel>{t('customer')}</InputLabel>
            </WithLabel>
          )}
        </DetailsDataGridContent>
      </DetailsDataGrid>
      {hasPermission('financial', 'manufacturing') && <ManufacturingOrderDetailsDataCosts />}
    </DetailsData>
  );
};

export default ManufacturingOrderDetailsData;
