import {FC} from 'react';

import {useAtomValue} from 'jotai';
import {useTranslations} from 'next-intl';
import {useFormContext} from 'react-hook-form';

import {Costs} from '@/components/ui/special/Cost';
import {DetailsDataGrid, DetailsDataGridContent} from '@/components/ui/special/DetailsData';
import {defaultCurrencyAtom} from '@/store/defaults';
import {ManufacturingOrder} from '@/types/manufacturing';

type Props = {
  className?: string;
};

const ManufacturingOrderDetailsDataCosts: FC<Props> = ({className = 'grid-cols-1'}) => {
  const t = useTranslations();
  const defaultCurrency = useAtomValue(defaultCurrencyAtom);
  const {watch} = useFormContext<ManufacturingOrder>();

  return (
    <DetailsDataGrid
      title={`
          ${t('costs')} (${
            watch('totalCost.currency') ||
            watch('product.materialCosts.currency') ||
            watch('product.employeeAndWorkstationCosts.currency') ||
            watch('product.manufacturingOverheadCosts.currency') ||
            defaultCurrency
          })`}
    >
      <DetailsDataGridContent className={className}>
        <Costs
          cost={watch('totalCost.amount')}
          items={[
            {color: 'blue', cost: watch('product.materialCosts.amount'), label: t('materials')},
            {color: 'green', cost: watch('product.employeeAndWorkstationCosts.amount'), label: t('labor')},
            {
              color: 'orange',
              cost: watch('product.manufacturingOverheadCosts.amount'),
              label: t('indirect production'),
            },
          ]}
          label={t('production cost')}
        />
      </DetailsDataGridContent>
    </DetailsDataGrid>
  );
};

export default ManufacturingOrderDetailsDataCosts;
