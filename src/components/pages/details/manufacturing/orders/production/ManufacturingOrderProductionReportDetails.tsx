import {FC} from 'react';

import {ArrowLeftIcon, CloudDownloadIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';

import {Button} from '@/components/ui/Button';
import {DocumentPreview} from '@/components/ui/special/DocumentPreview';
import {Link} from '@/components/ui/special/Link';
import {Page, PageContent, PageHeader, PageHeaderTitle, PageTitle} from '@/components/ui/special/Page';
import useManufacturingProductionReportDocument from '@/hooks/documents/useManufacturingProductionReportDocument';
import useHeight from '@/hooks/helpers/useHeight';
import {useRouter} from '@/hooks/helpers/useRouter';

type Props = {
  manufacturingId: string;
};

const ManufacturingOrderProductionReportDetails: FC<Props> = ({manufacturingId}) => {
  const {elementRef} = useHeight();
  const {productionReportDocument} = useManufacturingProductionReportDocument(manufacturingId);
  const {back} = useRouter();
  const t = useTranslations();

  return (
    <Page>
      <PageTitle>{`${t('production report')} - ${t('manufacturing')}`}</PageTitle>
      <PageHeader>
        <PageHeaderTitle className='inline-flex items-center gap-2'>
          <Button onClick={() => back(`/manufacturing/orders/${manufacturingId}`)} size='icon' variant='none'>
            <ArrowLeftIcon />
          </Button>
          {t('production report')}
        </PageHeaderTitle>
        <div className='grow' />
        <Button asChild>
          <Link href={`/api/manufacturing/orders/${manufacturingId}/reports/production?mediaType=application/pdf`}>
            <CloudDownloadIcon />
            {t('download')}
          </Link>
        </Button>
      </PageHeader>
      <PageContent>
        <div className='flex' ref={elementRef}>
          <div className='flex size-full justify-center bg-gray-100 p-4'>
            <DocumentPreview content={productionReportDocument} />
          </div>
        </div>
      </PageContent>
    </Page>
  );
};

export default ManufacturingOrderProductionReportDetails;
