import {FC} from 'react';

import {useTranslations} from 'next-intl';
import {useFormContext} from 'react-hook-form';

import {ControlledDatePickerInput} from '@/components/ui/DatePicker';
import {Input, InputLabel} from '@/components/ui/Input';
import {WithLabel} from '@/components/ui/Label';
import {Consumption, ManufacturingOrder} from '@/types/manufacturing';

type Props = {
  order: ManufacturingOrder;
};

const ManufacturingConsumptionCreateData: FC<Props> = ({order}) => {
  const t = useTranslations();
  const {
    control,
    formState: {errors},
    register,
  } = useFormContext<Consumption>();

  return (
    <div className='mx-6 mt-6 flex items-stretch gap-6'>
      <div className='grow rounded-lg border border-border p-6'>
        <div className='flex flex-col gap-4'>
          <div className='text-base font-medium'>{t('consumption details')}</div>
          <div className='flex gap-4'>
            <WithLabel>
              <ControlledDatePickerInput control={control} controlName={'date'} error={!!errors?.date} />
              <InputLabel>{t('consumption date')}*</InputLabel>
            </WithLabel>
            <WithLabel>
              <Input disabled value={order.product.name} />
              <InputLabel>{t('item')}</InputLabel>
            </WithLabel>
            <WithLabel>
              <Input disabled value={order.product.code} />
              <InputLabel>{t('code')}</InputLabel>
            </WithLabel>
            <WithLabel>
              <Input disabled value={order.quantity} />
              <InputLabel>
                {t('quantity')} ({t(`unit.id.${order.product?.measurementUnit?.toLowerCase() || 'pcs'}` as any)})
              </InputLabel>
            </WithLabel>
            <WithLabel>
              <Input {...register('inventoryManager')} />
              <InputLabel>{t('manager')}</InputLabel>
            </WithLabel>
            <WithLabel>
              <Input {...register('worker')} />
              <InputLabel>{t('worker')}</InputLabel>
            </WithLabel>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ManufacturingConsumptionCreateData;
