import {FC} from 'react';

import Jo<PERSON> from 'joi';
import {Trash2Icon} from 'lucide-react';
import {useTranslations} from 'next-intl';
import {useFieldArray, useFormContext} from 'react-hook-form';

import {Button} from '@/components/ui/Button';
import {PopoverInput} from '@/components/ui/Input';
import InventoryItemLink from '@/components/ui/special/InventoryItemLink/InventoryItemLink';
import {TableActions, TableCell, TableRow} from '@/components/ui/Table';
import {Consumption, ConsumptionMaterial} from '@/types/manufacturing';

type Props = {
  index: number;
  item: ConsumptionMaterial;
};

const ManufacturingConsumptionCreateTableRow: FC<Props> = ({index, item}) => {
  const t = useTranslations();
  const {control} = useFormContext<Consumption>();
  const {remove, update} = useFieldArray({control, name: 'materials'});

  return (
    <TableRow>
      <TableCell>
        <InventoryItemLink item={item} />
      </TableCell>
      <TableCell className='text-right'>
        <PopoverInput
          className='w-fit justify-end'
          defaultValue={item.quantity.toString()}
          label={t('quantity')}
          onChange={(value) => {
            update(index, {
              ...item,
              quantity: Number(value),
            });
          }}
          validation={Joi.number().positive().required()}
        >
          {item.quantity} {t(`unit.name.${item.measurementUnit.name.toLowerCase()}` as any)}
        </PopoverInput>
      </TableCell>
      <TableActions>
        <Button className='mt-1.5' onClick={() => remove(index)} size='icon' variant='none'>
          <Trash2Icon className='size-5 text-red' strokeWidth={1} />
        </Button>
      </TableActions>
    </TableRow>
  );
};

export default ManufacturingConsumptionCreateTableRow;
