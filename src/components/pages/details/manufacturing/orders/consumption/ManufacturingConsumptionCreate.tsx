import {FC, useEffect, useState} from 'react';

import {joiResolver} from '@hookform/resolvers/joi';
import {ArrowLeftIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';
import {FormProvider, useForm} from 'react-hook-form';

import {Button} from '@/components/ui/Button';
import {Page, PageContent, PageHeader, PageHeaderTitle, PageTitle} from '@/components/ui/special/Page';
import {toast} from '@/components/ui/Toast';
import useLeaveConfirm from '@/hooks/helpers/useLeaveConfirm';
import {useRouter} from '@/hooks/helpers/useRouter';
import {consumptionSchema} from '@/hooks/useConsumptions';
import useManufacturingOrder from '@/hooks/useManufacturingOrder';
import useManufacturingOrderActions from '@/hooks/useManufacturingOrderActions';
import {Consumption} from '@/types/manufacturing';

import ManufacturingConsumptionCreateData from './ManufacturingConsumptionCreateData';
import ManufacturingConsumptionCreateTable from './ManufacturingConsumptionCreateTable';

type Props = {
  isService?: boolean;
  manufacturingId: string;
};

const ManufacturingConsumptionCreate: FC<Props> = ({isService, manufacturingId}) => {
  const {isLoading, manufacturingOrder} = useManufacturingOrder(manufacturingId);
  const {finishManufacturingOrder} = useManufacturingOrderActions();
  const {back, replace} = useRouter();
  const [created, setCreated] = useState(false);
  const t = useTranslations();

  const {
    formState: {dirtyFields, ...restUseFormState},
    handleSubmit,
    setValue,
    watch,
    ...restUseFormActions
  } = useForm<Consumption>({
    defaultValues: {
      date: new Date().toISOString(),
      materials: [],
    },
    mode: 'onSubmit',
    resolver: joiResolver(consumptionSchema),
  });

  const isDirty = !!Object.keys(dirtyFields).length;

  useLeaveConfirm(created ? false : isDirty, async () => await finishManufacturingOrder(manufacturingId, watch()));

  useEffect(() => {
    if (!isLoading && manufacturingOrder.materials) {
      setValue(
        'materials',
        manufacturingOrder.materials.map((material) => ({
          ...material,
          code: material.materialGoods?.[0]?.code,
          id: material.materialGoods?.[0]?.id,
          measurementUnit: material.materialGoods?.[0]?.measurementUnit,
          name: material.materialGoods?.[0]?.name,
          quantity: material.requiredTotal,
        })),
      );
    }
  }, [isLoading, manufacturingOrder.materials, setValue]);

  useEffect(() => {
    if (created) back(`/manufacturing/${isService ? 'services' : 'orders'}`);
  }, [back, created, isService, manufacturingId, replace]);

  if (isLoading) return null;

  return (
    <Page>
      <PageTitle>{`${t('new consumption')} - ${isService ? t('services') : t('orders')} - ${t('manufacturing')}`}</PageTitle>
      <PageHeader>
        <PageHeaderTitle className='inline-flex items-center gap-2'>
          <Button
            onClick={() => back(`/manufacturing/${isService ? 'services' : 'orders'}/${manufacturingId}`)}
            size='icon'
            variant='none'
          >
            <ArrowLeftIcon />
          </Button>
          {t('new consumption')}
        </PageHeaderTitle>
        <div className='grow' />
        <Button
          onClick={() =>
            handleSubmit(
              (values) => {
                finishManufacturingOrder(manufacturingId, values).then((item) => {
                  if (item) setCreated(true);
                });
              },
              () => toast.warning(t('please fill in all mandatory fields before saving')),
            )()
          }
        >
          {t('create consumption')}
        </Button>
      </PageHeader>
      <PageContent>
        <FormProvider
          {...{
            formState: {
              dirtyFields,
              ...restUseFormState,
            },
            handleSubmit,
            setValue,
            watch,
            ...restUseFormActions,
          }}
        >
          <ManufacturingConsumptionCreateData order={manufacturingOrder} />
          <ManufacturingConsumptionCreateTable />
        </FormProvider>
      </PageContent>
    </Page>
  );
};

export default ManufacturingConsumptionCreate;
