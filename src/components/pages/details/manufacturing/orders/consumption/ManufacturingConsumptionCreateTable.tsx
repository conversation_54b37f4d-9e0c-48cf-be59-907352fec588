import {FC} from 'react';

import {useTranslations} from 'next-intl';
import {useFormContext} from 'react-hook-form';

import {
  Table,
  TableBody,
  TableContainer,
  TableHead,
  TableHeadActions,
  TableHeader,
  TableRow,
} from '@/components/ui/Table';
import {TabsMenuItem} from '@/components/ui/Tabs';
import useHeight from '@/hooks/helpers/useHeight';
import {Consumption} from '@/types/manufacturing';

import ManufacturingConsumptionCreateButtons from './ManufacturingConsumptionCreateButtons';
import ManufacturingConsumptionCreateTableRow from './ManufacturingConsumptionCreateTableRow';

const ManufacturingConsumptionCreateTable: FC = () => {
  const {watch} = useFormContext<Consumption>();
  const {elementRef} = useHeight();
  const t = useTranslations();

  return (
    <>
      <div className='mr-6 flex items-center justify-between'>
        <TabsMenuItem badge={watch('materials')?.length || 0}>{t('items')}</TabsMenuItem>
        <ManufacturingConsumptionCreateButtons />
      </div>
      <TableContainer ref={elementRef}>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>{t('name')}</TableHead>
              <TableHead className='text-right'>{t('quantity')}</TableHead>
              <TableHeadActions />
            </TableRow>
          </TableHeader>
          <TableBody className='overflow-y-hidden' isValidating={!watch()}>
            {watch('materials')?.map((item, index) => (
              <ManufacturingConsumptionCreateTableRow index={index} item={item} key={`${item.id}-${index}`} />
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </>
  );
};

export default ManufacturingConsumptionCreateTable;
