import {FC, useCallback, useMemo, useState} from 'react';

import {PlusIcon, XIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';
import {useFieldArray, UseFieldArrayAppend, useFormContext} from 'react-hook-form';

import {Button} from '@/components/ui/Button';
import {Combobox} from '@/components/ui/Combobox';
import useItemActions from '@/hooks/useItemActions';
import useItems from '@/hooks/useItems';
import {Consumption} from '@/types/manufacturing';
import {classes} from '@/utils/common';

type AddProps = {
  excludeIds: string[];
  onClose: () => void;
  onSelect: UseFieldArrayAppend<Consumption, 'materials'>;
};

const AddMaterial: FC<AddProps> = ({excludeIds, onClose, onSelect}) => {
  const t = useTranslations();
  const {items} = useItems();
  const {getItem} = useItemActions();

  const availableItems = useMemo(() => items.filter((item) => !excludeIds.includes(item.id)), [excludeIds, items]);

  const handleSelect = useCallback(
    async (id: string | undefined) => {
      if (id) {
        const item = await getItem(id);

        if (item) {
          onSelect({
            ...item,
            quantity: 1,
          });
          onClose();
        }
      }
    },
    [getItem, onClose, onSelect],
  );

  return (
    <>
      <Combobox
        className='w-[300px]'
        onChange={(value) => handleSelect(value)}
        open
        options={availableItems.map((item) => ({id: item.id, value: item.name}))}
        placeholder={t('material')}
        renderNotFound={() => t('material not found')}
        searchPlaceholder={t('search material')}
      />
      <Button onClick={onClose} variant='secondary'>
        <XIcon />
        {t('cancel')}
      </Button>
    </>
  );
};

type Props = {
  className?: string;
};

const ManufacturingConsumptionCreateButtons: FC<Props> = ({className}) => {
  const t = useTranslations();
  const [addEnabled, setAddEnabled] = useState(false);
  const {control, watch} = useFormContext<Consumption>();
  const {append} = useFieldArray({control, name: 'materials'});

  return (
    <div className={classes('flex items-center gap-4', className)}>
      {!addEnabled && (
        <Button
          onClick={() => {
            setAddEnabled(true);
          }}
          variant='secondary'
        >
          <PlusIcon /> {t('add material')}
        </Button>
      )}
      {addEnabled && (
        <AddMaterial
          excludeIds={watch('materials')?.map((material) => material.id) ?? []}
          onClose={() => {
            setAddEnabled(false);
          }}
          onSelect={append}
        />
      )}
    </div>
  );
};

export default ManufacturingConsumptionCreateButtons;
