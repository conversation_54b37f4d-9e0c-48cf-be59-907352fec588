import {FC, useEffect, useState} from 'react';

import {isEqual, sortBy} from 'lodash';
import {ChevronDownIcon, ChevronUpIcon, PencilIcon, PlusIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';
import {useFormContext} from 'react-hook-form';

import {Badge} from '@/components/ui/Badge';
import {IconButton} from '@/components/ui/Button';
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from '@/components/ui/Select';
import {TableMultiSelect} from '@/components/ui/special/TableMultiSelect';
import {TableCell, TableRow} from '@/components/ui/Table';
import {useHasPermission} from '@/hooks/helpers/useHasPermission';
import useEmployees from '@/hooks/useEmployees';
import useTaskActions from '@/hooks/useTaskActions';
import useWorkstations from '@/hooks/useWorkstations';
import {ManufacturingStatus, ManufacturingTaskStatus, ManufacturingTaskStatusReason} from '@/types/global';
import {ManufacturingOrder, ManufacturingOrderTask} from '@/types/manufacturing';
import {minutesToWorkHoursTimeString} from '@/utils/common';
import {formatCurrency} from '@/utils/format';

import ManufacturingOrderDetailsMaterialsTable from '../manufacturingOrderDetailsMaterials/ManufacturingOrderDetailsMaterialsTable';

type Props = {
  index: number;
  task: ManufacturingOrderTask;
};

const ManufacturingOrderDetailsTasksTableRow: FC<Props> = ({index, task}) => {
  const t = useTranslations();
  const {getAvailableStatuses, updateManufacturingTaskStatus, updateTaskEmployees, updateTaskWorkstations} =
    useTaskActions();
  const [assignedEmployees, setAssignedEmployees] = useState<string[]>(
    task.assignedEmployees.map((employee) => employee.id),
  );
  const [assignedWorkstations, setAssignedWorkstations] = useState<string[]>(
    task.assignedWorkstations.map((workstation) => workstation.id),
  );
  const {employees} = useEmployees();
  const {workstations} = useWorkstations();
  const {watch} = useFormContext<ManufacturingOrder>();
  const {hasPermission, isLoading} = useHasPermission();
  const [open, setOpen] = useState(false);

  useEffect(() => {
    setAssignedEmployees(task.assignedEmployees.map((employee) => employee.id));
  }, [task.assignedEmployees]);

  useEffect(() => {
    setAssignedWorkstations(task.assignedWorkstations.map((workstation) => workstation.id));
  }, [task.assignedWorkstations]);

  if (isLoading) return null;

  return (
    <>
      <TableRow>
        <TableCell className='mt-0.5 inline-flex h-14 items-center gap-2 align-top'>{task.name}</TableCell>
        <TableCell className='text-right align-top'>
          <div className='mt-2'>
            {minutesToWorkHoursTimeString(task.durationInMinutes)} /{' '}
            {minutesToWorkHoursTimeString(task.actualDurationInMinutes)}
          </div>
        </TableCell>
        {hasPermission('financial', 'manufacturing') && (
          <TableCell className='text-right'>{formatCurrency(task.costPerHour)}</TableCell>
        )}
        <TableCell>
          <div className='flex items-center gap-2'>
            <IconButton disabled icon={<PlusIcon className='size-6' />} />
            {task.materials.length > 0 && (
              <Badge className='cursor-pointer' onClick={() => setOpen(!open)} size='sm' variant='info'>
                {task.materials.length} {t('materials')}
                {!open && <ChevronDownIcon className='size-5' />}
                {open && <ChevronUpIcon className='size-5' />}
              </Badge>
            )}
          </div>
        </TableCell>
        <TableCell>
          <TableMultiSelect
            defaultValue={assignedEmployees}
            disabled={task.status !== ManufacturingTaskStatus.TODO}
            onOpenChange={(open) => {
              if (
                !open &&
                !isEqual(sortBy(assignedEmployees), sortBy(task.assignedEmployees.map((employee) => employee.id)))
              )
                updateTaskEmployees(task.id, assignedEmployees, watch('id'), task.name);
            }}
            onValueChange={(values) => setAssignedEmployees(values)}
            options={sortBy(
              employees.map((employee) => ({id: employee.id, value: employee.name})),
              'name',
            )}
            renderNotFound={() => t('employee not found')}
            searchPlaceholder={t('search employee')}
            side='left'
            summaryLabel={t('selected.male')}
            withoutRemove
          >
            {task.manuallyAssigned && (
              <div className='flex size-6 items-center justify-center rounded-full bg-button-primary hover:bg-button-primary-hovered'>
                <PencilIcon className='size-4' />
              </div>
            )}
          </TableMultiSelect>
        </TableCell>
        <TableCell>
          <TableMultiSelect
            defaultValue={assignedWorkstations}
            disabled={task.status !== ManufacturingTaskStatus.TODO}
            onOpenChange={(open) => {
              if (
                !open &&
                !isEqual(
                  sortBy(assignedWorkstations),
                  sortBy(task.assignedWorkstations.map((workstation) => workstation.id)),
                )
              )
                updateTaskWorkstations(task.id, assignedWorkstations, watch('id'), task.name);
            }}
            onValueChange={(values) => setAssignedWorkstations(values)}
            options={sortBy(
              workstations.map((workstation) => ({id: workstation.id, value: workstation.name})),
              'name',
            )}
            renderNotFound={() => t('workstation not found')}
            searchPlaceholder={t('search workstation')}
            side='left'
            summaryLabel={t('selected.female')}
            withoutRemove
          >
            {task.manuallyAssigned && (
              <div className='flex size-6 items-center justify-center rounded-full bg-button-primary hover:bg-button-primary-hovered'>
                <PencilIcon className='size-4' />
              </div>
            )}
          </TableMultiSelect>
        </TableCell>
        <TableCell>
          <div className='flex items-center'>
            {[ManufacturingStatus.DONE, ManufacturingStatus.MANUFACTURED].includes(watch('status')) && (
              <Badge
                variant={
                  task.status === ManufacturingTaskStatus.TODO
                    ? 'secondary'
                    : task.status === ManufacturingTaskStatus.IN_PROGRESS
                      ? 'info'
                      : task.status === ManufacturingTaskStatus.DONE
                        ? 'success'
                        : 'none'
                }
              >
                {t(task.status)}
              </Badge>
            )}
            {![ManufacturingStatus.DONE, ManufacturingStatus.MANUFACTURED].includes(watch('status')) && (
              <Select
                onValueChange={(value) => {
                  if (Object.values(ManufacturingTaskStatusReason).includes(value as ManufacturingTaskStatusReason)) {
                    updateManufacturingTaskStatus(task.id, ManufacturingTaskStatus.STOPPED, {
                      name: task.name,
                      orderId: watch('id'),
                      reason: value as ManufacturingTaskStatusReason,
                    });
                  } else {
                    updateManufacturingTaskStatus(task.id, value as ManufacturingTaskStatus, {
                      name: task.name,
                      orderId: watch('id'),
                    });
                  }
                }}
                value={task.statusReason || task.status}
              >
                <SelectTrigger
                  className='w-fit self-end'
                  size='badge-md'
                  variant={
                    task.status === ManufacturingTaskStatus.TODO
                      ? 'badge-secondary'
                      : task.status === ManufacturingTaskStatus.IN_PROGRESS
                        ? 'badge-info'
                        : task.status === ManufacturingTaskStatus.DONE
                          ? 'badge-success'
                          : task.status === ManufacturingTaskStatus.STOPPED
                            ? 'badge-error'
                            : 'none'
                  }
                >
                  <SelectValue>
                    {task.status === ManufacturingTaskStatus.STOPPED ? t(task.statusReason) : t(task.status)}
                  </SelectValue>
                </SelectTrigger>
                <SelectContent>
                  {getAvailableStatuses(task.status).map((status) => (
                    <SelectItem key={status} value={status}>
                      {t(status)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
          </div>
        </TableCell>
      </TableRow>
      {open && (
        <TableRow className='bg-input/50 border-b-black'>
          <TableCell className='p-0' colSpan={99}>
            <ManufacturingOrderDetailsMaterialsTable
              embedded
              materials={watch(`manufacturingTasks.${index}.materials`)}
              operationIndex={index}
            />
          </TableCell>
        </TableRow>
      )}
    </>
  );
};

export default ManufacturingOrderDetailsTasksTableRow;
