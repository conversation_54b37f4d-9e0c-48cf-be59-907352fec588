import {FC} from 'react';

import {useAtomValue} from 'jotai';
import {useTranslations} from 'next-intl';
import {useFormContext} from 'react-hook-form';

import {detailsDataShownAtom} from '@/components/pages/details/detailsStore';
import {Table, TableBody, TableContainer, TableHead, TableHeader, TableRow} from '@/components/ui/Table';
import {useHasPermission} from '@/hooks/helpers/useHasPermission';
import useHeight from '@/hooks/helpers/useHeight';
import {ManufacturingOrder} from '@/types/manufacturing';

import ManufacturingOrderDetailsTasksTableRow from './ManufacturingOrderDetailsTasksTableRow';

const ManufacturingOrderDetailsTasksTable: FC = () => {
  const {watch} = useFormContext<ManufacturingOrder>();
  const detailsDataShown = useAtomValue(detailsDataShownAtom(watch('id')));
  const {elementRef} = useHeight({dependencies: [detailsDataShown]});
  const t = useTranslations();
  const {hasPermission, isLoading} = useHasPermission();

  return (
    <TableContainer ref={elementRef}>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>{t('name')}</TableHead>
            <TableHead className='text-right'>
              {t('planned duration')} / {t('actual duration')}
            </TableHead>
            {hasPermission('financial', 'manufacturing') && (
              <TableHead className='text-right'>{t('cost per hour')}</TableHead>
            )}
            <TableHead>{t('materials')}</TableHead>
            <TableHead>{t('employees')}</TableHead>
            <TableHead>{t('workstations')}</TableHead>
            <TableHead>{t('status')}</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody className='overflow-y-hidden' isValidating={!watch() || isLoading}>
          {watch('manufacturingTasks')?.map((task, index) => (
            <ManufacturingOrderDetailsTasksTableRow index={index} key={`${task.id}-${index}`} task={task} />
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );
};

export default ManufacturingOrderDetailsTasksTable;
