import {FC, useRef} from 'react';

import {first} from 'lodash';
import {PlusIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';

import {Button} from '@/components/ui/Button';
import {classes} from '@/utils/common';

type Props = {
  className?: string;
  uploadFile: (file?: File) => void;
};

const ManufacturingOrderDetailsFilesButtons: FC<Props> = ({className, uploadFile}) => {
  const uploadBtn = useRef<HTMLInputElement>(null);
  const t = useTranslations();

  return (
    <div className={classes('flex items-center gap-4', className)}>
      <Button onClick={() => uploadBtn.current?.click()} variant='secondary'>
        <PlusIcon /> {t('add file')}
      </Button>
      <input
        className='hidden'
        onChange={({target: {files}}) => uploadFile(first(files))}
        ref={uploadBtn}
        type='file'
      />
    </div>
  );
};

export default ManufacturingOrderDetailsFilesButtons;
