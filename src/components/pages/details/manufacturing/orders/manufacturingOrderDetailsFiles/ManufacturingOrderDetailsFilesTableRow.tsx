import {FC} from 'react';

import {CloudDownloadIcon, Trash2Icon} from 'lucide-react';
import {useFormContext} from 'react-hook-form';

import {Button} from '@/components/ui/Button';
import {Link} from '@/components/ui/special/Link';
import {TableActions, TableCell, TableRow} from '@/components/ui/Table';
import {useRouter} from '@/hooks/helpers/useRouter';
import {CustomFile, ManufacturingStatus} from '@/types/global';
import {ManufacturingOrder} from '@/types/manufacturing';

type Props = {
  deleteFile: (id: string) => void;
  file: CustomFile;
};

const ManufacturingOrderDetailsFilesTableRow: FC<Props> = ({deleteFile, file}) => {
  const {push} = useRouter();
  const {watch} = useFormContext<ManufacturingOrder>();

  return (
    <TableRow>
      <TableCell>
        <Link href={`/api${file.previewLink}`}>{file.name}</Link>
      </TableCell>
      <TableCell className='w-full'>
        <Button
          onClick={() => {
            push(`/api${file.downloadLink}`);
          }}
          size='icon'
          variant='none'
        >
          <CloudDownloadIcon />
        </Button>
      </TableCell>
      <TableActions>
        {watch('status') !== ManufacturingStatus.DONE && (
          <Button onClick={() => deleteFile(file.id)} size='icon' variant='none'>
            <Trash2Icon className='size-5 text-red' strokeWidth={1} />
          </Button>
        )}
      </TableActions>
    </TableRow>
  );
};

export default ManufacturingOrderDetailsFilesTableRow;
