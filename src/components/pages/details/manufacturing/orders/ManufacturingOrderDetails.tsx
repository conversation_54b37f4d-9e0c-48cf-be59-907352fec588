import {FC} from 'react';

import {use<PERSON><PERSON>} from 'jotai';
import {
  ArrowLeftIcon,
  CloudDownloadIcon,
  EllipsisVerticalIcon,
  EyeIcon,
  FileChartColumnIcon,
  ReceiptTextIcon,
} from 'lucide-react';
import {useTranslations} from 'next-intl';
import {FormProvider} from 'react-hook-form';

import {detailsDataShownAtom} from '@/components/pages/details/detailsStore';
import {Button} from '@/components/ui/Button';
import {DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger} from '@/components/ui/DropdownMenu';
import ActivityButton from '@/components/ui/special/ActivityButton';
import {HideableContentToggle} from '@/components/ui/special/HideableContentToggle';
import {Link} from '@/components/ui/special/Link';
import {Page, PageContent, PageHeader, PageHeaderTitle, PageTitle} from '@/components/ui/special/Page';
import {<PERSON><PERSON>, <PERSON><PERSON>List, TabsTrigger} from '@/components/ui/Tabs';
import {useHasPermission} from '@/hooks/helpers/useHasPermission';
import {useRouter} from '@/hooks/helpers/useRouter';
import useActivities from '@/hooks/useActivities';
import useManufacturingOrder from '@/hooks/useManufacturingOrder';
import {ActivityType, ManufacturingStatus} from '@/types/global';
import {formatNumber} from '@/utils/format';

import ManufacturingOrderDetailsData from './ManufacturingOrderDetailsData';
import ManufacturingOrderDetailsFilesButtons from './manufacturingOrderDetailsFiles/ManufacturingOrderDetailsFilesButtons';
import ManufacturingOrderDetailsFilesTable from './manufacturingOrderDetailsFiles/ManufacturingOrderDetailsFilesTable';
import ManufacturingOrderDetailsMaterialsTable from './manufacturingOrderDetailsMaterials/ManufacturingOrderDetailsMaterialsTable';
import ManufacturingOrderDetailsOperationsButtons from './manufacturingOrderDetailsOperations/ManufacturingOrderDetailsOperationsButtons/ManufacturingOrderDetailsOperationsButtons';
import ManufacturingOrderDetailsOperationsTable from './manufacturingOrderDetailsOperations/ManufacturingOrderDetailsOperationsTable';
import {ManufacturingOrderTab, manufacturingOrderTabAtom} from './manufacturingOrderDetailsStore';
import ManufacturingOrderDetailsTasksTable from './manufacturingOrderDetailsTasks/ManufacturingOrderDetailsTasksTable';

type Props = {
  id: string;
};

const ManufacturingOrderDetails: FC<Props> = ({id}) => {
  const t = useTranslations();
  const {
    deleteManufacturingOrder,
    deleteManufacturingOrderFile,
    finishManufacturingOrderCustomization,
    isDirty,
    isLoading,
    saveManufacturingOrder,
    uploadManufacturingOrderFile,
    useFormActions: {
      formState: {errors, ...restUseFormState},
      register,
      resetField,
      watch,
      ...restUseFormActions
    },
  } = useManufacturingOrder(id);
  const {activities, createActivity} = useActivities(id, ActivityType.MANUFACTURING);
  const [tab, setTab] = useAtom(manufacturingOrderTabAtom(id));
  const {hasPermission, isLoading: permissionIsLoading} = useHasPermission();
  const {back, push} = useRouter();

  if (isLoading || permissionIsLoading) return null;

  return (
    <Page>
      <PageTitle>{`${watch('number') || t('new manufacturing order')} - ${t('manufacturing')}`}</PageTitle>
      <PageHeader>
        <PageHeaderTitle className='inline-flex items-center gap-2'>
          <Button onClick={() => back('/manufacturing/orders')} size='icon' variant='none'>
            <ArrowLeftIcon />
          </Button>
          <div className='flex flex-col'>
            {watch('number') || t('new manufacturing order')}
            <div className='text-sm font-normal'>{t(watch('status'))}</div>
          </div>
        </PageHeaderTitle>
        <HideableContentToggle store={detailsDataShownAtom(watch('id'))} />
        <div className='grow' />
        {watch('id') && <ActivityButton activities={activities} createActivity={createActivity} />}
        {isDirty && <Button onClick={saveManufacturingOrder}>{t('save')}</Button>}
        {!isDirty && (
          <>
            {watch('status') !== ManufacturingStatus.CUSTOMIZATION_NEEDED && (
              <Button
                onClick={() => push(`/api/manufacturing/orders/${id}/details?mediaType=application/pdf`)}
                variant='secondary'
              >
                <CloudDownloadIcon />
                {t('print')}
              </Button>
            )}
            {hasPermission('financial', 'manufacturing') && watch('materialIssueNote') && (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant='secondary'>
                    <EyeIcon /> {t('documents')}
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuItem asChild>
                    <Link href={`/inventory/consumptions/${watch('materialIssueNote.id')}`}>
                      <ReceiptTextIcon strokeWidth={1} /> {t('consumption')}
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link href={`/manufacturing/orders/${id}/production`}>
                      <FileChartColumnIcon strokeWidth={1} /> {t('production report')}
                    </Link>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            )}
            {watch('status') === ManufacturingStatus.CUSTOMIZATION_NEEDED && (
              <Button onClick={finishManufacturingOrderCustomization}>{t('complete the customization')}</Button>
            )}
            {watch('status') === ManufacturingStatus.MANUFACTURED && (
              <Button onClick={() => push(`/manufacturing/orders/${id}/consumption`)}>{t('mark as done')}</Button>
            )}
            {![ManufacturingStatus.DONE, ManufacturingStatus.MANUFACTURED].includes(watch('status')) && (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button className='px-1.5' variant='ghost'>
                    <EllipsisVerticalIcon />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align='end'>
                  <DropdownMenuItem onSelect={deleteManufacturingOrder}>{t('delete')}</DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            )}
          </>
        )}
      </PageHeader>
      <PageContent>
        <FormProvider
          {...{formState: {errors, ...restUseFormState}, register, resetField, watch, ...restUseFormActions}}
        >
          <ManufacturingOrderDetailsData />
          <div className='mx-6 flex items-center justify-between'>
            <div className='inline-flex items-center'>
              <Tabs onValueChange={(value) => setTab(value as ManufacturingOrderTab)} value={tab} variant='menu'>
                <TabsList variant='menu'>
                  <TabsTrigger
                    badge={formatNumber(
                      watch('status') === ManufacturingStatus.CUSTOMIZATION_NEEDED
                        ? watch('manufacturingOperations')?.length
                        : watch('manufacturingTasks')?.length,
                    )}
                    error={
                      !!errors?.manufacturingOperations ||
                      !!watch('materials')?.find((material) => !material.allAvailable && material.allAvailable !== null)
                    }
                    value='tasks'
                    variant='menu'
                  >
                    {t('bill of materials')}
                  </TabsTrigger>
                  <TabsTrigger badge={formatNumber(watch('materials').length)} value='materials' variant='menu'>
                    {t('materials summary')}
                  </TabsTrigger>
                  <TabsTrigger badge={formatNumber(watch('files')?.length)} value='files' variant='menu'>
                    {t('files')}
                  </TabsTrigger>
                </TabsList>
              </Tabs>
            </div>
            <div>
              {watch('status') === ManufacturingStatus.CUSTOMIZATION_NEEDED && tab === 'tasks' && (
                <ManufacturingOrderDetailsOperationsButtons />
              )}

              {tab === 'files' && <ManufacturingOrderDetailsFilesButtons uploadFile={uploadManufacturingOrderFile} />}
            </div>
          </div>
          {tab === 'tasks' && (
            <>
              {watch('status') === ManufacturingStatus.CUSTOMIZATION_NEEDED && (
                <ManufacturingOrderDetailsOperationsTable />
              )}
              {watch('status') !== ManufacturingStatus.CUSTOMIZATION_NEEDED && <ManufacturingOrderDetailsTasksTable />}
            </>
          )}
          {tab === 'materials' && <ManufacturingOrderDetailsMaterialsTable materials={watch('materials')} />}
          {tab === 'files' && <ManufacturingOrderDetailsFilesTable deleteFile={deleteManufacturingOrderFile} />}
        </FormProvider>
      </PageContent>
    </Page>
  );
};

export default ManufacturingOrderDetails;
