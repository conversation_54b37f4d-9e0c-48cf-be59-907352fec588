import {FC} from 'react';

import {useAtom} from 'jotai';
import {first} from 'lodash';
import {
  ArrowLeftIcon,
  BookTextIcon,
  CheckIcon,
  EllipsisVerticalIcon,
  EyeIcon,
  ReceiptIcon,
  ReceiptTextIcon,
} from 'lucide-react';
import {useTranslations} from 'next-intl';
import {FormProvider} from 'react-hook-form';

import {detailsDataShownAtom} from '@/components/pages/details/detailsStore';
import {Button} from '@/components/ui/Button';
import {DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger} from '@/components/ui/DropdownMenu';
import ActivityButton from '@/components/ui/special/ActivityButton';
import {HideableContentToggle} from '@/components/ui/special/HideableContentToggle';
import {Link} from '@/components/ui/special/Link';
import {Page, PageContent, PageHeader, PageHeaderTitle, PageTitle} from '@/components/ui/special/Page';
import StatusButton from '@/components/ui/special/StatusButton';
import {Tabs, TabsList, TabsTrigger} from '@/components/ui/Tabs';
import {useHasPermission} from '@/hooks/helpers/useHasPermission';
import {useRouter} from '@/hooks/helpers/useRouter';
import useActivities from '@/hooks/useActivities';
import useSaleOrder from '@/hooks/useSaleOrder';
import useSaleOrderActions from '@/hooks/useSaleOrderActions';
import {ActivityType, SaleStatus} from '@/types/global';
import {SaleType} from '@/types/sales';
import {formatNumber} from '@/utils/format';

import SaleDetailsFilesButtons from './saleDetailsFiles/SaleDetailsFilesButtons';
import SalesDetailsFilesTable from './saleDetailsFiles/SalesDetailsFilesTable';
import SaleDetailsItemsTable from './saleDetailsItems/SaleDetailsItemsTable';
import SaleDetailsItemsTableButtons from './saleDetailsItems/SaleDetailsItemsTableButtons';
import SalesDetailsData from './SalesDetailsData';
import {SalesTab, salesTabAtom} from './salesDetailsStore';

type Props = {
  id?: string;
  saleType: SaleType;
};

const SalesDetails: FC<Props> = ({id, saleType}) => {
  const t = useTranslations();
  const {
    cloneSaleOrder,
    deleteSaleOrderFile,
    isDirty,
    isLoading,
    saveSaleOrder,
    uploadSaleOrderFile,
    useFormActions: {
      formState: {errors, ...restUseFormState},
      register,
      resetField,
      setValue,
      watch,
      ...restUseFormActions
    },
  } = useSaleOrder({
    id,
    type: saleType,
  });
  const {getAvailableStatuses, getRemainingNoteItems} = useSaleOrderActions();
  const [tab, setTab] = useAtom(salesTabAtom(watch('id')));
  const {hasPermission, isLoading: permissionIsLoading} = useHasPermission();
  const {activities, createActivity} = useActivities(id, ActivityType.SALE);
  const {back} = useRouter();

  const hasRemainingNoteItems =
    saleType === SaleType.QUOTE
      ? false
      : getRemainingNoteItems(watch('items'), watch('goodsAccompanyingNotes')).length > 0;

  if (isLoading || permissionIsLoading) return null;

  return (
    <Page>
      <PageTitle>
        {watch('id') ? watch('number') : t(saleType === SaleType.ORDER ? 'new order' : 'new quote')}
      </PageTitle>
      <PageHeader>
        <PageHeaderTitle className='inline-flex items-center gap-2'>
          <Button onClick={() => back(`/sales/${saleType}`)} size='icon' variant='none'>
            <ArrowLeftIcon />
          </Button>
          <div className='flex flex-col'>
            {watch('id') ? watch('number') : t(saleType === SaleType.ORDER ? 'new order' : 'new quote')}
            {watch('id') && <div className='text-sm font-normal'>{t(watch('status'))}</div>}
          </div>
        </PageHeaderTitle>
        <HideableContentToggle store={detailsDataShownAtom(watch('id'))} />
        <div className='grow' />
        {watch('id') && <ActivityButton activities={activities} createActivity={createActivity} />}
        {isDirty && <Button onClick={saveSaleOrder}>{t('save')}</Button>}
        {!isDirty && watch('id') && hasPermission('financial', 'sales') && (
          <>
            <StatusButton
              nextStatus={
                watch('status') === SaleStatus.READY_TO_SHIP
                  ? first(getAvailableStatuses(watch('status'), {withoutNext: true, withoutSelf: true}))
                  : first(getAvailableStatuses(watch('status'), {withoutSelf: true}))
              }
              onChange={(status) => {
                setValue('status', status as SaleStatus);
                saveSaleOrder();
              }}
              statuses={getAvailableStatuses(watch('status'), {withoutNext: true, withoutSelf: true})}
            />
            {(saleType !== SaleType.QUOTE || watch('versions').length > 0) && (
              <>
                {(watch('invoice.id') ||
                  watch('proformaInvoice.id') ||
                  watch('goodsAccompanyingNotes').length > 0 ||
                  watch('versions').length > 0) && (
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant='secondary'>
                        <EyeIcon /> {t('documents')}
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent>
                      {watch('proformaInvoice.id') && (
                        <DropdownMenuItem asChild>
                          <Link href={`/sales/orders/${id}/invoice/${watch('proformaInvoice.id')}?isProforma=true`}>
                            <ReceiptTextIcon strokeWidth={1} /> {t('proforma invoice')}
                          </Link>
                        </DropdownMenuItem>
                      )}
                      {watch('invoice.id') && (
                        <DropdownMenuItem asChild>
                          <Link href={`/sales/orders/${id}/invoice/${watch('invoice.id')}`}>
                            <ReceiptIcon strokeWidth={1} /> {t('fiscal invoice')}
                          </Link>
                        </DropdownMenuItem>
                      )}
                      {watch('goodsAccompanyingNotes').map((note) => (
                        <DropdownMenuItem asChild key={note.id}>
                          <Link href={`/sales/orders/${id}/notes/${note.id}`}>
                            <BookTextIcon strokeWidth={1} /> {note.number}
                          </Link>
                        </DropdownMenuItem>
                      ))}
                      {watch('versions').map((offer) => (
                        <DropdownMenuItem asChild key={offer.id}>
                          <Link href={`/sales/${saleType}/${id}/offer/${offer.id}`}>
                            <ReceiptTextIcon strokeWidth={1} /> {offer.name}
                          </Link>
                        </DropdownMenuItem>
                      ))}
                    </DropdownMenuContent>
                  </DropdownMenu>
                )}
                {saleType === SaleType.ORDER && (!watch('invoice.id') || hasRemainingNoteItems) && (
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button>
                        <CheckIcon /> {t('generate document')}
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent>
                      {!watch('proformaInvoice.id') && !watch('invoice.id') && (
                        <DropdownMenuItem asChild>
                          <Link href={`/sales/orders/${id}/invoice?isProforma=true`}>
                            <ReceiptIcon strokeWidth={1} />
                            {t('proforma invoice')}
                          </Link>
                        </DropdownMenuItem>
                      )}
                      {!watch('invoice.id') && (
                        <DropdownMenuItem asChild>
                          <Link href={`/sales/orders/${id}/invoice`}>
                            <ReceiptIcon strokeWidth={1} />
                            {t('fiscal invoice')}
                          </Link>
                        </DropdownMenuItem>
                      )}
                      {hasRemainingNoteItems && (
                        <DropdownMenuItem asChild>
                          <Link href={`/sales/orders/${id}/notes`}>
                            <BookTextIcon strokeWidth={1} />
                            {t('goods accompanying note')}
                          </Link>
                        </DropdownMenuItem>
                      )}
                    </DropdownMenuContent>
                  </DropdownMenu>
                )}
              </>
            )}
            {saleType === SaleType.QUOTE && (
              <Button asChild>
                <Link href={`/sales/quotes/${id}/offer`}>
                  <ReceiptTextIcon strokeWidth={1} /> {t('view offer')}
                </Link>
              </Button>
            )}
          </>
        )}
        {watch('id') && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button className='px-1.5' variant='ghost'>
                <EllipsisVerticalIcon />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align='end'>
              <DropdownMenuItem disabled={isDirty} onSelect={cloneSaleOrder}>
                {t(saleType === SaleType.ORDER ? 'clone order' : 'clone quote')}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        )}
      </PageHeader>
      <PageContent>
        <FormProvider
          {...{formState: {errors, ...restUseFormState}, register, resetField, setValue, watch, ...restUseFormActions}}
        >
          <SalesDetailsData saleType={saleType} />
          <div className='mx-6 flex items-center justify-between'>
            <div className='inline-flex items-center'>
              <Tabs onValueChange={(value) => setTab(value as SalesTab)} value={tab} variant='menu'>
                <TabsList variant='menu'>
                  <TabsTrigger
                    badge={formatNumber(watch('items')?.length)}
                    error={!!errors.items}
                    value='items'
                    variant='menu'
                  >
                    {t('items')}
                  </TabsTrigger>
                  <TabsTrigger badge={formatNumber(watch('files')?.length)} value='files' variant='menu'>
                    {t('files')}
                  </TabsTrigger>
                </TabsList>
              </Tabs>
            </div>
            <div>
              {tab === 'items' && <SaleDetailsItemsTableButtons />}
              {tab === 'files' && <SaleDetailsFilesButtons uploadFile={uploadSaleOrderFile} />}
            </div>
          </div>
          {tab === 'items' && <SaleDetailsItemsTable saveOrder={saveSaleOrder} />}
          {tab === 'files' && <SalesDetailsFilesTable deleteFile={deleteSaleOrderFile} />}
        </FormProvider>
      </PageContent>
    </Page>
  );
};

export default SalesDetails;
