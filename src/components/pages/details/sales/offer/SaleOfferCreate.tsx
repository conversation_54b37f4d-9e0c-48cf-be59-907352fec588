import {FC, useState} from 'react';

import {add} from 'date-fns';
import {ArrowLeftIcon, FileStackIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';

import {Button} from '@/components/ui/Button';
import {Checkbox} from '@/components/ui/Checkbox';
import {DatePickerInput} from '@/components/ui/DatePicker';
import HTMLEditor from '@/components/ui/HTMLEditor';
import {Label, WithLabel} from '@/components/ui/Label';
import {DocumentPreview} from '@/components/ui/special/DocumentPreview';
import {Page, PageContent, PageHeader, PageHeaderTitle, PageTitle} from '@/components/ui/special/Page';
import useSaleOrderOfferDocumentPreview from '@/hooks/documents/useSaleOrderOfferDocumentPreview';
import useHeight from '@/hooks/helpers/useHeight';
import {useRouter} from '@/hooks/helpers/useRouter';
import {DateRange} from '@/types/global';

import {saleOrderHideableDocumentColumns} from '../salesDetailsConstants';

type Props = {
  saleId: string;
};

const SaleOfferCreate: FC<Props> = ({saleId}) => {
  const {back, replace} = useRouter();
  const {elementRef} = useHeight();
  const {elementRef: containerRef} = useHeight();
  const [offerExpiration, setOfferExpiration] = useState<DateRange>({from: add(new Date(), {days: 14})});
  const [customerNotes, setCustomerNotes] = useState('');
  const {createSaleOrderOffer, renderingDetails, saleOfferDocumentPreview, setRenderingDetails} =
    useSaleOrderOfferDocumentPreview(saleId, {
      customerNotes,
      offerExpiration: offerExpiration.from,
    });
  const t = useTranslations();

  return (
    <Page>
      <PageTitle>{`${t('offer')} - ${t('quotes')} - ${t('sales')}`}</PageTitle>
      <PageHeader>
        <PageHeaderTitle className='inline-flex items-center gap-2'>
          <Button onClick={() => back(`/sales/quotes/${saleId}`)} size='icon' variant='none'>
            <ArrowLeftIcon />
          </Button>
          {t('offer')}
        </PageHeaderTitle>
        <div className='grow' />
        <Button
          onClick={() => {
            createSaleOrderOffer({customerNotes, offerExpiration: offerExpiration.from}, renderingDetails).then(
              ({id}) => {
                if (id) replace(`/sales/quotes/${saleId}/offer/${id}`);
              },
            );
          }}
        >
          <FileStackIcon /> {t('create a new offer')}
        </Button>
      </PageHeader>
      <PageContent>
        <div className='flex' ref={elementRef}>
          <div className='flex h-full justify-center bg-gray-100 p-4 w-3/4'>
            <DocumentPreview content={saleOfferDocumentPreview} />
          </div>
          <div className='flex w-1/3 flex-col overflow-y-auto' ref={containerRef}>
            <div className='flex flex-col gap-4 p-6'>
              <WithLabel>
                <DatePickerInput disablePast onChange={setOfferExpiration} value={offerExpiration} />
                <Label>{t('quote expiration')}</Label>
              </WithLabel>
              <div className='text-lg font-semibold'>{t('hide columns')}:</div>
              {saleOrderHideableDocumentColumns.map((column, index) => (
                <WithLabel direction='horizontal' key={`${column}-${index}`}>
                  <Checkbox
                    checked={renderingDetails.columnsToHide?.includes(column as any)}
                    id={column}
                    onCheckedChange={(checked) =>
                      setRenderingDetails((prev) => ({
                        ...prev,
                        columnsToHide: checked
                          ? [...(prev.columnsToHide || []), column as any]
                          : prev.columnsToHide?.filter((c) => c !== column),
                      }))
                    }
                  />
                  <Label htmlFor={column}>{t(column as any)}</Label>
                </WithLabel>
              ))}
            </div>
            <div className='text-lg font-semibold px-6'>{t('mentions')}:</div>
            <HTMLEditor onChange={setCustomerNotes} value={customerNotes} />
          </div>
        </div>
      </PageContent>
    </Page>
  );
};

export default SaleOfferCreate;
