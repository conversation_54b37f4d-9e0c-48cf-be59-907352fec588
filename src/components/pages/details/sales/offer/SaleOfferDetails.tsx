import {FC} from 'react';

import {ArrowLeftIcon, CloudDownloadIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';

import {Button} from '@/components/ui/Button';
import {DocumentPreview} from '@/components/ui/special/DocumentPreview';
import {Link} from '@/components/ui/special/Link';
import {Page, PageContent, PageHeader, PageHeaderTitle, PageTitle} from '@/components/ui/special/Page';
import useSaleOrderOfferDocument from '@/hooks/documents/useSaleOrderOfferDocument';
import useHeight from '@/hooks/helpers/useHeight';
import {useRouter} from '@/hooks/helpers/useRouter';

type Props = {
  offerId: string;
  saleId: string;
};

const SaleOfferDetails: FC<Props> = ({offerId, saleId}) => {
  const {elementRef} = useHeight();
  const {saleOfferDocument} = useSaleOrderOfferDocument(offerId, saleId);
  const {back} = useRouter();
  const t = useTranslations();

  return (
    <Page>
      <PageTitle>{`${t('order')} - ${t('quotes')} - ${t('sales')}`}</PageTitle>
      <PageHeader>
        <PageHeaderTitle className='inline-flex items-center gap-2'>
          <Button onClick={() => back(`/sales/quotes/${saleId}`)} size='icon' variant='none'>
            <ArrowLeftIcon />
          </Button>
          {t('order')}
        </PageHeaderTitle>
        <div className='grow' />
        <Button asChild variant='secondary'>
          <Link href={`/api/sales/orders/${saleId}/view-version/${offerId}??mediaType=application/pdf`}>
            <CloudDownloadIcon />
            {t('download')}
          </Link>
        </Button>
      </PageHeader>
      <PageContent>
        <div className='flex' ref={elementRef}>
          <div className='flex size-full justify-center bg-gray-100 p-4'>
            <DocumentPreview content={saleOfferDocument} />
          </div>
        </div>
      </PageContent>
    </Page>
  );
};

export default SaleOfferDetails;
