import {FC, useEffect} from 'react';

import {joiResolver} from '@hookform/resolvers/joi';
import {isEmpty} from 'lodash';
import {PlusIcon, Trash2Icon} from 'lucide-react';
import {useTranslations} from 'next-intl';
import {useForm} from 'react-hook-form';

import {Button} from '@/components/ui/Button';
import {Checkbox} from '@/components/ui/Checkbox';
import {Input, InputLabel} from '@/components/ui/Input';
import {Label, WithLabel} from '@/components/ui/Label';
import {Sheet, SheetClose, SheetContent, SheetFooter, SheetPage, SheetTitle} from '@/components/ui/Sheet';
import CountriesCombobox from '@/components/ui/special/CountriesCombobox';
import {customerSchema} from '@/hooks/useCustomer';
import useCustomerActions from '@/hooks/useCustomerActions';
import {Address, AddressType} from '@/types/global';
import {Customer, CustomerType} from '@/types/sales';

type Props = {
  customer?: Customer;
  onClose: () => void;
  onCreate: (address: Address) => void;
};

const NewCustomerAddressSheet: FC<Props> = ({customer, onClose, onCreate}) => {
  const t = useTranslations();
  const {updateCustomer} = useCustomerActions();
  const {
    formState: {errors},
    register,
    setValue,
    trigger,
    watch,
  } = useForm<Customer>({
    defaultValues: {
      addresses: [{address2: '', country: '', types: [AddressType.BILLING]}],
      name: 'test',
      taxIdentificationNumber: 'test',
      type: CustomerType.LOCAL_LEGAL_ENTITY,
    },
    mode: 'onSubmit',
    resolver: joiResolver(customerSchema),
  });

  useEffect(() => {
    setTimeout(trigger, 50);
  }, [trigger]);

  if (!customer) return null;

  return (
    <Sheet defaultOpen={true} onOpenChange={() => setTimeout(onClose, 500)}>
      <SheetPage side='right'>
        <SheetTitle className='text-2xl'>{t('create new address')}</SheetTitle>
        <SheetContent className='flex flex-col gap-4 px-6'>
          <div className='relative flex w-full flex-col gap-2 rounded-lg border p-4'>
            <WithLabel>
              <Input {...register('addresses.0.address1')} error={!!errors.addresses?.[0]?.address1} />
              <InputLabel>{t('address (str no bl ap st fl)')}</InputLabel>
            </WithLabel>
            <div className='flex items-center gap-2'>
              <WithLabel className='w-full'>
                <Input {...register('addresses.0.city')} error={!!errors.addresses?.[0]?.city} />
                <InputLabel>{t('city')}</InputLabel>
              </WithLabel>
              <WithLabel className='w-full'>
                <Input {...register('addresses.0.state')} error={!!errors.addresses?.[0]?.state} />
                <InputLabel>{t('state')}</InputLabel>
              </WithLabel>
            </div>
            <div className='flex items-center gap-2'>
              <WithLabel className='w-full'>
                <CountriesCombobox
                  error={!!errors.addresses?.[0]?.country}
                  onChange={(value) => setValue('addresses.0.country', value)}
                  value={watch('addresses.0.country')}
                />
                <InputLabel>{t('country')}</InputLabel>
              </WithLabel>
              <WithLabel>
                <Input {...register('addresses.0.zip')} error={!!errors.addresses?.[0]?.zip} size='md' />
                <InputLabel>{t('zip')}</InputLabel>
              </WithLabel>
            </div>
            <WithLabel>
              <Input {...register('addresses.0.name')} error={!!errors.addresses?.[0]?.name} />
              <InputLabel>{t('preferential name')}</InputLabel>
            </WithLabel>
            <div className='flex h-10 justify-between'>
              <div className='flex gap-8'>
                <WithLabel direction='horizontal'>
                  <Checkbox defaultChecked={true} disabled id='billing' />
                  <Label htmlFor='billing'>{t('billing')}</Label>
                </WithLabel>
                {!watch('addresses.1') && (
                  <WithLabel direction='horizontal'>
                    <Checkbox
                      checked={watch('addresses.0.types')?.includes(AddressType.SHIPPING)}
                      id='shipping'
                      onCheckedChange={(value) =>
                        setValue(
                          'addresses.0.types',
                          value
                            ? [...(watch('addresses.0.types') || []), AddressType.SHIPPING]
                            : (watch('addresses.0.types') || []).filter((type) => type !== AddressType.SHIPPING),
                        )
                      }
                    />
                    <Label htmlFor='shipping'>{t('shipping')}</Label>
                  </WithLabel>
                )}
              </div>
              {!watch('addresses.1') && (
                <Button
                  onClick={() =>
                    setValue(
                      'addresses',
                      [
                        {...watch('addresses.0'), types: [AddressType.BILLING]},
                        {
                          address1: '',
                          address2: '',
                          city: '',
                          country: '',
                          name: '',
                          state: '',
                          types: [AddressType.SHIPPING],
                          zip: '',
                        },
                      ],
                      {shouldValidate: true},
                    )
                  }
                  variant='secondary'
                >
                  <PlusIcon />
                  {t('shipping address')}
                </Button>
              )}
            </div>
          </div>
          {watch('addresses.1') && (
            <div className='relative flex w-full flex-col gap-2 rounded-lg border p-4'>
              <Button
                className='absolute right-1 top-1'
                onClick={() => {
                  setValue('addresses', [watch('addresses.0')]);
                  trigger();
                }}
                size='icon'
                variant='ghost'
              >
                <Trash2Icon className='size-5 text-red' strokeWidth={1} />
              </Button>
              <WithLabel>
                <Input {...register('addresses.1.address1')} error={!!errors.addresses?.[1]?.address1} />
                <InputLabel>{t('address (str no bl ap st fl)')}</InputLabel>
              </WithLabel>
              <div className='flex items-center gap-2'>
                <WithLabel className='w-full'>
                  <Input {...register('addresses.1.city')} error={!!errors.addresses?.[1]?.city} />
                  <InputLabel>{t('city')}</InputLabel>
                </WithLabel>
                <WithLabel className='w-full'>
                  <Input {...register('addresses.1.state')} error={!!errors.addresses?.[1]?.state} />
                  <InputLabel>{t('state')}</InputLabel>
                </WithLabel>
              </div>
              <div className='flex items-center gap-2'>
                <WithLabel className='w-full'>
                  <CountriesCombobox
                    error={!!errors.addresses?.[1]?.country}
                    onChange={(value) => setValue('addresses.1.country', value)}
                    value={watch('addresses.1.country')}
                  />
                  <InputLabel>{t('country')}</InputLabel>
                </WithLabel>
                <WithLabel>
                  <Input {...register('addresses.1.zip')} error={!!errors.addresses?.[1]?.zip} size='md' />
                  <InputLabel>{t('zip')}</InputLabel>
                </WithLabel>
              </div>
              <WithLabel>
                <Input {...register('addresses.1.name')} error={!!errors.addresses?.[1]?.name} />
                <InputLabel>{t('preferential name')}</InputLabel>
              </WithLabel>
              <WithLabel className='h-10' direction='horizontal'>
                <Checkbox defaultChecked={true} disabled id='shipping' />
                <Label htmlFor='shipping'>{t('shipping')}</Label>
              </WithLabel>
            </div>
          )}
        </SheetContent>
        <SheetFooter className='px-6'>
          <SheetClose asChild disabled={!isEmpty(errors)}>
            <Button
              onClick={() => {
                updateCustomer(customer?.id, {
                  ...customer,
                  addresses: [...(customer.addresses || []), ...watch('addresses')],
                });
                onCreate(watch('addresses.0'));
              }}
            >
              {t('add customer')}
            </Button>
          </SheetClose>
        </SheetFooter>
      </SheetPage>
    </Sheet>
  );
};

export default NewCustomerAddressSheet;
