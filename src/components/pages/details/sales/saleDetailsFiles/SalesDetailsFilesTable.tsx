import {FC} from 'react';

import {useAtomValue} from 'jotai';
import {useTranslations} from 'next-intl';
import {useFormContext} from 'react-hook-form';

import {detailsDataShownAtom} from '@/components/pages/details/detailsStore';
import {
  Table,
  TableBody,
  TableContainer,
  TableHead,
  TableHeadActions,
  TableHeader,
  TableRow,
} from '@/components/ui/Table';
import useHeight from '@/hooks/helpers/useHeight';
import {SaleOrder} from '@/types/sales';

import SalesDetailsFilesTableRow from './SalesDetailsFilesTableRow';

type Props = {
  deleteFile: (id: string) => void;
};

const SalesDetailsFilesTable: FC<Props> = ({deleteFile}) => {
  const {watch} = useFormContext<SaleOrder>();
  const detailsDataShown = useAtomValue(detailsDataShownAtom(watch('id')));
  const {elementRef} = useHeight({dependencies: [detailsDataShown]});
  const t = useTranslations();

  return (
    <TableContainer ref={elementRef}>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>{t('name')}</TableHead>
            <TableHead />
            <TableHeadActions />
          </TableRow>
        </TableHeader>
        <TableBody className='overflow-y-hidden' isValidating={!watch()}>
          {watch('files')?.map((file, index) => (
            <SalesDetailsFilesTableRow deleteFile={deleteFile} file={file} key={`${file.id}-${index}`} />
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );
};

export default SalesDetailsFilesTable;
