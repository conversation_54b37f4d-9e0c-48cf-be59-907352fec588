import {FC, useState} from 'react';

import {useAtomValue} from 'jotai';
import {useTranslations} from 'next-intl';
import {useFormContext} from 'react-hook-form';

import {detailsDataShownAtom} from '@/components/pages/details/detailsStore';
import {Button} from '@/components/ui/Button';
import {Combobox, ComboboxLabel, ControlledCombobox} from '@/components/ui/Combobox';
import {ControlledDatePickerInput} from '@/components/ui/DatePicker';
import {InputLabel} from '@/components/ui/Input';
import {WithLabel} from '@/components/ui/Label';
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from '@/components/ui/Select';
import {HideableContent} from '@/components/ui/special/HideableContent';
import NewCustomerSheet from '@/components/ui/special/NewCustomerSheet';
import {TextareaAutosize} from '@/components/ui/Textarea';
import {useHasPermission} from '@/hooks/helpers/useHasPermission';
import useCurrencies from '@/hooks/useCurrencies';
import useCustomers from '@/hooks/useCustomers';
import useSaleOrderActions from '@/hooks/useSaleOrderActions';
import {defaultCurrencyAtom, exchangeRatesAtom} from '@/store/defaults';
import {AddressType, SaleStatus} from '@/types/global';
import {SaleOrder, SaleType} from '@/types/sales';
import {addressToString, convertCurrency, formatCurrency} from '@/utils/format';

import NewCustomerAddressSheet from './NewCustomerAddressSheet';

type Props = {
  saleType: SaleType;
};

const SalesDetailsData: FC<Props> = ({saleType}) => {
  const t = useTranslations();
  const [newCustomerName, setNewCustomerName] = useState('');
  const [showNewCustomerAddressModal, setShowNewCustomerAddressModal] = useState(false);
  const {hasPermission, isLoading: permissionIsLoading} = useHasPermission();
  const {customers, isLoading} = useCustomers();
  const {calculateSaleOrderSummary} = useSaleOrderActions();
  const defaultCurrency = useAtomValue(defaultCurrencyAtom);
  const {currencies, isLoading: currenciesIsLoading} = useCurrencies();
  const exchangeRates = useAtomValue(exchangeRatesAtom);
  const {
    control,
    formState: {errors},
    register,
    setValue,
    watch,
  } = useFormContext<SaleOrder>();
  const detailsDataShown = useAtomValue(detailsDataShownAtom(watch('id')));

  if (isLoading || permissionIsLoading || currenciesIsLoading) return null;

  return (
    <HideableContent show={detailsDataShown}>
      {newCustomerName && (
        <NewCustomerSheet
          defaultValue={newCustomerName}
          onClose={() => setNewCustomerName('')}
          onCreate={(customer) => {
            setValue('customer', customer, {shouldValidate: true});

            if (customer.addresses?.length === 1) {
              setValue('shippingAddress', customer.addresses[0], {shouldValidate: true});
            }
          }}
        />
      )}
      {showNewCustomerAddressModal && (
        <NewCustomerAddressSheet
          customer={customers.find((customer) => customer.id === watch('customer.id'))}
          onClose={() => setShowNewCustomerAddressModal(false)}
          onCreate={(address) => {
            setValue('shippingAddress', address, {shouldValidate: true});
          }}
        />
      )}
      <div className='mx-6 mt-6 flex items-stretch gap-6'>
        <div className='grow rounded-lg border border-border p-6'>
          <div className='flex flex-col gap-4'>
            <div className='text-base font-medium'>
              {t(saleType === SaleType.ORDER ? 'order details' : 'quote details')}
            </div>
            <div className='flex gap-8'>
              <WithLabel className='w-full'>
                <ControlledCombobox
                  className='w-full'
                  control={control}
                  controlName='customer.id'
                  error={!!errors?.customer}
                  notFoundClassName='mx-1'
                  onChange={(value) => {
                    const customer = customers.find(({id}) => id === value);
                    if (customer) {
                      setValue('customer.name', customer.name, {shouldValidate: true});
                      if (customer.addresses?.length === 1) {
                        setValue('shippingAddress', customer.addresses[0], {shouldValidate: true});
                      }
                    }
                  }}
                  options={customers.map(({id, name}) => ({id, value: name}))}
                  placeholder={t('customer')}
                  renderNotFound={(query) => (
                    <Button
                      className='w-full justify-start'
                      onClick={() => setNewCustomerName(query)}
                      variant='secondary'
                    >
                      {t('new customer')}
                    </Button>
                  )}
                  searchPlaceholder={t('search customer')}
                />
                <ComboboxLabel>{t('customer')}*</ComboboxLabel>
              </WithLabel>
              <div className='flex w-full flex-col gap-4'>
                {saleType === SaleType.ORDER && (
                  <div className='flex gap-8'>
                    <WithLabel className='w-full'>
                      <ControlledDatePickerInput
                        className='w-full'
                        control={control}
                        controlName='submittedDate'
                        disabled={!!watch('id')}
                        error={!!errors?.createTime}
                      />
                      <InputLabel>{t('order date')}*</InputLabel>
                    </WithLabel>
                    <WithLabel className='w-full'>
                      <ControlledDatePickerInput
                        control={control}
                        controlName='deliveryDeadline'
                        disabled={[SaleStatus.CANCELED, SaleStatus.DELIVERED, SaleStatus.SHIPPING].includes(
                          watch('status'),
                        )}
                        error={!!errors?.createTime}
                      />
                      <InputLabel>{t('delivery deadline')}</InputLabel>
                    </WithLabel>
                  </div>
                )}
                {saleType === SaleType.QUOTE && (
                  <div className='flex gap-8'>
                    <WithLabel className='w-full'>
                      <ControlledDatePickerInput
                        className='w-full'
                        control={control}
                        controlName='offerDate'
                        disabled={!!watch('id')}
                        error={!!errors?.createTime}
                      />
                      <InputLabel>{t('quote date')}</InputLabel>
                    </WithLabel>
                    <WithLabel className='w-full'>
                      <ControlledDatePickerInput
                        control={control}
                        controlName='offerExpiration'
                        disabled
                        error={!!errors?.createTime}
                      />
                      <InputLabel>{t('quote expiration')}</InputLabel>
                    </WithLabel>
                  </div>
                )}
              </div>
            </div>
            <WithLabel>
              <Combobox
                disabled={[SaleStatus.CANCELED, SaleStatus.DELIVERED, SaleStatus.SHIPPING].includes(watch('status'))}
                error={!!errors?.shippingAddress}
                onChange={(value) => {
                  const address = customers
                    .find((customer) => customer.id === watch('customer.id'))
                    ?.addresses?.find((address) => Object.values(address).sort().toString() === value);

                  if (address) setValue('shippingAddress', address, {shouldValidate: true});
                }}
                options={
                  customers
                    .find((customer) => customer.id === watch('customer.id'))
                    ?.addresses?.filter((address) => address.types?.includes(AddressType.BILLING))
                    ?.map((address) => ({
                      id: Object.values(address).sort().toString(),
                      value: addressToString(address),
                    })) || []
                }
                placeholder={t('address')}
                renderNotFound={() =>
                  !!watch('customer.id') ? (
                    <Button
                      className='w-full justify-start'
                      onClick={() => setShowNewCustomerAddressModal(true)}
                      variant='secondary'
                    >
                      {t('new address')}
                    </Button>
                  ) : (
                    t('address not found')
                  )
                }
                searchPlaceholder={t('search address')}
                value={Object.values(watch('shippingAddress') || {})
                  .sort()
                  .toString()}
              />
              <ComboboxLabel>{t('ships to')}*</ComboboxLabel>
            </WithLabel>
            <WithLabel>
              <TextareaAutosize {...register('notes')} error={!!errors.notes} maxRows={8} minRows={2} />
              <InputLabel>{t('notes')}</InputLabel>
            </WithLabel>
          </div>
        </div>
        {hasPermission('financial', 'sales') && (
          <div className='flex max-w-xl grow flex-col gap-5 rounded-lg border border-border p-6'>
            <div className='flex items-center justify-between gap-4'>
              <div className='text-base font-medium'>{t('summary')}</div>
              <Select
                onValueChange={(toCurrency) => {
                  const fromCurrency = watch('items.0.price.currency') || defaultCurrency;
                  const items = watch('items').map((item) => ({
                    ...item,
                    administrativeOverheadCosts: {
                      amount: convertCurrency(
                        item.administrativeOverheadCosts?.amount,
                        fromCurrency,
                        toCurrency,
                        exchangeRates,
                      ),
                      currency: toCurrency,
                    },
                    discountAmount: {
                      amount: convertCurrency(item.discountAmount?.amount, fromCurrency, toCurrency, exchangeRates),
                      currency: toCurrency,
                    },
                    laborCosts: {
                      amount: convertCurrency(item.laborCosts?.amount, fromCurrency, toCurrency, exchangeRates),
                      currency: toCurrency,
                    },
                    materialCosts: {
                      amount: convertCurrency(item.materialCosts?.amount, fromCurrency, toCurrency, exchangeRates),
                      currency: toCurrency,
                    },
                    price: {
                      amount: convertCurrency(item.price?.amount, fromCurrency, toCurrency, exchangeRates),
                      currency: toCurrency,
                    },
                    taxAmount: {
                      amount: convertCurrency(item.taxAmount?.amount, fromCurrency, toCurrency, exchangeRates),
                      currency: toCurrency,
                    },
                    totalAmount: {
                      amount: convertCurrency(item.totalAmount?.amount, fromCurrency, toCurrency, exchangeRates),
                      currency: toCurrency,
                    },
                  }));

                  setValue('items', items);
                  calculateSaleOrderSummary(items, setValue);
                }}
                value={watch('items.0.price.currency') || defaultCurrency}
              >
                <SelectTrigger className='w-fit'>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {currencies?.map((currency) => (
                    <SelectItem key={currency.displayName} value={currency.symbol}>
                      {currency.symbol}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className='flex items-center justify-between gap-8 border-b pb-5'>
              <div className='text-muted'>{t('subtotal')}</div>
              {formatCurrency({
                amount: watch('subTotalAmount')?.amount,
                currency: watch('subTotalAmount')?.currency || defaultCurrency,
              })}
            </div>
            <div className='flex items-center justify-between gap-8 border-b pb-5'>
              <div className='text-muted'>{t('discount')}</div>
              {formatCurrency({
                amount: watch('discountAmount')?.amount,
                currency: watch('discountAmount')?.currency || defaultCurrency,
              })}
            </div>
            <div className='flex items-center justify-between gap-8 border-b pb-5'>
              <div className='text-muted'>{t('vat')}</div>
              {formatCurrency({
                amount: watch('taxAmount')?.amount,
                currency: watch('taxAmount')?.currency || defaultCurrency,
              })}
            </div>
            <div className='flex items-center justify-between gap-8 font-bold'>
              <div>{t('total price with vat')}</div>
              {formatCurrency({
                amount: watch('totalAmount')?.amount,
                currency: watch('totalAmount')?.currency || defaultCurrency,
              })}
            </div>
          </div>
        )}
      </div>
    </HideableContent>
  );
};

export default SalesDetailsData;
