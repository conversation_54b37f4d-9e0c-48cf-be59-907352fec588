import {FC, useRef, useState} from 'react';

import {XIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';

import {Button} from '@/components/ui/Button';
import {Input, InputLabel} from '@/components/ui/Input';
import {WithLabel} from '@/components/ui/Label';
import {Sheet, SheetClose, SheetContent, SheetFooter, SheetHeader, SheetPage, SheetTitle} from '@/components/ui/Sheet';
import {TextareaAutosize} from '@/components/ui/Textarea';
import useManufacturingOrderActions from '@/hooks/useManufacturingOrderActions';
import {Id} from '@/types/global';

type Props = {
  onClose: () => void;
  product: Id & {code: string};
  quantity: number;
  saleId: string;
};

const NewCustomManufacturingOrderSheet: FC<Props> = ({onClose, product, quantity, saleId}) => {
  const t = useTranslations();
  const {createLinkedCustomizedManufacturingOrder} = useManufacturingOrderActions();
  const [customizationNote, setCustomizationNote] = useState('');
  const [files, setFiles] = useState<File[]>([]);
  const uploadBtn = useRef<HTMLInputElement>(null);

  return (
    <Sheet defaultOpen={true} onOpenChange={() => setTimeout(onClose, 500)}>
      <SheetPage side='right'>
        <SheetTitle className='text-2xl'>{t('new custom manufacturing order')}</SheetTitle>
        <SheetHeader className='gap-0'>
          <WithLabel className='w-full'>
            <Input disabled value={product.name} />
            <InputLabel>{t('item')}</InputLabel>
          </WithLabel>
        </SheetHeader>
        <SheetContent className='flex flex-col gap-4 px-6'>
          <div className='rounded-lg border p-4'>
            <TextareaAutosize
              minRows={7}
              onChange={({target: {value}}) => setCustomizationNote(value)}
              placeholder={t('customization notes')}
              value={customizationNote}
            />
          </div>
          <div className='text-base font-medium'>{t('files')}</div>
          <div className='flex flex-col gap-1'>
            {files.map((file, index) => (
              <div
                className='group relative w-fit cursor-pointer'
                key={`${file.name}-${index}`}
                onClick={() => setFiles((prevFiles) => prevFiles.filter((_, i) => i !== index))}
              >
                <XIcon className='absolute -right-5 top-1/2 hidden size-5 -translate-y-1/2 group-hover:block' />{' '}
                {file.name}
              </div>
            ))}
          </div>
        </SheetContent>
        <SheetFooter className='px-6'>
          <Button onClick={() => uploadBtn.current?.click()} variant='secondary'>
            {t('add files')}
          </Button>
          <input
            className='hidden'
            multiple
            onChange={({target: {files}}) => {
              if (files) setFiles((prevFiles) => [...prevFiles, ...files]);
            }}
            ref={uploadBtn}
            type='file'
          />
          <SheetClose asChild>
            <Button
              disabled={!customizationNote && files.length === 0}
              onClick={() => {
                createLinkedCustomizedManufacturingOrder(
                  {customizationNote, id: product.id, quantity},
                  files,
                  saleId,
                ).then(() => {
                  onClose();
                });
              }}
            >
              {t('create custom manufacturing order')}
            </Button>
          </SheetClose>
        </SheetFooter>
      </SheetPage>
    </Sheet>
  );
};

export default NewCustomManufacturingOrderSheet;
