import {FC, useCallback, useMemo, useState} from 'react';

import {useAtomValue} from 'jotai';
import {PlusIcon, XIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';
import {useFieldArray, useFormContext} from 'react-hook-form';

import {Button} from '@/components/ui/Button';
import {Combobox} from '@/components/ui/Combobox';
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from '@/components/ui/Select';
import AddItemModal from '@/components/ui/special/AddItemModal/AddItemModal';
import useEnabledUnits from '@/hooks/useEnabledUnits';
import useItemActions from '@/hooks/useItemActions';
import useSaleOrderActions from '@/hooks/useSaleOrderActions';
import useServiceTemplateActions from '@/hooks/useServiceTemplateActions';
import useServiceTemplates from '@/hooks/useServiceTemplates';
import {defaultCurrency<PERSON>tom, defaultVATAtom, exchangeRatesAtom} from '@/store/defaults';
import {ExchangeRates, SaleItemStatus, Service} from '@/types/global';
import {SaleItem, SaleOrder} from '@/types/sales';
import {classes} from '@/utils/common';
import {convertCurrency} from '@/utils/format';
import useItemStockActions from 'hooks/useItemStockActions';

type AddProps = {
  currency: string;
  exchangeRates: ExchangeRates;
  excludeIds: (null | string | undefined)[];
  onClose: () => void;
  onSelect: (value: SaleItem) => void;
  orderId: string;
};

const AddItem: FC<AddProps> = ({currency, exchangeRates, excludeIds, onClose, onSelect, orderId}) => {
  const {getItem} = useItemActions();
  const {getItemAvailable} = useItemStockActions();
  const defaultVAT = useAtomValue(defaultVATAtom);
  const defaultCurrency = useAtomValue(defaultCurrencyAtom);

  const handleAdd = useCallback(
    (values: {id: string; quantity: number}[]) => {
      values.forEach(async ({id, quantity}) => {
        const item = await getItem(id);

        if (!item) return;
        const available = await getItemAvailable(item.id, quantity, {id: orderId, type: 'sales'});

        const price = convertCurrency(
          item.sellPrice.amount,
          item.sellPrice.currency || defaultCurrency,
          currency,
          exchangeRates,
        );
        const tax = price * (item.vatRate || defaultVAT);
        const total = price + tax;

        onSelect({
          ...item,
          addedToWishlist: false,
          administrativeOverheadCosts: {
            amount: convertCurrency(
              item.estimatedAdministrativeCost.amount || 0,
              item.estimatedAdministrativeCost.currency || defaultCurrency,
              currency,
              exchangeRates,
            ),
            currency,
          },
          customizations: [],
          discount: 0,
          discountAmount: {amount: 0, currency},
          laborCosts: {
            amount: convertCurrency(
              (item.estimatedEmployeeAndWorkstationCost?.amount || 0) +
                (item.estimatedProductionOverheadCost?.amount || 0),
              item.estimatedEmployeeAndWorkstationCost?.currency ||
                item.estimatedProductionOverheadCost?.currency ||
                defaultCurrency,
              currency,
              exchangeRates,
            ),
            currency,
          },
          materialCosts: {
            amount: convertCurrency(
              item.estimatedMaterialCost.amount,
              item.estimatedMaterialCost.currency || defaultCurrency,
              currency,
              exchangeRates,
            ),
            currency,
          },
          price: {
            amount: price,
            currency,
          },
          productId: item.id,
          quantity,
          status: available ? SaleItemStatus.READY : SaleItemStatus.NEED_SUPPLY,
          taxAmount: {
            amount: tax,
            currency,
          },
          totalAmount: {
            amount: total,
            currency,
          },
        });
      });

      onClose();
    },
    [currency, defaultCurrency, defaultVAT, exchangeRates, getItem, getItemAvailable, onClose, onSelect, orderId],
  );

  return <AddItemModal excludeIds={excludeIds} onAdd={handleAdd} onClose={onClose} />;
};

const AddService: FC<AddProps> = ({currency, exchangeRates, excludeIds, onClose, onSelect}) => {
  const t = useTranslations();
  const {services} = useServiceTemplates();
  const defaultVAT = useAtomValue(defaultVATAtom);
  const defaultCurrency = useAtomValue(defaultCurrencyAtom);
  const {createService} = useServiceTemplateActions();
  const [selectedUnit, setSelectedUnit] = useState('PIECE');
  const {enabledUnits, units} = useEnabledUnits();

  const availableServices = useMemo(
    () => services.filter((item) => !excludeIds.includes(item.id)),
    [excludeIds, services],
  );

  const handleSelect = useCallback(
    async (service: Service | undefined) => {
      if (service) {
        const price = convertCurrency(
          service.sellPrice?.amount || 0,
          service.sellPrice?.currency || defaultCurrency,
          currency,
          exchangeRates,
        );
        const tax = price * (service.vatRate || defaultVAT);
        const total = price + tax;

        onSelect({
          addedToWishlist: false,
          administrativeOverheadCosts: {
            amount: 0,
            currency,
          },
          customizable: false,
          customizations: [],
          discount: 0,
          discountAmount: {amount: 0, currency},
          laborCosts: service.cost || {amount: 0, currency},
          materialCosts: {
            amount: 0,
            currency,
          },
          measurementUnit: units.find((unit) => unit.id === service.measurementUnit) || {id: 'PIECE', name: 'pcs'},
          name: service.name,
          price: {
            amount: price,
            currency,
          },
          quantity: 1,
          serviceId: service.id,
          status: SaleItemStatus.READY,
          taxAmount: {
            amount: tax,
            currency,
          },
          totalAmount: {
            amount: total,
            currency,
          },
          vatRate: service.vatRate || defaultVAT,
        });
        onClose();
      }
    },
    [currency, defaultCurrency, defaultVAT, exchangeRates, onClose, onSelect, units],
  );

  return (
    <>
      <Combobox
        className='w-[300px]'
        notFoundClassName='mx-1'
        onChange={(value) => handleSelect(services.find((item) => item.id === value))}
        open
        options={availableServices.map((item) => ({id: item.id, value: item.name}))}
        placeholder={t('service')}
        renderNotFound={(query) => (
          <div className='flex items-center gap-4'>
            <Button
              className='grow justify-start'
              onClick={() => {
                createService({measurementUnit: selectedUnit, name: query}).then((service) => {
                  if (service) handleSelect(service);
                });
              }}
              variant='secondary'
            >
              {t('create service')}
            </Button>
            <Select onValueChange={setSelectedUnit} value={selectedUnit}>
              <SelectTrigger size='sm'>
                <SelectValue>
                  {t(`unit.name.${units.find(({id}) => id === selectedUnit)?.name || 'pcs'}` as any)}
                </SelectValue>
              </SelectTrigger>
              <SelectContent>
                {enabledUnits.map(({id, name}) => (
                  <SelectItem key={id} value={id}>
                    {t(`unit.name.${name.toLowerCase()}` as any)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        )}
        searchPlaceholder={t('search item')}
      />
      <Button onClick={onClose} variant='secondary'>
        <XIcon />
        {t('cancel')}
      </Button>
    </>
  );
};

type Props = {
  className?: string;
};

const SaleDetailsItemsTableButtons: FC<Props> = ({className}) => {
  const t = useTranslations();
  const [addEnabled, setAddEnabled] = useState(false);
  const [addServiceEnabled, setAddServiceEnabled] = useState(false);
  const {control, setValue, watch} = useFormContext<SaleOrder>();
  const {calculateSaleOrderSummary} = useSaleOrderActions();
  const defaultCurrency = useAtomValue(defaultCurrencyAtom);
  const exchangeRates = useAtomValue(exchangeRatesAtom);
  const {append, fields} = useFieldArray({control, name: 'items'});

  const handleAppend = useCallback(
    (item: SaleItem) => {
      calculateSaleOrderSummary([...fields, item], setValue);
      append(item);
    },
    [append, calculateSaleOrderSummary, fields, setValue],
  );
  const currency = watch('items.0.price.currency') || defaultCurrency;

  return (
    <div className={classes('flex items-center gap-4', className)}>
      {!addEnabled && !addServiceEnabled && (
        <>
          <Button onClick={() => setAddEnabled(true)} variant='secondary'>
            <PlusIcon /> {t('add item')}
          </Button>
          <Button onClick={() => setAddServiceEnabled(true)} variant='secondary'>
            <PlusIcon /> {t('add service')}
          </Button>
        </>
      )}
      {addEnabled && (
        <AddItem
          currency={currency}
          exchangeRates={exchangeRates}
          excludeIds={watch('items')?.map((item) => item.productId) ?? []}
          onClose={() => setAddEnabled(false)}
          onSelect={handleAppend}
          orderId={watch('id')}
        />
      )}
      {addServiceEnabled && (
        <AddService
          currency={currency}
          exchangeRates={exchangeRates}
          excludeIds={watch('items')?.map((item) => item.serviceId) ?? []}
          onClose={() => setAddServiceEnabled(false)}
          onSelect={handleAppend}
          orderId={watch('id')}
        />
      )}
    </div>
  );
};

export default SaleDetailsItemsTableButtons;
