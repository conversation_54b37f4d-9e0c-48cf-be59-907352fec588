import {Dispatch, FC, useEffect, useMemo, useState} from 'react';

import {SetStateAction, useAtomValue} from 'jotai';
import {sumBy} from 'lodash';
import {PlusIcon, Trash2Icon} from 'lucide-react';
import {useTranslations} from 'next-intl';
import {useFormContext} from 'react-hook-form';

import {Accordion, AccordionContent, AccordionItem, AccordionTrigger} from '@/components/ui/Accordion';
import {Button} from '@/components/ui/Button';
import {NumberInput} from '@/components/ui/Input';
import {Sheet, SheetClose, SheetContent, SheetFooter, SheetPage, SheetTitle} from '@/components/ui/Sheet';
import {useHasPermission} from '@/hooks/helpers/useHasPermission';
import useItem from '@/hooks/useItem';
import {defaultCurrencyAtom} from '@/store/defaults';
import {Currency, Id} from '@/types/global';
import {InventoryItemOption} from '@/types/inventory';
import {SaleOrder} from '@/types/sales';
import {formatCurrency} from '@/utils/format';

type ButtonProps = {
  customization: CustomizationDefinition;
  setCustomizations: Dispatch<SetStateAction<CustomizationValue[]>>;
  value: CustomizationValue | undefined;
};

type CustomizationDefinition = Id & {
  code: string;
  cost: Currency;
  optional: boolean;
  options: InventoryItemOption[];
  quantity: number;
};

type CustomizationValue = Id & {
  code: string;
  cost: Currency;
  parentId: string;
  quantity: number;
  unitCost: Currency;
};

const CustomizationButton: FC<ButtonProps> = ({customization, setCustomizations, value}) => {
  const t = useTranslations();
  const {hasPermission} = useHasPermission();

  return (
    <div className='inline-flex w-full items-center justify-between gap-4'>
      <div className='flex items-center gap-4'>
        <div className='inline-flex flex-col items-start'>
          <div className='line-clamp-1 text-left'>{value?.name || customization.name}</div>
          <div className='text-xs text-border-foreground'>
            {t('sku')}: {value?.code || customization.code}
          </div>
        </div>
        {customization.optional && (
          <Button
            asChild
            onClick={(event) => {
              setCustomizations((prev) => prev.filter((custom) => custom.parentId !== customization.id));
              event.preventDefault();
            }}
            variant='ghost'
          >
            <div>
              <Trash2Icon className='size-5 text-red' />
            </div>
          </Button>
        )}
      </div>
      <div className='flex items-center gap-4'>
        {hasPermission('financial', 'sales') && <div>{formatCurrency(value?.cost || customization.cost)}</div>}
        <NumberInput
          className='mr-2'
          onChange={(value) => {
            setCustomizations((prev) =>
              prev.map((custom) =>
                custom.id === customization.id
                  ? {
                      ...custom,
                      cost: {...custom.cost, amount: value * custom.unitCost.amount},
                      quantity: value,
                    }
                  : custom,
              ),
            );
          }}
          value={value?.quantity || customization.quantity || 1}
        />
      </div>
    </div>
  );
};

type OptionButtonProps = {
  customization: CustomizationDefinition;
  option: InventoryItemOption;
  setAccordion: Dispatch<SetStateAction<string[]>>;
  setCustomizations: Dispatch<SetStateAction<CustomizationValue[]>>;
};

const CustomizationOptionButton: FC<OptionButtonProps> = ({customization, option, setAccordion, setCustomizations}) => {
  const t = useTranslations();
  return (
    <Button
      className='flex h-auto w-full items-center justify-between border-b px-0 py-2 last-of-type:border-b-0'
      onClick={() => {
        setAccordion((prev) => prev.filter((value) => value !== customization.options?.[0]?.id || ''));
        setCustomizations((prev) =>
          prev.map((custom) =>
            custom.parentId === customization.id
              ? {
                  code: option.code,
                  cost: option.cost,
                  id: option.id,
                  name: option.name,
                  parentId: customization.id,
                  quantity: customization.quantity || 1,
                  unitCost: {
                    ...option.cost,
                    amount: option.cost.amount / (customization.quantity || 1),
                  },
                }
              : custom,
          ),
        );
      }}
      variant='none'
    >
      <div className='inline-flex flex-col items-start'>
        <div className='line-clamp-1 text-left'>{option?.name}</div>
        <div className='text-xs text-border-foreground'>
          {t('sku')}: {option?.code}
        </div>
      </div>
      <div>{formatCurrency(option.cost)}</div>
    </Button>
  );
};

type Props = {
  index: number;
  onClose: () => void;
  onSave: () => void;
};

const SaleDetailsItemsCustomizeSheet: FC<Props> = ({index, onClose, onSave}) => {
  const t = useTranslations();
  const {setValue, watch} = useFormContext<SaleOrder>();
  const [accordion, setAccordion] = useState<string[]>([]);
  const [customizations, setCustomizations] = useState<CustomizationValue[]>([]);
  const {isLoading, item} = useItem(watch(`items.${index}.productId`) || undefined);
  const defaultCurrency = useAtomValue(defaultCurrencyAtom);
  const {hasPermission} = useHasPermission();

  const availableCustomizations = useMemo(
    () =>
      isLoading
        ? []
        : (item?.requiredMaterials || []).reduce((acc, material) => {
            if (!material.configurableWithOptions && !material.optional) return acc;
            const firstOption = material.options[0];

            return [
              ...acc,
              {
                code: firstOption.code,
                cost: firstOption.cost,
                id: firstOption.id,
                name: firstOption.name,
                optional: material.optional,
                options: material.options,
                quantity: material.quantity,
              },
            ];
          }, [] as CustomizationDefinition[]),
    [isLoading, item?.requiredMaterials],
  );

  useEffect(() => {
    if (isLoading) return;

    const currentCustomizations = watch(`items.${index}.customizations`);

    const newCustomizations = item?.requiredMaterials?.reduce((acc, material) => {
      if (!material.configurableWithOptions && !material.optional) return acc;
      const foundOption = material.options?.find((option) =>
        currentCustomizations?.find((customization) => customization.materialId === option.id),
      );

      const firstOption = material.options[0];

      if (!foundOption)
        return !material.optional
          ? [
              ...acc,
              {
                code: firstOption.code,
                cost: firstOption.cost,
                id: firstOption.id,
                name: firstOption.name,
                parentId: firstOption.id,
                quantity: material.quantity || 1,
                unitCost: {
                  ...firstOption.cost,
                  amount: firstOption.cost.amount / (material.quantity || 1),
                },
              },
            ]
          : acc;

      const foundCustomization = currentCustomizations?.find(
        (customization) => customization.materialId === foundOption.id,
      );

      return [
        ...acc,
        foundOption
          ? {
              code: foundOption.code,
              cost: foundOption.cost,
              id: foundOption.id,
              name: foundOption.name,
              parentId: firstOption.id,
              quantity: foundCustomization?.quantity || material.quantity || 1,
              unitCost: {
                ...foundOption.cost,
                amount: foundOption.cost.amount / (foundCustomization?.quantity || material.quantity || 1),
              },
            }
          : {
              code: firstOption.code,
              cost: firstOption.cost,
              id: firstOption.id,
              name: firstOption.name,
              parentId: firstOption.id,
              quantity: material.quantity || 1,
              unitCost: {
                ...firstOption.cost,
                amount: firstOption.cost.amount / (material.quantity || 1),
              },
            },
      ];
    }, [] as CustomizationValue[]);

    setCustomizations(newCustomizations || []);
  }, [index, isLoading, item?.requiredMaterials, watch]);

  const initialCostAmount = useMemo(
    () =>
      sumBy(item.requiredMaterials, (material) =>
        !material.optional && !material.configurableWithOptions ? material.options?.[0]?.cost?.amount || 0 : 0,
      ) +
      ((item.estimatedEmployeeAndWorkstationCost?.amount || 0) + (item.estimatedProductionOverheadCost?.amount || 0)),
    [
      item.estimatedEmployeeAndWorkstationCost?.amount,
      item.estimatedProductionOverheadCost?.amount,
      item.requiredMaterials,
    ],
  );

  if (isLoading) return null;

  const requiredCustomizations = availableCustomizations.filter((customization) => !customization.optional);
  const optionalCustomizations = availableCustomizations.filter(
    (customization) => customization.optional && !customizations.find((custom) => custom.parentId === customization.id),
  );
  const selectedOptionalCustomizations = availableCustomizations
    .filter(
      (customization) =>
        customization.optional && customizations.find((custom) => custom.parentId === customization.id),
    )
    .sort((a, b) => {
      const indexA = customizations.findIndex((custom) => custom.parentId === a.id);
      const indexB = customizations.findIndex((custom) => custom.parentId === b.id);
      return indexA - indexB;
    });

  return (
    <Sheet defaultOpen={true} onOpenChange={() => setTimeout(onClose, 500)}>
      <SheetPage side='right' size='xl'>
        <SheetTitle className='text-2xl'>
          {t('configure')} {item.name}
        </SheetTitle>
        <SheetContent className='flex flex-col gap-4 px-6'>
          <Accordion className='flex flex-col gap-4' onValueChange={setAccordion} type='multiple' value={accordion}>
            {requiredCustomizations.map((customization, customizationIndex) => (
              <AccordionItem
                className='w-full'
                key={`${JSON.stringify(customization.options)}-${customizationIndex}`}
                value={customization.options?.[0]?.id || ''}
              >
                <AccordionTrigger className='border-b-2 py-1 hover:bg-white'>
                  <CustomizationButton
                    customization={customization}
                    setCustomizations={setCustomizations}
                    value={customizations.find((custom) => custom.parentId === customization.id)}
                  />
                </AccordionTrigger>
                <AccordionContent className='mt-2 rounded-lg border-2 px-6 py-4'>
                  {customization.options?.map((option) =>
                    option.id !== customizations.find((custom) => custom.parentId === customization.id)?.id ? (
                      <CustomizationOptionButton
                        customization={customization}
                        key={option.id}
                        option={option}
                        setAccordion={setAccordion}
                        setCustomizations={setCustomizations}
                      />
                    ) : null,
                  )}
                </AccordionContent>
              </AccordionItem>
            ))}
            {selectedOptionalCustomizations.map((customization, customizationIndex) =>
              customization.options.length > 1 ? (
                <AccordionItem
                  className='w-full'
                  key={`${JSON.stringify(customization.options)}-${customizationIndex}`}
                  value={customization.options?.[0]?.id || ''}
                >
                  <AccordionTrigger className='border-b-2 py-1 hover:bg-white'>
                    <CustomizationButton
                      customization={customization}
                      setCustomizations={setCustomizations}
                      value={customizations.find((custom) => custom.parentId === customization.id)}
                    />
                  </AccordionTrigger>
                  <AccordionContent className='mt-2 rounded-lg border-2 px-6 py-4'>
                    {customization.options?.map((option) => (
                      <CustomizationOptionButton
                        customization={customization}
                        key={option.id}
                        option={option}
                        setAccordion={setAccordion}
                        setCustomizations={setCustomizations}
                      />
                    ))}
                  </AccordionContent>
                </AccordionItem>
              ) : (
                <div className='border-b-2 py-1 pr-5 hover:bg-white' key={`${customization.id}-${customizationIndex}`}>
                  <CustomizationButton
                    customization={customization}
                    setCustomizations={setCustomizations}
                    value={customizations.find((custom) => custom.parentId === customization.id)}
                  />
                </div>
              ),
            )}
          </Accordion>
          {optionalCustomizations.length > 0 && <div className='text-lg font-semibold'>{t('optional items')}</div>}
          {optionalCustomizations.map((customization, customizationIndex) => (
            <div
              className='flex items-center justify-between border-b pb-4 last-of-type:border-b-0'
              key={`${customization.id}-${customizationIndex}`}
            >
              <div className='flex items-center gap-4'>
                <Button
                  className='size-8 rounded-full'
                  onClick={() =>
                    setCustomizations((prev) => [
                      ...prev,
                      {
                        code: customization.code,
                        cost: customization.cost,
                        id: customization.id,
                        name: customization.name,
                        parentId: customization.id,
                        quantity: customization.quantity || 1,
                        unitCost: {
                          ...customization.cost,
                          amount: customization.cost.amount / (customization.quantity || 1),
                        },
                      },
                    ])
                  }
                  size='icon'
                >
                  <PlusIcon />
                </Button>
                <div className='inline-flex flex-col items-start'>
                  <div className='line-clamp-1 text-left'>
                    {customization.name} ({customization.quantity})
                  </div>
                  <div className='text-xs text-border-foreground'>
                    {t('sku')}: {customization.code}
                  </div>
                </div>
              </div>
              <div>{formatCurrency(customization.cost)}</div>
            </div>
          ))}
        </SheetContent>
        <SheetFooter className='px-6'>
          <div className='inline-flex items-center justify-between gap-4'>
            <div className='flex items-center gap-4'>
              {hasPermission('financial', 'sales') && (
                <>
                  <div className='flex flex-col'>
                    <div className='text-xs font-medium uppercase text-border-foreground'>{t('production cost')}</div>
                    <div className='font-medium'>
                      {formatCurrency({
                        amount:
                          initialCostAmount + sumBy(customizations, (customization) => customization.cost?.amount || 0),
                        currency: item.sellPrice?.currency || defaultCurrency,
                      })}
                    </div>
                  </div>
                  <div className='flex flex-col'>
                    <div className='text-xs font-medium uppercase text-border-foreground'>{t('total cost')}</div>
                    <div className='font-medium'>
                      {formatCurrency({
                        amount:
                          initialCostAmount +
                          (item.estimatedAdministrativeCost?.amount || 0) +
                          sumBy(customizations, (customization) => customization.cost?.amount || 0),
                        currency: item.sellPrice?.currency || defaultCurrency,
                      })}
                    </div>
                  </div>
                </>
              )}
            </div>
            <div className='flex gap-2'>
              {(watch(`items.${index}.customizations`)?.length ?? 0) > 0 && (
                <SheetClose asChild>
                  <Button
                    onClick={() => {
                      setValue(`items.${index}.customizations`, null);
                      setValue(`items.${index}.laborCosts.amount`, 0);
                      setValue(`items.${index}.materialCosts.amount`, initialCostAmount);
                      onSave();
                    }}
                    variant='secondary'
                  >
                    {t('remove configuration')}
                  </Button>
                </SheetClose>
              )}
              <SheetClose asChild>
                <Button
                  onClick={() => {
                    setValue(
                      `items.${index}.customizations`,
                      customizations.map((customization) => ({
                        materialId: customization.id,
                        quantity: customization.quantity || 1,
                      })),
                    );
                    setValue(`items.${index}.laborCosts.amount`, 0);
                    setValue(
                      `items.${index}.materialCosts.amount`,
                      initialCostAmount + sumBy(customizations, (customization) => customization.cost?.amount || 0),
                    );
                    onSave();
                  }}
                >
                  {t('save configuration')}
                </Button>
              </SheetClose>
            </div>
          </div>
        </SheetFooter>
      </SheetPage>
    </Sheet>
  );
};

export default SaleDetailsItemsCustomizeSheet;
