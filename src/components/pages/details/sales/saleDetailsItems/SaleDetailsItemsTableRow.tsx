import {FC, useCallback, useState} from 'react';

import Jo<PERSON> from 'joi';
import {useAtomValue} from 'jotai';
import {
  BrushIcon,
  CheckIcon,
  ClockIcon,
  CogIcon,
  EllipsisVerticalIcon,
  ShoppingCartIcon,
  SlidersVerticalIcon,
  TagIcon,
  Trash2Icon,
  XIcon,
} from 'lucide-react';
import {useTranslations} from 'next-intl';
import {useFieldArray, useFormContext} from 'react-hook-form';

import {Badge} from '@/components/ui/Badge';
import {Button} from '@/components/ui/Button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/DropdownMenu';
import {PopoverInput} from '@/components/ui/Input';
import InventoryItemLink from '@/components/ui/special/InventoryItemLink/InventoryItemLink';
import {ItemHistoryModal} from '@/components/ui/special/ItemHistoryModal';
import {Link} from '@/components/ui/special/Link';
import {WithoutEmpty} from '@/components/ui/special/WithoutEmpty';
import {TableActions, TableCell, TableRow} from '@/components/ui/Table';
import {Tooltip, TooltipContent, TooltipTrigger} from '@/components/ui/Tooltip';
import {useHasPermission} from '@/hooks/helpers/useHasPermission';
import useManufacturingOrderActions from '@/hooks/useManufacturingOrderActions';
import useSaleOrderActions from '@/hooks/useSaleOrderActions';
import {defaultCurrencyAtom, defaultVATAtom} from '@/store/defaults';
import {ManufacturingStatus, SaleItemStatus, SaleStatus, ServicingStatus} from '@/types/global';
import {SaleItem, SaleOrder, Supplier} from '@/types/sales';
import {classes} from '@/utils/common';
import {formatCurrency, formatDate, formatNumber, formatVAT} from '@/utils/format';
import useItemStockActions from 'hooks/useItemStockActions';

import BuySubmenu from './BuySubmenu/BuySubmenu';
import NewCustomManufacturingOrderSheet from './NewCustomManufacturingOrderSheet';

type Props = {
  index: number;
  item: SaleItem;
  onCustomizeClick: () => void;
  suppliers: Supplier[];
};

const SaleDetailsItemsTableRow: FC<Props> = ({index, item, onCustomizeClick, suppliers}) => {
  const t = useTranslations();
  const defaultVAT = useAtomValue(defaultVATAtom);
  const defaultCurrency = useAtomValue(defaultCurrencyAtom);
  const {hasPermission, isLoading} = useHasPermission();
  const {getItemAvailable} = useItemStockActions();
  const {calculateSaleOrderSummary} = useSaleOrderActions();
  const {createLinkedManufacturingOrder} = useManufacturingOrderActions();
  const [showMenu, setShowMenu] = useState(false);
  const [showNew, setShowNew] = useState(false);
  const {
    control,
    formState: {dirtyFields},
    setValue,
    watch,
  } = useFormContext<SaleOrder>();
  const {remove, update} = useFieldArray({control, name: 'items'});

  const renderStatusBadge = useCallback(() => {
    let badgeIcon = <XIcon className='size-4' />;
    let badgeText = t(item.status);
    let badgeVariant: 'error' | 'success' | 'warning' = 'error';

    if (item.status === SaleItemStatus.READY) {
      badgeIcon = <CheckIcon className='size-4' />;
      badgeVariant = 'success';
    } else if (!item.produced && (item.purchaseOrder?.id || item.addedToWishlist)) {
      badgeIcon = <ShoppingCartIcon className='size-4' />;
      badgeText = t('purchase in progress');
      badgeVariant = 'warning';
    } else if (
      item.produced ||
      item.serviceId ||
      (!item.produced && (item.manufacturingOrder?.id || item.servicingOrderDetails?.id))
    ) {
      badgeIcon = <CogIcon className='size-4' />;
      badgeVariant = 'warning';

      if (item.servicingOrderDetails?.id) {
        switch (item.servicingOrderDetails.status) {
          case ServicingStatus.IN_PROGRESS:
            badgeText = t('servicing in progress');
            break;
          case ServicingStatus.SUBMITTED:
            badgeText = t('servicing planned');
            break;
          default:
            badgeIcon = <XIcon className='size-4' />;
            badgeText = t(item.status);
            badgeVariant = 'error';
        }
      } else {
        switch (item.manufacturingOrder?.status) {
          case ManufacturingStatus.CUSTOMIZATION_NEEDED:
            badgeText = t('awaiting customization');
            break;
          case ManufacturingStatus.MANUFACTURING:
            badgeText = t('manufacturing in progress');
            break;
          case ManufacturingStatus.SUBMITTED:
            badgeText = t('manufacturing planned');
            break;
          default:
            badgeIcon = <XIcon className='size-4' />;
            badgeText = t(item.status);
            badgeVariant = 'error';
        }
      }
    }

    const badge = (
      <Badge className={classes('gap-1', item.productId && 'cursor-pointer')} variant={badgeVariant}>
        {badgeIcon} {badgeText}
      </Badge>
    );

    if (item.productId) {
      return <ItemHistoryModal item={{...item, code: item.code || '', id: item.productId}}>{badge}</ItemHistoryModal>;
    }

    return badge;
  }, [item, t]);

  if (isLoading) return null;

  return (
    <>
      {showNew && (
        <NewCustomManufacturingOrderSheet
          onClose={() => {
            setShowNew(false);
          }}
          product={{code: item.code || '', id: item.productId || '', name: item.name}}
          quantity={item.quantity || 1}
          saleId={watch('id')}
        />
      )}
      <TableRow>
        <TableCell>
          {item.productId && (
            <InventoryItemLink
              configured={
                !!item.productId && item.customizable && !!item.customizations && item.customizations.length > 0
              }
              customized={item.manufacturingOrder?.customProduct}
              item={{...item, code: item.code || '', id: item.productId}}
            />
          )}
          {item.serviceId && (
            <PopoverInput
              defaultValue={item.name}
              label={t('name')}
              onChange={(value) => {
                update(index, {
                  ...item,
                  name: value,
                });
              }}
              validation={Joi.string().required()}
            >
              {item.name}
            </PopoverInput>
          )}
        </TableCell>
        <TableCell className='text-right'>
          <PopoverInput
            className='justify-end'
            defaultValue={(item.quantity || 0)?.toString()}
            disabled={!hasPermission('financial', 'sales')}
            label={t('quantity')}
            onChange={async (value) => {
              const discountAmount = item.price.amount * Number(value) * (item.discount / 100);
              const taxAmount = (item.price.amount * Number(value) - discountAmount) * (item.vatRate ?? defaultVAT);
              const totalAmount = item.price.amount * Number(value) - discountAmount;

              update(index, {
                ...item,
                discountAmount: {
                  ...item.discountAmount,
                  amount: discountAmount,
                },
                quantity: Number(value),
                taxAmount: {...item.taxAmount, amount: taxAmount},
                totalAmount: {...item.totalAmount, amount: totalAmount},
              });

              calculateSaleOrderSummary(watch('items'), setValue);

              if (item.productId) {
                const available = await getItemAvailable(item.productId, Number(value), {
                  id: watch('id'),
                  type: 'sales',
                });

                setValue(`items.${index}.status`, available ? SaleItemStatus.READY : SaleItemStatus.NEED_SUPPLY);
              }
            }}
            type='number'
            validation={Joi.number().required().positive()}
          >
            {formatNumber(item.quantity || 0)} {t(`unit.name.${item.measurementUnit.name}` as any)}
          </PopoverInput>
        </TableCell>
        {hasPermission('financial', 'sales') && (
          <>
            <TableCell className='text-right'>
              {item.productId && (
                <div className='flex flex-col'>
                  <div className='flex items-center justify-between gap-1'>
                    {item.produced && (item.laborCosts?.amount || 0) + (item.materialCosts?.amount || 0) > 0 && (
                      <>
                        <Tooltip>
                          <TooltipTrigger>
                            <CogIcon className='size-4' />
                          </TooltipTrigger>
                          <TooltipContent>{t('production cost')}</TooltipContent>
                        </Tooltip>

                        <div>
                          {formatCurrency({
                            amount: item.laborCosts?.amount + item.materialCosts?.amount,
                            currency: item.laborCosts?.currency || item.materialCosts?.currency || defaultCurrency,
                          })}
                        </div>
                      </>
                    )}
                    {!item.produced && (item.lastPurchase?.price?.amount || 0 > 0) && (
                      <>
                        <Tooltip>
                          <TooltipTrigger>
                            <ShoppingCartIcon className='size-4' />
                          </TooltipTrigger>
                          <TooltipContent>{t('purchase cost')}</TooltipContent>
                        </Tooltip>

                        {formatCurrency(item.lastPurchase?.price)}
                      </>
                    )}
                  </div>
                  <div className='flex items-center justify-between gap-1'>
                    {item.produced &&
                      ((item.laborCosts?.amount || 0) + (item.materialCosts?.amount || 0) > 0 ||
                        (item.administrativeOverheadCosts?.amount || 0) > 0) && (
                        <>
                          <Tooltip>
                            <TooltipTrigger>
                              <TagIcon className='size-4' />
                            </TooltipTrigger>
                            <TooltipContent>{t('total cost')}</TooltipContent>
                          </Tooltip>

                          <div>
                            {formatCurrency({
                              amount:
                                item.laborCosts?.amount +
                                item.materialCosts?.amount +
                                item.administrativeOverheadCosts?.amount,
                              currency:
                                item.laborCosts?.currency ||
                                item.materialCosts?.currency ||
                                item.administrativeOverheadCosts?.currency ||
                                defaultCurrency,
                            })}
                          </div>
                        </>
                      )}
                  </div>
                </div>
              )}
              {!item.productId && item.laborCosts?.amount && (
                <div className='flex items-center justify-between gap-1'>
                  <Tooltip>
                    <TooltipTrigger>
                      <ClockIcon className='size-4' />
                    </TooltipTrigger>
                    <TooltipContent>{t('cost per hour')}</TooltipContent>
                  </Tooltip>

                  <div>{formatCurrency(item.laborCosts)}</div>
                </div>
              )}
            </TableCell>
            <TableCell className='text-right'>
              {item.productId &&
                item.produced &&
                ((item.laborCosts?.amount || 0) + (item.materialCosts?.amount || 0) > 0 ||
                  (item.administrativeOverheadCosts?.amount || 0) > 0) && (
                  <div className='flex flex-col'>
                    <PopoverInput
                      className='justify-end'
                      defaultValue={(
                        ((item.price.amount - (item.laborCosts?.amount + item.materialCosts?.amount)) /
                          (item.laborCosts?.amount + item.materialCosts?.amount)) *
                        100
                      ).toFixed(2)}
                      label={t('markup')}
                      onChange={(value) => {
                        const priceAmount =
                          item.laborCosts?.amount +
                          item.materialCosts?.amount +
                          ((item.laborCosts?.amount + item.materialCosts?.amount) * Number(value)) / 100;
                        const discountAmount = priceAmount * (item.quantity || 0) * (item.discount / 100);
                        const taxAmount =
                          (priceAmount * (item.quantity || 0) - discountAmount) * (item.vatRate ?? defaultVAT);
                        const totalAmount = priceAmount * (item.quantity || 0) - discountAmount;

                        update(index, {
                          ...item,
                          discountAmount: {
                            ...item.discountAmount,
                            amount: discountAmount,
                          },
                          price: {...item.price, amount: priceAmount},
                          taxAmount: {...item.taxAmount, amount: taxAmount},
                          totalAmount: {
                            ...item.totalAmount,
                            amount: totalAmount,
                          },
                        });

                        calculateSaleOrderSummary(watch('items'), setValue);
                      }}
                      type='number'
                      validation={Joi.number().required()}
                    >
                      {(
                        ((item.price.amount - (item.laborCosts?.amount + item.materialCosts?.amount)) /
                          (item.laborCosts?.amount + item.materialCosts?.amount)) *
                        100
                      ).toFixed(2)}
                      {' %'}
                    </PopoverInput>
                    <PopoverInput
                      className='justify-end'
                      defaultValue={(
                        ((item.price.amount -
                          (item.laborCosts?.amount +
                            item.materialCosts?.amount +
                            item.administrativeOverheadCosts?.amount)) /
                          (item.laborCosts?.amount +
                            item.materialCosts?.amount +
                            item.administrativeOverheadCosts?.amount)) *
                        100
                      ).toFixed(2)}
                      label={t('markup')}
                      onChange={(value) => {
                        const priceAmount =
                          item.laborCosts?.amount +
                          item.materialCosts?.amount +
                          item.administrativeOverheadCosts?.amount +
                          ((item.laborCosts?.amount +
                            item.materialCosts?.amount +
                            item.administrativeOverheadCosts?.amount) *
                            Number(value)) /
                            100;
                        const discountAmount = priceAmount * (item.quantity || 0) * (item.discount / 100);
                        const taxAmount =
                          (priceAmount * (item.quantity || 0) - discountAmount) * (item.vatRate ?? defaultVAT);
                        const totalAmount = priceAmount * (item.quantity || 0) - discountAmount;

                        update(index, {
                          ...item,
                          discountAmount: {
                            ...item.discountAmount,
                            amount: discountAmount,
                          },
                          price: {...item.price, amount: priceAmount},
                          taxAmount: {...item.taxAmount, amount: taxAmount},
                          totalAmount: {
                            ...item.totalAmount,
                            amount: totalAmount,
                          },
                        });

                        calculateSaleOrderSummary(watch('items'), setValue);
                      }}
                      type='number'
                      validation={Joi.number().required()}
                    >
                      {(
                        ((item.price.amount -
                          (item.laborCosts?.amount +
                            item.materialCosts?.amount +
                            item.administrativeOverheadCosts?.amount)) /
                          (item.laborCosts?.amount +
                            item.materialCosts?.amount +
                            item.administrativeOverheadCosts?.amount)) *
                        100
                      ).toFixed(2)}
                      {' %'}
                    </PopoverInput>
                  </div>
                )}
              {item.productId && !item.produced && (item.lastPurchase?.price?.amount || 0) > 0 && (
                <PopoverInput
                  className='justify-end'
                  defaultValue={(
                    ((item.price.amount - (item.lastPurchase?.price?.amount || 0)) /
                      (item.lastPurchase?.price?.amount || 0)) *
                    100
                  ).toFixed(2)}
                  label={t('markup')}
                  onChange={(value) => {
                    const priceAmount =
                      (item.lastPurchase?.price?.amount || 0) +
                      ((item.lastPurchase?.price?.amount || 0) * Number(value)) / 100;
                    const discountAmount = priceAmount * (item.quantity || 0) * (item.discount / 100);
                    const taxAmount =
                      (priceAmount * (item.quantity || 0) - discountAmount) * (item.vatRate ?? defaultVAT);
                    const totalAmount = priceAmount * (item.quantity || 0) - discountAmount;

                    update(index, {
                      ...item,
                      discountAmount: {
                        ...item.discountAmount,
                        amount: discountAmount,
                      },
                      price: {...item.price, amount: priceAmount},
                      taxAmount: {...item.taxAmount, amount: taxAmount},
                      totalAmount: {
                        ...item.totalAmount,
                        amount: totalAmount,
                      },
                    });

                    calculateSaleOrderSummary(watch('items'), setValue);
                  }}
                  type='number'
                  validation={Joi.number().required()}
                >
                  {(
                    ((item.price.amount - (item.lastPurchase?.price?.amount || 0)) /
                      (item.lastPurchase?.price?.amount || 0)) *
                    100
                  ).toFixed(2)}
                  {' %'}
                </PopoverInput>
              )}
            </TableCell>
            <TableCell className='text-right'>
              <PopoverInput
                className='justify-end'
                defaultValue={(item.price.amount || 0)?.toString()}
                error={item.price.amount <= 0}
                label={`${t('unit price')} (${item.price.currency})`}
                onChange={(value) => {
                  const discountAmount = Number(value) * (item.quantity || 0) * (item.discount / 100);
                  const taxAmount =
                    (Number(value) * (item.quantity || 0) - discountAmount) * (item.vatRate ?? defaultVAT);
                  const totalAmount = Number(value) * (item.quantity || 0) - discountAmount;

                  update(index, {
                    ...item,
                    discountAmount: {
                      ...item.discountAmount,
                      amount: discountAmount,
                    },
                    price: {...item.price, amount: Number(value)},
                    taxAmount: {...item.taxAmount, amount: taxAmount},
                    totalAmount: {
                      ...item.totalAmount,
                      amount: totalAmount,
                    },
                  });

                  calculateSaleOrderSummary(watch('items'), setValue);
                }}
                type='number'
                validation={Joi.number().required().positive()}
              >
                {formatCurrency(item.price)}
              </PopoverInput>
            </TableCell>
            <TableCell className='text-right'>
              <PopoverInput
                className='justify-end'
                defaultValue={(item.discount || 0)?.toString()}
                label={`${t('discount')} (%)`}
                onChange={(value) => {
                  const discountAmount = item.price.amount * (item.quantity || 0) * (Number(value) / 100);
                  const taxAmount =
                    (item.price.amount * (item.quantity || 0) - discountAmount) * (item.vatRate ?? defaultVAT);
                  const totalAmount = item.price.amount * (item.quantity || 0) - discountAmount;

                  update(index, {
                    ...item,
                    discount: Number(value),
                    discountAmount: {
                      ...item.discountAmount,
                      amount: discountAmount,
                    },
                    taxAmount: {...item.taxAmount, amount: taxAmount},
                    totalAmount: {
                      ...item.totalAmount,
                      amount: totalAmount,
                    },
                  });

                  calculateSaleOrderSummary(watch('items'), setValue);
                }}
                type='number'
                validation={Joi.number().required().positive().allow(0).max(100)}
              >
                {formatNumber(item.discount)} %
              </PopoverInput>
            </TableCell>
            <TableCell className='text-right'>
              <PopoverInput
                className='justify-end'
                defaultValue={(item.discountAmount.amount || 0)?.toString()}
                label={t('discount amount')}
                onChange={(value) => {
                  const discount = Number((Number(value) / ((item.quantity || 0) * item.price.amount)) * 100);
                  const taxAmount =
                    (item.price.amount * (item.quantity || 0) - Number(value)) * (item.vatRate ?? defaultVAT);
                  const totalAmount = item.price.amount * (item.quantity || 0) - Number(value);

                  update(index, {
                    ...item,
                    discount,
                    discountAmount: {
                      ...item.discountAmount,
                      amount: Number(value),
                    },
                    taxAmount: {...item.taxAmount, amount: taxAmount},
                    totalAmount: {...item.totalAmount, amount: totalAmount},
                  });

                  calculateSaleOrderSummary(watch('items'), setValue);
                }}
                type='number'
                validation={Joi.number()
                  .required()
                  .positive()
                  .allow(0)
                  .max(item.price.amount * (item.quantity || 0))}
              >
                {formatCurrency(item.discountAmount)}
              </PopoverInput>
            </TableCell>
            <TableCell className='text-right'>{formatCurrency(item.totalAmount)}</TableCell>
            <TableCell className='text-right'>{formatVAT(item.vatRate, defaultVAT)}%</TableCell>
            <TableCell className='text-right'>{formatCurrency(item.taxAmount)}</TableCell>
          </>
        )}
        <TableCell className='text-right'>{renderStatusBadge()}</TableCell>
        <TableCell className='text-right'>
          {(() => {
            const isPurchaseOrder = !!item.purchaseOrder?.id;
            const isServicingOrder = !!item.servicingOrderDetails?.id;

            const id = item.purchaseOrder?.id ?? item.servicingOrderDetails?.id ?? item.manufacturingOrder?.id;
            const name = isPurchaseOrder
              ? (item.purchaseOrder?.name ?? '')
              : isServicingOrder
                ? (item.servicingOrderDetails?.number ?? '')
                : (item.manufacturingOrder?.number ?? '');

            const displayValue = item.expectedBy ? formatDate(item.expectedBy) : name;

            const hasUpdatePermission = isPurchaseOrder
              ? hasPermission('update', 'purchases')
              : hasPermission('update', 'manufacturing');

            if (!hasUpdatePermission) {
              return <WithoutEmpty value={id}>{displayValue}</WithoutEmpty>;
            }

            const path = isPurchaseOrder
              ? 'purchases/orders'
              : `manufacturing/${isServicingOrder ? 'services' : 'orders'}`;

            return (
              <WithoutEmpty value={id}>
                <Link className='whitespace-nowrap hover:underline' href={`/${path}/${id}`}>
                  {displayValue}
                </Link>
              </WithoutEmpty>
            );
          })()}
        </TableCell>

        <TableActions>
          <DropdownMenu onOpenChange={(open) => setShowMenu(open)} open={showMenu}>
            <DropdownMenuTrigger asChild>
              <Button className='px-1.5' variant='ghost'>
                <EllipsisVerticalIcon />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align='end'>
              {watch('id') && !item.manufacturingOrder && (
                <>
                  {item.status === SaleItemStatus.NEED_SUPPLY && (
                    <>
                      {[SaleStatus.PROCESSING, SaleStatus.READY_TO_SHIP, SaleStatus.SUBMITTED].includes(
                        watch('status'),
                      ) && (
                        <>
                          {(item.produced || item.serviceId) &&
                            hasPermission('update', 'manufacturing') &&
                            !item.servicingOrderDetails?.id && (
                              <DropdownMenuItem
                                disabled={Object.keys(dirtyFields).length > 0}
                                onSelect={() =>
                                  createLinkedManufacturingOrder(
                                    {
                                      ...(item.serviceId
                                        ? {serviceId: item.serviceId}
                                        : {productId: item.productId || ''}),
                                      quantity: item.quantity || 1,
                                    },
                                    watch('id'),
                                  )
                                }
                              >
                                <CogIcon className='size-5' /> {t('command execution')}
                              </DropdownMenuItem>
                            )}
                          {item.productId && !item.produced && hasPermission('update', 'purchases') && (
                            <BuySubmenu
                              disabled={Object.keys(dirtyFields).length > 0}
                              item={item}
                              saleId={watch('id')}
                              suppliers={suppliers}
                            />
                          )}
                        </>
                      )}
                      {hasPermission('update', 'manufacturing') &&
                        item.productId &&
                        item.customizable &&
                        ![SaleStatus.DELIVERED, SaleStatus.IN_QUOTATION, SaleStatus.SHIPPING].includes(
                          watch('status'),
                        ) && (
                          <DropdownMenuItem onSelect={onCustomizeClick}>
                            <SlidersVerticalIcon className='size-5' /> {t('configure')}
                          </DropdownMenuItem>
                        )}
                    </>
                  )}
                  {hasPermission('update', 'manufacturing') &&
                    item.productId &&
                    !(item.customizable && !!item.customizations && item.customizations.length > 0) && (
                      <DropdownMenuItem
                        disabled={Object.keys(dirtyFields).length > 0}
                        onSelect={() => setShowNew(true)}
                      >
                        <BrushIcon className='size-5' /> {t('customize')}
                      </DropdownMenuItem>
                    )}
                  <DropdownMenuSeparator />
                </>
              )}

              <DropdownMenuItem onSelect={() => remove(index)}>
                <Trash2Icon className='size-5 text-red' /> {t('delete')}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </TableActions>
      </TableRow>
    </>
  );
};

export default SaleDetailsItemsTableRow;
