import {FC, KeyboardEvent, useCallback, useMemo, useState} from 'react';

import {useTranslations} from 'next-intl';

import {DropdownMenuItem} from '@/components/ui/DropdownMenu';
import {SearchInput} from '@/components/ui/special/SearchInput';
import {Supplier} from '@/types/sales';

type Props = {
  onSelect: (supplierId: string) => void;
  suppliers: Supplier[];
};

const BuySuppliersSubmenu: FC<Props> = ({onSelect, suppliers}) => {
  const t = useTranslations();
  const [search, setSearch] = useState('');

  const filteredSuppliers = useMemo(
    () =>
      search ? suppliers.filter((supplier) => supplier.name.toLowerCase().includes(search.toLowerCase())) : suppliers,
    [search, suppliers],
  );

  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    event.stopPropagation();
  }, []);

  return (
    <>
      <DropdownMenuItem asChild className='cursor-text'>
        <SearchInput
          autoFocus
          minLength={0}
          onChange={({target: {value}}) => setSearch(value)}
          onClick={(event) => event.preventDefault()}
          onKeyDown={handleKeyDown}
          placeholder={t('search supplier')}
        />
      </DropdownMenuItem>
      {filteredSuppliers.map((supplier, index) => (
        <DropdownMenuItem key={`${supplier.id}-${index}`} onSelect={() => onSelect(supplier.id)}>
          {supplier.name}
        </DropdownMenuItem>
      ))}
    </>
  );
};

export default BuySuppliersSubmenu;
