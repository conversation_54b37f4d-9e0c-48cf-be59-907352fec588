import {FC, useCallback, useEffect, useState} from 'react';

import axios from 'axios';
import {ShoppingCartIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';
import {useSWRConfig} from 'swr';

import {
  DropdownMenuItem,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
} from '@/components/ui/DropdownMenu';
import {toast} from '@/components/ui/Toast';
import useWishlistActions from '@/hooks/useWishlistActions';
import {Id} from '@/types/global';
import {SaleItem, Supplier} from '@/types/sales';

import BuySuppliersSubmenu from './BuySuppliersSubmenu';

type Props = {
  disabled?: boolean;
  item: SaleItem;
  saleId: string;
  suppliers: Supplier[];
};

const BuySubmenu: FC<Props> = ({disabled, item, saleId, suppliers}) => {
  const t = useTranslations();
  const [lastSuppliers, setLastSuppliers] = useState<Id[]>([]);
  const {createSalesOrderWishlistItem} = useWishlistActions();
  const {mutate: globalMutate} = useSWRConfig();

  useEffect(() => {
    axios
      .get(`/api/suppliers/last-suppliers-for?materialGoodId=${item.productId}`)
      .then((res) => setLastSuppliers(res.data))
      .catch(() => alert(t('an error occurred while loading name', {name: t('suffixed.supplier.end')})));
  }, [item.productId, t]);

  const handleBuy = useCallback(
    (supplierId: string) => {
      createSalesOrderWishlistItem(
        {
          materialGood: {code: item.code || '', id: item.productId || '', name: item.name},
          quantity: item.quantity || 1,
          supplier: {id: supplierId, name: ''},
        },
        saleId,
      )
        .then(() => {
          toast.success(t('material name has been added to the wishlist', {name: item.name}));
          globalMutate(
            (key) => Array.isArray(key) && (key[0] === 'wishlists' || (key[0] === 'sale' && key[1] === saleId)),
          );
        })
        .catch(() => toast.error(t('material name has failed to add to the wishlist', {name: item.name})));
    },
    [createSalesOrderWishlistItem, globalMutate, item.code, item.name, item.productId, item.quantity, saleId, t],
  );

  return (
    <DropdownMenuSub>
      <DropdownMenuSubTrigger chevronPosition='left' disabled={disabled}>
        <ShoppingCartIcon className='size-5' /> {t('buy')}
      </DropdownMenuSubTrigger>
      <DropdownMenuSubContent>
        {lastSuppliers.map((supplier, index) => (
          <DropdownMenuItem key={`${supplier.id}-${index}`} onSelect={() => handleBuy(supplier.id)}>
            {supplier.name}
          </DropdownMenuItem>
        ))}
        {lastSuppliers.length == 0 && <BuySuppliersSubmenu onSelect={handleBuy} suppliers={suppliers} />}
        {lastSuppliers.length > 0 && (
          <DropdownMenuSub>
            <DropdownMenuSubTrigger chevronPosition='left' className='pl-6'>
              {t('other supplier')}
            </DropdownMenuSubTrigger>
            <DropdownMenuSubContent>
              <BuySuppliersSubmenu onSelect={handleBuy} suppliers={suppliers} />
            </DropdownMenuSubContent>
          </DropdownMenuSub>
        )}
      </DropdownMenuSubContent>
    </DropdownMenuSub>
  );
};

export default BuySubmenu;
