import {FC, useState} from 'react';

import {useAtomValue} from 'jotai';
import {useTranslations} from 'next-intl';
import {useFormContext} from 'react-hook-form';

import {detailsDataShownAtom} from '@/components/pages/details/detailsStore';
import {
  Table,
  TableBody,
  TableContainer,
  TableHead,
  TableHeadActions,
  TableHeader,
  TableRow,
} from '@/components/ui/Table';
import {useHasPermission} from '@/hooks/helpers/useHasPermission';
import useHeight from '@/hooks/helpers/useHeight';
import useSuppliers from '@/hooks/useSuppliers';
import {SaleOrder} from '@/types/sales';

import SaleDetailsItemsCustomizeSheet from './SaleDetailsItemsCustomizeSheet';
import SaleDetailsItemsTableRow from './SaleDetailsItemsTableRow';

type Props = {
  saveOrder: () => void;
};

const SaleDetailsItemsTable: FC<Props> = ({saveOrder}) => {
  const {hasPermission, isLoading} = useHasPermission();
  const {suppliers} = useSuppliers();
  const [customizeIndex, setCustomizeIndex] = useState<number>();
  const {watch} = useFormContext<SaleOrder>();
  const detailsDataShown = useAtomValue(detailsDataShownAtom(watch('id')));
  const {elementRef} = useHeight({dependencies: [detailsDataShown]});
  const t = useTranslations();

  return (
    <>
      <TableContainer ref={elementRef}>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>{t('name')}</TableHead>
              <TableHead className='text-right'>{t('quantity')}</TableHead>
              {hasPermission('financial', 'sales') && (
                <>
                  <TableHead className='text-right'>{t('cost')}</TableHead>
                  <TableHead className='text-right'>{t('markup')}</TableHead>
                  <TableHead className='text-right'>{t('unit price')}</TableHead>
                  <TableHead className='text-right'>{t('discount')}</TableHead>
                  <TableHead className='text-right'>{t('discount amount')}</TableHead>
                  <TableHead className='text-right'>{t('net value')}</TableHead>
                  <TableHead className='text-right'>{t('vat rate')}</TableHead>
                  <TableHead className='text-right'>{t('vat value')}</TableHead>
                </>
              )}
              <TableHead className='text-right'>{t('status')}</TableHead>
              <TableHead className='text-right'>{t('expected by')}</TableHead>
              <TableHeadActions />
            </TableRow>
          </TableHeader>
          <TableBody className='overflow-y-hidden' isValidating={!watch() || isLoading}>
            {watch('items')?.map((item, index) => (
              <SaleDetailsItemsTableRow
                index={index}
                item={item}
                key={`${item.name}-${index}`}
                onCustomizeClick={() => setCustomizeIndex(index)}
                suppliers={suppliers}
              />
            ))}
          </TableBody>
        </Table>
      </TableContainer>
      {customizeIndex !== undefined && (
        <SaleDetailsItemsCustomizeSheet
          index={customizeIndex}
          onClose={() => setCustomizeIndex(undefined)}
          onSave={saveOrder}
        />
      )}
    </>
  );
};

export default SaleDetailsItemsTable;
