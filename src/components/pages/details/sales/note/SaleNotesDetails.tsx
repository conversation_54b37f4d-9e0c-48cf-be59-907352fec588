import {FC, useCallback} from 'react';

import {ArrowLeftIcon, CloudDownloadIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';

import {Button} from '@/components/ui/Button';
import {DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger} from '@/components/ui/DropdownMenu';
import {DocumentPreview} from '@/components/ui/special/DocumentPreview';
import {Page, PageContent, PageHeader, PageHeaderTitle, PageTitle} from '@/components/ui/special/Page';
import useAccompanyingNoteDocument from '@/hooks/documents/useAccompanyingNoteDocument';
import useHeight from '@/hooks/helpers/useHeight';
import {useRouter} from '@/hooks/helpers/useRouter';

type Props = {
  id: string;
  saleId: string;
};

const SaleNotesDetails: FC<Props> = ({id, saleId}) => {
  const {elementRef} = useHeight();
  const {back, push} = useRouter();
  const t = useTranslations();
  const {accompanyingNoteDocument} = useAccompanyingNoteDocument(id);

  const handleDownload = useCallback(
    (mediaType: string) => {
      push(`/api/goods-accompanying-notes/${id}/details?mediaType=${mediaType}`);
    },
    [push, id],
  );

  return (
    <Page>
      <PageTitle>{`${t('goods accompanying note')} - ${t('orders')} - ${t('sales')}`}</PageTitle>
      <PageHeader>
        <PageHeaderTitle className='inline-flex items-center gap-2'>
          <Button onClick={() => back(`/sales/orders/${saleId}`)} size='icon' variant='none'>
            <ArrowLeftIcon />
          </Button>
          {t('order')}
        </PageHeaderTitle>
        <div className='grow' />
        <DropdownMenu>
          <DropdownMenuTrigger>
            <Button>
              <CloudDownloadIcon /> {t('download')}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuItem onClick={() => handleDownload('application/pdf')}>{t('pdf')}</DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleDownload('application/factura-saga%2Bxml')}>
              {t('saga')}
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </PageHeader>
      <PageContent>
        <div className='flex' ref={elementRef}>
          <div className='flex size-full justify-center bg-gray-100 p-4'>
            <DocumentPreview content={accompanyingNoteDocument} />
          </div>
        </div>
      </PageContent>
    </Page>
  );
};

export default SaleNotesDetails;
