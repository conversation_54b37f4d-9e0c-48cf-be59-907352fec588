import {FC, useCallback, useState} from 'react';

import {ArrowLeftIcon, CheckIcon, CloudDownloadIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';

import {Button} from '@/components/ui/Button';
import {Checkbox} from '@/components/ui/Checkbox';
import {DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger} from '@/components/ui/DropdownMenu';
import {Label, WithLabel} from '@/components/ui/Label';
import {DocumentPreview} from '@/components/ui/special/DocumentPreview';
import {Page, PageContent, PageHeader, PageHeaderTitle, PageTitle} from '@/components/ui/special/Page';
import {TextareaAutosize} from '@/components/ui/Textarea';
import useSaleOrderInvoiceDocumentPreview from '@/hooks/documents/useSaleOrderInvoiceDocumentPreview';
import useHeight from '@/hooks/helpers/useHeight';
import {useRouter} from '@/hooks/helpers/useRouter';

import {invoiceHideableDocumentColumns} from '../salesDetailsConstants';

type Props = {
  saleId: string;
};

const SaleInvoicesCreate: FC<Props> = ({saleId}) => {
  const {
    back,
    push,
    query: {isProforma},
    replace,
  } = useRouter();
  const {elementRef} = useHeight();
  const {elementRef: containerRef} = useHeight();
  const [notes, setNotes] = useState('');
  const {createSaleOrderInvoice, renderingDetails, saleInvoiceDocumentPreview, setRenderingDetails} =
    useSaleOrderInvoiceDocumentPreview(saleId, {
      isProforma: !!isProforma,
      notes,
    });
  const t = useTranslations();

  const handleCreateDownload = useCallback(
    (mediaType: string) =>
      createSaleOrderInvoice(saleId, {isProforma: !!isProforma, notes}).then((invoice) => {
        setTimeout(() => push(`/api/invoices/${invoice.id}/details?mediaType=${mediaType}`), 500);
        replace(`/sales/orders/${saleId}/invoice/${invoice.id}${!!isProforma ? '?isProforma=true' : ''}`);
      }),
    [createSaleOrderInvoice, isProforma, notes, push, replace, saleId],
  );

  return (
    <Page>
      <PageTitle>{`${t(!isProforma ? 'invoice' : 'proforma')} - ${t('orders')} - ${t('sales')}`}</PageTitle>
      <PageHeader>
        <PageHeaderTitle className='inline-flex items-center gap-2'>
          <Button onClick={() => back(`/sales/orders/${saleId}`)} size='icon' variant='none'>
            <ArrowLeftIcon />
          </Button>
          {t('order')}
        </PageHeaderTitle>
        <div className='grow' />
        <DropdownMenu>
          <DropdownMenuTrigger>
            <Button variant='secondary'>
              <CloudDownloadIcon />
              {t('create invoice and download')}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuItem onClick={() => handleCreateDownload('application/pdf')}>{t('pdf')}</DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleCreateDownload('application/factura-saga%2Bxml')}>
              {t('saga')}
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
        <Button
          onClick={() => {
            createSaleOrderInvoice(saleId, {
              isProforma: !!isProforma,
              notes,
              renderingDetails,
            }).then((invoice) =>
              replace(`/sales/orders/${saleId}/invoice/${invoice.id}${!!isProforma ? '?isProforma=true' : ''}`),
            );
          }}
        >
          <CheckIcon />
          {t('create invoice')}
        </Button>
      </PageHeader>
      <PageContent>
        <div className='flex' ref={elementRef}>
          <div className='flex h-full justify-center bg-gray-100 p-4 w-3/4'>
            <DocumentPreview content={saleInvoiceDocumentPreview} />
          </div>
          <div className='flex w-1/3 flex-col gap-4 overflow-y-auto p-6' ref={containerRef}>
            <div className='w-full rounded-lg border p-4'>
              <TextareaAutosize
                className='w-full'
                maxRows={20}
                minRows={10}
                onChange={({target: {value}}) => setNotes(value)}
                placeholder={t('mentions')}
                value={notes}
              />
            </div>
            <div className='text-lg font-semibold'>{t('hide columns')}:</div>
            {invoiceHideableDocumentColumns.map((column, index) => (
              <WithLabel direction='horizontal' key={`${column}-${index}`}>
                <Checkbox
                  checked={renderingDetails.columnsToHide?.includes(column as any)}
                  id={column}
                  onCheckedChange={(checked) =>
                    setRenderingDetails((prev) => ({
                      ...prev,
                      columnsToHide: checked
                        ? [...(prev.columnsToHide || []), column as any]
                        : prev.columnsToHide?.filter((c) => c !== column),
                    }))
                  }
                />
                <Label htmlFor={column}>{t(column as any)}</Label>
              </WithLabel>
            ))}
          </div>
        </div>
      </PageContent>
    </Page>
  );
};

export default SaleInvoicesCreate;
