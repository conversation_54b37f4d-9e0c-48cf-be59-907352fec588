import {FC, useCallback} from 'react';

import {ArrowLeftIcon, CloudDownloadIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';

import {Button} from '@/components/ui/Button';
import {DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger} from '@/components/ui/DropdownMenu';
import {DocumentPreview} from '@/components/ui/special/DocumentPreview';
import {Page, PageContent, PageHeader, PageHeaderTitle, PageTitle} from '@/components/ui/special/Page';
import useInvoiceDocument from '@/hooks/documents/useInvoiceDocument';
import useHeight from '@/hooks/helpers/useHeight';
import {useRouter} from '@/hooks/helpers/useRouter';

type Props = {
  invoiceId: string;
  saleId: string;
};

const SaleInvoicesDetails: FC<Props> = ({invoiceId, saleId}) => {
  const {
    query: {isProforma},
  } = useRouter();
  const {elementRef} = useHeight();
  const {invoice} = useInvoiceDocument(invoiceId);
  const {back, push} = useRouter();
  const t = useTranslations();

  const handleDownload = useCallback(
    (mediaType: string) => {
      push(`/api/invoices/${invoiceId}/details?mediaType=${mediaType}`);
    },
    [invoiceId, push],
  );

  return (
    <Page>
      <PageTitle>{`${t(!isProforma ? 'invoice' : 'proforma')} - ${t('orders')} - ${t('sales')}`}</PageTitle>
      <PageHeader>
        <PageHeaderTitle className='inline-flex items-center gap-2'>
          <Button onClick={() => back(`/sales/orders/${saleId}`)} size='icon' variant='none'>
            <ArrowLeftIcon />
          </Button>
          {t('order')}
        </PageHeaderTitle>
        <div className='grow' />
        <DropdownMenu>
          <DropdownMenuTrigger>
            <Button variant='secondary'>
              <CloudDownloadIcon /> {t('download')}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuItem onClick={() => handleDownload('application/pdf')}>{t('pdf')}</DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleDownload('application/factura-saga%2Bxml')}>
              {t('saga')}
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </PageHeader>
      <PageContent>
        <div className='flex' ref={elementRef}>
          <div className='flex size-full justify-center bg-gray-100 p-4'>
            <DocumentPreview content={invoice} />
          </div>
        </div>
      </PageContent>
    </Page>
  );
};

export default SaleInvoicesDetails;
