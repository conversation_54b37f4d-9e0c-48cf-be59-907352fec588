import {FC} from 'react';

import {useAtomValue} from 'jotai';
import {useTranslations} from 'next-intl';
import {useFormContext} from 'react-hook-form';

import {detailsDataShownAtom} from '@/components/pages/details/detailsStore';
import {ComboboxLabel, ControlledCombobox} from '@/components/ui/Combobox';
import {ControlledDatePickerInput} from '@/components/ui/DatePicker';
import {Input, InputLabel} from '@/components/ui/Input';
import {WithLabel} from '@/components/ui/Label';
import {HideableContent} from '@/components/ui/special/HideableContent';
import {useHasPermission} from '@/hooks/helpers/useHasPermission';
import useUsers from '@/hooks/useUsers';
import {PurchaseStatus} from '@/types/global';
import {PurchaseOrder} from '@/types/purchases';
import {formatCurrency, formatDate} from '@/utils/format';

const PurchasesDetailsData: FC = () => {
  const t = useTranslations();
  const {hasPermission, isLoading} = useHasPermission();
  const {
    control,
    formState: {errors},
    watch,
  } = useFormContext<PurchaseOrder>();
  const detailsDataShown = useAtomValue(detailsDataShownAtom(watch('id')));
  const {users} = useUsers();

  if (isLoading) return null;

  return (
    <HideableContent show={detailsDataShown}>
      <div className='mx-6 mt-6 rounded-lg border border-border p-6'>
        <div className='flex flex-col gap-4'>
          <div className='text-base font-medium'>{t('list details')}</div>
          <div className='flex items-center gap-6'>
            <WithLabel>
              <Input disabled value={formatDate(watch('createTime'))} />
              <InputLabel>{t('order date')}</InputLabel>
            </WithLabel>
            <WithLabel>
              <Input disabled value={watch('supplier.name')} />
              <InputLabel>{t('supplier')}</InputLabel>
            </WithLabel>
            <WithLabel>
              <ControlledDatePickerInput
                control={control}
                controlName='expectedDelivery'
                disabled={watch('status') !== PurchaseStatus.SUBMITTED}
                error={!!errors?.expectedDelivery}
                size='lg'
              />
              <InputLabel>
                {t('expected delivery')}
                {watch('status') === PurchaseStatus.SUBMITTED && '*'}
              </InputLabel>
            </WithLabel>
            <WithLabel>
              <ControlledCombobox
                className='h-10 w-48'
                containerClassName='max-w-none'
                control={control}
                controlName='managedBy.id'
                error={!!errors?.managedBy}
                options={users.map((user) => ({id: user.id, value: user.name}))}
                renderNotFound={() => t('user not found')}
                searchPlaceholder={t('search user')}
              />
              <ComboboxLabel htmlFor='category'>{t('managed by')}</ComboboxLabel>
            </WithLabel>
            {hasPermission('financial', 'purchases') && (
              <WithLabel>
                <Input disabled value={formatCurrency(watch('totalAmount'))} />
                <InputLabel>{t('total price')}</InputLabel>
              </WithLabel>
            )}
          </div>
        </div>
      </div>
    </HideableContent>
  );
};

export default PurchasesDetailsData;
