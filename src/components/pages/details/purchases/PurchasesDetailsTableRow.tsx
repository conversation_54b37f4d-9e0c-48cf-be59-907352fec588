import {FC} from 'react';

import {useAtomValue} from 'jotai';
import {useTranslations} from 'next-intl';

import InventoryItemLink from '@/components/ui/special/InventoryItemLink/InventoryItemLink';
import {TableCell, TableRow} from '@/components/ui/Table';
import {useHasPermission} from '@/hooks/helpers/useHasPermission';
import {defaultCurrencyAtom} from '@/store/defaults';
import {PurchaseOrderItem} from '@/types/purchases';
import {formatCurrency} from '@/utils/format';

type Props = {
  item: PurchaseOrderItem;
};

const PurchasesDetailsTableRow: FC<Props> = ({item}) => {
  const t = useTranslations();
  const {hasPermission, isLoading} = useHasPermission();
  const defaultCurrency = useAtomValue(defaultCurrencyAtom);

  if (isLoading) return null;

  return (
    <TableRow>
      <TableCell>
        <InventoryItemLink item={item.materialGood} />
      </TableCell>
      <TableCell className='text-right'>
        {item.quantity} {t(`unit.name.${item.measurementUnit.name.toLowerCase()}` as any)}
      </TableCell>
      {hasPermission('financial', 'purchases') && (
        <>
          <TableCell className='text-right'>{formatCurrency(item.price)}</TableCell>
          <TableCell className='text-right'>
            {formatCurrency({
              amount: (item.price?.amount || 0) * item.quantity,
              currency: item.price?.currency || defaultCurrency,
            })}
          </TableCell>
        </>
      )}
    </TableRow>
  );
};

export default PurchasesDetailsTableRow;
