import {FC, useState} from 'react';

import {ArrowLeftIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';
import {FormProvider} from 'react-hook-form';

import {detailsDataShownAtom} from '@/components/pages/details/detailsStore';
import ReceptionCreateFilesButtons from '@/components/pages/details/inventory/receptions/ReceptionCreateFiles/ReceptionCreateFilesButtons';
import ReceptionCreateFiles from '@/components/pages/details/inventory/receptions/ReceptionCreateFiles/ReceptionCreateFilesTable';
import {Button} from '@/components/ui/Button';
import {HideableContentToggle} from '@/components/ui/special/HideableContentToggle';
import {Page, PageContent, PageHeader, PageHeaderTitle, PageTitle} from '@/components/ui/special/Page';
import {Tabs, TabsList, TabsTrigger} from '@/components/ui/Tabs';
import {useRouter} from '@/hooks/helpers/useRouter';
import usePurchaseOrderReception from '@/hooks/usePurchaseOrderReception';

import ReceptionCreateTransport from '../../inventory/receptions/expenses/ReceptionCreateTransport';
import ReceptionCreateButton from '../../inventory/receptions/ReceptionCreateButton';
import ReceptionCreateData from '../../inventory/receptions/ReceptionCreateData';
import ReceptionCreateTable from '../../inventory/receptions/ReceptionCreateTable';

type Props = {
  saleId: string;
};

type ReceptionTab = 'files' | 'items' | 'transport';

const PurchasesReceptionCreate: FC<Props> = ({saleId}) => {
  const t = useTranslations();
  const {
    deliverPurchaseOrder,
    isLoading,
    useFormActions: {
      formState: {errors, ...restUseFormState},
      register,
      resetField,
      watch,
      ...restUseFormActions
    },
  } = usePurchaseOrderReception(saleId);
  const [tab, setTab] = useState<ReceptionTab>('items');
  const [files, setFiles] = useState<File[]>([]);
  const {back} = useRouter();

  if (isLoading) return null;

  return (
    <Page>
      <PageTitle>{`${t('reception')} - ${t('purchases')}`}</PageTitle>
      <PageHeader>
        <PageHeaderTitle className='inline-flex items-center gap-2'>
          <Button onClick={() => back(`/purchases/orders/${saleId}`)} size='icon' variant='none'>
            <ArrowLeftIcon />
          </Button>
          {t('reception')}
        </PageHeaderTitle>
        <HideableContentToggle store={detailsDataShownAtom(watch('id'))} />
        <div className='grow' />
        <Button onClick={() => deliverPurchaseOrder(files)}>{t('add reception')}</Button>
      </PageHeader>
      <PageContent>
        <FormProvider
          {...{formState: {errors, ...restUseFormState}, register, resetField, watch, ...restUseFormActions}}
        >
          <ReceptionCreateData saleId={saleId} />
          <div className='mx-6 flex items-center justify-between'>
            <div className='inline-flex items-center'>
              <Tabs onValueChange={(value) => setTab(value as ReceptionTab)} value={tab} variant='menu'>
                <TabsList variant='menu'>
                  <TabsTrigger
                    badge={watch('goods')?.length || 0}
                    error={!!errors.goods}
                    value={'items'}
                    variant='menu'
                  >
                    {t('items')}
                  </TabsTrigger>
                  <TabsTrigger
                    badge={watch('additionalCosts')?.length || 0}
                    error={!!errors.additionalCosts || !!(errors as any)?.['']}
                    value={'transport'}
                    variant='menu'
                  >
                    {t('transport')}
                  </TabsTrigger>
                  <TabsTrigger
                    badge={watch('id') ? watch('files')?.length || 0 : files.length}
                    value={'files'}
                    variant='menu'
                  >
                    {t('files')}
                  </TabsTrigger>
                </TabsList>
              </Tabs>
            </div>
            <div>
              {tab === 'items' && <ReceptionCreateButton />}
              {tab === 'files' && <ReceptionCreateFilesButtons setFiles={setFiles} />}
            </div>
          </div>
          {tab === 'items' && <ReceptionCreateTable />}
          {tab === 'transport' && <ReceptionCreateTransport />}
          {tab === 'files' && <ReceptionCreateFiles files={files} setFiles={setFiles} />}
        </FormProvider>
      </PageContent>
    </Page>
  );
};

export default PurchasesReceptionCreate;
