import {FC, useCallback, useMemo, useState} from 'react';

import {useSet<PERSON>tom} from 'jotai';
import {useAtomValue} from 'jotai';
import {ArrowLeftIcon, CheckIcon, CloudDownloadIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';

import {PurchasesView, purchasesViewAtom} from '@/components/pages/lists/purchases/purchasesListStore';
import {Button} from '@/components/ui/Button';
import {Checkbox} from '@/components/ui/Checkbox';
import {Input, InputLabel} from '@/components/ui/Input';
import {Label, WithLabel} from '@/components/ui/Label';
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from '@/components/ui/Select';
import {DocumentPreview} from '@/components/ui/special/DocumentPreview';
import {Page, PageContent, PageHeader, PageHeaderTitle, PageTitle} from '@/components/ui/special/Page';
import {documentLocales} from '@/constants/config';
import useWishlistInvoice from '@/hooks/documents/useWishlistDocumentPreview';
import useHeight from '@/hooks/helpers/useHeight';
import {useRouter} from '@/hooks/helpers/useRouter';
import {defaultLocaleAtom} from '@/store/defaults';
import {DocumentLocale} from '@/types/global';
import {PurchaseRenderingDetails} from '@/types/purchases';

import {purchaseOrderHideableDocumentColumns} from '../purchasesDetailsConstants';

type Props = {
  wishlistId: string;
};

const WishlistInvoicesCreate: FC<Props> = ({wishlistId}) => {
  const t = useTranslations();
  const {elementRef} = useHeight();
  const {elementRef: containerRef} = useHeight();
  const defaultLocale = useAtomValue(defaultLocaleAtom);
  const [renderingDetails, setRenderingDetails] = useState<PurchaseRenderingDetails>({
    columnsToHide: [],
    language: documentLocales.includes(defaultLocale as DocumentLocale) ? (defaultLocale as DocumentLocale) : 'ro',
    title: t('purchase order'),
  });
  const {createPurchaseOrder, wishlistDocumentPreview} = useWishlistInvoice(wishlistId, renderingDetails);
  const {back, push} = useRouter();
  const setPurchasesView = useSetAtom(purchasesViewAtom);

  const languages = useMemo(
    () =>
      [
        {id: 'ro', value: t('romanian')},
        {id: 'en', value: t('english')},
        {id: 'hu', value: t('hungarian')},
        {id: 'de', value: t('german')},
      ] as const satisfies readonly {id: DocumentLocale; value: string}[],
    [t],
  );

  const handleCreateDownload = useCallback(
    (mediaType?: string) =>
      createPurchaseOrder().then((order) => {
        if (mediaType) setTimeout(() => push(`/api/purchases/orders/${order?.id}/details?mediaType=${mediaType}`), 500);
        setPurchasesView(PurchasesView.active);
        push('/purchases?tab=active');
      }),
    [createPurchaseOrder, push, setPurchasesView],
  );

  return (
    <Page>
      <PageTitle>{`${t('order')} - ${t('purchases')}`}</PageTitle>
      <PageHeader>
        <PageHeaderTitle className='inline-flex items-center gap-2'>
          <Button onClick={() => back('/purchases')} size='icon' variant='none'>
            <ArrowLeftIcon />
          </Button>
          {t('order')}
        </PageHeaderTitle>
        <div className='grow' />
        <Button onClick={() => handleCreateDownload('application/pdf')} variant='secondary'>
          <CloudDownloadIcon />
          {t('save and download')}
        </Button>
        <Button onClick={() => handleCreateDownload()}>
          <CheckIcon />
          {t('save')}
        </Button>
      </PageHeader>
      <PageContent>
        <div className='flex' ref={elementRef}>
          <div className='flex h-full w-3/4 justify-center bg-gray-100 p-4'>
            <DocumentPreview content={wishlistDocumentPreview} />
          </div>
          <div className='flex w-1/3 flex-col gap-4 overflow-y-auto p-6' ref={containerRef}>
            <div className='flex gap-4'>
              <WithLabel className='w-full'>
                <Input
                  onChange={({target: {value}}) => setRenderingDetails((prev) => ({...prev, title: value}))}
                  value={renderingDetails.title}
                />
                <InputLabel>{t('document title')}</InputLabel>
              </WithLabel>
              <WithLabel>
                <Select
                  onValueChange={(value) => {
                    setRenderingDetails((prev) => ({...prev, language: value as DocumentLocale}));
                  }}
                  value={renderingDetails.language}
                >
                  <SelectTrigger size='lg'>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {languages.map((language) => (
                      <SelectItem key={language.id} value={language.id}>
                        {language.value}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <InputLabel>{t('document language')}</InputLabel>
              </WithLabel>
            </div>
            <div className='text-lg font-semibold'>{t('hide columns')}:</div>
            {purchaseOrderHideableDocumentColumns.map((column, index) => (
              <WithLabel direction='horizontal' key={`${column}-${index}`}>
                <Checkbox
                  defaultChecked={renderingDetails.columnsToHide?.includes(column as any)}
                  id={column}
                  onCheckedChange={(checked) =>
                    setRenderingDetails((prev) => ({
                      ...prev,
                      columnsToHide: checked
                        ? [...(prev.columnsToHide || []), column as any]
                        : prev.columnsToHide?.filter((c) => c !== column),
                    }))
                  }
                />
                <Label htmlFor={column}>{t(column as any)}</Label>
              </WithLabel>
            ))}
          </div>
        </div>
      </PageContent>
    </Page>
  );
};

export default WishlistInvoicesCreate;
