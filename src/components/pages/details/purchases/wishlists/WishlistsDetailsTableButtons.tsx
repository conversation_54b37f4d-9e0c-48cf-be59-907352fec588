import {FC, useCallback, useState} from 'react';

import {PlusIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';
import {useFieldArray, useFormContext} from 'react-hook-form';

import {Button} from '@/components/ui/Button';
import AddItemModal from '@/components/ui/special/AddItemModal/AddItemModal';
import useItemActions from '@/hooks/useItemActions';
import {Id} from '@/types/global';
import {Wishlist, WishlistItem} from '@/types/purchases';
import {classes} from '@/utils/common';

type AddItemProps = {
  excludeIds: (null | string | undefined)[];
  onClose: () => void;
  onSelect: (value: WishlistItem) => void;
  supplier: Id;
};

const AddItem: FC<AddItemProps> = ({excludeIds, onClose, onSelect, supplier}) => {
  const {getItem} = useItemActions();

  const handleAdd = useCallback(
    (values: {id: string; quantity: number}[]) => {
      values.forEach(async ({id, quantity}) => {
        const item = await getItem(id);

        if (!item) return;

        onSelect({
          id: '',
          materialGood: item,
          measurementUnit: item.measurementUnit,
          quantity,
          supplier,
        });
      });
      onClose();
    },
    [getItem, onClose, onSelect, supplier],
  );

  return <AddItemModal excludeIds={excludeIds} onAdd={handleAdd} onClose={onClose} />;
};

type Props = {
  className?: string;
};

const WishlistsDetailsTableButtons: FC<Props> = ({className}) => {
  const t = useTranslations();
  const [addEnabled, setAddEnabled] = useState(false);
  const {control, watch} = useFormContext<Wishlist>();
  const {append} = useFieldArray({control, name: 'wishlistItems'});

  return (
    <div className={classes('flex items-center gap-4', className)}>
      {!addEnabled && (
        <Button disabled={!watch('id')} onClick={() => setAddEnabled(true)} variant='secondary'>
          <PlusIcon /> {t('add item')}
        </Button>
      )}
      {addEnabled && (
        <AddItem
          excludeIds={watch('wishlistItems')?.map((item) => item.materialGood.id) ?? []}
          onClose={() => setAddEnabled(false)}
          onSelect={append}
          supplier={{id: watch('id'), name: watch('name')}}
        />
      )}
    </div>
  );
};

export default WishlistsDetailsTableButtons;
