import {FC} from 'react';

import {useTranslations} from 'next-intl';

import {TableCell, TableHeadActions, TableRow} from '@/components/ui/Table';

const WishlistsDetailsLastOrderedTable: FC = () => {
  const t = useTranslations();

  return (
    <>
      <TableRow>
        <TableCell className='h-0.5 bg-black p-0' />
        <TableCell className='h-0.5 bg-black p-0' />
        <TableCell className='h-0.5 bg-black p-0' />
        <TableCell className='h-0.5 bg-black p-0' />
        <TableCell className='h-0.5 bg-black p-0' />
        <TableCell className='h-0.5 bg-black p-0' />
        <TableCell className='h-0.5 bg-black p-0' />
        <TableCell className='h-0.5 bg-black p-0' />
        <TableCell className='h-0.5 bg-black p-0' />
      </TableRow>
      <TableRow className='bg-input'>
        <TableCell className='py-0 uppercase text-muted-foreground'>{t('previously ordered')}:</TableCell>
        <TableCell className='py-0'></TableCell>
        <TableCell className='py-0'></TableCell>
        <TableCell className='py-0'></TableCell>
        <TableCell className='py-0 text-right text-xs uppercase text-muted-foreground'>{t('stock')}</TableCell>
        <TableCell className='py-0 text-right text-xs uppercase text-muted-foreground'>{t('committed')}</TableCell>
        <TableCell className='py-0 text-right text-xs uppercase text-muted-foreground'>{t('incoming')}</TableCell>
        <TableCell className='py-0 text-right text-xs uppercase text-muted-foreground'>
          {t('critical on hand')}
        </TableCell>
        <TableHeadActions />
      </TableRow>
    </>
  );
};

export default WishlistsDetailsLastOrderedTable;
