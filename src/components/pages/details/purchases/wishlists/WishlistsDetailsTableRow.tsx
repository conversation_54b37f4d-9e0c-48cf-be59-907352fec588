import {FC} from 'react';

import Jo<PERSON> from 'joi';
import {useAtomValue} from 'jotai';
import {Trash2Icon} from 'lucide-react';
import {useTranslations} from 'next-intl';
import {useFieldArray, useFormContext} from 'react-hook-form';

import {Button} from '@/components/ui/Button';
import {PopoverInput} from '@/components/ui/Input';
import InventoryItemLink from '@/components/ui/special/InventoryItemLink/InventoryItemLink';
import {TableActions, TableCell, TableRow} from '@/components/ui/Table';
import {useHasPermission} from '@/hooks/helpers/useHasPermission';
import {defaultCurrencyAtom} from '@/store/defaults';
import {Wishlist, WishlistItem} from '@/types/purchases';
import {formatCurrency} from '@/utils/format';

type Props = {
  index: number;
  item: WishlistItem;
};

const WishlistsDetailsTableRow: FC<Props> = ({index, item}) => {
  const t = useTranslations();
  const {hasPermission, isLoading} = useHasPermission();
  const defaultCurrency = useAtomValue(defaultCurrencyAtom);
  const {control} = useFormContext<Wishlist>();
  const {remove, update} = useFieldArray({control, name: 'wishlistItems'});

  if (isLoading) return null;

  return (
    <TableRow>
      <TableCell>
        <InventoryItemLink item={item.materialGood} />
      </TableCell>
      <TableCell className='text-right'>
        <PopoverInput
          className='w-fit justify-end'
          defaultValue={item.quantity.toString()}
          label={t('quantity')}
          onChange={(value) => {
            update(index, {
              ...item,
              quantity: Number(value),
            });
          }}
          validation={Joi.number().positive().required()}
        >
          {item.quantity} {t(`unit.name.${item.measurementUnit.name.toLowerCase()}` as any)}
        </PopoverInput>
      </TableCell>
      {hasPermission('financial', 'purchases') && (
        <>
          <TableCell className='text-right'>
            <PopoverInput
              className='w-fit justify-end'
              defaultValue={item.price?.amount.toString()}
              label={t('quantity')}
              onChange={(value) => {
                update(index, {
                  ...item,
                  price:
                    value === ''
                      ? undefined
                      : {
                          amount: Number(value),
                          currency: item.price?.currency || defaultCurrency,
                        },
                });
              }}
              validation={Joi.number().positive().required().allow('')}
            >
              {formatCurrency(item.price)}
            </PopoverInput>
          </TableCell>
          <TableCell className='text-right'>
            {formatCurrency({
              amount: (item.price?.amount || 0) * item.quantity,
              currency: item.price?.currency,
            })}
          </TableCell>
        </>
      )}
      <TableCell />
      <TableCell />
      <TableCell />
      <TableCell />
      <TableActions>
        <Button onClick={() => remove(index)} size='icon' variant='none'>
          <Trash2Icon className='size-5 text-red' strokeWidth={1} />
        </Button>
      </TableActions>
    </TableRow>
  );
};

export default WishlistsDetailsTableRow;
