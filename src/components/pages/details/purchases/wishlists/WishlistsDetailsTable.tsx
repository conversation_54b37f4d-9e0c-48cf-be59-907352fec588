import {FC, useMemo} from 'react';

import {useAtomValue} from 'jotai';
import {find} from 'lodash';
import {useTranslations} from 'next-intl';
import {useFormContext} from 'react-hook-form';

import {detailsDataShownAtom} from '@/components/pages/details/detailsStore';
import {
  Table,
  TableBody,
  TableContainer,
  TableHead,
  TableHeadActions,
  TableHeader,
  TableRow,
} from '@/components/ui/Table';
import {TabsMenuItem} from '@/components/ui/Tabs';
import {useHasPermission} from '@/hooks/helpers/useHasPermission';
import useHeight from '@/hooks/helpers/useHeight';
import useWishlistLastOrdered from '@/hooks/useWishlistLastOrdered';
import {Wishlist} from '@/types/purchases';

import WishlistsDetailsLastOrderedTable from './WishlistsDetailsLastOrderedTable';
import WishlistsDetailsLastOrderedTableRow from './WishlistsDetailsLastOrderedTableRow';
import WishlistsDetailsTableButtons from './WishlistsDetailsTableButtons';
import WishlistsDetailsTableRow from './WishlistsDetailsTableRow';

const WishlistsDetailsTable: FC = () => {
  const {hasPermission, isLoading} = useHasPermission();
  const {
    formState: {errors},
    watch,
  } = useFormContext<Wishlist>();
  const detailsDataShown = useAtomValue(detailsDataShownAtom(watch('id')));
  const {isLoading: wishlistLastOrderedGoodsIsLoading, wishlistLastOrderedGoods} = useWishlistLastOrdered(watch('id'));
  const {elementRef} = useHeight({dependencies: [detailsDataShown, wishlistLastOrderedGoodsIsLoading]});
  const t = useTranslations();

  const currentItems = watch('wishlistItems');

  const previouslyOrderedItems = useMemo(
    () =>
      wishlistLastOrderedGoods?.filter(
        (item) => !find(currentItems, (wishlist) => wishlist.materialGood.id === item.id),
      ),
    [currentItems, wishlistLastOrderedGoods],
  );

  if (isLoading) return null;

  return (
    <>
      <div className='mr-6 flex items-center justify-between'>
        <TabsMenuItem badge={watch('wishlistItems')?.length || 0} disabled error={!!errors?.wishlistItems}>
          {t('items')}
        </TabsMenuItem>
        <WishlistsDetailsTableButtons />
      </div>
      <TableContainer ref={elementRef}>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>{t('name')}</TableHead>
              <TableHead className='text-right'>{t('quantity')}</TableHead>
              {hasPermission('financial', 'purchases') && (
                <>
                  <TableHead className='text-right'>{t('unit price')}</TableHead>
                  <TableHead className='text-right'>{t('total price')}</TableHead>
                </>
              )}
              <TableHeadActions />
              <TableHeadActions />
              <TableHeadActions />
              <TableHeadActions />
              <TableHeadActions />
            </TableRow>
          </TableHeader>
          <TableBody className='overflow-y-hidden' isValidating={!watch()}>
            {currentItems?.map((item, index) => (
              <WishlistsDetailsTableRow index={index} item={item} key={`${item.materialGood.name}-${index}`} />
            ))}
            {previouslyOrderedItems?.length > 0 && <WishlistsDetailsLastOrderedTable />}
            {previouslyOrderedItems?.map((item, index) => (
              <WishlistsDetailsLastOrderedTableRow index={index} item={item} key={`${item.name}-${index}`} />
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </>
  );
};

export default WishlistsDetailsTable;
