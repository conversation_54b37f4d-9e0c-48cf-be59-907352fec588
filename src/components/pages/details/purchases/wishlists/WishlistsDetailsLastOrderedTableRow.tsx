import {FC} from 'react';

import {PlusIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';
import {useFieldArray, useFormContext} from 'react-hook-form';

import {IconButton} from '@/components/ui/Button';
import {TableActions, TableCell, TableRow} from '@/components/ui/Table';
import {useHasPermission} from '@/hooks/helpers/useHasPermission';
import {Wishlist, WishlistLastOrderedGoods} from '@/types/purchases';
import {formatCurrency, formatNumber} from '@/utils/format';

type Props = {
  index: number;
  item: WishlistLastOrderedGoods;
};

const WishlistsDetailsLastOrderedTableRow: FC<Props> = ({item}) => {
  const t = useTranslations();
  const {hasPermission, isLoading} = useHasPermission();
  const {control, watch} = useFormContext<Wishlist>();
  const {append} = useFieldArray({control, name: 'wishlistItems'});

  if (isLoading) return null;

  return (
    <TableRow
      className='group/row cursor-cell text-gray hover:text-black'
      onClick={() =>
        append({
          expectedDelivery: '',
          id: '',
          materialGood: {
            ...item,
          },
          measurementUnit: item.measurementUnit,
          price: item.price,
          quantity: item.quantity,
          supplier: {id: watch('id'), name: watch('name')},
          totalPrice: {amount: item.price.amount * item.quantity, currency: item.price.currency},
        })
      }
    >
      <TableCell>
        <div className='flex items-center gap-4'>
          <div className='flex flex-col whitespace-nowrap'>
            <div>{item.name}</div>
            <div className='text-xs text-border-foreground'>{`${t('sku')}: ${item.code}`}</div>
          </div>
        </div>
      </TableCell>
      <TableCell className='text-right'>
        {item.quantity} {t(`unit.name.${item.measurementUnit.name.toLowerCase()}` as any)}
      </TableCell>
      {hasPermission('financial', 'purchases') && (
        <>
          <TableCell className='text-right'>{formatCurrency(item.price)}</TableCell>
          <TableCell className='text-right'>
            {formatCurrency({
              amount: item.price.amount * item.quantity,
              currency: item.price.currency,
            })}
          </TableCell>
        </>
      )}
      <TableCell className='text-right'>{formatNumber(item.quantity)}</TableCell>
      <TableCell className='text-right'>{formatNumber(item.committed)}</TableCell>
      <TableCell className='text-right'>{formatNumber(item.incoming)}</TableCell>
      <TableCell className='text-right'>{formatNumber(item.criticalOnHand)}</TableCell>
      <TableActions className='w-0 py-0'>
        <div className='invisible group-hover/row:visible'>
          <IconButton icon={<PlusIcon />} />
        </div>
      </TableActions>
    </TableRow>
  );
};

export default WishlistsDetailsLastOrderedTableRow;
