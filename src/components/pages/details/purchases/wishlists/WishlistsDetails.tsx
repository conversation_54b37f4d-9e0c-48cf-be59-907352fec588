import {FC} from 'react';

import {ArrowLeftIcon, CheckIcon, EllipsisVerticalIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';
import {FormProvider} from 'react-hook-form';

import {detailsDataShownAtom} from '@/components/pages/details/detailsStore';
import {Button} from '@/components/ui/Button';
import {DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger} from '@/components/ui/DropdownMenu';
import {HideableContentToggle} from '@/components/ui/special/HideableContentToggle';
import {Page, PageContent, PageHeader, PageHeaderTitle, PageTitle} from '@/components/ui/special/Page';
import {useRouter} from '@/hooks/helpers/useRouter';
import useWishlist from '@/hooks/useWishlist';

import WishlistsDetailsData from './WishlistsDetailsData';
import WishlistsDetailsTable from './WishlistsDetailsTable';

type Props = {
  id?: string;
};

const WishlistsDetails: FC<Props> = ({id}) => {
  const t = useTranslations();
  const {
    deleteWishlist,
    isDirty,
    isLoading,
    saveWishlist,
    useFormActions: {
      formState: {errors, ...restUseFormState},
      register,
      resetField,
      watch,
      ...restUseFormActions
    },
  } = useWishlist(id);
  const {back, push} = useRouter();

  if (isLoading) return null;

  return (
    <Page>
      <PageTitle>{`${watch('name') || t('new list')} - ${t('lists')}`}</PageTitle>
      <PageHeader>
        <PageHeaderTitle className='inline-flex items-center gap-2'>
          <Button onClick={() => back('/purchases')} size='icon' variant='none'>
            <ArrowLeftIcon />
          </Button>
          {id ? watch('name') : t('new list')}
        </PageHeaderTitle>
        <HideableContentToggle store={detailsDataShownAtom(watch('id'))} />
        <div className='grow' />
        {isDirty && <Button onClick={saveWishlist}>{t('save')}</Button>}
        {id && !isDirty && (
          <Button onClick={() => push(`/purchases/wishlists/${id}/invoice`)}>
            <CheckIcon />
            {t('preview and save document')}
          </Button>
        )}
        {id && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button className='px-1.5' variant='ghost'>
                <EllipsisVerticalIcon />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align='end'>
              <DropdownMenuItem onSelect={deleteWishlist}>{t('delete')}</DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        )}
      </PageHeader>
      <PageContent>
        <FormProvider
          {...{formState: {errors, ...restUseFormState}, register, resetField, watch, ...restUseFormActions}}
        >
          <WishlistsDetailsData id={id} />
          <WishlistsDetailsTable />
        </FormProvider>
      </PageContent>
    </Page>
  );
};

export default WishlistsDetails;
