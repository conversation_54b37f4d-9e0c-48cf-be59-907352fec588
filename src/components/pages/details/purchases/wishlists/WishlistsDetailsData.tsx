import {FC, ReactNode, useState} from 'react';

import {useAtomValue} from 'jotai';
import {sumBy} from 'lodash';
import {useTranslations} from 'next-intl';
import {useFormContext} from 'react-hook-form';

import {detailsDataShownAtom} from '@/components/pages/details/detailsStore';
import {Button} from '@/components/ui/Button';
import {Combobox, ComboboxLabel} from '@/components/ui/Combobox';
import {Input, InputLabel} from '@/components/ui/Input';
import {WithLabel} from '@/components/ui/Label';
import {HideableContent} from '@/components/ui/special/HideableContent';
import NewSupplierSheet from '@/components/ui/special/NewSupplierSheet';
import {useRouter} from '@/hooks/helpers/useRouter';
import useSuppliers from '@/hooks/useSuppliers';
import useWishlistActions from '@/hooks/useWishlistActions';
import {defaultCurrencyAtom} from '@/store/defaults';
import {Wishlist} from '@/types/purchases';
import {addDays} from '@/utils/common';
import {formatCurrency, formatDate} from '@/utils/format';

type Props = {
  id?: string;
};

const WishlistsDetailsData: FC<Props> = ({id}) => {
  const t = useTranslations();
  const defaultCurrency = useAtomValue(defaultCurrencyAtom);
  const {hasItems} = useWishlistActions();
  const {replace} = useRouter();
  const [newSupplierName, setNewSupplierName] = useState('');
  const {
    formState: {errors},
    reset,
    setValue,
    watch,
  } = useFormContext<Wishlist>();
  const detailsDataShown = useAtomValue(detailsDataShownAtom(watch('id')));

  return (
    <HideableContent show={detailsDataShown}>
      {newSupplierName && (
        <NewSupplierSheet
          defaultValue={newSupplierName}
          onClose={() => setNewSupplierName('')}
          onCreate={(supplier) => {
            reset();
            setValue('id', supplier.id);
          }}
        />
      )}
      <div className='mx-6 mt-6 rounded-lg border border-border p-6'>
        <div className='flex flex-col gap-4'>
          <div className='text-base font-medium'>{t('list details')}</div>
          <div className='flex items-center gap-6'>
            {!id && (
              <SupplierInput
                error={!!errors.id}
                onChange={async (value) => {
                  reset();
                  if (await hasItems(value)) {
                    setTimeout(() => replace(`/purchases/wishlists/${value}`), 100);
                  } else {
                    setValue('id', value);
                  }
                }}
                open={!watch('id')}
                renderNotFound={(query) => (
                  <Button
                    className='w-full justify-start'
                    onClick={() => setNewSupplierName(query)}
                    variant='secondary'
                  >
                    {t('new supplier')}
                  </Button>
                )}
                value={watch('id')}
              />
            )}
            <WithLabel>
              <Input disabled value={formatDate(addDays(watch('lastOrderDeliveredIn') || 0))} />
              <InputLabel>{t('expected delivery')}</InputLabel>
            </WithLabel>
            <WithLabel>
              <Input
                disabled
                value={formatCurrency(
                  watch('wishlistItems').every((item) => item.price?.amount === undefined)
                    ? undefined
                    : {
                        amount: sumBy(watch('wishlistItems'), (item) => (item.price?.amount || 0) * item.quantity),
                        currency: defaultCurrency,
                      },
                )}
              />
              <InputLabel>{t('total price')}</InputLabel>
            </WithLabel>
          </div>
        </div>
      </div>
    </HideableContent>
  );
};

type SupplierInputProps = {
  error: boolean;
  onChange?: (value: string) => void;
  onOpenChange?: (isOpen: boolean) => void;
  open: boolean;
  renderNotFound: (query: string) => ReactNode;
  value: string;
};

const SupplierInput: FC<SupplierInputProps> = ({error, onChange, onOpenChange, open, renderNotFound, value}) => {
  const {suppliers} = useSuppliers();
  const t = useTranslations();

  return (
    <WithLabel>
      <Combobox
        className='w-[250px]'
        error={error}
        onChange={onChange}
        onOpenChange={onOpenChange}
        open={open}
        options={suppliers.map((supplier) => ({id: supplier.id, value: supplier.name}))}
        placeholder={t('supplier')}
        renderNotFound={renderNotFound}
        searchPlaceholder={t('search supplier')}
        value={value}
      />
      <ComboboxLabel>{t('supplier')}*</ComboboxLabel>
    </WithLabel>
  );
};

export default WishlistsDetailsData;
