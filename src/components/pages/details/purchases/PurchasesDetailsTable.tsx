import {FC} from 'react';

import {useAtomValue} from 'jotai';
import {useTranslations} from 'next-intl';
import {useFormContext} from 'react-hook-form';

import {detailsDataShownAtom} from '@/components/pages/details/detailsStore';
import {Table, TableBody, TableContainer, TableHead, TableHeader, TableRow} from '@/components/ui/Table';
import {TabsMenuItem} from '@/components/ui/Tabs';
import {useHasPermission} from '@/hooks/helpers/useHasPermission';
import useHeight from '@/hooks/helpers/useHeight';
import {PurchaseOrder} from '@/types/purchases';

import PurchasesDetailsTableRow from './PurchasesDetailsTableRow';

const PurchasesDetailsTable: FC = () => {
  const {hasPermission, isLoading} = useHasPermission();
  const {watch} = useFormContext<PurchaseOrder>();
  const detailsDataShown = useAtomValue(detailsDataShownAtom(watch('id')));
  const {elementRef} = useHeight({dependencies: [detailsDataShown]});
  const t = useTranslations();

  if (isLoading) return null;

  return (
    <>
      <div className='mr-6 flex items-center justify-between'>
        <TabsMenuItem badge={watch('items')?.length || 0} disabled>
          {t('items')}
        </TabsMenuItem>
      </div>
      <TableContainer ref={elementRef}>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>{t('name')}</TableHead>
              <TableHead className='text-right'>{t('quantity')}</TableHead>
              {hasPermission('financial', 'purchases') && (
                <>
                  <TableHead className='text-right'>{t('unit price')}</TableHead>
                  <TableHead className='text-right'>{t('total price')}</TableHead>
                </>
              )}
            </TableRow>
          </TableHeader>
          <TableBody className='overflow-y-hidden' isValidating={!watch()}>
            {watch('items')?.map((item, index) => (
              <PurchasesDetailsTableRow item={item} key={`${item.materialGood.name}-${index}`} />
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </>
  );
};

export default PurchasesDetailsTable;
