import {FC} from 'react';

import SupplierDetailsDataAddress from './addresses/SupplierDetailsDataAddress';
import SupplierDetailsDataBank from './bankAccounts/SupplierDetailsDataBank';
import SupplierDetailsDataContacts from './contacts/SupplierDetailsDataContacts';

type Props = {
  onSave: () => void;
};

const SupplierDetailsData: FC<Props> = ({onSave}) => {
  return (
    <div className='my-6 ml-6 grid  h-fit grid-cols-2 gap-x-20 gap-y-10'>
      <SupplierDetailsDataAddress onSave={onSave} />
      <SupplierDetailsDataContacts onSave={onSave} />
      <SupplierDetailsDataBank onSave={onSave} />
    </div>
  );
};

export default SupplierDetailsData;
