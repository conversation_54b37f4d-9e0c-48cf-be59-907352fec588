import {FC, useState} from 'react';

import {CheckIcon, PencilIcon, Trash2Icon, XIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';
import {useFieldArray, useFormContext} from 'react-hook-form';

import {Button} from '@/components/ui/Button';
import {Checkbox} from '@/components/ui/Checkbox';
import {Input, InputLabel} from '@/components/ui/Input';
import {Label, WithLabel} from '@/components/ui/Label';
import CountriesCombobox from '@/components/ui/special/CountriesCombobox';
import {Address, AddressType} from '@/types/global';
import {Customer} from '@/types/sales';
import {classes} from '@/utils/common';
import {addressToString} from '@/utils/format';

type Props = {
  address?: Address;
  index?: number;
  onCancel?: () => void;
  onSave: () => void;
};

const SupplierDetailsDataAddressRow: FC<Props> = ({address, index, onCancel, onSave}) => {
  const t = useTranslations();
  const [editableAddress, setEditableAddress] = useState<Address | undefined>(
    address ? undefined : {address1: '', address2: '', city: '', country: '', name: '', state: '', types: [], zip: ''},
  );
  const {control} = useFormContext<Customer>();
  const {append, remove, update} = useFieldArray({control, name: `addresses`});

  const handleClose = () => {
    setEditableAddress(undefined);
    if (index === undefined) onCancel?.();
  };

  return (
    <div
      className={classes(
        'flex items-center justify-between rounded-lg border p-6',
        address?.address1 ? 'border-border' : 'border-red',
      )}
    >
      {!editableAddress && (
        <>
          <div className='flex flex-col'>
            <div className='text-sm font-medium'>{address?.name}</div>
            <div className='text-sm'>{addressToString(address, true)}</div>
            <div className='text-sm'>{address?.address1}</div>
          </div>
          <Button onClick={() => setEditableAddress(address)} variant='secondary'>
            <PencilIcon className='size-5' /> {t('edit')}
          </Button>
        </>
      )}
      {editableAddress && (
        <div className='flex w-full flex-col gap-4'>
          {!address && <div>{t('add new address')}</div>}
          <WithLabel>
            <Input
              defaultValue={editableAddress?.address1}
              error={!editableAddress?.address1}
              onChange={({target: {value}}) => setEditableAddress((prev) => ({...(prev as Address), address1: value}))}
            />
            <InputLabel>{t('address (str no bl ap st fl)')}*</InputLabel>
          </WithLabel>
          <div className='flex items-center gap-6'>
            <WithLabel className='w-full'>
              <Input
                defaultValue={editableAddress?.city}
                onChange={({target: {value}}) => setEditableAddress((prev) => ({...(prev as Address), city: value}))}
              />
              <InputLabel>{t('city')}</InputLabel>
            </WithLabel>
            <WithLabel className='w-full'>
              <Input
                defaultValue={editableAddress?.state}
                onChange={({target: {value}}) => setEditableAddress((prev) => ({...(prev as Address), state: value}))}
              />
              <InputLabel>{t('state')}</InputLabel>
            </WithLabel>
          </div>
          <div className='flex items-center gap-6'>
            <WithLabel className='w-full'>
              <CountriesCombobox
                onChange={(value) => setEditableAddress((prev) => ({...(prev as Address), country: value}))}
                value={editableAddress?.country}
              />
              <InputLabel>{t('country')}</InputLabel>
            </WithLabel>
            <WithLabel>
              <Input
                defaultValue={editableAddress?.zip}
                onChange={({target: {value}}) => setEditableAddress((prev) => ({...(prev as Address), zip: value}))}
                size='md'
              />
              <InputLabel>{t('zip')}</InputLabel>
            </WithLabel>
          </div>
          <WithLabel>
            <Input
              defaultValue={editableAddress?.name}
              onChange={({target: {value}}) => setEditableAddress((prev) => ({...(prev as Address), name: value}))}
            />
            <InputLabel>{t('preferential name')}</InputLabel>
          </WithLabel>
          <div className={classes('flex gap-8', (editableAddress?.types || []).length === 0 && 'text-alert-red')}>
            <WithLabel direction='horizontal'>
              <Checkbox
                defaultChecked={editableAddress?.types?.includes(AddressType.SHIPPING)}
                id='shipping'
                onCheckedChange={(checked) => {
                  const types = checked
                    ? [...(editableAddress?.types || []), AddressType.SHIPPING]
                    : (editableAddress?.types || []).filter((type) => type !== AddressType.SHIPPING);

                  setEditableAddress((prev) => ({...(prev as Address), types}));
                }}
              />
              <Label htmlFor='shipping'>{t('shipping')}</Label>
            </WithLabel>
            <WithLabel direction='horizontal'>
              <Checkbox
                defaultChecked={editableAddress?.types?.includes(AddressType.BILLING)}
                id='billing'
                onCheckedChange={(checked) => {
                  const types = checked
                    ? [...(editableAddress?.types || []), AddressType.BILLING]
                    : (editableAddress?.types || []).filter((type) => type !== AddressType.BILLING);

                  setEditableAddress((prev) => ({...(prev as Address), types}));
                }}
              />
              <Label htmlFor='billing'>{t('billing')}</Label>
            </WithLabel>
          </div>
          <div className='flex justify-between'>
            {index !== undefined && (
              <Button
                onClick={() => {
                  remove(index);
                  handleClose();
                }}
                variant='destructive'
              >
                <Trash2Icon className='size-5 text-red' strokeWidth={1} /> {t('delete')}
              </Button>
            )}
            <div className='flex w-full justify-end gap-4'>
              <Button
                onClick={() => {
                  handleClose();
                }}
                variant='secondary'
              >
                <XIcon /> {t('cancel')}
              </Button>
              <Button
                disabled={!editableAddress?.address1 || editableAddress?.types.length === 0}
                onClick={() => {
                  if (index === undefined) {
                    append(editableAddress);
                  } else {
                    update(index, editableAddress);
                  }
                  handleClose();
                  onSave();
                }}
              >
                <CheckIcon /> {t(index === undefined ? 'add' : 'save')}
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SupplierDetailsDataAddressRow;
