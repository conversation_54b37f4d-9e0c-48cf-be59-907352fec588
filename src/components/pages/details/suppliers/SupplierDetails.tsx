import {FC, useEffect} from 'react';

import Joi from 'joi';
import {useAtom} from 'jotai';
import {ArrowLeftIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';
import {FormProvider} from 'react-hook-form';

import {PurchasesView} from '@/components/pages/lists/purchases/purchasesListStore';
import PurchasesListTable from '@/components/pages/lists/purchases/PurchasesListTable';
import {Button} from '@/components/ui/Button';
import {InputLabel, PopoverInput} from '@/components/ui/Input';
import {WithLabel} from '@/components/ui/Label';
import {Page, PageContent, PageHeader, PageHeaderTitle, PageTitle} from '@/components/ui/special/Page';
import {Tabs, TabsList, TabsTrigger} from '@/components/ui/Tabs';
import {useHasPermission} from '@/hooks/helpers/useHasPermission';
import {useRouter} from '@/hooks/helpers/useRouter';
import useSupplier from '@/hooks/useSupplier';
import {formatNumber} from '@/utils/format';

import SupplierDetailsData from './SupplierDetailsData';
import SupplierDetailsFilesButtons from './SupplierDetailsFilesTable/SupplierDetailsFilesButtons';
import SupplierDetailsFilesTable from './SupplierDetailsFilesTable/SupplierDetailsFilesTable';
import {SupplierTab, supplierTabAtom} from './supplierDetailsStore';

type Props = {
  id: string;
};

const SupplierDetails: FC<Props> = ({id}) => {
  const t = useTranslations();
  const {
    deleteSupplierFile,
    isDirty,
    isLoading,
    saveSupplier,
    uploadSupplierFile,
    useFormActions: {
      formState: {errors, ...restUseFormState},
      register,
      resetField,
      setValue,
      watch,
      ...restUseFormActions
    },
  } = useSupplier(id);
  const [tab, setTab] = useAtom(supplierTabAtom(watch('id')));
  const {hasPermission} = useHasPermission();
  const {back} = useRouter();

  useEffect(() => {
    if (!hasPermission('update', 'purchases') && tab === 'orders') setTab('files');
  }, [hasPermission, setTab, tab]);

  if (isLoading) return null;

  return (
    <Page>
      <PageTitle>{`${watch('name') || t('new supplier name')} - ${t('supplier')}`}</PageTitle>
      <PageHeader>
        <PageHeaderTitle className='inline-flex items-center gap-2'>
          <Button onClick={() => back(`/suppliers`)} size='icon' variant='none'>
            <ArrowLeftIcon />
          </Button>
          <PopoverInput
            className='mr-2'
            defaultValue={watch('name')}
            error={!!errors?.name}
            label={t('name')}
            onChange={(value) => setValue('name', value)}
            validation={Joi.string().required()}
          >
            {watch('name') || t('new supplier name')}
          </PopoverInput>
        </PageHeaderTitle>
        <WithLabel>
          <PopoverInput
            className='mr-2 h-fit'
            defaultValue={watch('taxIdentificationNumber')}
            error={!!errors?.taxIdentificationNumber}
            onChange={(value) => setValue('taxIdentificationNumber', value)}
            validation={Joi.string().required()}
          >
            {watch('taxIdentificationNumber') || t('new tax identification number')}
          </PopoverInput>
          <InputLabel>{t('tax identification number')}</InputLabel>
        </WithLabel>
        <WithLabel>
          <PopoverInput
            className='mr-2 h-fit'
            defaultValue={watch('identificationNumber')}
            error={!!errors?.identificationNumber}
            onChange={(value) => setValue('identificationNumber', value)}
            validation={Joi.string().required()}
          >
            {watch('identificationNumber') || t('new identification number')}
          </PopoverInput>
          <InputLabel>{t('identification number')}</InputLabel>
        </WithLabel>

        <div className='grow' />
        {isDirty && <Button onClick={saveSupplier}>{t('save')}</Button>}
      </PageHeader>
      <PageContent>
        <FormProvider
          {...{formState: {errors, ...restUseFormState}, register, resetField, setValue, watch, ...restUseFormActions}}
        >
          <SupplierDetailsData onSave={saveSupplier} />
          <div className='mx-6 flex items-center justify-between'>
            <div className='inline-flex items-center'>
              <Tabs onValueChange={(value) => setTab(value as SupplierTab)} value={tab} variant='menu'>
                <TabsList variant='menu'>
                  {hasPermission('update', 'purchases') && (
                    <TabsTrigger value='orders' variant='menu'>
                      {t('orders')}
                    </TabsTrigger>
                  )}
                  <TabsTrigger
                    badge={formatNumber(watch('files')?.length)}
                    disabled={!watch('id')}
                    value='files'
                    variant='menu'
                  >
                    {t('files')}
                  </TabsTrigger>
                </TabsList>
              </Tabs>
            </div>
            {tab === 'files' && <SupplierDetailsFilesButtons uploadFile={uploadSupplierFile} />}
          </div>
          {hasPermission('update', 'purchases') && tab === 'orders' && (
            <PurchasesListTable disableHeight preview supplierId={id} view={PurchasesView.all} />
          )}
          {tab === 'files' && <SupplierDetailsFilesTable deleteFile={deleteSupplierFile} />}
        </FormProvider>
      </PageContent>
    </Page>
  );
};

export default SupplierDetails;
