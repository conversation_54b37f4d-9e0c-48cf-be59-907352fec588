import {FC} from 'react';

import {useTranslations} from 'next-intl';
import {useFormContext} from 'react-hook-form';

import {
  Table,
  TableBody,
  TableContainer,
  TableHead,
  TableHeadActions,
  TableHeader,
  TableRow,
} from '@/components/ui/Table';
import {Supplier} from '@/types/sales';

import SupplierDetailsFilesTableRow from './SupplierDetailsFilesTableRow';

type Props = {
  deleteFile: (id: string) => void;
};

const SupplierDetailsFilesTable: FC<Props> = ({deleteFile}) => {
  const {watch} = useFormContext<Supplier>();
  const t = useTranslations();

  return (
    <TableContainer>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>{t('name')}</TableHead>
            <TableHead />
            <TableHeadActions />
          </TableRow>
        </TableHeader>
        <TableBody className='overflow-y-hidden' isValidating={!watch()}>
          {watch('files')?.map((file, index) => (
            <SupplierDetailsFilesTableRow deleteFile={deleteFile} file={file} key={`${file.id}-${index}`} />
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );
};

export default SupplierDetailsFilesTable;
