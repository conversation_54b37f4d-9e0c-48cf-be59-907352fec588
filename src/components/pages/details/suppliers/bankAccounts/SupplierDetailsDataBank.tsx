import {FC, useState} from 'react';

import {PlusIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';
import {useFormContext} from 'react-hook-form';

import {IconButton} from '@/components/ui/Button';
import {Customer} from '@/types/sales';

import SupplierDetailsDataBankRow from './SupplierDetailsDataBankRow';

type Props = {
  onSave: () => void;
};

const SupplierDetailsDataBank: FC<Props> = ({onSave}) => {
  const t = useTranslations();
  const [addEnabled, setAddEnabled] = useState(false);
  const {watch} = useFormContext<Customer>();

  return (
    <div className='flex flex-col gap-4'>
      <div className='text-base font-medium'>{t('bank accounts')}</div>
      {(watch('bankAccounts') || []).map((account, index) => (
        <SupplierDetailsDataBankRow account={account} index={index} key={`${account.name}-${index}`} onSave={onSave} />
      ))}
      {!addEnabled && (
        <IconButton className='self-start' icon={<PlusIcon />} onClick={() => setAddEnabled(true)}>
          {t('add bank account')}
        </IconButton>
      )}
      {addEnabled && <SupplierDetailsDataBankRow onCancel={() => setAddEnabled(false)} onSave={onSave} />}
    </div>
  );
};

export default SupplierDetailsDataBank;
