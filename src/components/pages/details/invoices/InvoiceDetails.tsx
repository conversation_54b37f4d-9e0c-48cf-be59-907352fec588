import {FC, useCallback} from 'react';

import {ArrowLeftIcon, CloudDownloadIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';

import {Button} from '@/components/ui/Button';
import {DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger} from '@/components/ui/DropdownMenu';
import {DocumentPreview} from '@/components/ui/special/DocumentPreview';
import {Page, PageContent, PageHeader, PageHeaderTitle, PageTitle} from '@/components/ui/special/Page';
import useInvoiceDocument from '@/hooks/documents/useInvoiceDocument';
import useHeight from '@/hooks/helpers/useHeight';
import {useRouter} from '@/hooks/helpers/useRouter';

type Props = {
  id: string;
};

const InvoiceDetails: FC<Props> = ({id}) => {
  const {invoice} = useInvoiceDocument(id);
  const {
    back,
    push,
    query: {isProforma},
  } = useRouter();
  const {elementRef} = useHeight();
  const t = useTranslations();

  const handleDownload = useCallback(
    (mediaType: string) => {
      push(`/api/invoices/${id}/details?mediaType=${mediaType}`);
    },
    [id, push],
  );

  return (
    <Page>
      <PageTitle>{t(isProforma ? 'proforma' : 'invoices')}</PageTitle>
      <PageHeader>
        <PageHeaderTitle className='inline-flex items-center gap-2'>
          <Button onClick={() => back('/invoices')} size='icon' variant='none'>
            <ArrowLeftIcon />
          </Button>
          {t(isProforma ? 'proforma' : 'invoice')}
        </PageHeaderTitle>
        <div className='grow' />
        <DropdownMenu>
          <DropdownMenuTrigger>
            <Button variant='secondary'>
              <CloudDownloadIcon /> {t('download')}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuItem onClick={() => handleDownload('application/pdf')}>{t('pdf')}</DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleDownload('application/factura-saga%2Bxml')}>
              {t('saga')}
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </PageHeader>
      <PageContent>
        <div className='flex' ref={elementRef}>
          <div className='flex size-full justify-center bg-gray-100 p-4'>
            <DocumentPreview content={invoice} />
          </div>
        </div>
      </PageContent>
    </Page>
  );
};

export default InvoiceDetails;
