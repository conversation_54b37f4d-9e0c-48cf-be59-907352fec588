import {FC, useEffect, useState} from 'react';

import axios from 'axios';
import {useAtom} from 'jotai';
import {useTranslations} from 'next-intl';

import {Page, PageContent, PageHeader, PageHeaderTitle, PageTitle} from '@/components/ui/special/Page';
import {Tabs, TabsList, TabsTrigger} from '@/components/ui/Tabs';
import useHeight from '@/hooks/helpers/useHeight';

import {ReportsTab, reportsTabAtom} from './reportsStore';

const Reports: FC = () => {
  const t = useTranslations();
  const [tab, setTab] = useAtom(reportsTabAtom);
  const {elementRef} = useHeight();
  const [link, setLink] = useState('');

  useEffect(() => {
    axios.get(`/api/reporting/${tab}/link`).then((response) => {
      setLink(response.data?.link);
    });
  }, [tab]);

  return (
    <Page>
      <PageTitle>{`${t(tab)} - ${t('reports')}`}</PageTitle>
      <PageHeader>
        <PageHeaderTitle className='mr-10'>{t('reports')}</PageHeaderTitle>
        <Tabs
          onValueChange={(newTab) => {
            setTab(newTab as ReportsTab);
            setLink('');
          }}
          value={tab}
          variant='menu'
        >
          <TabsList variant='menu'>
            <TabsTrigger value='overview' variant='menu'>
              {t('overview')}
            </TabsTrigger>
            <TabsTrigger value='production' variant='menu'>
              {t('production')}
            </TabsTrigger>
          </TabsList>
        </Tabs>
        <div className='grow' />
      </PageHeader>
      <PageContent>
        <iframe
          allowFullScreen
          className='size-full border-none'
          ref={elementRef}
          sandbox='allow-storage-access-by-user-activation allow-scripts allow-same-origin allow-popups allow-popups-to-escape-sandbox'
          src={link}
        ></iframe>
      </PageContent>
    </Page>
  );
};

export default Reports;
