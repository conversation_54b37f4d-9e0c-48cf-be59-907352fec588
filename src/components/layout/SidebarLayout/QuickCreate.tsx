import {ForwardedRef, forwardRef, useState} from 'react';

import {useAtomValue} from 'jotai';
import {PlusIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';

import {Button} from '@/components/ui/Button';
import {Popover, PopoverContent, PopoverTrigger} from '@/components/ui/Popover';
import {Link} from '@/components/ui/special/Link';
import {sidebarConfigAtom} from '@/store/ui';
import {classes} from '@/utils/common';

const QuickCreate = forwardRef((_, ref: ForwardedRef<HTMLDivElement>) => {
  const [open, setOpen] = useState(false);
  const {isOpened} = useAtomValue(sidebarConfigAtom);
  const t = useTranslations();

  return (
    <div className='group mt-auto' ref={ref}>
      <Popover onOpenChange={setOpen} open={open}>
        <PopoverTrigger asChild>
          <Button
            className={classes(
              'flex h-14 items-center justify-start gap-4 overflow-hidden bg-menu text-sm text-muted hover:bg-menu-foreground hover:text-background hover:no-underline',
              isOpened ? 'pl-6 ' : 'pl-4',
            )}
            onClick={() => {}}
            size='full'
            variant='text'
          >
            <div className='inline-flex size-9 shrink-0 items-center justify-center rounded-full bg-button-primary text-foreground ring-0 transition-colors focus-visible:outline-hidden group-hover:bg-button-primary-hovered'>
              <PlusIcon />
            </div>

            {isOpened && (
              <span aria-hidden='true' className='w-32 truncate text-left'>
                {t('quick create')}
              </span>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className='flex max-w-none flex-col items-center gap-4 p-4!' side='right'>
          <Button asChild className='px-4 text-sm' onClick={() => setOpen(false)} size='full'>
            <Link href='/sales/quotes/new'>{t('new quote')}</Link>
          </Button>
          <Button asChild className='px-4 text-sm' onClick={() => setOpen(false)} size='full'>
            <Link href='/sales/orders/new'>{t('new sale order')}</Link>
          </Button>
          <Button asChild className='px-4 text-sm' onClick={() => setOpen(false)} size='full'>
            <Link href='/purchases/wishlists/new'>{t('new purchase list')}</Link>
          </Button>
          <Button asChild className='px-4 text-sm' onClick={() => setOpen(false)} size='full'>
            <Link href='/manufacturing/orders?create=true'>{t('new manufacturing order')}</Link>
          </Button>
        </PopoverContent>
      </Popover>
    </div>
  );
});

QuickCreate.displayName = 'QuickCreate';

export default QuickCreate;
