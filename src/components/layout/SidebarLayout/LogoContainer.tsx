import {FC} from 'react';

import {useAtomValue} from 'jotai';

import Logo from '@/assets/Logo';
import {Config} from '@/constants/config';
import {sidebarConfigAtom} from '@/store/ui';
import {classes} from '@/utils/common';

const LogoContainer: FC = () => {
  const {isOpened} = useAtomValue(sidebarConfigAtom);

  return (
    <div
      className={classes(
        'flex h-[72px] shrink-0 select-none items-center justify-center border-b border-border-foreground text-sm font-semibold leading-6',
        isOpened ? 'mb-5' : 'mb-1',
      )}
    >
      <div className='flex shrink-0 items-center gap-5 text-background'>
        <Logo className='size-8 shrink-0' />
        {isOpened && <div className='uppercase'>{Config.AppName}</div>}
      </div>
    </div>
  );
};

export default LogoContainer;
