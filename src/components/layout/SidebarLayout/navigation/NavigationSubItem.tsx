import {FC, RefObject, useCallback, useEffect, useRef} from 'react';

import {filter} from 'lodash';
import {useTranslations} from 'next-intl';

import {Link} from '@/components/ui/special/Link';
import {useRouter} from '@/hooks/helpers/useRouter';
import {classes} from 'utils/common';

import {NavigationItemType} from './Navigation';

type Props = {
  item: NavigationItemType;
  itemRef: RefObject<HTMLLIElement>;
  onClick: () => void;
};

const NavigationSubItem: FC<Props> = ({item, itemRef, onClick}) => {
  const {asPath} = useRouter();
  const t = useTranslations();
  const ref = useRef<HTMLDivElement>(null);

  const isSubItemActive = useCallback(
    (subItem: NavigationItemType) =>
      (subItem.exclude ? false : asPath.startsWith(subItem.href)) ||
      filter(subItem.active || [], (active) => asPath === active).length > 0,
    [asPath],
  );

  useEffect(() => {
    if (itemRef.current && ref.current) {
      const rect = itemRef.current.getBoundingClientRect();
      ref.current.style.left = `${rect.width}px`;
      ref.current.style.top = `${rect.top}px`;
    }
  }, [itemRef]);

  return (
    <div className='absolute z-20 w-56 animate-fadeIn' ref={ref}>
      <div className='ml-2 flex flex-col gap-1 rounded-lg border border-border-foreground bg-menu p-2 shadow-lg'>
        <div className='mb-1 border-b border-border-foreground px-3 py-2 text-xs font-medium uppercase tracking-wider text-white'>
          {t(item.text as any)}
        </div>
        {item.subItems?.map((subItem, index) => (
          <Link
            className='group'
            href={subItem.href}
            key={subItem.text}
            onClick={onClick}
            style={{animationDelay: `${index * 50}ms`}}
          >
            <div
              className={classes(
                'truncate rounded-lg p-3 text-sm leading-6 group-hover:bg-menu-foreground',
                isSubItemActive(subItem)
                  ? 'bg-menu-foreground text-background'
                  : 'text-muted group-hover:text-background',
              )}
            >
              {t(subItem.text as any)}
            </div>
          </Link>
        ))}
      </div>
    </div>
  );
};

export default NavigationSubItem;
