import {FC, RefObject, useCallback, useEffect, useMemo, useRef, useState} from 'react';

import {useAtomValue} from 'jotai';
import {some} from 'lodash';
import {ChevronDownIcon, ChevronRightIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';

import {Link} from '@/components/ui/special/Link';
import {useHasPermission} from '@/hooks/helpers/useHasPermission';
import {useRouter} from '@/hooks/helpers/useRouter';
import {classes} from '@/utils/common';
import {sidebarConfigAtom} from 'store/ui';

import {NavigationItemType} from './Navigation';

type Props = {
  isActive: boolean;
  item: NavigationItemType;
  onItemSelect: (value: undefined | {item: NavigationItemType; ref: RefObject<HTMLLIElement>}) => void;
};

const NavigationItem: FC<Props> = ({isActive, item, onItemSelect}) => {
  const {isOpened} = useAtomValue(sidebarConfigAtom);
  const {asPath} = useRouter();
  const t = useTranslations();
  const [isSubmenuOpen, setIsSubmenuOpen] = useState<boolean | undefined>(undefined);
  const itemRef = useRef<HTMLLIElement>(null);
  const {hasPermission, isLoading} = useHasPermission();
  const hasSubItems = (item.subItems?.length || 0) > 0;

  const isItemActive = useMemo(() => {
    const directlyActive =
      (item.exclude ? some(item.active || [], (active) => asPath.startsWith(active)) : asPath.startsWith(item.href)) ||
      some(item.active || [], (active) => asPath === active);

    const hasActiveSubItem =
      hasSubItems &&
      item.subItems?.some(
        (subItem) =>
          (subItem.exclude
            ? some(subItem.active || [], (active) => asPath.startsWith(active))
            : asPath.startsWith(subItem.href)) || some(subItem.active || [], (active) => asPath === active),
      );

    return directlyActive || hasActiveSubItem;
  }, [item.exclude, item.href, item.active, asPath, hasSubItems, item.subItems]);

  const isSubItemActive = useCallback(
    (subItem: NavigationItemType) =>
      (subItem.exclude
        ? some(subItem.active || [], (active) => asPath.startsWith(active))
        : asPath.startsWith(subItem.href)) || some(subItem.active || [], (active) => asPath === active),
    [asPath],
  );

  useEffect(() => {
    setIsSubmenuOpen(undefined);
  }, [asPath]);

  if (isLoading) return null;

  return (
    <li className='relative' ref={itemRef}>
      <button
        className={classes(
          'group flex items-center justify-start rounded-md py-2 text-left text-sm font-semibold leading-6 transition-height duration-500 hover:bg-menu-foreground',
          isActive ? 'text-background' : 'text-muted hover:text-background',
          isOpened ? 'w-full px-2' : 'ml-2.5 w-fit rounded-lg pl-2',
          (isItemActive || isActive) && 'bg-menu-foreground text-background',
          hasSubItems ? 'pr-1' : 'pr-4',
        )}
        onClick={() => {
          if (!isOpened && hasSubItems) {
            onItemSelect({item, ref: itemRef as RefObject<HTMLLIElement>});
          } else if (!hasSubItems) {
            onItemSelect(undefined);
          }
        }}
      >
        {isOpened || !hasSubItems ? (
          <Link className='flex w-full items-center' href={item.href}>
            {item.icon && (
              <item.icon
                aria-hidden='true'
                className={classes(
                  'shrink-0 text-background opacity-60 group-hover:opacity-100',
                  (isItemActive || isActive) && 'opacity-100',
                  isOpened && 'mr-5',
                )}
              />
            )}
            {isOpened && <div className='max-w-[100px] truncate'>{t(item.text as any)}</div>}
            {!isOpened && hasSubItems && (
              <ChevronRightIcon aria-hidden='true' className='size-3 shrink-0' strokeWidth={3} />
            )}
          </Link>
        ) : (
          <div className='flex w-full items-center'>
            {item.icon && (
              <item.icon
                aria-hidden='true'
                className={classes(
                  'shrink-0 text-background opacity-60 group-hover:opacity-100',
                  (isItemActive || isActive) && 'opacity-100',
                )}
              />
            )}
            {!isOpened && hasSubItems && (
              <ChevronRightIcon aria-hidden='true' className='size-3 shrink-0' strokeWidth={3} />
            )}
          </div>
        )}
        {hasSubItems && isOpened && (
          <div onClick={() => setIsSubmenuOpen((prev) => (prev === undefined ? !isItemActive : !prev))}>
            <ChevronDownIcon
              aria-hidden='true'
              className={classes(
                'ml-auto shrink-0 transition-transform duration-200',
                (isSubmenuOpen === undefined ? isItemActive : isSubmenuOpen) && 'rotate-180',
              )}
            />
          </div>
        )}
      </button>
      {hasSubItems && (
        <ul
          className={classes(
            'mt-1 overflow-hidden pl-2 transition-max-height duration-500 ease-in-out',
            (isSubmenuOpen === undefined ? isItemActive : isSubmenuOpen) && isOpened ? 'max-h-screen' : 'max-h-0',
          )}
        >
          {item.subItems?.map(
            (subItem) =>
              (!subItem.requiredPermissions ||
                subItem.requiredPermissions.every((p) => hasPermission(p.action, p.subject))) && (
                <Link
                  className={classes(
                    'group block cursor-pointer border-l py-0.5',
                    isSubItemActive(subItem) ? 'border-background' : 'border-border-foreground hover:border-background',
                    isOpened ? 'ml-3' : 'ml-5 h-12',
                  )}
                  href={subItem.href}
                  key={subItem.text}
                >
                  <div
                    className={classes(
                      'ml-5 block rounded-lg py-2 pl-3 pr-2 text-sm leading-6 group-hover:bg-menu-foreground',
                      isSubItemActive(subItem) ? 'text-background' : 'text-muted group-hover:text-background',
                      isSubItemActive(subItem) && isOpened && 'bg-menu-foreground',
                    )}
                  >
                    {isOpened && <div className='truncate'>{t(subItem.text as any)}</div>}
                  </div>
                </Link>
              ),
          )}
        </ul>
      )}
    </li>
  );
};

export default NavigationItem;
