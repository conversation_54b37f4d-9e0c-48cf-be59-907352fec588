import {FC, ForwardedRef, forwardRef, RefObject, useEffect, useState} from 'react';

import {useAtomValue} from 'jotai';
import {
  BoxesIcon,
  ChartNoAxesCombinedIcon,
  CogIcon,
  PackageOpenIcon,
  PieChartIcon,
  ReceiptIcon,
  ShoppingCartIcon,
  SlidersVerticalIcon,
  UserCogIcon,
  UsersIcon,
} from 'lucide-react';

import {useHasPermission} from '@/hooks/helpers/useHasPermission';
import {sidebarConfigAtom} from '@/store/ui';
import {IconProps} from '@/types/icon';
import {Permission} from '@/types/permissions';
import {classes} from '@/utils/common';

import NavigationItem from './NavigationItem';
import NavigationSubItem from './NavigationSubItem';

export type NavigationItemType = {
  active?: string[];
  exclude?: boolean;
  href: string;
  icon?: FC<IconProps>;
  requiredPermissions?: Permission[];
  subItems?: NavigationItemType[];
  text: string;
};

const Navigation = forwardRef(({}, ref: ForwardedRef<HTMLUListElement>) => {
  const {isOpened} = useAtomValue(sidebarConfigAtom);
  const {hasPermission, isLoading} = useHasPermission();
  const [openSubmenuItem, setOpenSubmenuItem] = useState<
    undefined | {item: NavigationItemType; ref: RefObject<HTMLLIElement>}
  >(undefined);
  const [activeParentItem, setActiveParentItem] = useState<string | undefined>(undefined);

  useEffect(() => {
    setOpenSubmenuItem(undefined);
    setActiveParentItem(undefined);
  }, [isOpened]);

  const items: NavigationItemType[] = [
    {
      active: ['/'],
      href: '/sales',
      icon: ChartNoAxesCombinedIcon,
      requiredPermissions: [{action: 'read', subject: 'sales'}],
      subItems: [
        {
          active: ['/', '/sales', '/sales/orders'],
          href: '/sales/orders',
          text: 'orders',
        },
        {href: '/sales/quotes', text: 'quotes'},
      ],
      text: 'sales',
    },
    {
      href: '/purchases',
      icon: ShoppingCartIcon,
      requiredPermissions: [{action: 'read', subject: 'purchases'}],
      text: 'purchases',
    },
    {
      href: '/manufacturing',
      icon: CogIcon,
      requiredPermissions: [{action: 'read', subject: 'manufacturing'}],
      subItems: [
        {
          active: ['/manufacturing'],
          href: '/manufacturing/orders',
          text: 'manufacturing',
        },
        {
          href: '/manufacturing/services',
          text: 'services',
        },
        {
          href: '/manufacturing/planning',
          text: 'task management',
        },
        {
          href: '/manufacturing/tasks',
          text: 'my tasks',
        },
      ],
      text: 'execution',
    },
    hasPermission('financial', 'inventory')
      ? {
          href: '/inventory',
          icon: BoxesIcon,
          requiredPermissions: [{action: 'read', subject: 'inventory'}],
          subItems: [
            {
              active: ['/inventory', '/inventory/items'],
              href: '/inventory/items',
              text: 'items',
            },
            {
              href: '/inventory/adjustments',
              text: 'adjustments',
            },
            {
              href: '/inventory/receptions',
              text: 'receptions',
            },
            {
              href: '/inventory/consumptions',
              requiredPermissions: [{action: 'read', subject: 'manufacturing'}],
              text: 'consumptions',
            },
            {
              href: '/inventory/accompanying',
              text: 'goods accompanying notes',
            },
          ],
          text: 'inventory',
        }
      : {active: ['/inventory', '/inventory/items'], href: '/inventory/items', icon: BoxesIcon, text: 'items'},
    {
      href: '/customers',
      icon: UsersIcon,
      requiredPermissions: [{action: 'read', subject: 'customers'}],
      text: 'customers',
    },
    {
      href: '/suppliers',
      icon: PackageOpenIcon,
      requiredPermissions: [{action: 'read', subject: 'suppliers'}],
      text: 'suppliers',
    },
    {
      href: '/employees',
      icon: UserCogIcon,
      requiredPermissions: [{action: 'read', subject: 'employees'}],
      text: 'employees',
    },
    {
      href: '/invoices',
      icon: ReceiptIcon,
      requiredPermissions: [{action: 'financial', subject: 'invoices'}],
      text: 'invoices',
    },
    {
      href: '/reports',
      icon: PieChartIcon,
      requiredPermissions: [{action: 'financial', subject: 'reports'}],
      text: 'reports',
    },
    {
      href: '/settings',
      icon: SlidersVerticalIcon,
      requiredPermissions: [{action: 'update', subject: 'settings'}],
      text: 'settings',
    },
  ];

  if (isLoading) return null;

  return (
    <>
      <ul className={classes('space-y-1 overflow-y-auto', isOpened ? 'px-4' : '')} ref={ref} role='navigation'>
        {items.map(
          (item) =>
            (!item.requiredPermissions ||
              item.requiredPermissions.every((p) => hasPermission(p.action, p.subject))) && (
              <NavigationItem
                isActive={openSubmenuItem?.item?.text === item.text || activeParentItem === item.text}
                item={item}
                key={item.text}
                onItemSelect={(selected) => {
                  setOpenSubmenuItem(undefined);
                  setActiveParentItem(undefined);

                  if (selected) {
                    setOpenSubmenuItem(selected);
                    setActiveParentItem(item.subItems?.length ? item.text : undefined);
                  }
                }}
              />
            ),
        )}
      </ul>
      {!isOpened && openSubmenuItem && openSubmenuItem.item.subItems?.length && (
        <NavigationSubItem
          item={openSubmenuItem.item}
          itemRef={openSubmenuItem.ref}
          onClick={() => setOpenSubmenuItem(undefined)}
        />
      )}
    </>
  );
});

Navigation.displayName = 'Navigation';

export default Navigation;
