import {FC, useEffect} from 'react';

import {useAtom, useAtomValue} from 'jotai';
import {ArrowLeftIcon, LogOutIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';

import {Button} from '@/components/ui/Button';
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from '@/components/ui/Select';
import {useRouter} from '@/hooks/helpers/useRouter';
import useNotifications from '@/hooks/useNotifications';
import useLoading from 'hooks/helpers/useLoading';
import {notificationsAtom, notificationsConfigAtom} from 'store/ui';
import {classes} from 'utils/common';

import NotificationItem from './NotificationItem';

const NotificationsPanel: FC = () => {
  const [notificationsConfig, setNotificationsConfig] = useAtom(notificationsConfigAtom);
  const isLoading = useLoading();
  const notifications = useAtomValue(notificationsAtom);
  const t = useTranslations();
  const {push} = useRouter();
  const {refreshNotifications} = useNotifications();

  useEffect(() => {
    if (notificationsConfig.isOpened) refreshNotifications();
  }, [notificationsConfig.isOpened, refreshNotifications]);

  if (isLoading) return null;

  return (
    <div
      className={classes(
        'flex h-full shrink-0 flex-col overflow-x-hidden border-gray-300 transition-all duration-300',
        notificationsConfig.isOpened ? 'w-80 border-r' : 'w-0',
      )}
    >
      <div className='flex shrink-0 h-[72px] items-center justify-between border-b border-border px-2'>
        <Button onClick={() => setNotificationsConfig({...notificationsConfig, isOpened: false})} variant='text'>
          <ArrowLeftIcon className='shrink-0' />
          {t('notifications')}
        </Button>
        <Select
          onValueChange={(value) => setNotificationsConfig({...notificationsConfig, currentFilter: value})}
          value={notificationsConfig.currentFilter}
        >
          <SelectTrigger className='w-fit' variant='none'>
            <SelectValue>{t(notificationsConfig.currentFilter as any, {isFemale: 'true'})}</SelectValue>
          </SelectTrigger>
          <SelectContent>
            {[
              {id: 'all', text: 'all'},
              {id: 'unresolved', text: 'unresolved'},
              {id: 'resolved', text: 'resolved'},
            ].map(({id, text}) => (
              <SelectItem key={id} value={id}>
                {t(text as any, {isFemale: 'true'})}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
      <div className='grow overflow-y-auto overflow-x-hidden'>
        <div className='flex flex-col divide-y border-b'>
          {notifications.map((notification, index) => (
            <NotificationItem key={`${index}-${notification.id}`} notification={notification} />
          ))}
          {!notifications.length && (
            <div className='line-clamp-2 p-6 text-center'>{t('your notifications list is currently empty')}</div>
          )}
        </div>
      </div>
      <div className='relative h-14 border-t border-border'>
        <Button
          className='size-full justify-start rounded-none pl-7 font-medium'
          onClick={() => push('/api/auth/logout')}
          size='full'
          variant='secondary'
        >
          <LogOutIcon />
          <div>{t('logout')}</div>
        </Button>
      </div>
    </div>
  );
};

export default NotificationsPanel;
