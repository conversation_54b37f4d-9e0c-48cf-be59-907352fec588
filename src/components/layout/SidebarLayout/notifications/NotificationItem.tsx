import {FC} from 'react';

import {useSet<PERSON><PERSON>} from 'jotai';
import {ChevronRightIcon, CircleAlertIcon, CircleCheckIcon} from 'lucide-react';

import {Link} from '@/components/ui/special/Link';
import useNotificationActions from '@/hooks/useNotificationActions';
import {Badge, TimeAgo} from 'oldcomponents';
import {selectedNotificationsAtom} from 'store/ui';
import {NotificationMeta} from 'types/interface';
import {classes} from 'utils/common';

type Props = {
  notification: NotificationMeta;
};

const NotificationItem: FC<Props> = ({notification}) => {
  const setSelectedNotification = useSetAtom(selectedNotificationsAtom);
  const {markNotificationRead} = useNotificationActions();

  if (!notification.href) return null;

  return (
    <Link
      className='flex items-center justify-between p-4 font-medium hover:bg-input'
      href={notification.href || ''}
      onClick={() => {
        setSelectedNotification(notification);
        markNotificationRead(notification.id);
      }}
    >
      <div className='flex grow flex-col'>
        <div className='flex gap-3'>
          <Badge
            anchorOrigin='bottom-right'
            element={
              notification.resolvedBy ? (
                <CircleCheckIcon className='rounded-full bg-green size-4 text-background' />
              ) : (
                <CircleAlertIcon className='rounded-full bg-red size-4 text-background' />
              )
            }
            overlap='circular'
          >
            {notification.icon}
          </Badge>
          <div className='flex flex-col gap-1'>
            <div className={classes('text-sm', notification.resolvedBy && 'opacity-40')}>{notification.message}</div>
            <div
              className={classes(
                'flex items-center gap-1 text-xs',
                notification.resolvedBy ? 'opacity-40' : 'opacity-60',
              )}
            >
              <div className='flex items-center gap-1 text-xs opacity-40'>
                <TimeAgo date={notification.createTime} useShort />
                <div>&bull;</div>
                {notification.triggeredBy}
              </div>
            </div>
          </div>
        </div>
      </div>
      <ChevronRightIcon className='shrink-0' />
    </Link>
  );
};

export default NotificationItem;
