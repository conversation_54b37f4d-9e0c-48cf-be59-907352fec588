import {FC} from 'react';

import {useAtomValue} from 'jotai';
import {CircleAlertIcon, CircleCheckIcon} from 'lucide-react';
import {CheckIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';

import {useRouter} from '@/hooks/helpers/useRouter';
import useNotificationActions from '@/hooks/useNotificationActions';
import {NotificationType} from '@/types/interface';
import {Badge, PrimaryButton, TimeAgo} from 'oldcomponents';
import {selectedNotificationsAtom} from 'store/ui';

const NotificationHeader: FC = () => {
  const {asPath} = useRouter();
  const notification = useAtomValue(selectedNotificationsAtom);
  const {resolveNotification} = useNotificationActions();
  const t = useTranslations();

  if (!notification || !notification.relatedId || !asPath.includes(notification.relatedId)) return null;

  return (
    <div className='flex items-center justify-between border-b p-4 font-medium'>
      <div className='flex items-center gap-3'>
        <Badge
          anchorOrigin='bottom-right'
          element={
            notification.resolvedBy ? (
              <CircleCheckIcon className='rounded-full bg-green size-4 text-background' />
            ) : (
              <CircleAlertIcon className='rounded-full bg-red size-4 text-background' />
            )
          }
          overlap='circular'
        >
          {notification.icon}
        </Badge>

        <div className='flex flex-col'>
          <div className='mb-1 flex items-center text-sm'>{notification.message}</div>
          <div className='flex items-center gap-1 text-xs opacity-40'>
            <TimeAgo date={notification.createTime} useShort />
            <div>&bull;</div>
            {t('viewed by count', {count: (notification.readBy || []).length})}
            <div>&bull;</div>
            {notification.triggeredBy}
          </div>
        </div>
      </div>
      {!notification.resolvedBy && (
        <PrimaryButton Icon={CheckIcon} onClick={() => resolveNotification(notification)}>
          {t(notification.type === NotificationType.COMMENT ? 'mark as read' : 'resolve issue')}
        </PrimaryButton>
      )}
    </div>
  );
};

export default NotificationHeader;
