import {ForwardedRef, forwardRef, useMemo} from 'react';

import {useUser} from '@auth0/nextjs-auth0/client';
import {useAtom, useAtomValue, useSetAtom} from 'jotai';

import SidebarCollapse from '@/components/layout/SidebarLayout/SidebarCollapse';
import {Avatar, AvatarBadge, AvatarFallback, AvatarImage} from '@/components/ui/Avatar';
import {Button} from '@/components/ui/Button';
import useAccount from '@/hooks/useAccount';
import {notificationsConfigAtom, selectedNotificationsAtom, sidebarConfigAtom} from 'store/ui';
import {getBgColor} from 'utils/colors';
import {classes, getInitials} from 'utils/common';

const NotificationsButton = forwardRef((_, ref: ForwardedRef<HTMLDivElement>) => {
  const {user} = useUser();
  const {isOpened} = useAtomValue(sidebarConfigAtom);
  const [notificationsConfig, setNotificationsConfig] = useAtom(notificationsConfigAtom);
  const setSelectedNotification = useSetAtom(selectedNotificationsAtom);
  const {account} = useAccount();

  const userDetails = useMemo(
    () => ({
      color: getBgColor(user?.name, {dark: true}),
      initials: getInitials(user?.name),
    }),
    [user?.name],
  );

  return (
    <div className={classes('relative mt-auto border-t border-border-foreground')} ref={ref}>
      <SidebarCollapse className={classes('transition-opacity', notificationsConfig.isOpened && 'opacity-0')} />
      <Button
        className={classes(
          'flex h-14 items-center justify-start gap-4 overflow-hidden bg-menu text-sm text-muted hover:bg-menu-foreground hover:text-background hover:no-underline',
          isOpened ? 'pl-6 ' : 'pl-4',
        )}
        onClick={() => {
          setNotificationsConfig({...notificationsConfig, isOpened: !notificationsConfig.isOpened, unread: false});
          setSelectedNotification(undefined);
        }}
        size='full'
        variant='text'
      >
        {userDetails.color && userDetails.initials && (
          <>
            <AvatarBadge
              badge={notificationsConfig.count ? notificationsConfig.count.toString() : ''}
              pulse={notificationsConfig.unread}
            >
              <Avatar>
                <AvatarImage src={user?.picture || undefined} />
                <AvatarFallback className={userDetails.color}>{userDetails.initials}</AvatarFallback>
              </Avatar>
            </AvatarBadge>

            {isOpened && (
              <div className='flex flex-col'>
                <span aria-hidden='true' className='w-32 truncate text-left'>
                  {user?.name}
                </span>
                <span aria-hidden='true' className='w-32 truncate text-left'>
                  {account?.name}
                </span>
              </div>
            )}
          </>
        )}
      </Button>
    </div>
  );
});

NotificationsButton.displayName = 'NotificationsButton';

export default NotificationsButton;
