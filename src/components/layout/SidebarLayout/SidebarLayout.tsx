import {FC, ReactNode} from 'react';

import NotificationHeader from '@/components/layout/SidebarLayout/notifications/NotificationHeader';
import useNotifications from '@/hooks/useNotifications';

import LeaveConfirmModal from '../LeaveConfirmModal';
import NotificationsPanel from './notifications/NotificationsPanel';
import Sidebar from './Sidebar';

type Props = {
  children: ReactNode;
};

const SidebarLayout: FC<Props> = ({children}) => {
  useNotifications();

  return (
    <div className='flex h-screen'>
      <Sidebar />
      <NotificationsPanel />
      <div className='flex grow flex-col overflow-x-auto'>
        <NotificationHeader />
        {children}
      </div>
      <LeaveConfirmModal />
    </div>
  );
};

export default SidebarLayout;
