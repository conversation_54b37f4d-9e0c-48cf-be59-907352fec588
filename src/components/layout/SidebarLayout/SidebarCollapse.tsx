import {FC} from 'react';

import {useAtom, useAtomValue} from 'jotai';
import {ChevronsLeftIcon, ChevronsRightIcon} from 'lucide-react';

import {notificationsConfigAtom, sidebarConfigAtom} from 'store/ui';
import {classes} from 'utils/common';

type Props = {
  className?: string;
};

const SidebarCollapse: FC<Props> = ({className}) => {
  const [{isOpened}, setUi] = useAtom(sidebarConfigAtom);
  const {isOpened: notificationsIsOpen} = useAtomValue(notificationsConfigAtom);

  return (
    <div
      className={classes(
        'absolute -top-3 z-10 size-6 rounded-full border-2 border-background bg-button-primary hover:bg-button-primary-hovered',
        isOpened ? '-right-3' : '-right-3.5 ',
        className,
      )}
    >
      <button
        className='flex size-full items-center justify-center rounded-full'
        disabled={notificationsIsOpen}
        onClick={() => setUi((prev) => ({...prev, isOpened: !prev.isOpened}))}
      >
        {isOpened && <ChevronsLeftIcon className='size-4' />}
        {!isOpened && <ChevronsRightIcon className='size-4' />}
      </button>
    </div>
  );
};

export default SidebarCollapse;
