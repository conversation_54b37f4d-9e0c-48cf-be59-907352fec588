import {FC} from 'react';

import {useAtomValue} from 'jotai';

import {useHasPermission} from '@/hooks/helpers/useHasPermission';
import useHeight from '@/hooks/helpers/useHeight';
import useLoading from '@/hooks/helpers/useLoading';
import {sidebarConfigAtom} from 'store/ui';
import {classes} from 'utils/common';

import LogoContainer from './LogoContainer';
import Navigation from './navigation/Navigation';
import NotificationsButton from './notifications/NotificationsButton';
import QuickCreate from './QuickCreate';

const Sidebar: FC = () => {
  const {isOpened} = useAtomValue(sidebarConfigAtom);
  const isLoading = useLoading();
  const {isLoading: permissionIsLoading} = useHasPermission();

  const {elementRef, targetElementRef} = useHeight({dependencies: [isOpened, permissionIsLoading]});

  if (isLoading || permissionIsLoading) return null;

  return (
    <aside
      className={classes(
        'flex h-screen shrink-0 flex-col bg-menu transition-width duration-500',
        isOpened ? 'w-52' : 'w-16',
      )}
    >
      <LogoContainer />
      <Navigation ref={elementRef} />
      <QuickCreate ref={targetElementRef} />
      <NotificationsButton />
    </aside>
  );
};

export default Sidebar;
