import {useCallback} from 'react';

import {useAtom, useSetAtom} from 'jotai';
import {TriangleAlertIcon} from 'lucide-react';
import {useRouter} from 'next/router';
import {useTranslations} from 'next-intl';

import {Button} from '@/components/ui/Button';
import {Sheet, SheetContent, SheetFooter, SheetOverlay, SheetPage, SheetTitle} from '@/components/ui/Sheet';
import {isNavigatingAtom, leaveConfirmModalOpenAtom, pendingUrlAtom, saveCallbackAtom} from '@/store/ui';

const LeaveConfirmModal = () => {
  const t = useTranslations();
  const router = useRouter();
  const [isModalOpen, setIsModalOpen] = useAtom(leaveConfirmModalOpenAtom);
  const [pendingUrl, setPendingUrl] = useAtom(pendingUrlAtom);
  const [onSave, setOnSave] = useAtom(saveCallbackAtom);
  const setIsNavigating = useSetAtom(isNavigatingAtom);

  const handleCancel = useCallback(() => {
    setIsModalOpen(false);
    setPendingUrl(null);
    setOnSave(null);
  }, [setIsModalOpen, setOnSave, setPendingUrl]);

  const handleConfirm = useCallback(() => {
    setIsModalOpen(false);
    const urlToNavigate = pendingUrl;
    setPendingUrl(null);
    setOnSave(null);

    if (urlToNavigate) {
      setIsNavigating(true);
      setTimeout(() => {
        router.push(urlToNavigate);
      }, 0);
    }
  }, [pendingUrl, router, setIsModalOpen, setIsNavigating, setOnSave, setPendingUrl]);

  const handleSaveAndContinue = useCallback(async () => {
    const urlToNavigate = pendingUrl;

    if (onSave && urlToNavigate) {
      setIsNavigating(true);

      await onSave()
        .then(() => {
          setIsModalOpen(false);
          setTimeout(() => {
            router.push(urlToNavigate);
          }, 0);
        })
        .catch(() => {
          setIsNavigating(false);
          handleCancel();
        })
        .finally(() => {
          setPendingUrl(null);
          setOnSave(null);
        });
    }
  }, [handleCancel, onSave, pendingUrl, router, setIsModalOpen, setIsNavigating, setOnSave, setPendingUrl]);

  return (
    <Sheet onOpenChange={(open) => !open && handleCancel()} open={isModalOpen}>
      <SheetOverlay />
      <SheetPage className='min-h-fit' side='middle' size='md'>
        <SheetTitle className='flex items-center gap-2'>
          <TriangleAlertIcon className='text-dot-orange' /> {t('unsaved changes')}
        </SheetTitle>
        <SheetContent className='mx-6 text-sm'>
          {t('you have unsaved changes')}
          <div>{t('if you leave now, your changes will be lost')}</div>
          <div className='mt-2'>{t('are you sure you want to leave this page?')}</div>
        </SheetContent>
        <SheetFooter className='mx-6'>
          <div className='flex items-center justify-end gap-2 flex-wrap'>
            <Button onClick={handleCancel} variant='secondary'>
              {t('stay')}
            </Button>
            <Button onClick={handleConfirm} variant='destructive'>
              {t('leave')}
            </Button>
            {onSave && (
              <Button onClick={handleSaveAndContinue} variant='success'>
                {t('save before leaving')}
              </Button>
            )}
          </div>
        </SheetFooter>
      </SheetPage>
    </Sheet>
  );
};

export default LeaveConfirmModal;
