import {FC} from 'react';

import {IconProps} from 'types/icon';

const SettingsIcon: FC<IconProps> = ({className}) => (
  <svg className={className} fill='none' height='20' viewBox='0 0 20 20' width='20' xmlns='http://www.w3.org/2000/svg'>
    <path
      d='M3.33301 8.33341C3.33301 8.77544 3.5086 9.19937 3.82116 9.51193C4.13372 9.82449 4.55765 10.0001 4.99967 10.0001C5.4417 10.0001 5.86563 9.82449 6.17819 9.51193C6.49075 9.19937 6.66634 8.77544 6.66634 8.33341C6.66634 7.89139 6.49075 7.46746 6.17819 7.1549C5.86563 6.84234 5.4417 6.66675 4.99967 6.66675C4.55765 6.66675 4.13372 6.84234 3.82116 7.1549C3.5086 7.46746 3.33301 7.89139 3.33301 8.33341Z'
      stroke='currentColor'
      strokeLinecap='round'
      strokeLinejoin='round'
      strokeWidth='1.5'
    />
    <path d='M5 3.33325V6.66659' stroke='currentColor' strokeLinecap='round' strokeLinejoin='round' strokeWidth='1.5' />
    <path d='M5 10V16.6667' stroke='currentColor' strokeLinecap='round' strokeLinejoin='round' strokeWidth='1.5' />
    <path
      d='M8.33301 13.3334C8.33301 13.7754 8.5086 14.1994 8.82116 14.5119C9.13372 14.8245 9.55765 15.0001 9.99967 15.0001C10.4417 15.0001 10.8656 14.8245 11.1782 14.5119C11.4907 14.1994 11.6663 13.7754 11.6663 13.3334C11.6663 12.8914 11.4907 12.4675 11.1782 12.1549C10.8656 11.8423 10.4417 11.6667 9.99967 11.6667C9.55765 11.6667 9.13372 11.8423 8.82116 12.1549C8.5086 12.4675 8.33301 12.8914 8.33301 13.3334Z'
      stroke='currentColor'
      strokeLinecap='round'
      strokeLinejoin='round'
      strokeWidth='1.5'
    />
    <path
      d='M10 3.33325V11.6666'
      stroke='currentColor'
      strokeLinecap='round'
      strokeLinejoin='round'
      strokeWidth='1.5'
    />
    <path d='M10 15V16.6667' stroke='currentColor' strokeLinecap='round' strokeLinejoin='round' strokeWidth='1.5' />
    <path
      d='M13.333 5.83341C13.333 6.27544 13.5086 6.69937 13.8212 7.01193C14.1337 7.32449 14.5576 7.50008 14.9997 7.50008C15.4417 7.50008 15.8656 7.32449 16.1782 7.01193C16.4907 6.69937 16.6663 6.27544 16.6663 5.83341C16.6663 5.39139 16.4907 4.96746 16.1782 4.6549C15.8656 4.34234 15.4417 4.16675 14.9997 4.16675C14.5576 4.16675 14.1337 4.34234 13.8212 4.6549C13.5086 4.96746 13.333 5.39139 13.333 5.83341Z'
      stroke='currentColor'
      strokeLinecap='round'
      strokeLinejoin='round'
      strokeWidth='1.5'
    />
    <path
      d='M15 3.33325V4.16659'
      stroke='currentColor'
      strokeLinecap='round'
      strokeLinejoin='round'
      strokeWidth='1.5'
    />
    <path d='M15 7.5V16.6667' stroke='currentColor' strokeLinecap='round' strokeLinejoin='round' strokeWidth='1.5' />
  </svg>
);
export default SettingsIcon;
