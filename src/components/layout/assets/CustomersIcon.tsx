import {FC} from 'react';

import {IconProps} from 'types/icon';

const CustomersIcon: FC<IconProps> = ({className}) => (
  <svg className={className} fill='none' height='20' viewBox='0 0 20 20' width='20' xmlns='http://www.w3.org/2000/svg'>
    <path
      d='M14.1663 17.5V15.8333C14.1663 13.9924 12.674 12.5 10.833 12.5H4.16634C2.32539 12.5 0.833008 13.9924 0.833008 15.8333V17.5'
      fill='none'
      stroke='currentColor'
      strokeLinecap='round'
      strokeLinejoin='round'
      strokeWidth='1.5'
    />
    <path
      clipRule='evenodd'
      d='M7.50033 9.16667C9.34127 9.16667 10.8337 7.67428 10.8337 5.83333C10.8337 3.99238 9.34127 2.5 7.50033 2.5C5.65938 2.5 4.16699 3.99238 4.16699 5.83333C4.16699 7.67428 5.65938 9.16667 7.50033 9.16667Z'
      fill='none'
      fillRule='evenodd'
      stroke='currentColor'
      strokeLinecap='round'
      strokeLinejoin='round'
      strokeWidth='1.5'
    />
    <path
      d='M19.167 17.5001V15.8334C19.1659 14.3143 18.1378 12.9882 16.667 12.6084'
      fill='none'
      stroke='currentColor'
      strokeLinecap='round'
      strokeLinejoin='round'
      strokeWidth='1.5'
    />
    <path
      d='M13.333 2.6084C14.8079 2.98604 15.8395 4.31506 15.8395 5.83757C15.8395 7.36007 14.8079 8.68909 13.333 9.06673'
      fill='none'
      stroke='currentColor'
      strokeLinecap='round'
      strokeLinejoin='round'
      strokeWidth='1.5'
    />
  </svg>
);
export default CustomersIcon;
