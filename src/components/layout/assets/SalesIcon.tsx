import {FC} from 'react';

import {IconProps} from 'types/icon';

const SalesIcon: FC<IconProps> = ({className}) => (
  <svg className={className} fill='none' height='20' viewBox='0 0 20 20' width='20' xmlns='http://www.w3.org/2000/svg'>
    <mask fill='white' id='path-1-inside-1_2483_6979'>
      <rect height='5' rx='1' width='6' x='2' y='14' />
    </mask>
    <rect
      fill='none'
      height='5'
      mask='url(#path-1-inside-1_2483_6979)'
      rx='1'
      stroke='currentColor'
      strokeWidth='3'
      width='6'
      x='2'
      y='14'
    />
    <path
      d='M8 11.75H12C12.1381 11.75 12.25 11.8619 12.25 12V18.25H7.75V12C7.75 11.8619 7.86193 11.75 8 11.75Z'
      fill='none'
      stroke='currentColor'
      strokeWidth='1.5'
    />
    <mask fill='white' id='path-3-inside-2_2483_6979'>
      <rect height='11' rx='1' width='6' x='12' y='8' />
    </mask>
    <rect
      fill='none'
      height='11'
      mask='url(#path-3-inside-2_2483_6979)'
      rx='1'
      stroke='currentColor'
      strokeWidth='3'
      width='6'
      x='12'
      y='8'
    />
    <path
      d='M4 9.5L4.74545 9.27063C8.50953 8.11245 11.8836 5.94347 14.5 3V3'
      stroke='currentColor'
      strokeLinecap='round'
      strokeLinejoin='round'
      strokeWidth='1.5'
    />
    <path d='M12 2H15.5V5.5' stroke='currentColor' strokeLinecap='round' strokeLinejoin='round' strokeWidth='1.5' />
  </svg>
);
export default SalesIcon;
