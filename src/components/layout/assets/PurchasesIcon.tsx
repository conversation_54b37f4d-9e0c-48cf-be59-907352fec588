import {FC} from 'react';

import {IconProps} from 'types/icon';

const NavSales: FC<IconProps> = ({className}) => (
  <svg className={className} fill='none' height='20' viewBox='0 0 20 20' width='20' xmlns='http://www.w3.org/2000/svg'>
    <path
      clipRule='evenodd'
      d='M6.66667 19.1668C7.58714 19.1668 8.33333 18.4206 8.33333 17.5002C8.33333 16.5797 7.58714 15.8335 6.66667 15.8335C5.74619 15.8335 5 16.5797 5 17.5002C5 18.4206 5.74619 19.1668 6.66667 19.1668Z'
      fill='none'
      fillRule='evenodd'
      stroke='currentColor'
      strokeLinecap='round'
      strokeLinejoin='round'
      strokeWidth='1.5'
    />
    <path
      clipRule='evenodd'
      d='M16.6667 19.1668C17.5871 19.1668 18.3333 18.4206 18.3333 17.5002C18.3333 16.5797 17.5871 15.8335 16.6667 15.8335C15.7462 15.8335 15 16.5797 15 17.5002C15 18.4206 15.7462 19.1668 16.6667 19.1668Z'
      fill='none'
      fillRule='evenodd'
      stroke='currentColor'
      strokeLinecap='round'
      strokeLinejoin='round'
      strokeWidth='1.5'
    />
    <path
      d='M4.72467 5.00016H19.1663L17.7663 11.9918C17.6091 12.7837 16.9069 13.349 16.0997 13.3335H7.29134C6.44924 13.3406 5.73412 12.7185 5.62467 11.8835L4.35801 2.2835C4.24936 1.45483 3.54377 0.834762 2.70801 0.833496H0.833008'
      fill='none'
      stroke='currentColor'
      strokeLinecap='round'
      strokeLinejoin='round'
      strokeWidth='1.5'
    />
  </svg>
);
export default NavSales;
