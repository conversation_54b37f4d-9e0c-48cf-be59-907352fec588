import {FC} from 'react';

import {IconProps} from 'types/icon';
import {classes} from 'utils/common';

const SuppliersIcon: FC<IconProps> = ({className}) => (
  <svg
    className={classes('scale-[1.4]', className)}
    fill='none'
    height='20'
    viewBox='0 0 20 20'
    width='20'
    xmlns='http://www.w3.org/2000/svg'
  >
    <g clipPath='url(#clip0_2520_6425)' fill='none'>
      <path
        d='M13.3337 10.4165L9.16699 7.9165L13.3337 5.4165L17.5003 7.9165V12.4998L13.3337 14.9998V10.4165Z'
        stroke='currentColor'
        strokeLinecap='round'
        strokeLinejoin='round'
        strokeWidth='1.5'
      />
      <path
        d='M9.16699 7.9165V12.4998L13.3337 14.9998'
        stroke='currentColor'
        strokeLinecap='round'
        strokeLinejoin='round'
        strokeWidth='1.5'
      />
      <path
        d='M13.333 10.4542L17.4997 7.9292'
        stroke='currentColor'
        strokeLinecap='round'
        strokeLinejoin='round'
        strokeWidth='1.5'
      />
      <path
        d='M5.83366 7.5H1.66699'
        stroke='currentColor'
        strokeLinecap='round'
        strokeLinejoin='round'
        strokeWidth='1.5'
      />
      <path
        d='M5.83301 10H3.33301'
        stroke='currentColor'
        strokeLinecap='round'
        strokeLinejoin='round'
        strokeWidth='1.5'
      />
      <path d='M5.83333 12.5H5' stroke='currentColor' strokeLinecap='round' strokeLinejoin='round' strokeWidth='1.5' />
    </g>
    <defs>
      <clipPath id='clip0_2520_6425'>
        <rect fill='currentFill' height='20' width='20' />
      </clipPath>
    </defs>
  </svg>
);
export default SuppliersIcon;
