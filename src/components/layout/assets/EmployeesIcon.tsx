import {FC} from 'react';

import {IconProps} from 'types/icon';

const EmployeesIcon: FC<IconProps> = ({className}) => (
  <svg className={className} fill='none' height='20' viewBox='0 0 20 20' width='20' xmlns='http://www.w3.org/2000/svg'>
    <path
      d='M7 1.7219L8.09091 4H11.9091L13 1.7219C9.72727 0.355067 8.09091 1.26628 7 1.7219Z'
      fill='none'
      stroke='currentColor'
      strokeLinecap='round'
      strokeLinejoin='round'
      strokeWidth='1.5'
    />
    <path
      d='M15 7C15 5.29077 13.9945 3.79844 12.5 3M5 7C5 5.29077 6.0055 3.79844 7.5 3'
      fill='none'
      stroke='currentColor'
      strokeLinecap='round'
      strokeWidth='1.5'
    />
    <path
      d='M13.9339 7.93413C14.9 9.65286 14.6518 11.87 13.1892 13.3326C11.4276 15.0942 8.57146 15.0942 6.80986 13.3326C5.34726 11.87 5.09902 9.65286 6.06513 7.93413'
      fill='none'
      stroke='currentColor'
      strokeWidth='1.5'
    />
    <rect fill='white' height='1.5' rx='0.75' width='14' x='3' y='7' />
    <path
      d='M17 19V17.6667C17 16.1939 15.433 15 13.5 15H6.5C4.567 15 3 16.1939 3 17.6667V19'
      fill='none'
      stroke='currentColor'
      strokeLinecap='round'
      strokeLinejoin='round'
      strokeWidth='1.5'
    />
  </svg>
);
export default EmployeesIcon;
