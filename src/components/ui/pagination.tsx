// todo: refactor
import * as React from 'react';

import {ChevronLeft, ChevronRight, MoreHorizontal} from 'lucide-react';

import {ButtonProps, buttonVariants} from '@/components/ui/Button';
import {classes} from '@/utils/common';

const Pagination = ({className, ...props}: React.ComponentProps<'nav'>) => (
  <nav
    aria-label='pagination'
    className={classes('mx-auto flex w-full justify-center', className)}
    role='navigation'
    {...props}
  />
);
Pagination.displayName = 'Pagination';

const PaginationContent = React.forwardRef<HTMLUListElement, React.ComponentProps<'ul'>>(
  ({className, ...props}, ref) => (
    <ul className={classes('flex flex-row items-center gap-1', className)} ref={ref} {...props} />
  ),
);
PaginationContent.displayName = 'PaginationContent';

const PaginationItem = React.forwardRef<HTMLLIElement, React.ComponentProps<'li'>>(({className, ...props}, ref) => (
  <li className={classes('', className)} ref={ref} {...props} />
));
PaginationItem.displayName = 'PaginationItem';

type PaginationLinkProps = Pick<ButtonProps, 'size'> &
  React.ComponentProps<'a'> & {
    isActive?: boolean;
  };

const PaginationLink = ({className, isActive, size = 'icon', ...props}: PaginationLinkProps) => (
  <a
    aria-current={isActive ? 'page' : undefined}
    className={classes(
      buttonVariants({
        size,
        variant: isActive ? 'secondary' : 'ghost',
      }),
      className,
    )}
    {...props}
  />
);
PaginationLink.displayName = 'PaginationLink';

const PaginationPrevious = ({className, ...props}: React.ComponentProps<typeof PaginationLink>) => (
  <PaginationLink
    aria-label='Go to previous page'
    className={classes('gap-1 pl-2.5', className)}
    size='default'
    {...props}
  >
    <ChevronLeft className='size-4' />
    <span>Previous</span>
  </PaginationLink>
);
PaginationPrevious.displayName = 'PaginationPrevious';

const PaginationNext = ({className, ...props}: React.ComponentProps<typeof PaginationLink>) => (
  <PaginationLink aria-label='Go to next page' className={classes('gap-1 pr-2.5', className)} size='default' {...props}>
    <span>Next</span>
    <ChevronRight className='size-4' />
  </PaginationLink>
);
PaginationNext.displayName = 'PaginationNext';

const PaginationEllipsis = ({className, ...props}: React.ComponentProps<'span'>) => (
  <span aria-hidden className={classes('flex size-9 items-center justify-center', className)} {...props}>
    <MoreHorizontal className='size-4' />
    <span className='sr-only'>More pages</span>
  </span>
);
PaginationEllipsis.displayName = 'PaginationEllipsis';

export {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
};
