import {ComponentPropsWithoutRef, FC, forwardRef, ReactNode, useEffect, useMemo, useRef, useState} from 'react';

import {filter, isEmpty} from 'lodash';
import {ChevronDownIcon, PencilIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';
import {Control, Controller, FieldValues, Path} from 'react-hook-form';

import {Button} from '@/components/ui/Button';
import {Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList} from '@/components/ui/Command';
import {Label, LabelType, WithLabel} from '@/components/ui/Label';
import {Popover, PopoverContent, PopoverTrigger} from '@/components/ui/Popover';
import useClickOutside from '@/hooks/helpers/useClickOutside';
import {arrayOrObjectCheck, classes} from '@/utils/common';

export type ComboboxProps = {
  autofocus?: boolean;
  className?: string;
  containerClassName?: string;
  defaultValue?: string;
  disabled?: boolean;
  error?: boolean;
  notFoundClassName?: string;
  onChange?: (value: string) => void;
  onOpenChange?: (open: boolean) => void;
  open?: boolean;
  options: OptionType[];
  placeholder?: string;
  preferredValues?: string[];
  renderNotFound?: (query: string) => ReactNode;
  searchPlaceholder?: string;
  value?: string;
};

type OptionType = {id: string; value: string};

const Combobox = forwardRef<HTMLButtonElement, ComboboxProps>(
  (
    {
      autofocus,
      className,
      containerClassName,
      defaultValue,
      disabled,
      error,
      notFoundClassName,
      onChange,
      onOpenChange,
      open: defaultOpen,
      options,
      placeholder,
      preferredValues,
      renderNotFound,
      searchPlaceholder,
      value: actualValue,
      ...rest
    },
    ref,
  ) => {
    const [open, setOpen] = useState(defaultOpen);
    const [value, setValue] = useState(actualValue || defaultValue || '');
    const [query, setQuery] = useState('');
    const [searching, setSearching] = useState(false);
    const t = useTranslations();

    useEffect(() => {
      if (actualValue !== undefined) setValue(actualValue);
    }, [actualValue]);

    useEffect(() => {
      setOpen(defaultOpen);
    }, [defaultOpen]);

    const valueName = useMemo(
      () => (value ? options.find((option) => option.id === value)?.value : undefined),
      [value, options],
    );

    return (
      <Popover
        onOpenChange={(val) => {
          setOpen(val);
          setSearching(false);
          onOpenChange?.(val);
        }}
        open={open}
      >
        <PopoverTrigger asChild>
          <Button
            aria-expanded={open}
            className={classes(
              'flex justify-between border-b px-0 hover:bg-background',
              error ? 'border-red' : 'border-muted',
              className,
            )}
            disabled={disabled}
            ref={ref}
            role='combobox'
            variant='dropdown'
          >
            <div className={classes('truncate', error && 'text-red', !valueName && !error && 'text-muted')}>
              {valueName || placeholder || `${t('select')}...`}
            </div>
            <ChevronDownIcon
              className={classes('size-4 shrink-0 opacity-50 transition-transform', open && 'rotate-180')}
            />
          </Button>
        </PopoverTrigger>
        <PopoverContent autoFocus={autofocus} className={classes('overflow-visible p-2', containerClassName)} {...rest}>
          <Command>
            <div className='sticky top-0 z-10 border-b bg-background'>
              <CommandInput
                onValueChange={(value) => {
                  setSearching(!!value);
                  setQuery(value);
                }}
                placeholder={searchPlaceholder || `${t('search')}...`}
              />
            </div>
            <CommandList className='max-h-[calc(var(--radix-popover-content-available-height)_-_75px)]'>
              <CommandEmpty className={classes('mx-3 my-2.5', notFoundClassName)} onClick={() => setOpen(false)}>
                {renderNotFound?.(query) || t('no results found')}
              </CommandEmpty>
              <CommandGroup className='mt-1'>
                {(
                  arrayOrObjectCheck(
                    options,
                    searching,
                    preferredValues && !isEmpty(preferredValues)
                      ? filter(options, (option) => preferredValues.includes(option.id))
                      : options,
                  ) as OptionType[]
                ).map((option) => (
                  <CommandItem
                    className={classes('data-[selected=true]:bg-gray-200', value === option.id && 'font-bold')}
                    key={option.id}
                    onSelect={() => {
                      setOpen(false);
                      setValue(option.id);
                      onChange?.(option.id);
                    }}
                    value={option.value}
                  >
                    {option.value}
                  </CommandItem>
                ))}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    );
  },
);
Combobox.displayName = 'Combobox';

const ComboboxLabel = forwardRef<HTMLLabelElement, LabelType>(({className, ...props}, ref) => (
  <Label className={classes('text-xs font-medium opacity-60', className)} ref={ref} {...props} />
));
ComboboxLabel.displayName = 'ComboboxLabel';

type ControlledProps<T extends FieldValues> = Omit<ComboboxProps, 'value'> & {
  control: Control<T>;
  controlName: Path<T>;
};

const ControlledCombobox = forwardRef<HTMLButtonElement, ControlledProps<any>>(
  ({control, controlName, onChange, ...props}, ref) => (
    <Controller
      control={control}
      name={controlName}
      render={({field}) => (
        <Combobox
          onChange={(value) => {
            field.onChange(value);
            onChange?.(value);
          }}
          ref={ref}
          value={field.value}
          {...props}
        />
      )}
    />
  ),
);
ControlledCombobox.displayName = 'ControlledCombobox';

type PopoverComboboxProps = Omit<ComboboxProps, 'className' | 'onChange'> &
  Pick<ComponentPropsWithoutRef<typeof PopoverContent>, 'align' | 'side'> & {
    children: ReactNode;
    className?: string;
    comboboxClassName?: string;
    defaultValue?: string;
    label?: string;
    onChange: (value: string) => void;
    withoutIcon?: boolean;
  };

const PopoverCombobox: FC<PopoverComboboxProps> = ({
  align,
  children,
  className,
  comboboxClassName,
  containerClassName,
  disabled,
  label,
  onChange,
  options,
  renderNotFound,
  searchPlaceholder,
  side,
  withoutIcon,
  ...rest
}) => {
  const [query, setQuery] = useState('');
  const [searching, setSearching] = useState(false);
  const [open, setOpen] = useState(false);
  const ref = useRef(null);
  const t = useTranslations();

  useClickOutside([ref], () => {
    setOpen(false);
  });

  return (
    <Popover open={open}>
      <PopoverTrigger asChild>
        <Button
          className={classes('group w-full justify-normal', disabled && 'cursor-default', className)}
          onClick={() => !disabled && setOpen(true)}
          size='none'
          variant='none'
        >
          <div className={classes('relative', className)}>
            {String(children).trim() === '' ? '-' : children}
            {!withoutIcon && !disabled && (
              <div className='absolute -right-1 top-1/2 hidden -translate-y-1/2 translate-x-full group-hover:block'>
                <PencilIcon className='size-4' />
              </div>
            )}
          </div>
        </Button>
      </PopoverTrigger>
      <PopoverContent
        align={align}
        className={classes('overflow-visible p-2', containerClassName)}
        ref={ref}
        side={side}
        {...rest}
      >
        <Command className={comboboxClassName}>
          <div className='sticky top-0 z-10 border-b bg-background'>
            <CommandInput
              onValueChange={(value) => {
                setSearching(!!value);
                setQuery(value);
              }}
              placeholder={searchPlaceholder || `${t('search')}...`}
            />
          </div>
          <WithLabel>
            <CommandList className='max-h-[calc(var(--radix-popover-content-available-height)_-_57px_-_16px)] overflow-y-auto'>
              <CommandEmpty className={classes('mx-3 my-2.5')} onClick={() => setOpen(false)}>
                {renderNotFound?.(query) || t('no results found')}
              </CommandEmpty>
              <CommandGroup className='mt-1'>
                {(arrayOrObjectCheck(options, searching, options) as OptionType[]).map((option) => (
                  <CommandItem
                    className={classes('data-[selected=true]:bg-gray-200')}
                    key={option.id}
                    onSelect={() => {
                      setOpen(false);
                      onChange?.(option.id);
                    }}
                    value={option.value}
                  >
                    {option.value}
                  </CommandItem>
                ))}
              </CommandGroup>
            </CommandList>
            <ComboboxLabel>{label}</ComboboxLabel>
          </WithLabel>
        </Command>
      </PopoverContent>
    </Popover>
  );
};

export {Combobox, ComboboxLabel, ControlledCombobox, PopoverCombobox};
