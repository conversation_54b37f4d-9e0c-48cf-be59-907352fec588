'use client';

import {ComponentPropsWithoutRef, ComponentRef, forwardRef} from 'react';

import {Content, Portal, Provider, Root, Trigger} from '@radix-ui/react-tooltip';
import {Inter} from 'next/font/google';

import {classes} from '@/utils/common';

const inter = Inter({
  subsets: ['latin'],
  variable: '--font-sans',
});

const TooltipProvider = Provider;

const Tooltip = Root;

const TooltipTrigger = Trigger;

const TooltipContent = forwardRef<ComponentRef<typeof Content>, ComponentPropsWithoutRef<typeof Content>>(
  ({className, sideOffset = 4, ...props}, ref) => (
    <Portal>
      <Content
        className={classes(
          'z-50 overflow-hidden rounded-lg bg-menu p-1 font-sans text-sm text-white shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2',
          inter.variable,
          className,
        )}
        ref={ref}
        sideOffset={sideOffset}
        {...props}
      />
    </Portal>
  ),
);
TooltipContent.displayName = Content.displayName;

export {Tooltip, TooltipContent, TooltipProvider, TooltipTrigger};
