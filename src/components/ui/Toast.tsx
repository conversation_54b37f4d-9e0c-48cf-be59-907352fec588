'use client';

import {useMemo} from 'react';

import {CheckIcon, CircleAlertIcon, TriangleAlertIcon} from 'lucide-react';
import {Inter} from 'next/font/google';
import {useTheme} from 'next-themes';
import {Toaster as Sonner, toast} from 'sonner';

import {Badge} from '@/components/ui/Badge';
import {classes} from '@/utils/common';

type ToastClassNames = NonNullable<ToasterProps['toastOptions']>['classNames'];
type ToasterProps = React.ComponentProps<typeof Sonner>;
type ToastStyles = NonNullable<ToasterProps['toastOptions']>['style'];

const inter = Inter({
  subsets: ['latin'],
  variable: '--font-sans',
});

const Toaster = ({...rest}: ToasterProps) => {
  const {theme = 'system'} = useTheme();

  const iconComponents = useMemo(
    () => ({
      error: (
        <Badge className='flex size-10 items-center justify-center rounded-full' variant='error'>
          <TriangleAlertIcon className='size-5 shrink-0' />
        </Badge>
      ),
      info: (
        <Badge className='flex size-10 items-center justify-center rounded-full' variant='info'>
          <CircleAlertIcon className='size-5 shrink-0' />
        </Badge>
      ),
      success: (
        <Badge className='flex size-10 items-center justify-center rounded-full' variant='success'>
          <CheckIcon className='size-5 shrink-0' />
        </Badge>
      ),
      warning: (
        <Badge className='flex size-10 items-center justify-center rounded-full' variant='warning'>
          <TriangleAlertIcon className='size-5 shrink-0' />
        </Badge>
      ),
    }),
    [],
  );

  const defaultProps = useMemo<Partial<ToasterProps>>(
    () => ({
      className: classes('group/toaster font-sans', inter.variable),
      closeButton: true,
      icons: iconComponents,
      offset: 16,
      position: 'top-center' as ToasterProps['position'],
      theme: theme as ToasterProps['theme'],
      toastOptions: {
        classNames: {
          actionButton: 'bg-primary text-primary-foreground hover:bg-primary/90 px-3 py-2 text-xs rounded-md',
          cancelButton: 'bg-muted text-muted-foreground hover:bg-muted/90 px-3 py-2 text-xs rounded-md',
          closeButton:
            'absolute right-2 top-1/2 -translate-y-1/2 p-0! m-0! bg-black! hover:bg-black/80! rounded-full! size-6! flex items-center justify-center [&>svg]:text-white! [&>svg]:size-4! transition-colors',
          content: 'w-full',
          description: 'text-xs text-foreground cursor-default pl-14 pr-2',
          icon: 'absolute left-5 top-1/2 -translate-y-1/2',
          title: 'text-sm font-medium pl-14 pr-2 w-full',
          toast:
            'group pr-10 flex relative p-5 min-h-[64px] w-[356px] bg-background text-foreground border rounded-lg shadow-lg my-2 items-center border-border data-[type="success"]:border-green-dark! data-[type="error"]:border-red-dark! data-[type="warning"]:border-yellow-dark!',
        },
        style: {
          backgroundColor: 'hsl(var(--background))',
          boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
          color: 'hsl(var(--foreground))',
          width: '356px',
        },
        unstyled: true,
      },
    }),
    [theme, iconComponents],
  );

  const mergedProps = useMemo<ToasterProps>(() => {
    const merged: ToasterProps = {
      ...defaultProps,
      ...rest,
      className: classes(defaultProps.className, rest.className),
    };

    if (defaultProps.toastOptions || rest.toastOptions) {
      merged.toastOptions = {
        ...defaultProps.toastOptions,
        ...rest.toastOptions,
      };

      const defaultClassNames = defaultProps.toastOptions?.classNames || {};
      const restClassNames = rest.toastOptions?.classNames || {};
      if (Object.keys(defaultClassNames).length || Object.keys(restClassNames).length) {
        merged.toastOptions.classNames = {
          ...defaultClassNames,
          ...restClassNames,
        } as ToastClassNames;
      }

      const defaultStyles = defaultProps.toastOptions?.style || {};
      const restStyles = rest.toastOptions?.style || {};
      if (Object.keys(defaultStyles).length || Object.keys(restStyles).length) {
        merged.toastOptions.style = {
          ...defaultStyles,
          ...restStyles,
        } as ToastStyles;
      }
    }

    return merged;
  }, [defaultProps, rest]);

  return <Sonner {...mergedProps} />;
};

export {toast, Toaster};
