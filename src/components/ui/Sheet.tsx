'use client';

import {ComponentPropsWithoutRef, ComponentRef, forwardRef, HTMLAttributes} from 'react';

import {Close, Content, Overlay, Portal, Root, Title, Trigger} from '@radix-ui/react-dialog';
import {cva, type VariantProps} from 'class-variance-authority';
import {XIcon} from 'lucide-react';
import {Inter} from 'next/font/google';

import {classes} from '@/utils/common';

const inter = Inter({
  subsets: ['latin'],
  variable: '--font-sans',
});

const Sheet = Root;

const SheetTrigger = Trigger;

const SheetClose = Close;

const SheetPortal = Portal;

const overlayVariants = cva(
  'fixed inset-0 z-50 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0',
  {
    defaultVariants: {
      variant: 'medium',
    },
    variants: {
      variant: {
        dark: 'bg-black/80',
        light: 'bg-black/20',
        medium: 'bg-black/60',
        mediumLight: 'bg-black/40',
      },
    },
  },
);

type SheetOverlayProps = ComponentPropsWithoutRef<typeof Overlay> & VariantProps<typeof overlayVariants>;

const SheetOverlay = forwardRef<ComponentRef<typeof Overlay>, SheetOverlayProps>(
  ({className, variant, ...props}, ref) => (
    <Overlay className={classes(overlayVariants({className, variant}), className)} {...props} ref={ref} />
  ),
);
SheetOverlay.displayName = Overlay.displayName;

const sheetVariants = cva(
  classes(
    'fixed z-50 gap-4 border-border bg-background py-4 font-sans antialiased shadow-2xl transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500 data-[state=open]:animate-in data-[state=closed]:animate-out',
    inter.variable,
  ),
  {
    defaultVariants: {
      side: 'middle',
      size: 'md',
    },
    variants: {
      side: {
        bottom:
          'inset-x-0 bottom-0 border-t data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom',
        left: 'inset-y-0 left-0 flex h-full flex-col border-r data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left',
        middle:
          'left-1/2 top-1/2 flex max-h-[800px] min-h-[400px] -translate-x-1/2 -translate-y-1/2 flex-col rounded-2xl data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95',
        right:
          'inset-y-0 right-0 flex h-full flex-col border-l data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right',
        top: 'inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top',
      },
      size: {
        '2xl': 'w-[800px]',
        '3xl': 'w-[900px]',
        '4xl': 'w-[1000px]',
        '5xl': 'w-[1100px]',
        '6xl': 'w-[1200px]',
        '7xl': 'w-[1300px]',
        '8xl': 'w-[1400px]',
        '9xl': 'w-[1500px]',
        '10xl': 'w-[1600px]',
        lg: 'w-[600px]',
        md: 'w-[500px]',
        ms: 'w-[350px]',
        sm: 'w-[400px]',
        xl: 'w-[700px]',
        xs: 'w-[300px]',
      },
    },
  },
);

type SheetContentProps = ComponentPropsWithoutRef<typeof Content> & VariantProps<typeof sheetVariants>;

const SheetPage = forwardRef<ComponentRef<typeof Content>, SheetContentProps>(
  ({children, className, side, size, ...props}, ref) => (
    <SheetPortal>
      <SheetOverlay variant='light'>
        <Content
          className={classes('ring-0 focus-visible:outline-hidden', sheetVariants({side, size}), className)}
          ref={ref}
          {...props}
        >
          {children}
          <SheetClose className='absolute right-4 top-4 opacity-70 ring-0 transition-opacity hover:opacity-100 focus:outline-hidden disabled:pointer-events-none data-[state=open]:bg-background'>
            <XIcon className='size-4' />
            <span className='sr-only'>Close</span>
          </SheetClose>
        </Content>
      </SheetOverlay>
    </SheetPortal>
  ),
);
SheetPage.displayName = Content.displayName;

const SheetTitle = forwardRef<ComponentRef<typeof Title>, ComponentPropsWithoutRef<typeof Title>>(
  ({className, ...props}, ref) => (
    <Title className={classes('ml-6 mr-2 text-lg font-semibold', className)} ref={ref} {...props} />
  ),
);
SheetTitle.displayName = Title.displayName;

const SheetHeader = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(({className, ...props}, ref) => (
  <div className={classes('mx-6 flex flex-col gap-4', className)} ref={ref} {...props} />
));
SheetHeader.displayName = 'SheetHeader';

const SheetContent = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(({className, ...props}, ref) => (
  <div
    className={classes('grow overflow-y-auto ring-0 focus-visible:outline-hidden', className)}
    ref={ref}
    {...props}
  />
));
SheetContent.displayName = 'SheetContent';

const SheetFooter = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(({className, ...props}, ref) => (
  <div className={classes('flex flex-col space-y-2', className)} ref={ref} {...props} />
));
SheetFooter.displayName = 'SheetFooter';

export {
  Sheet,
  SheetClose,
  SheetContent,
  SheetFooter,
  SheetHeader,
  SheetOverlay,
  SheetPage,
  SheetPortal,
  SheetTitle,
  SheetTrigger,
};
