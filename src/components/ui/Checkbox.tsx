'use client';

import {ComponentPropsWithoutRef, ComponentRef, forwardRef, Ref} from 'react';

import {Indicator, Root} from '@radix-ui/react-checkbox';
import {CheckIcon} from 'lucide-react';
import {Control, Controller, FieldValues, Path} from 'react-hook-form';

import {classes} from '@/utils/common';

type Props = ComponentPropsWithoutRef<typeof Root>;

const Checkbox = forwardRef<ComponentRef<typeof Root>, Props>(({className, ...props}, ref) => (
  <Root
    className={classes(
      'peer cursor-pointer size-4 shrink-0 border-2 border-transparent ring-0 hover:bg-input focus-visible:outline-hidden disabled:cursor-not-allowed disabled:opacity-50 data-[state=unchecked]:border-border-foreground data-[state=checked]:bg-button-primary data-[state=unchecked]:text-primary-foreground',
      className,
    )}
    ref={ref}
    {...props}
  >
    <div className='size-3'>
      <Indicator
        className={classes(
          'flex shrink-0 items-center justify-center text-current ring-0 focus-visible:outline-hidden',
        )}
      >
        <CheckIcon className='size-3' strokeWidth={3} />
      </Indicator>
    </div>
  </Root>
));
Checkbox.displayName = Root.displayName;

type ControlledProps<T extends FieldValues> = Omit<Props, 'checked'> & {
  control: Control<T>;
  controlName: Path<T>;
};

const ControlledCheckbox = forwardRef(
  <T extends FieldValues>(
    {control, controlName, onCheckedChange, ...props}: ControlledProps<T>,
    ref: Ref<HTMLButtonElement>,
  ) => (
    <Controller
      control={control}
      name={controlName}
      render={({field}) => (
        <Checkbox
          checked={field.value}
          onCheckedChange={(value) => {
            field.onChange(value);
            onCheckedChange?.(value);
          }}
          ref={ref}
          {...props}
        />
      )}
    />
  ),
) as <T extends FieldValues>(props: ControlledProps<T> & {ref?: Ref<HTMLDivElement>}) => ReturnType<typeof Checkbox>;

export {Checkbox, ControlledCheckbox};
