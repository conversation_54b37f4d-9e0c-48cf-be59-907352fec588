'use client';

import {ComponentPropsWithoutRef, ComponentRef, forwardRef, HTMLAttributes} from 'react';

import {
  Arrow,
  CheckboxItem,
  Content,
  Group,
  Item,
  ItemIndicator,
  Portal,
  RadioGroup,
  RadioItem,
  Root,
  Separator,
  Sub,
  SubContent,
  SubTrigger,
  Trigger,
} from '@radix-ui/react-dropdown-menu';
import {ChevronLeftIcon, ChevronRightIcon, Circle} from 'lucide-react';
import {Inter} from 'next/font/google';

import {Checkbox} from '@/components/ui/Checkbox';
import {Label, WithLabel} from '@/components/ui/Label';
import {classes} from '@/utils/common';

const inter = Inter({
  subsets: ['latin'],
  variable: '--font-sans',
});

const DropdownMenu = Root;

const DropdownMenuTrigger = Trigger;

const DropdownMenuGroup = Group;

const DropdownMenuPortal = Portal;

const DropdownMenuSub = Sub;

const DropdownMenuRadioGroup = RadioGroup;

const menuClassNames =
  'relative flex gap-4 cursor-pointer select-none items-center rounded-sm px-3 mx-1 py-3 my-1 text-sm outline-hidden transition-colors focus:bg-accent focus:text-accent-foreground data-disabled:cursor-not-allowed data-disabled:opacity-50';

const DropdownMenuSubTrigger = forwardRef<
  ComponentRef<typeof SubTrigger>,
  ComponentPropsWithoutRef<typeof SubTrigger> & {
    chevronPosition?: 'left' | 'right';
    hideChevron?: boolean;
  }
>(({chevronPosition = 'right', children, className, hideChevron, ...props}, ref) => (
  <SubTrigger className={classes(menuClassNames, className)} ref={ref} {...props}>
    {children}
    {!hideChevron && chevronPosition === 'right' && (
      <ChevronRightIcon className='absolute right-0 top-1/2 size-4 -translate-y-1/2' />
    )}
    {!hideChevron && chevronPosition === 'left' && (
      <ChevronLeftIcon className='absolute -left-0.5 top-1/2 size-4 -translate-y-1/2' />
    )}
  </SubTrigger>
));
DropdownMenuSubTrigger.displayName = SubTrigger.displayName;

const DropdownMenuSubContent = forwardRef<ComponentRef<typeof SubContent>, ComponentPropsWithoutRef<typeof SubContent>>(
  ({className, sideOffset = 4, ...props}, ref) => (
    <Portal>
      <SubContent
        className={classes(
          'z-50 max-h-96 min-w-44 overflow-y-auto overflow-x-hidden rounded-md border bg-background p-1 font-sans text-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2',
          inter.variable,
          className,
        )}
        ref={ref}
        sideOffset={sideOffset}
        {...props}
      />
    </Portal>
  ),
);
DropdownMenuSubContent.displayName = SubContent.displayName;

const DropdownMenuContent = forwardRef<ComponentRef<typeof Content>, ComponentPropsWithoutRef<typeof Content>>(
  ({children, className, sideOffset = 4, ...props}, ref) => (
    <Portal>
      <Content
        className={classes(
          'z-50 max-h-96 min-w-44 overflow-y-auto overflow-x-hidden rounded-md border bg-background p-1 font-sans text-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2',
          inter.variable,
          className,
        )}
        ref={ref}
        sideOffset={sideOffset}
        {...props}
      >
        {children}
        <Arrow className='arrow' />
      </Content>
    </Portal>
  ),
);
DropdownMenuContent.displayName = Content.displayName;

const DropdownMenuItem = forwardRef<ComponentRef<typeof Item>, ComponentPropsWithoutRef<typeof Item>>(
  ({className, ...props}, ref) => <Item className={classes(menuClassNames, className)} ref={ref} {...props} />,
);
DropdownMenuItem.displayName = Item.displayName;

const DropdownMenuButton = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(({className, ...props}, ref) => (
  <div className={classes(menuClassNames, className)} ref={ref} {...props} />
));
DropdownMenuButton.displayName = Item.displayName;

type DropdownMenuCheckboxItemProps = ComponentPropsWithoutRef<typeof CheckboxItem> & {id: string};

const DropdownMenuCheckboxItem = forwardRef<ComponentRef<typeof CheckboxItem>, DropdownMenuCheckboxItemProps>(
  ({checked, children, className, id, ...props}, ref) => (
    <CheckboxItem
      checked={checked}
      className={classes(
        'relative flex select-none items-center rounded-sm px-2 py-1.5 text-sm outline-hidden transition-colors focus:bg-accent focus:text-accent-foreground data-disabled:pointer-events-none data-disabled:opacity-50',
        className,
      )}
      ref={ref}
      {...props}
    >
      <WithLabel direction='horizontal'>
        <Checkbox checked={checked} id={id} />
        <Label htmlFor={id}>{children}</Label>
      </WithLabel>
    </CheckboxItem>
  ),
);
DropdownMenuCheckboxItem.displayName = CheckboxItem.displayName;

const DropdownMenuRadioItem = forwardRef<ComponentRef<typeof RadioItem>, ComponentPropsWithoutRef<typeof RadioItem>>(
  ({children, className, ...props}, ref) => (
    <RadioItem
      className={classes(
        'relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-hidden transition-colors focus:bg-accent focus:text-accent-foreground data-disabled:pointer-events-none data-disabled:opacity-50',
        className,
      )}
      ref={ref}
      {...props}
    >
      <span className='absolute left-2 flex size-3.5 items-center justify-center'>
        <ItemIndicator>
          <Circle className='size-2 fill-current' />
        </ItemIndicator>
      </span>
      {children}
    </RadioItem>
  ),
);
DropdownMenuRadioItem.displayName = RadioItem.displayName;

const DropdownMenuLabel = forwardRef<ComponentRef<typeof Label>, ComponentPropsWithoutRef<typeof Label>>(
  ({className, ...props}, ref) => (
    <Label className={classes('px-2 py-1.5 text-sm font-semibold', className)} ref={ref} {...props} />
  ),
);
DropdownMenuLabel.displayName = Label.displayName;

const DropdownMenuSeparator = forwardRef<ComponentRef<typeof Separator>, ComponentPropsWithoutRef<typeof Separator>>(
  ({className, ...props}, ref) => (
    <Separator className={classes('-mx-1 my-1 h-px bg-muted', className)} ref={ref} {...props} />
  ),
);
DropdownMenuSeparator.displayName = Separator.displayName;

export {
  DropdownMenu,
  DropdownMenuButton,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuPortal,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
  DropdownMenuSeparator,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
};
