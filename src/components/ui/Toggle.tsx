'use client';

import {ComponentPropsWithoutRef, ComponentRef, forwardRef, HTMLAttributes} from 'react';

import {Root} from '@radix-ui/react-toggle';
import {cva, type VariantProps} from 'class-variance-authority';

import {classes} from '@/utils/common';

export const toggleVariants = cva(
  'inline-flex h-7 items-center justify-center gap-1 whitespace-nowrap rounded-lg px-4 text-sm text-foreground ring-0 transition-colors focus-visible:outline-hidden disabled:pointer-events-none disabled:opacity-50',
  {
    defaultVariants: {
      variant: 'primary',
    },
    variants: {
      variant: {
        primary: 'hover:bg-button-primary-hovered data-[state=on]:bg-button-primary',
      },
    },
  },
);

export const toggleContainerVariants = cva(
  'flex size-fit items-center justify-center gap-1 rounded-xl  bg-input p-1.5',
);

const Toggle = forwardRef<
  ComponentRef<typeof Root>,
  ComponentPropsWithoutRef<typeof Root> & VariantProps<typeof toggleVariants>
>(({className, variant, ...props}, ref) => (
  <Root className={classes(toggleVariants({className, variant}), className)} ref={ref} {...props} />
));
Toggle.displayName = Root.displayName;

const ToggleContainer = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(({className, ...props}, ref) => (
  <div className={classes(toggleContainerVariants(), className)} ref={ref} {...props} />
));
ToggleContainer.displayName = 'ToggleContainer';

export {Toggle, ToggleContainer};
