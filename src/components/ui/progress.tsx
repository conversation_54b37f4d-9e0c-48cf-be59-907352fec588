// todo: refactor
'use client';

import * as React from 'react';

import * as ProgressPrimitive from '@radix-ui/react-progress';

import {classes} from '@/utils/common';

const Progress = React.forwardRef<
  React.ComponentRef<typeof ProgressPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root>
>(({className, value, ...props}, ref) => (
  <ProgressPrimitive.Root
    className={classes('relative h-4 w-full overflow-hidden rounded-full bg-background', className)}
    ref={ref}
    {...props}
  >
    <ProgressPrimitive.Indicator
      className='size-full flex-1 bg-primary transition-all'
      style={{transform: `translateX(-${100 - (value || 0)}%)`}}
    />
  </ProgressPrimitive.Root>
));
Progress.displayName = ProgressPrimitive.Root.displayName;

export {Progress};
