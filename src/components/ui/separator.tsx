// todo: refactor
'use client';

import * as React from 'react';

import * as SeparatorPrimitive from '@radix-ui/react-separator';

import {classes} from '@/utils/common';

const Separator = React.forwardRef<
  React.ComponentRef<typeof SeparatorPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof SeparatorPrimitive.Root>
>(({className, decorative = true, orientation = 'horizontal', ...props}, ref) => (
  <SeparatorPrimitive.Root
    className={classes('shrink-0 bg-border', orientation === 'horizontal' ? 'h-px w-full' : 'h-full w-px', className)}
    decorative={decorative}
    orientation={orientation}
    ref={ref}
    {...props}
  />
));
Separator.displayName = SeparatorPrimitive.Root.displayName;

export {Separator};
