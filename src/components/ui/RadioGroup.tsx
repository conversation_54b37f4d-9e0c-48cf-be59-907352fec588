'use client';

import {ComponentPropsWithoutRef, ComponentRef, forwardRef, Ref} from 'react';

import {Indicator, Item, Root} from '@radix-ui/react-radio-group';
import {Circle} from 'lucide-react';
import {Control, Controller, FieldValues, Path} from 'react-hook-form';

import {classes} from '@/utils/common';

type Props = ComponentPropsWithoutRef<typeof Root> & {error?: boolean};

const RadioGroup = forwardRef<ComponentRef<typeof Root>, Props>(({className, error, orientation, ...props}, ref) => (
  <Root
    className={classes('peer flex gap-2', orientation === 'vertical' && 'flex-col', error && 'text-red', className)}
    orientation={orientation}
    ref={ref}
    {...props}
  />
));
RadioGroup.displayName = Root.displayName;

type ControlledProps<T extends FieldValues> = Omit<Props, 'value'> & {
  control: Control<T>;
  controlName: Path<T>;
};

const ControlledRadioGroup = forwardRef(
  <T extends FieldValues>(
    {control, controlName, onValueChange, ...props}: ControlledProps<T>,
    ref: Ref<HTMLDivElement>,
  ) => (
    <Controller
      control={control}
      name={controlName}
      render={({field}) => (
        <RadioGroup
          onValueChange={(value) => {
            field.onChange(value);
            onValueChange?.(value);
          }}
          ref={ref}
          value={field.value?.toString()}
          {...props}
        />
      )}
    />
  ),
) as <T extends FieldValues>(props: ControlledProps<T> & {ref?: Ref<HTMLDivElement>}) => ReturnType<typeof RadioGroup>;

const RadioGroupItem = forwardRef<ComponentRef<typeof Item>, ComponentPropsWithoutRef<typeof Item>>(
  ({className, ...props}, ref) => {
    return (
      <Item
        className={classes(
          'group aspect-square size-4 rounded-full border border-border-foreground ring-0 hover:bg-input focus-visible:outline-hidden disabled:cursor-not-allowed disabled:opacity-50',
          className,
        )}
        ref={ref}
        {...props}
      >
        <Indicator className='flex items-center justify-center'>
          <Circle className='size-3 fill-button-primary stroke-button-primary text-current group-hover:fill-button-primary-hovered group-hover:stroke-button-primary-hovered' />
        </Indicator>
      </Item>
    );
  },
);
RadioGroupItem.displayName = Item.displayName;

export {ControlledRadioGroup, RadioGroup, RadioGroupItem};
