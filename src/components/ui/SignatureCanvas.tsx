import {FC, useRef, useState} from 'react';

import {CheckIcon, Trash2Icon} from 'lucide-react';
import {useTranslations} from 'next-intl';
import SignaturePad from 'react-signature-canvas';

import {Button} from '@/components/ui/Button';
import {classes} from '@/utils/common';

type SignatureCanvasProps = {
  initialConfirmed?: boolean;
  onConfirm: (png: string) => void;
  penColor?: string;
};

const SignatureCanvas: FC<SignatureCanvasProps> = ({initialConfirmed = false, onConfirm, penColor = 'blue'}) => {
  const t = useTranslations();
  const sigRef = useRef<null | SignaturePad>(null);
  const [hasDrawn, setHasDrawn] = useState(false);
  const [confirmed, setConfirmed] = useState(initialConfirmed);

  const deleteSignature = () => {
    sigRef.current?.clear();
    setHasDrawn(false);
    setConfirmed(false);
    onConfirm('');
  };

  const handleSignatureSave = () => {
    if (!sigRef.current || sigRef.current.isEmpty()) return;

    const trimmedCanvas = sigRef.current.getTrimmedCanvas();
    const trimmedData = trimmedCanvas.toDataURL('image/png').replace('data:image/png;base64,', '');

    setConfirmed(true);
    onConfirm(trimmedData);
  };

  return (
    <div className='flex flex-col gap-2'>
      <div className='relative'>
        <SignaturePad
          canvasProps={{
            className: classes(
              'w-full h-40 border',
              confirmed ? 'cursor-not-allowed bg-gray-light pointer-events-none' : 'cursor-crosshair',
            ),
          }}
          onEnd={() => setHasDrawn(true)}
          penColor={penColor}
          ref={sigRef}
        />
      </div>

      <div className='flex justify-between gap-2'>
        <Button onClick={deleteSignature} variant='secondary'>
          <Trash2Icon className='size-5' strokeWidth={1} />
          {t('delete')}
        </Button>
        {!confirmed && hasDrawn && (
          <Button onClick={handleSignatureSave}>
            <CheckIcon />
            {t('confirm')}
          </Button>
        )}
      </div>
    </div>
  );
};

export default SignatureCanvas;
