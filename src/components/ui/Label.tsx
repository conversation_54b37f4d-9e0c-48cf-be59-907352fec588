'use client';

import {ComponentPropsWithoutRef, ComponentRef, forwardRef, HTMLAttributes} from 'react';

import {Root} from '@radix-ui/react-label';
import {cva, type VariantProps} from 'class-variance-authority';

import {classes} from '@/utils/common';

const labelVariants = cva(
  'w-fit cursor-pointer text-sm leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70',
);

export type LabelType = ComponentPropsWithoutRef<typeof Root> & VariantProps<typeof labelVariants>;

const Label = forwardRef<ComponentRef<typeof Root>, LabelType>(({className, ...props}, ref) => (
  <Root className={classes(labelVariants(), className)} ref={ref} {...props} />
));
Label.displayName = Root.displayName;

const withLabelVariants = cva('flex select-none', {
  defaultVariants: {
    direction: 'vertical',
  },
  variants: {
    direction: {
      horizontal: 'items-center gap-2',
      vertical: 'flex-col-reverse',
    },
  },
});

type WithLabelProps = HTMLAttributes<HTMLDivElement> & VariantProps<typeof withLabelVariants>;

const WithLabel = forwardRef<HTMLDivElement, WithLabelProps>(({className, direction, ...props}, ref) => (
  <div className={classes(withLabelVariants({className, direction}), className)} ref={ref} {...props} />
));
WithLabel.displayName = 'WithLabel';

export {Label, WithLabel};
