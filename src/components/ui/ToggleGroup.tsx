'use client';

import {ComponentPropsWithoutRef, ComponentRef, createContext, forwardRef, useContext} from 'react';

import {Item, Root} from '@radix-ui/react-toggle-group';
import {VariantProps} from 'class-variance-authority';

import {classes} from '@/utils/common';

import {toggleContainerVariants, toggleVariants} from './Toggle';

const ToggleGroupContext = createContext<VariantProps<typeof toggleVariants>>({
  variant: 'primary',
});

type ToggleGroupProps = ComponentPropsWithoutRef<typeof Root> &
  VariantProps<typeof toggleVariants> & {
    allowEmpty?: boolean;
  };

const ToggleGroup = forwardRef<ComponentRef<typeof Root>, ToggleGroupProps>(
  ({allowEmpty, children, className, onValueChange, variant, ...props}, ref) => (
    <Root
      className={classes(toggleContainerVariants(), className)}
      onValueChange={(value: string & string[]) => {
        if (!allowEmpty && (!value || (Array.isArray(value) && value.length === 0))) {
          return;
        }
        onValueChange?.(value);
      }}
      ref={ref}
      {...props}
    >
      <ToggleGroupContext.Provider value={{variant}}>{children}</ToggleGroupContext.Provider>
    </Root>
  ),
);

ToggleGroup.displayName = Root.displayName;

const ToggleGroupItem = forwardRef<
  ComponentRef<typeof Item>,
  ComponentPropsWithoutRef<typeof Item> & VariantProps<typeof toggleVariants>
>(({className, variant, ...props}, ref) => {
  const context = useContext(ToggleGroupContext);

  return (
    <Item
      className={classes(
        toggleVariants({
          variant: context.variant || variant,
        }),
        className,
      )}
      ref={ref}
      {...props}
    />
  );
});

ToggleGroupItem.displayName = Item.displayName;

export {ToggleGroup, ToggleGroupItem};
