'use client';

import {ComponentPropsWithoutRef, ComponentRef, forwardRef, HTMLAttributes} from 'react';

import {type DialogProps} from '@radix-ui/react-dialog';
import {Command as CommandPrimitive} from 'cmdk';
import {SearchIcon} from 'lucide-react';

import {Button} from '@/components/ui/Button';
import {Input} from '@/components/ui/Input';
import {Label} from '@/components/ui/Label';
import {Sheet, SheetContent} from '@/components/ui/Sheet';
import {AtLeastOne} from '@/types/global';
import {classes} from '@/utils/common';

const Command = forwardRef<ComponentRef<typeof CommandPrimitive>, ComponentPropsWithoutRef<typeof CommandPrimitive>>(
  ({className, filter, ...props}, ref) => (
    <CommandPrimitive
      className={classes('flex w-full flex-col overflow-hidden rounded-md bg-background text-foreground', className)}
      filter={
        filter ||
        ((value, search, keywords = []) =>
          (value + ' ' + keywords.join(' ')).toLowerCase().includes(search.toLowerCase()) ? 1 : 0)
      }
      ref={ref}
      {...props}
    />
  ),
);
Command.displayName = CommandPrimitive.displayName;

type CommandDialogProps = DialogProps;

const CommandDialog = ({children, ...props}: CommandDialogProps) => {
  return (
    <Sheet {...props}>
      <SheetContent className='overflow-hidden p-0 shadow-lg'>
        <Command className='[&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:font-medium [&_[cmdk-group-heading]]:text-muted-foreground [&_[cmdk-group]:not([hidden])_~[cmdk-group]]:pt-0 [&_[cmdk-group]]:px-2 [&_[cmdk-input-wrapper]_svg]:size-5 [&_[cmdk-input]]:h-12 [&_[cmdk-item]]:px-2 [&_[cmdk-item]]:py-3 [&_[cmdk-item]_svg]:size-5'>
          {children}
        </Command>
      </SheetContent>
    </Sheet>
  );
};

type CommandInputProps = AtLeastOne<ComponentPropsWithoutRef<typeof CommandPrimitive.Input>, 'id' | 'placeholder'> & {
  containerClassName?: string;
};

const CommandInput = forwardRef<ComponentRef<typeof CommandPrimitive.Input>, CommandInputProps>(
  ({className, containerClassName, id, placeholder, ...props}, ref) => (
    <div className={classes('relative mx-1 flex items-center', containerClassName)}>
      <Label className='absolute left-2 cursor-text' htmlFor={id || placeholder}>
        <SearchIcon className='size-5 shrink-0' />
      </Label>
      <CommandPrimitive.Input asChild ref={ref} {...props}>
        <Input className={classes('pl-8 pr-2', className)} placeholder={placeholder} variant='outline' />
      </CommandPrimitive.Input>
    </div>
  ),
);
CommandInput.displayName = CommandPrimitive.Input.displayName;

const CommandList = forwardRef<
  ComponentRef<typeof CommandPrimitive.List>,
  ComponentPropsWithoutRef<typeof CommandPrimitive.List>
>(({className, ...props}, ref) => (
  <CommandPrimitive.List className={classes('overflow-y-auto overflow-x-hidden', className)} ref={ref} {...props} />
));
CommandList.displayName = CommandPrimitive.List.displayName;

const CommandEmpty = forwardRef<
  ComponentRef<typeof CommandPrimitive.Empty>,
  ComponentPropsWithoutRef<typeof CommandPrimitive.Empty>
>((props, ref) => <CommandPrimitive.Empty className='py-6 text-center text-sm' ref={ref} {...props} />);
CommandEmpty.displayName = CommandPrimitive.Empty.displayName;

const CommandGroup = forwardRef<
  ComponentRef<typeof CommandPrimitive.Group>,
  ComponentPropsWithoutRef<typeof CommandPrimitive.Group>
>(({className, ...props}, ref) => (
  <CommandPrimitive.Group
    className={classes(
      'overflow-hidden p-1 text-foreground [&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:py-1.5 [&_[cmdk-group-heading]]:text-xs [&_[cmdk-group-heading]]:font-medium [&_[cmdk-group-heading]]:text-muted-foreground',
      className,
    )}
    ref={ref}
    {...props}
  />
));
CommandGroup.displayName = CommandPrimitive.Group.displayName;

const CommandSeparator = forwardRef<
  ComponentRef<typeof CommandPrimitive.Separator>,
  ComponentPropsWithoutRef<typeof CommandPrimitive.Separator>
>(({className, ...props}, ref) => (
  <CommandPrimitive.Separator className={classes('-mx-1 h-px bg-border', className)} ref={ref} {...props} />
));
CommandSeparator.displayName = CommandPrimitive.Separator.displayName;

const CommandItem = forwardRef<
  ComponentRef<typeof CommandPrimitive.Item>,
  ComponentPropsWithoutRef<typeof CommandPrimitive.Item>
>(({children, className, ...props}, ref) => (
  <CommandPrimitive.Item asChild ref={ref} {...props}>
    <Button className={className} variant='dropdown'>
      {children}
    </Button>
  </CommandPrimitive.Item>
));
CommandItem.displayName = CommandPrimitive.Item.displayName;

const CommandSimpleItem = CommandPrimitive.Item;
CommandSimpleItem.displayName = 'CommandSimpleItem';

const CommandShortcut = ({className, ...props}: HTMLAttributes<HTMLSpanElement>) => {
  return <span className={classes('ml-auto text-xs tracking-widest text-muted-foreground', className)} {...props} />;
};
CommandShortcut.displayName = 'CommandShortcut';

export {
  Command,
  CommandDialog,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
  CommandShortcut,
  CommandSimpleItem,
};
