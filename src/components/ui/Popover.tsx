'use client';

import {ComponentPropsWithoutRef, ComponentRef, forwardRef} from 'react';

import {Arrow, Close, Content, Portal, Root, Trigger} from '@radix-ui/react-popover';
import {XIcon} from 'lucide-react';
import {Inter} from 'next/font/google';

import {classes} from '@/utils/common';

const inter = Inter({
  subsets: ['latin'],
  variable: '--font-sans',
});

const Popover = Root;

const PopoverTrigger = Trigger;

const PopoverContent = forwardRef<
  ComponentRef<typeof Content>,
  ComponentPropsWithoutRef<typeof Content> & {withoutArrow?: boolean}
>(
  (
    {
      align = 'center',
      avoidCollisions = true,
      children,
      className,
      collisionPadding = 10,
      sideOffset = 4,
      withoutArrow,
      ...props
    },
    ref,
  ) => (
    <Portal>
      <Content
        align={align}
        avoidCollisions={avoidCollisions}
        className={classes(
          'z-50 max-h-[var(--radix-popover-content-available-height)] max-w-[var(--radix-popover-trigger-width)] overflow-y-auto rounded-md border bg-background p-6 font-sans text-foreground shadow-lg outline-hidden data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2',
          inter.variable,
          className,
        )}
        collisionPadding={collisionPadding}
        ref={ref}
        sideOffset={sideOffset}
        {...props}
      >
        {children}
        {!withoutArrow && <Arrow className='arrow' />}
      </Content>
    </Portal>
  ),
);
PopoverContent.displayName = Content.displayName;

const PopoverClose = Close;
PopoverClose.displayName = 'PopoverClose';

const PopoverXClose = forwardRef<HTMLButtonElement, ComponentPropsWithoutRef<typeof Close>>((props, ref) => (
  <Close
    className='absolute right-2 top-2 opacity-70 ring-0 transition-opacity hover:opacity-100 focus:outline-hidden disabled:pointer-events-none data-[state=open]:bg-background'
    ref={ref}
    {...props}
  >
    <XIcon />
    <span className='sr-only'>Close</span>
  </Close>
));
PopoverXClose.displayName = 'PopoverXClose';

export {Popover, PopoverClose, PopoverContent, PopoverTrigger, PopoverXClose};
