'use client';

import 'react-day-picker/style.css';
import {ComponentProps, FC, useMemo} from 'react';

import {enUS, hu, ro} from 'date-fns/locale';
import {useLocale} from 'next-intl';
import {DayFlag, DayPicker, Matcher, SelectionState, UI} from 'react-day-picker';

import {buttonVariants} from '@/components/ui/Button';
import {classes} from '@/utils/common';

export type CalendarProps = ComponentProps<typeof DayPicker> & {
  disablePast?: boolean;
  disableWeekends?: boolean;
};

const Calendar: FC<CalendarProps> = ({
  className,
  classNames,
  disablePast,
  disableWeekends,
  showOutsideDays = true,
  ...props
}) => {
  const locale = useLocale();

  const matcher: Matcher = useMemo(() => {
    return (date: Date) => {
      const today = new Date();

      today.setHours(0, 0, 0, 0);

      if (disablePast && date < today) return true;

      const day = date.getDay();

      return !!(disableWeekends && (day === 0 || day === 6));
    };
  }, [disablePast, disableWeekends]);

  return (
    <DayPicker
      className={classes('rounded-lg p-3', className)}
      classNames={{
        [DayFlag.disabled]: 'text-muted-foreground opacity-50',
        [DayFlag.hidden]: 'invisible',
        [DayFlag.outside]:
          'text-muted-foreground opacity-50 aria-selected:bg-accent/50 aria-selected:text-muted-foreground aria-selected:opacity-30',
        [DayFlag.today]: 'font-bold text-button-primary-hovered!',
        [SelectionState.range_end]: 'rounded-r-md',
        [SelectionState.range_middle]:
          'aria-selected:bg-accent aria-selected:text-accent-foreground aria-selected:hover:bg-accent/30',
        [SelectionState.range_start]: 'rounded-l-md',
        [SelectionState.selected]:
          'bg-primary text-primary-foreground hover:bg-primary focus:bg-accent focus:text-primary-foreground transition-colors duration-200',
        [UI.CaptionLabel]: 'text-sm font-semibold text-foreground',
        [UI.Day]: 'size-9 text-center text-sm p-0 relative hover:bg-accent transition-colors duration-200 font-medium',
        [UI.DayButton]: 'size-9 p-0 transition-transform hover:scale-110 duration-200',
        [UI.Month]: 'space-y-4',
        [UI.MonthCaption]: 'text-center',
        [UI.MonthGrid]: 'w-full border-collapse space-y-1',
        [UI.Months]: 'flex flex-col sm:flex-row space-y-4 sm:space-y-0 relative gap-4',
        [UI.Nav]: 'absolute flex w-full justify-between items-center',
        [UI.NextMonthButton]: classes(
          buttonVariants({variant: 'none'}),
          'size-7 rounded-full bg-transparent p-0 opacity-50 transition-all duration-200 hover:bg-accent hover:opacity-100',
        ),
        [UI.PreviousMonthButton]: classes(
          buttonVariants({variant: 'none'}),
          'size-7 rounded-full bg-transparent p-0 opacity-50 transition-all duration-200 hover:bg-accent hover:opacity-100',
        ),
        [UI.Root]: 'flex justify-center items-center',
        [UI.Week]: 'w-full mt-2',
        [UI.Weekday]: 'text-muted-foreground rounded-md w-9 font-semibold uppercase text-[0.7rem] tracking-wider',
        ...classNames,
      }}
      // components={{
      //   IconLeft: ({className, ...props}) => <ChevronLeftIcon className={classes('size-6', className)} {...props} />,
      //   IconRight: ({className, ...props}) => (
      //     <ChevronLeftIcon className={classes('size-6 rotate-180', className)} {...props} />
      //   ),
      // }}
      disabled={matcher}
      locale={locale === 'hu' ? hu : locale === 'ro' ? ro : enUS}
      showOutsideDays={showOutsideDays}
      {...props}
    />
  );
};
Calendar.displayName = 'Calendar';

export {Calendar};
