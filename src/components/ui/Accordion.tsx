'use client';

import {ComponentPropsWithoutRef, ComponentRef, forwardRef} from 'react';

import {Content, Header, Item, Root, Trigger} from '@radix-ui/react-accordion';
import {ChevronDownIcon} from 'lucide-react';

import {classes} from '@/utils/common';

const Accordion = Root;

const AccordionItem = forwardRef<ComponentRef<typeof Item>, ComponentPropsWithoutRef<typeof Item>>(
  ({className, ...props}, ref) => <Item className={className} ref={ref} {...props} />,
);
AccordionItem.displayName = 'AccordionItem';

const AccordionTrigger = forwardRef<ComponentRef<typeof Trigger>, ComponentPropsWithoutRef<typeof Trigger>>(
  ({children, className, ...props}, ref) => (
    <Header className='flex ring-0 focus-visible:outline-hidden'>
      <Trigger
        className={classes(
          'flex flex-1 items-center justify-between py-4 font-medium ring-0 transition-transform hover:bg-input focus-visible:outline-hidden [&[data-state=open]>svg]:rotate-180',
          className,
        )}
        ref={ref}
        {...props}
      >
        {children}
        <ChevronDownIcon className='mr-0.5 shrink-0 ring-0 transition-transform duration-200 focus-visible:outline-hidden' />
      </Trigger>
    </Header>
  ),
);
AccordionTrigger.displayName = Trigger.displayName;

type ContentProps = ComponentPropsWithoutRef<typeof Content> & {
  quick?: boolean;
};

const AccordionContent = forwardRef<ComponentRef<typeof Content>, ContentProps>(
  ({children, className, quick, ...props}, ref) => (
    <Content
      className={classes(
        'overflow-hidden text-sm ring-0 transition-all focus-visible:outline-hidden',
        quick
          ? 'data-[state=closed]:animate-accordion-up-quick data-[state=open]:animate-accordion-down-quick'
          : 'data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down',
      )}
      ref={ref}
      {...props}
    >
      <div className={classes('pb-4 pt-2', className)}>{children}</div>
    </Content>
  ),
);

AccordionContent.displayName = Content.displayName;

export {Accordion, AccordionContent, AccordionItem, AccordionTrigger};
