'use client';

import {forwardRef, useEffect, useRef, useState} from 'react';

import {Editor} from '@tinymce/tinymce-react';
import {useLocale} from 'next-intl';

import {classes} from '@/utils/common';

type ExtraButton = {
  menu?: Array<{
    onAction: (resetContent: (html: string) => void) => void;
    text: string;
  }>;
  name: string;
  onAction?: (resetContent: (html: string) => void) => void;
  text: string;
  tooltip?: string;
};

type Props = {
  disabled?: boolean;
  extraButtons?: ExtraButton[];
  onChange: (html: string) => void;
  value: string;
};

const HTMLEditor = forwardRef<HTMLDivElement, Props>(({disabled, extraButtons, onChange, value}, ref) => {
  const editorRef = useRef<any>(null);
  const [isEditorReady, setIsEditorReady] = useState(false);
  const locale = useLocale();

  useEffect(() => {
    if (editorRef.current && value !== editorRef.current.getContent()) {
      editorRef.current.setContent(value || '');
    }
  }, [value]);

  const handleEditorChange = (content: string) => {
    onChange(content);
  };

  const resetContent = (html: string) => {
    if (editorRef.current) {
      editorRef.current.setContent(html);
      onChange(html);
    }
  };

  return (
    <div className='size-full' ref={ref}>
      <div className={classes('size-full cursor-text', !isEditorReady && 'hidden')}>
        <Editor
          apiKey='bmdnfkn8zzi8lkgzh7wm2ewzlwkg9r8qua245zkvy69kgzyq'
          disabled={disabled}
          init={{
            content_css: false,
            content_style: 'body { font-family:Inter,sans-serif; font-size:12px;}',
            font_family_formats:
              'Inter=Inter,sans-serif,Andale Mono=andale mono,times; Arial=arial,helvetica,sans-serif; Arial Black=arial black,avant garde; Book Antiqua=book antiqua,palatino; Comic Sans MS=comic sans ms,sans-serif; Courier New=courier new,courier; Georgia=georgia,palatino; Helvetica=helvetica; Impact=impact,chicago; Symbol=symbol; Tahoma=tahoma,arial,helvetica,sans-serif; Terminal=terminal,monaco; Times New Roman=times new roman,times; Trebuchet MS=trebuchet ms,geneva; Verdana=verdana,geneva; Webdings=webdings; Wingdings=wingdings,zapf dingbats',
            font_size_formats: '8px 10px 12px 14px 16px 18px 20px',
            height: '100%',
            language: locale,

            menubar: false,
            plugins: ['lists', 'table', 'autolink', 'link', 'fontfamily', 'fontsize'],
            resize: false,
            setup: (editor: any) => {
              editor.on('Change KeyUp', () => {
                handleEditorChange(editor.getContent());
              });

              editor.on('NodeChange', () => {
                const tables: Element[] = editor.dom.select('table:not([data-styled])');
                tables.forEach((table) => {
                  table.setAttribute('style', 'border: 1px solid #ccc; border-collapse: collapse; width: 100%;');
                  table.setAttribute('data-styled', 'true');

                  Array.from(table.querySelectorAll('th,td')).forEach((cell) => {
                    cell.setAttribute('style', 'border: 1px solid #ccc; padding: 8px;');
                  });
                });
              });
            },
            statusbar: false,
            toolbar: disabled
              ? false
              : [
                  'bold italic underline',
                  'fontfamily fontsize',
                  'alignleft aligncenter alignright',
                  'bullist numlist',
                  'table',
                  ...(extraButtons && extraButtons.length > 0 ? extraButtons.map((b) => b.name) : []),
                ].join(' | '),
          }}
          onInit={(_evt, editor) => {
            editorRef.current = editor;
            if (extraButtons && Array.isArray(extraButtons)) {
              extraButtons.forEach((btn) => {
                if (btn.menu && btn.menu.length > 0) {
                  if (!editor.ui.registry.getAll().buttons[btn.name]) {
                    editor.ui.registry.addMenuButton(btn.name, {
                      fetch: (callback: (items: any[]) => void) => {
                        callback(
                          btn.menu!.map((item) => ({
                            onAction: () => item.onAction(resetContent),
                            text: item.text,
                            type: 'menuitem',
                          })),
                        );
                      },
                      text: btn.text,
                      tooltip: btn.tooltip,
                    });
                  }
                } else {
                  if (!editor.ui.registry.getAll().buttons[btn.name]) {
                    editor.ui.registry.addButton(btn.name, {
                      onAction: () => btn.onAction && btn.onAction(resetContent),
                      text: btn.text,
                      tooltip: btn.tooltip,
                    });
                  }
                }
              });
            }

            setIsEditorReady(true);
          }}
          value={value}
        />
      </div>
    </div>
  );
});

export default HTMLEditor;
