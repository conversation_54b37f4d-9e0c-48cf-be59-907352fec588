'use client';

import {ComponentPropsWithoutRef, ComponentRef, forwardRef, HTMLAttributes} from 'react';

import {Fallback, Image, Root} from '@radix-ui/react-avatar';
import {cva, type VariantProps} from 'class-variance-authority';

import {classes} from '@/utils/common';

const contentVariants = cva('', {
  defaultVariants: {
    position: 'bottom-right',
  },
  variants: {
    position: {
      'bottom-left': 'bottom-[14%] left-[14%] -translate-x-2/4 translate-y-2/4',
      'bottom-right': 'bottom-[14%] right-[14%] translate-x-2/4 translate-y-2/4',
      'top-left': 'left-[14%] top-[14%] -translate-x-2/4 -translate-y-2/4',
      'top-right': 'right-[14%] top-[14%] -translate-y-2/4 translate-x-2/4',
    },
  },
});

type ContentProps = HTMLAttributes<HTMLDivElement> &
  VariantProps<typeof contentVariants> & {
    badge?: string;
    pulse?: boolean;
  };

const AvatarBadge = forwardRef<HTMLDivElement, ContentProps>(
  ({badge, children, className, position, pulse, ...props}, ref) => (
    <div className={classes(!!badge && 'relative size-fit', className)} ref={ref} {...props}>
      {children}
      {badge && (
        <span
          className={classes(
            contentVariants({position}),
            'absolute rounded-full',
            pulse && 'animate-pulse',
            'border-2 border-white bg-red-500 px-1 text-center text-xs font-bold text-white',
          )}
        >
          {badge}
        </span>
      )}
    </div>
  ),
);
AvatarBadge.displayName = 'AvatarBadge';

const Avatar = forwardRef<ComponentRef<typeof Root>, ComponentPropsWithoutRef<typeof Root>>(
  ({className, ...props}, ref) => (
    <Root
      className={classes('relative flex size-8 shrink-0 overflow-hidden rounded-full', className)}
      ref={ref}
      {...props}
    />
  ),
);
Avatar.displayName = Root.displayName;

const AvatarImage = forwardRef<ComponentRef<typeof Image>, ComponentPropsWithoutRef<typeof Image>>(
  ({className, ...props}, ref) => (
    <Image alt='' className={classes('aspect-square size-full', className)} ref={ref} {...props} />
  ),
);
AvatarImage.displayName = Image.displayName;

const AvatarFallback = forwardRef<ComponentRef<typeof Fallback>, ComponentPropsWithoutRef<typeof Fallback>>(
  ({className, ...props}, ref) => (
    <Fallback
      className={classes('flex size-full items-center justify-center rounded-full text-background', className)}
      ref={ref}
      {...props}
    />
  ),
);
AvatarFallback.displayName = Fallback.displayName;

export {Avatar, AvatarBadge, AvatarFallback, AvatarImage};
