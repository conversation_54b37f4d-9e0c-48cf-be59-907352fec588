import {forwardRef, TextareaHTMLAttributes} from 'react';

import {cva, VariantProps} from 'class-variance-authority';
import ReactTextareaAutosize, {TextareaAutosizeProps as ReactTextareaAutosizeProps} from 'react-textarea-autosize';

import {classes} from '@/utils/common';

const textareaVariants = cva(
  'flex bg-background py-2 text-sm ring-0 file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-hidden disabled:cursor-not-allowed disabled:opacity-50',
  {
    defaultVariants: {
      error: false,
      size: 'default',
      variant: 'default',
    },
    variants: {
      error: {
        false: '',
        true: 'border-b border-red! text-red',
      },
      size: {
        default: 'w-full',
        md: 'w-24',
        none: '',
        sm: 'w-16',
      },
      variant: {
        default: 'border-b border-muted placeholder:text-muted',
        outline: 'rounded-md border border-border placeholder:text-muted focus:bg-input',
      },
    },
  },
);

export type TextareaProps = TextareaHTMLAttributes<HTMLTextAreaElement> & VariantProps<typeof textareaVariants>;

const Textarea = forwardRef<HTMLTextAreaElement, TextareaProps>(
  ({className, error, rows, size, variant, ...props}, ref) => {
    return (
      <textarea
        className={classes(
          textareaVariants({className, error, size, variant}),
          ((size && size !== 'none') || rows === undefined) && 'h-10',
          'peer',
          className,
        )}
        ref={ref}
        rows={rows}
        {...props}
      />
    );
  },
);
Textarea.displayName = 'Textarea';

export type TextareaAutosizeProps = ReactTextareaAutosizeProps & VariantProps<typeof textareaVariants>;

const TextareaAutosize = forwardRef<HTMLTextAreaElement, TextareaAutosizeProps>(
  ({className, error, size, variant, ...props}, ref) => {
    return (
      <ReactTextareaAutosize
        className={classes(textareaVariants({className, error, size, variant}), 'peer', className)}
        ref={ref}
        {...props}
      />
    );
  },
);
TextareaAutosize.displayName = 'TextareaAutosize';

export {Textarea, TextareaAutosize};
