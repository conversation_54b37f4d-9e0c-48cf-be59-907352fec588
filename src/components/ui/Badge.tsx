import {ForwardedRef, forwardRef, HTMLAttributes} from 'react';

import {cva, type VariantProps} from 'class-variance-authority';

import {classes} from '@/utils/common';

const badgeVariants = cva(
  'inline-flex cursor-default select-none items-center whitespace-nowrap rounded-full border ring-0 transition-colors focus:outline-hidden',
  {
    defaultVariants: {
      size: 'md',
      variant: 'default',
    },
    variants: {
      size: {
        lg: 'h-10 px-3.5 py-0.5 text-base',
        md: 'h-8 px-2.5 py-0.5 text-sm',
        sm: 'h-6 px-2 py-0.5 text-xs',
      },
      variant: {
        default: 'border-gray-dark text-foreground',
        error: 'border-red-dark bg-red-light text-red-dark',
        info: 'border-blue-dark bg-blue-light text-blue-dark',
        light: 'border-border text-foreground',
        none: 'border-transparent text-foreground',
        primary: 'border-transparent bg-button-primary hover:bg-button-primary-hovered',
        secondary: 'border-gray-dark bg-gray-light text-gray-dark',
        success: 'border-green-dark bg-green-light text-green-dark',
        warning: 'border-yellow-dark bg-yellow-light text-yellow-dark',
      },
    },
  },
);

export type BadgeProps = HTMLAttributes<HTMLDivElement> & VariantProps<typeof badgeVariants>;

const Badge = forwardRef(({className, size, variant, ...props}: BadgeProps, ref: ForwardedRef<HTMLDivElement>) => {
  return <div className={classes(badgeVariants({size, variant}), className)} ref={ref} {...props} />;
});

export {Badge};
