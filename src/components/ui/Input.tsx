import {
  ChangeEvent,
  ComponentPropsWithoutRef,
  FC,
  FocusEvent,
  forwardRef,
  InputHTMLAttributes,
  KeyboardEvent,
  ReactNode,
  useCallback,
  useEffect,
  useRef,
  useState,
} from 'react';

import {cva, VariantProps} from 'class-variance-authority';
import Jo<PERSON>, {Schema} from 'joi';
import {clamp, isNaN} from 'lodash';
import {ChevronDownIcon, ChevronUpIcon, MinusIcon, PencilIcon, PlusIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';

import {Button} from '@/components/ui/Button';
import {Label, LabelType, WithLabel} from '@/components/ui/Label';
import {Popover, PopoverContent, PopoverTrigger} from '@/components/ui/Popover';
import useClickOutside from '@/hooks/helpers/useClickOutside';
import {classes, estimateTextWidth} from '@/utils/common';

const inputVariants = cva(
  'flex h-10 bg-background py-2 text-sm ring-0 file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-hidden disabled:cursor-not-allowed disabled:opacity-50',
  {
    defaultVariants: {
      error: false,
      size: 'default',
      variant: 'default',
    },
    variants: {
      error: {
        false: '',
        true: 'border-b border-red! text-red',
      },
      size: {
        '2xl': 'w-72',
        default: 'w-full',
        lg: 'w-48',
        md: 'w-24',
        sm: 'w-16',
        xl: 'w-64',
      },
      variant: {
        default: 'border-b border-muted placeholder:text-muted',
        outline: 'rounded-md border border-border placeholder:text-muted focus:bg-input',
      },
    },
  },
);

export type InputProps = Omit<InputHTMLAttributes<HTMLInputElement>, 'size'> & VariantProps<typeof inputVariants>;

const Input = forwardRef<HTMLInputElement, InputProps>(
  ({className, defaultValue, error, onChange, size, type, value, variant, ...props}, ref) => {
    const [internalValue, setInternalValue] = useState(value ?? defaultValue ?? '');

    useEffect(() => {
      if (value !== undefined) {
        setInternalValue(value);
      }
    }, [value]);

    const handleChange = (event: ChangeEvent<HTMLInputElement>) => {
      let val = event.target.value;

      if (type === 'number') {
        if (val === '') {
          setInternalValue('');
          onChange?.(event);
          return;
        }

        const numberRegex = /^-?\d*\.?\d*$/;
        if (numberRegex.test(val)) {
          setInternalValue(val);
          onChange?.(event);
        }
      } else {
        setInternalValue(val);
        onChange?.(event);
      }
    };

    return (
      <input
        className={classes(inputVariants({className, error, size, variant}), 'peer', className)}
        defaultValue={defaultValue}
        inputMode={type === 'number' ? 'decimal' : undefined}
        onChange={handleChange}
        ref={ref}
        type={type === 'number' ? 'text' : type}
        value={value !== undefined ? internalValue : undefined}
        {...props}
      />
    );
  },
);
Input.displayName = 'Input';

type InlineInputProps = InputProps & {
  defaultEnabled?: boolean;
  displayValue: string | undefined;
  error?: boolean;
  inlineClassName?: string;
  onEscape?: () => void;
};

const InlineInput = forwardRef<HTMLInputElement, InlineInputProps>(
  (
    {className, defaultEnabled, disabled, displayValue, error, inlineClassName, onBlur, onEscape, onKeyDown, ...props},
    ref,
  ) => {
    const [editEnabled, setEditEnabled] = useState(!displayValue || defaultEnabled || false);

    const handleKeyDown = useCallback(
      (event: KeyboardEvent<HTMLInputElement>) => {
        if (['Enter', 'Escape'].includes(event.key)) setEditEnabled(false);
        if (event.key === 'Escape' || error) onEscape?.();
        if (event.key === 'Enter' && !error) event.currentTarget.blur();
        onKeyDown?.(event);
      },
      [onEscape, onKeyDown, error],
    );

    const handleBlur = useCallback(
      (event: FocusEvent<HTMLInputElement>) => {
        setEditEnabled(false);
        onBlur?.(event);
        if (error) onEscape?.();
      },
      [onBlur, onEscape, error],
    );

    const handleEnableEdit = useCallback(() => {
      setEditEnabled(true);
    }, []);

    if (disabled || !editEnabled)
      return (
        <div
          className={classes(
            'group relative flex items-center',
            !disabled && 'cursor-pointer',
            error && 'text-red',
            inlineClassName,
          )}
          onClick={handleEnableEdit}
        >
          {displayValue}
          {!disabled && (
            <div className='absolute -right-1 hidden translate-x-full group-hover:block'>
              <PencilIcon className='size-4' />
            </div>
          )}
        </div>
      );

    if (!disabled && editEnabled)
      return (
        <Input
          autoFocus
          className={classes('px-0', className)}
          onBlur={handleBlur}
          onKeyDown={handleKeyDown}
          ref={ref}
          {...props}
        />
      );
  },
);
InlineInput.displayName = 'InlineInput';

const InputLabel = forwardRef<HTMLLabelElement, LabelType>(({className, ...props}, ref) => (
  <Label className={classes('text-xs font-medium opacity-60', className)} ref={ref} {...props} />
));
InputLabel.displayName = 'InputLabel';

type PopoverInputProps = Omit<InputProps, 'children' | 'className' | 'defaultValue' | 'onChange'> &
  Pick<ComponentPropsWithoutRef<typeof PopoverContent>, 'align' | 'side'> & {
    children: ReactNode;
    className?: string;
    defaultValue?: string;
    inputClassName?: string;
    label?: string;
    onChange?: (value: string) => void;
    validation?: Schema;
    withoutIcon?: boolean;
  };

const PopoverInput: FC<PopoverInputProps> = ({
  align,
  children,
  className,
  defaultValue = '',
  disabled,
  error: displayError,
  inputClassName,
  label,
  onChange,
  onKeyDown,
  side,
  size = 'default',
  validation = Joi.string(),
  withoutIcon,
  ...rest
}) => {
  const [value, setValue] = useState(defaultValue);
  const [open, setOpen] = useState(false);
  const [error, setError] = useState(false);
  const ref = useRef(null);

  useClickOutside([ref], () => {
    setOpen(false);
    handleChange(value, true);
  });

  useEffect(() => {
    setValue(defaultValue);
  }, [defaultValue]);

  const handleClose = useCallback(
    (reset?: boolean) => {
      setOpen(false);
      setTimeout(() => {
        if (reset) setValue(defaultValue);
        setError(false);
      }, 200);
    },
    [defaultValue],
  );

  const handleChange = useCallback(
    (val: string, close?: boolean) => {
      const {error: err} = validation.validate(val);

      if (!err && val !== defaultValue) {
        onChange?.(val);
        if (defaultValue === '') setValue('');
      }
      if (err && !close) setError(true);
      if (!err || close) handleClose(close && !!err);
    },
    [defaultValue, handleClose, onChange, validation],
  );

  const handleKeyDown = useCallback(
    (event: KeyboardEvent<HTMLInputElement>) => {
      onKeyDown?.(event);
      if (event.key === 'Escape') handleClose(true);
      if (event.key === 'Enter') handleChange(value);
      if (event.key !== 'Enter') setError(false);
    },
    [handleChange, handleClose, onKeyDown, value],
  );

  return (
    <Popover open={open}>
      <PopoverTrigger asChild>
        <Button
          className={classes(
            'group w-full justify-normal',
            disabled && 'cursor-default',
            !disabled && displayError && 'text-red',
            className,
          )}
          onClick={() => !disabled && setOpen(true)}
          size='none'
          variant='none'
        >
          <div className={classes('relative', className)}>
            {String(children).trim() === '' ? '-' : children}
            {!withoutIcon && !disabled && (
              <div className='absolute -right-1 top-1/2 hidden -translate-y-1/2 translate-x-full group-hover:block'>
                <PencilIcon className='size-4' />
              </div>
            )}
          </div>
        </Button>
      </PopoverTrigger>
      <PopoverContent align={align} className='max-w-none p-4' ref={ref} side={side}>
        <WithLabel>
          <Input
            {...rest}
            className={classes('min-w-10', inputClassName)}
            error={error}
            onChange={({target: {value: val}}) => setValue(val)}
            onKeyDown={handleKeyDown}
            size={size}
            style={{width: `${estimateTextWidth(value)}px`}}
            value={value}
          />
          <InputLabel>{label}</InputLabel>
        </WithLabel>
      </PopoverContent>
    </Popover>
  );
};
PopoverInput.displayName = 'PopoverInput';

type NumberInputProps = {
  className?: string;
  disabled?: boolean;
  indicator?: ReactNode;
  max?: number;
  min?: number;
  onChange?: (value: number) => void;
  small?: boolean;
  value: number;
};

const NumberInput: FC<NumberInputProps> = ({
  className,
  disabled,
  indicator,
  max = Infinity,
  min = 1,
  onChange,
  small,
  value,
}) => {
  const [number, setNumber] = useState(value);
  const [isEmpty, setEmpty] = useState(false);

  useEffect(() => {
    setNumber(value);
  }, [value]);

  return (
    <div className={classes('inline-flex items-center justify-center', className)}>
      <Button
        className='rounded-r-none'
        disabled={disabled || number <= min}
        onClick={(event) => {
          onChange?.(clamp(number - 1, min, max));
          event.preventDefault();
          setNumber((prev) => clamp(prev - 1, min, max));
        }}
        size={small ? 'sm' : 'default'}
        variant='secondary'
      >
        <div className={classes((disabled || number <= min) && 'text-gray-400')}>
          <MinusIcon className='size-4' />
        </div>
      </Button>
      <div className='relative'>
        <Input
          className={classes('rounded-none border-x-0 text-center', small && 'h-8 w-10')}
          disabled={disabled}
          onBlur={({currentTarget: {value: val}}) => {
            onChange?.(clamp(Number(val), min, max));
            setEmpty(false);
          }}
          onChange={({target: {value: val}}) => {
            if (val === '' || isNaN(Number(val))) {
              setEmpty(true);
              setNumber(clamp(0, min, max));
            } else {
              setEmpty(false);
              setNumber(clamp(Number(val), min, max));
            }
          }}
          onClick={(event) => event.preventDefault()}
          onKeyDown={({currentTarget: {value: val}, key}) => {
            if (key === 'ArrowUp') {
              onChange?.(clamp(Number(val) + 1, min, max));
              setEmpty(false);
            } else if (key === 'ArrowDown') {
              onChange?.(clamp(Number(val) - 1, min, max));
              setEmpty(false);
            } else if (key === 'Enter') {
              onChange?.(clamp(Number(val), min, max));
              setEmpty(false);
            }
          }}
          size='sm'
          type='number'
          value={isEmpty ? '' : number}
          variant='outline'
        />
        {indicator && <div className='absolute right-0.5 top-1/2 -translate-y-1/2'>{indicator}</div>}
      </div>
      <Button
        className='rounded-l-none'
        disabled={disabled || number >= max}
        onClick={(event) => {
          onChange?.(clamp(number + 1, min, max));
          event.preventDefault();
          setNumber((prev) => clamp(prev + 1, min, max));
        }}
        size={small ? 'sm' : 'default'}
        variant='secondary'
      >
        <div className={classes((disabled || number >= max) && 'text-gray-400')}>
          <PlusIcon className='size-4' />
        </div>
      </Button>
    </div>
  );
};
NumberInput.displayName = 'NumberInput';

type DurationTimeInputProps = {
  autoFocus?: boolean;
  disabled?: boolean;
  error?: boolean;
  label: string;
  limit: number;
  onChange: (value: number) => void;
  onKeyDown?: (event: KeyboardEvent<HTMLInputElement>) => void;
  value: number;
};

const DurationTimeInput: FC<DurationTimeInputProps> = ({
  autoFocus,
  disabled,
  error,
  label,
  limit,
  onChange,
  onKeyDown,
  value,
}) => {
  const [isEmpty, setEmpty] = useState(false);

  return (
    <WithLabel className='mx-0.5 flex flex-col items-center'>
      <InputLabel className='mb-2 w-full text-center'>{label}</InputLabel>
      <Button
        className='h-4 rounded-b-none border-b-transparent'
        disabled={disabled || value >= limit}
        onClick={() => {
          onChange(clamp(value + 1, 0, limit));
          setEmpty(false);
        }}
        size='full'
        variant='secondary'
      >
        <ChevronUpIcon className='size-4' />
      </Button>
      <Input
        autoFocus={autoFocus}
        className='w-14 rounded-none text-center'
        disabled={disabled}
        error={error}
        onChange={({target: {value: val}}) => {
          const numberValue = parseInt(val, 10);

          if (val === '' || isNaN(numberValue)) {
            setEmpty(true);
            onChange(0);
          } else {
            setEmpty(false);
            onChange(clamp(numberValue, 0, limit));
          }
        }}
        onKeyDown={(event) => {
          if (event.key === 'ArrowUp') {
            onChange(clamp(value + 1, 0, limit));
            setEmpty(false);
          } else if (event.key === 'ArrowDown') {
            onChange(clamp(value - 1, 0, limit));
            setEmpty(false);
          }

          onKeyDown?.(event);
        }}
        type='number'
        value={isEmpty ? '' : value}
        variant='outline'
      />
      <Button
        className='h-4 rounded-t-none border-t-transparent'
        disabled={disabled || value <= 0}
        onClick={() => {
          onChange(clamp(value - 1, 0, limit));
          setEmpty(false);
        }}
        size='full'
        variant='secondary'
      >
        <ChevronDownIcon className='size-4' />
      </Button>
    </WithLabel>
  );
};

type DurationInputProps = {
  className?: string;
  disabled?: boolean;
  error?: boolean;
  minutes: number;
  onChange?: (minutes: number) => void;
  onKeyDown?: (event: KeyboardEvent<HTMLInputElement>) => void;
};

const DurationInput: FC<DurationInputProps> = ({className, disabled, error, minutes: value, onChange, onKeyDown}) => {
  const t = useTranslations();
  const [days, setDays] = useState(Math.floor(value / (24 * 60)));
  const [hours, setHours] = useState(Math.floor((value % (24 * 60)) / 60));
  const [minutes, setMinutes] = useState(value % 60);

  useEffect(() => {
    setDays(Math.floor(value / (24 * 60)));
    setHours(Math.floor((value % (24 * 60)) / 60));
    setMinutes(value % 60);
  }, [value]);

  const handleOnChange = (days: number, hours: number, minutes: number) => {
    const totalMinutes = days * 24 * 60 + hours * 60 + minutes;
    onChange?.(totalMinutes);
  };

  return (
    <div className={classes('flex items-center', className)}>
      <DurationTimeInput
        disabled={disabled}
        error={error}
        label={t('days')}
        limit={999}
        onChange={(value) => {
          setDays(value);
          handleOnChange(value, hours, minutes);
        }}
        onKeyDown={onKeyDown}
        value={days}
      />
      <DurationTimeInput
        disabled={disabled}
        error={error}
        label={t('hours')}
        limit={23}
        onChange={(value) => {
          setHours(value);
          handleOnChange(days, value, minutes);
        }}
        onKeyDown={onKeyDown}
        value={hours}
      />
      <DurationTimeInput
        autoFocus
        disabled={disabled}
        error={error}
        label={t('minutes')}
        limit={59}
        onChange={(value) => {
          setMinutes(value);
          handleOnChange(days, hours, value);
        }}
        onKeyDown={onKeyDown}
        value={minutes}
      />
    </div>
  );
};
DurationInput.displayName = 'DurationInput';

type PopoverDurationInputProps = Omit<DurationInputProps, 'className' | 'minutes' | 'onChange'> &
  Pick<ComponentPropsWithoutRef<typeof PopoverContent>, 'align' | 'side'> & {
    children: ReactNode;
    className?: string;
    error?: boolean;
    inputClassName?: string;
    minutes?: number;
    onChange?: (value: number) => void;
    withoutIcon?: boolean;
  };

const PopoverDurationInput: FC<PopoverDurationInputProps> = ({
  align,
  children,
  className,
  disabled,
  error: displayError,
  inputClassName,
  minutes,
  onChange,
  side,
  withoutIcon,
}) => {
  const [value, setValue] = useState(minutes || 0);
  const [open, setOpen] = useState(false);
  const [error, setError] = useState(false);
  const ref = useRef(null);

  useClickOutside([ref], () => {
    setOpen(false);
    handleChange(value, true);
  });

  useEffect(() => {
    setValue(minutes || 0);
  }, [minutes]);

  const handleClose = useCallback((reset?: boolean) => {
    setOpen(false);
    setTimeout(() => {
      if (reset) setValue(0);
      setError(false);
    }, 200);
  }, []);

  const handleChange = useCallback(
    (val: number, close?: boolean) => {
      if (val && val !== minutes) {
        onChange?.(val);
      }
      if (!val && !close) setError(true);
      if (val || close) handleClose(close && !val);
    },
    [handleClose, minutes, onChange],
  );

  const handleKeyDown = useCallback(
    (event: KeyboardEvent<HTMLInputElement>) => {
      if (event.key === 'Escape') handleClose(true);
      if (event.key === 'Enter') handleChange(value);
      if (event.key !== 'Enter') setError(false);
    },
    [handleChange, handleClose, value],
  );

  return (
    <Popover open={open}>
      <PopoverTrigger asChild>
        <Button
          className={classes(
            'group w-full justify-normal',
            disabled && 'cursor-default',
            !disabled && displayError && 'text-red',
            className,
          )}
          onClick={() => !disabled && setOpen(true)}
          size='none'
          variant='none'
        >
          <div className={classes('relative', className)}>
            {String(children).trim() === '' ? '-' : children}
            {!withoutIcon && !disabled && (
              <div className='absolute -right-1 top-1/2 hidden -translate-y-1/2 translate-x-full group-hover:block'>
                <PencilIcon className='size-4' />
              </div>
            )}
          </div>
        </Button>
      </PopoverTrigger>
      <PopoverContent align={align} className='max-w-none p-4' ref={ref} side={side}>
        <DurationInput
          className={inputClassName}
          error={error}
          minutes={value}
          onChange={setValue}
          onKeyDown={handleKeyDown}
        />
      </PopoverContent>
    </Popover>
  );
};
PopoverDurationInput.displayName = 'PopoverDurationInput';

export {
  DurationInput,
  DurationTimeInput,
  InlineInput,
  Input,
  InputLabel,
  NumberInput,
  PopoverDurationInput,
  PopoverInput,
};
