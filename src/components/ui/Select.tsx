'use client';

import {ComponentPropsWithoutRef, ComponentRef, forwardRef} from 'react';

import {
  Content,
  Group,
  Icon,
  Item,
  ItemIndicator,
  ItemText,
  Label,
  Portal,
  Root,
  ScrollDownButton,
  ScrollUpButton,
  Separator,
  Trigger,
  Value,
  Viewport,
} from '@radix-ui/react-select';
import {cva, VariantProps} from 'class-variance-authority';
import {CheckIcon, ChevronDownIcon, ChevronUpIcon} from 'lucide-react';
import {Inter} from 'next/font/google';
import {useTranslations} from 'next-intl';
import {Control, Controller, FieldValues, Path} from 'react-hook-form';

import {classes} from '@/utils/common';

const inter = Inter({
  subsets: ['latin'],
  variable: '--font-sans',
});

const Select = Root;

type ControlledProps<T extends FieldValues> = Omit<Props, 'value'> & {
  control: Control<T>;
  controlName: Path<T>;
};

type Props = ComponentPropsWithoutRef<typeof Root>;

const ControlledSelect = <T extends FieldValues>({
  control,
  controlName,
  onValueChange,
  ...props
}: ControlledProps<T>) => (
  <Controller
    control={control}
    name={controlName}
    render={({field}) => (
      <Select
        onValueChange={(value) => {
          field.onChange(value);
          onValueChange?.(value);
        }}
        value={field.value}
        {...props}
      />
    )}
  />
);
ControlledSelect.displayName = 'ControlledSelect';

const SelectGroup = Group;

type ValueProps = ComponentPropsWithoutRef<typeof Value> & {};

const SelectValue = forwardRef<ComponentRef<typeof Value>, ValueProps>(({placeholder, ...props}, ref) => {
  const t = useTranslations();

  return <Value placeholder={placeholder || `${t('select')}...`} ref={ref} {...props} />;
});
SelectValue.displayName = Value.displayName;

const selectTriggerVariants = cva(
  'flex w-full items-center justify-between gap-1 bg-background text-sm ring-0 focus:outline-hidden disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1 [&[data-placeholder]>span]:text-muted [&[data-state=open]>svg[data-type=collapse]]:rotate-180',
  {
    defaultVariants: {
      error: false,
      size: 'default',
      variant: 'default',
    },
    variants: {
      error: {
        false: '',
        true: 'border-red!',
      },
      size: {
        'badge-lg': 'h-10 px-3.5 py-0.5 text-base',
        'badge-md': 'h-8 px-2.5 py-0.5 text-sm',
        'badge-sm': 'h-6 px-2 py-0.5 text-xs',
        default: 'h-10 w-full',
        lg: 'h-10 w-32',
        md: 'h-10 w-24',
        sm: 'h-10 w-16',
        xl: 'h-10 w-48',
      },
      variant: {
        'badge-default': 'whitespace-nowrap rounded-full border border-gray-dark text-gray-dark transition-colors',
        'badge-error':
          'whitespace-nowrap rounded-full border border-red-dark bg-red-light text-red-dark transition-colors',
        'badge-info':
          'whitespace-nowrap rounded-full border border-blue-dark bg-blue-light text-blue-dark transition-colors',
        'badge-light': 'whitespace-nowrap rounded-full border border-border text-gray-dark transition-colors',
        // 'badge-none': 'whitespace-nowrap rounded-full border border-transparent text-gray-dark transition-colors',
        'badge-secondary':
          'whitespace-nowrap rounded-full border border-gray-dark bg-gray-light text-gray-dark transition-colors',
        'badge-success':
          'whitespace-nowrap rounded-full border border-green-dark bg-green-light text-green-dark transition-colors',
        'badge-warning':
          'whitespace-nowrap rounded-full border border-yellow-dark bg-yellow-light text-yellow-dark transition-colors',
        default: 'border-b border-muted',
        none: '',
        outline: 'rounded-md border border-border',
      },
    },
  },
);

type SelectTriggerProps = ComponentPropsWithoutRef<typeof Trigger> & VariantProps<typeof selectTriggerVariants>;

const SelectTrigger = forwardRef<ComponentRef<typeof Trigger>, SelectTriggerProps>(
  ({children, className, error, size, variant, ...props}, ref) => (
    <Trigger
      className={classes(selectTriggerVariants({className, error, size, variant}), 'peer', className)}
      ref={ref}
      {...props}
    >
      {children}
      <Icon asChild data-type='evotests'>
        <ChevronDownIcon className='size-4 opacity-50 transition-transform' data-type='collapse' />
      </Icon>
    </Trigger>
  ),
);
SelectTrigger.displayName = Trigger.displayName;

const SelectScrollUpButton = forwardRef<
  ComponentRef<typeof ScrollUpButton>,
  ComponentPropsWithoutRef<typeof ScrollUpButton>
>(({className, ...props}, ref) => (
  <ScrollUpButton
    className={classes('flex cursor-default items-center justify-center py-1', className)}
    ref={ref}
    {...props}
  >
    <ChevronUpIcon className='size-4' />
  </ScrollUpButton>
));
SelectScrollUpButton.displayName = ScrollUpButton.displayName;

const SelectScrollDownButton = forwardRef<
  ComponentRef<typeof ScrollDownButton>,
  ComponentPropsWithoutRef<typeof ScrollDownButton>
>(({className, ...props}, ref) => (
  <ScrollDownButton
    className={classes('flex cursor-default items-center justify-center py-1', className)}
    ref={ref}
    {...props}
  >
    <ChevronDownIcon className='size-4' />
  </ScrollDownButton>
));
SelectScrollDownButton.displayName = ScrollDownButton.displayName;

const SelectContent = forwardRef<ComponentRef<typeof Content>, ComponentPropsWithoutRef<typeof Content>>(
  ({children, className, position = 'popper', ...props}, ref) => (
    <Portal>
      <Content
        className={classes(
          'relative z-50 max-h-96 min-w-32 overflow-hidden rounded-md border bg-background font-sans text-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2',
          position === 'popper' &&
            'data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1',
          inter.variable,
          className,
        )}
        position={position}
        ref={ref}
        {...props}
      >
        <SelectScrollUpButton />
        <Viewport
          className={classes(
            'p-1',
            position === 'popper' &&
              'h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]',
          )}
        >
          {children}
        </Viewport>
        <SelectScrollDownButton />
      </Content>
    </Portal>
  ),
);
SelectContent.displayName = Content.displayName;

const SelectLabel = forwardRef<ComponentRef<typeof Label>, ComponentPropsWithoutRef<typeof Label>>(
  ({className, ...props}, ref) => (
    <Label className={classes('py-1.5 pl-8 pr-2 text-sm font-semibold', className)} ref={ref} {...props} />
  ),
);
SelectLabel.displayName = Label.displayName;

const SelectItem = forwardRef<ComponentRef<typeof Item>, ComponentPropsWithoutRef<typeof Item>>(
  ({children, className, ...props}, ref) => (
    <Item
      className={classes(
        'relative flex w-full cursor-pointer select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-hidden focus:bg-accent focus:text-accent-foreground data-disabled:pointer-events-none data-disabled:opacity-50',
        className,
      )}
      ref={ref}
      {...props}
    >
      <span className='absolute left-2 flex size-3.5 items-center justify-center'>
        <ItemIndicator>
          <CheckIcon className='size-4' />
        </ItemIndicator>
      </span>

      <ItemText>{children}</ItemText>
    </Item>
  ),
);
SelectItem.displayName = Item.displayName;

const SelectSeparator = forwardRef<ComponentRef<typeof Separator>, ComponentPropsWithoutRef<typeof Separator>>(
  ({className, ...props}, ref) => (
    <Separator className={classes('-mx-1 my-1 h-px bg-muted', className)} ref={ref} {...props} />
  ),
);
SelectSeparator.displayName = Separator.displayName;

export {
  ControlledSelect,
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectScrollDownButton,
  SelectScrollUpButton,
  SelectSeparator,
  SelectTrigger,
  SelectValue,
};
