import {ButtonHTMLAttributes, forwardRef, ReactNode} from 'react';

import {Slot} from '@radix-ui/react-slot';
import {cva, type VariantProps} from 'class-variance-authority';

import {classes} from '@/utils/common';

const buttonVariants = cva(
  'group inline-flex cursor-pointer h-10 shrink-0 items-center justify-center gap-1 whitespace-nowrap text-foreground ring-0 transition-colors focus-visible:outline-hidden disabled:pointer-events-none disabled:opacity-50',
  {
    defaultVariants: {
      size: 'default',
      variant: 'primary',
    },
    variants: {
      size: {
        default: 'px-4 text-sm',
        full: 'w-full',
        icon: 'size-6',
        none: 'h-auto',
        sm: 'h-8 px-1',
      },
      variant: {
        destructive: 'rounded-lg border border-transparent bg-red text-white hover:bg-red-dark ',
        dropdown:
          'flex h-fit w-full justify-start whitespace-normal bg-background py-2 pl-2 pr-1 text-left hover:bg-gray-200 data-[selected=true]:bg-gray-200',
        ghost: 'border border-transparent hover:bg-input',
        none: '',
        primary: 'rounded-lg border border-transparent bg-button-primary hover:bg-button-primary-hovered',
        secondary: 'rounded-lg border border-border bg-background  hover:bg-gray-200',
        success: 'rounded-lg border border-transparent bg-green text-white hover:bg-green-dark ',
        text: 'hover:underline',
      },
    },
  },
);

export type ButtonProps = ButtonHTMLAttributes<HTMLButtonElement> &
  VariantProps<typeof buttonVariants> & {
    asChild?: boolean;
  };

const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  ({asChild = false, className, size, variant, ...props}, ref) => {
    const Comp = asChild ? Slot : 'button';
    return (
      <Comp
        className={classes(
          buttonVariants({size: variant === 'text' && size === 'default' ? 'none' : size, variant}),
          className,
        )}
        ref={ref}
        {...props}
      />
    );
  },
);
Button.displayName = 'Button';

export type ButtonIconProps = ButtonHTMLAttributes<HTMLButtonElement> & {
  asChild?: boolean;
  icon: ReactNode;
};

const IconButton = forwardRef<HTMLButtonElement, ButtonIconProps>(({children, icon, ...props}, ref) => {
  return (
    <Button ref={ref} {...props} size='none' variant='none'>
      <div className='mr-2 rounded-full bg-button-primary group-hover:bg-button-primary-hovered'>{icon}</div>
      {children}
    </Button>
  );
});
IconButton.displayName = 'IconButton';

export {Button, buttonVariants, IconButton};
