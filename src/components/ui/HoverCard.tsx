'use client';

import {forwardRef} from 'react';

import {Arrow, Content, Portal, Root, Trigger} from '@radix-ui/react-hover-card';
import {Inter} from 'next/font/google';

import {classes} from '@/utils/common';

const inter = Inter({
  subsets: ['latin'],
  variable: '--font-sans',
});

const HoverCard = Root;

const HoverCardTrigger = Trigger;

const HoverCardContent = forwardRef<React.ComponentRef<typeof Content>, React.ComponentPropsWithoutRef<typeof Content>>(
  ({align = 'center', children, className, sideOffset = 4, ...props}, ref) => (
    <Portal>
      <Content
        align={align}
        className={classes(
          'z-50 max-h-[700px] max-w-[500px] overflow-y-auto rounded-md border bg-background p-4 font-sans text-foreground shadow-md outline-hidden data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2',
          inter.variable,
          className,
        )}
        ref={ref}
        sideOffset={sideOffset}
        {...props}
      >
        {children}
        <Arrow className='arrow' />
      </Content>
    </Portal>
  ),
);
HoverCardContent.displayName = Content.displayName;

export {HoverCard, HoverCardContent, HoverCardTrigger};
