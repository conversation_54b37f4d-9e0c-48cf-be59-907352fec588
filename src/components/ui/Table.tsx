import {
  forwardRef,
  HTMLAttributes,
  TdHTMLAttributes,
  ThHTMLAttributes,
  useCallback,
  useEffect,
  useRef,
  useState,
} from 'react';

import {LoadingBar} from '@/components/ui/special/LoadingBar';
import {classes} from '@/utils/common';

const TableContainer = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(({className, ...props}, ref) => (
  <div
    className={classes('relative w-full overflow-auto ring-0 focus-visible:outline-hidden', className)}
    ref={ref}
    {...props}
  />
));
TableContainer.displayName = 'TableContainer';

const Table = forwardRef<HTMLTableElement, HTMLAttributes<HTMLTableElement>>(({className, ...props}, ref) => (
  <table className={classes('w-full caption-bottom text-sm', className)} ref={ref} {...props} />
));
Table.displayName = 'Table';

const TableHeader = forwardRef<HTMLTableSectionElement, HTMLAttributes<HTMLTableSectionElement>>(
  ({className, ...props}, ref) => (
    <thead
      className={classes('sticky top-0 z-10 bg-input text-xs uppercase [&_tr]:border-b', className)}
      ref={ref}
      {...props}
    />
  ),
);
TableHeader.displayName = 'TableHeader';

const TableHead = forwardRef<HTMLTableCellElement, ThHTMLAttributes<HTMLTableCellElement>>(
  ({className, ...props}, ref) => (
    <th
      className={classes(
        'h-12 text-nowrap p-4 text-left align-middle font-medium text-muted-foreground first-of-type:pl-6 last-of-type:pr-6 [&:has([role=checkbox])]:pr-0',
        className,
      )}
      ref={ref}
      {...props}
    />
  ),
);
TableHead.displayName = 'TableHead';

const TableHeadActions = forwardRef<HTMLTableCellElement, ThHTMLAttributes<HTMLTableCellElement>>(
  ({className, ...props}, ref) => <TableHead className={classes('w-0', className)} ref={ref} {...props} />,
);
TableHeadActions.displayName = 'TableHeadActions';

const TableRow = forwardRef<HTMLTableRowElement, HTMLAttributes<HTMLTableRowElement>>(({className, ...props}, ref) => (
  <tr
    className={classes(
      'whitespace-nowrap border-b transition-colors hover:bg-input/30 data-[state=selected]:bg-input/30',
      className,
    )}
    ref={ref}
    {...props}
  />
));
TableRow.displayName = 'TableRow';

const VirtualTableRow = forwardRef<HTMLTableRowElement, HTMLAttributes<HTMLTableRowElement>>(
  ({children, className, ...props}, ref) => {
    const [isVisible, setIsVisible] = useState(false);
    const localRef = useRef<HTMLTableRowElement>(null);

    const combinedRef = useCallback(
      (node: HTMLTableRowElement) => {
        if (ref) {
          if (typeof ref === 'function') {
            ref(node);
          } else {
            (ref as React.MutableRefObject<HTMLTableRowElement | null>).current = node;
          }
        }
        localRef.current = node;
      },
      [ref],
    );

    useEffect(() => {
      const observer = new IntersectionObserver(
        ([entry]) => {
          if (entry.isIntersecting) {
            setIsVisible(true);
            observer.disconnect();
          }
        },
        {rootMargin: '100px'},
      );

      if (localRef.current) {
        observer.observe(localRef.current);
      }

      return () => {
        observer.disconnect();
      };
    }, []);

    if (!isVisible) return <tr className='h-60' ref={combinedRef} />;

    return (
      <tr
        className={classes(
          'whitespace-nowrap border-b transition-colors hover:bg-input/30 data-[state=selected]:bg-input/30',
          className,
        )}
        ref={combinedRef}
        {...props}
      >
        {children}
      </tr>
    );
  },
);
VirtualTableRow.displayName = 'VirtualTableRow';

type TableBodyProps = HTMLAttributes<HTMLTableSectionElement> & {
  isValidating: boolean;
};

const TableBody = forwardRef<HTMLTableSectionElement, TableBodyProps>(
  ({children, className, isValidating, ...props}, ref) => {
    return (
      <tbody className={classes('[&_tr:last-child]:border-0', className)} ref={ref} {...props}>
        {children}
        <tr className='z-10 bg-white'>
          <td className={classes('text-center align-top')} colSpan={999}>
            {isValidating && <LoadingBar barClassName='bg-alert-light-yellow' progressClassName='bg-oldprimary' />}
          </td>
        </tr>
      </tbody>
    );
  },
);
TableBody.displayName = 'TableBody';

const TableCell = forwardRef<HTMLTableCellElement, TdHTMLAttributes<HTMLTableCellElement>>(
  ({className, ...props}, ref) => (
    <td
      className={classes(
        'px-4 py-3 align-middle font-normal first-of-type:pl-6 last-of-type:pr-6 [&:has([role=checkbox])]:pr-0',
        className,
      )}
      ref={ref}
      {...props}
    />
  ),
);
TableCell.displayName = 'TableCell';

const TableActions = forwardRef<HTMLTableCellElement, TdHTMLAttributes<HTMLTableCellElement>>(
  ({children, ...props}, ref) => (
    <TableCell ref={ref} {...props}>
      <div className='flex items-center justify-end gap-2'>{children}</div>
    </TableCell>
  ),
);
TableActions.displayName = 'TableActions';

const TableFooter = forwardRef<HTMLTableSectionElement, HTMLAttributes<HTMLTableSectionElement>>(
  ({className, ...props}, ref) => (
    <tfoot
      className={classes('border-t bg-background font-medium last:[&>tr]:border-b-0', className)}
      ref={ref}
      {...props}
    />
  ),
);
TableFooter.displayName = 'TableFooter';

const TableCaption = forwardRef<HTMLTableCaptionElement, HTMLAttributes<HTMLTableCaptionElement>>(
  ({className, ...props}, ref) => (
    <caption className={classes('mt-4 text-sm text-muted-foreground', className)} ref={ref} {...props} />
  ),
);
TableCaption.displayName = 'TableCaption';

export {
  Table,
  TableActions,
  TableBody,
  TableCaption,
  TableCell,
  TableContainer,
  TableFooter,
  TableHead,
  TableHeadActions,
  TableHeader,
  TableRow,
  VirtualTableRow,
};
