'use client';

import {ComponentPropsWithoutRef, ComponentRef, forwardRef} from 'react';

import {Content, List, Root, Trigger} from '@radix-ui/react-tabs';
import {cva, type VariantProps} from 'class-variance-authority';

import {Badge} from '@/components/ui/Badge';
import {classes} from '@/utils/common';

const tabVariants = cva('ring-0 focus-visible:outline-hidden', {
  defaultVariants: {
    variant: 'default',
  },
  variants: {
    variant: {
      default: '',
      menu: 'h-[72px]',
    },
  },
});

const listVariants = cva('inline-flex items-center justify-center ring-0 focus-visible:outline-hidden', {
  defaultVariants: {
    variant: 'default',
  },
  variants: {
    variant: {
      default: 'h-10 gap-2 rounded-md',
      menu: 'h-full gap-6 text-base font-medium',
    },
  },
});

const triggerVariants = cva('ring-0 focus-visible:outline-hidden disabled:cursor-default', {
  defaultVariants: {
    variant: 'default',
  },
  variants: {
    variant: {
      default:
        'border-b-2 border-transparent px-3 py-1.5 opacity-80 data-[state=active]:border-b-button-primary data-[state=active]:opacity-100 not-disabled:hover:border-b-button-primary not-disabled:hover:opacity-100',
      menu: 'h-full border-b-2 border-transparent opacity-80 data-[state=active]:border-b-button-primary data-[state=active]:opacity-100 not-disabled:hover:border-b-button-primary not-disabled:hover:opacity-100',
    },
  },
});

export type TabsProps = ComponentPropsWithoutRef<typeof Root> & VariantProps<typeof tabVariants>;

const Tabs = forwardRef<HTMLDivElement, TabsProps>(({className, variant, ...props}, ref) => (
  <Root className={classes(tabVariants({className, variant}))} ref={ref} {...props} />
));

export type ListProps = ComponentPropsWithoutRef<typeof List> & VariantProps<typeof listVariants>;

const TabsList = forwardRef<ComponentRef<typeof List>, ListProps>(({className, variant, ...props}, ref) => (
  <List className={classes(listVariants({variant}), className)} ref={ref} {...props} />
));
TabsList.displayName = List.displayName;

export type TriggerProps = ComponentPropsWithoutRef<typeof Trigger> &
  VariantProps<typeof triggerVariants> & {
    badge?: number | string;
    error?: boolean;
  };

const TabsTrigger = forwardRef<ComponentRef<typeof Trigger>, TriggerProps>(
  ({badge, children, className, disabled, error, variant, ...props}, ref) => (
    <Trigger
      className={classes(
        triggerVariants({className, variant}),
        badge !== undefined &&
          'group flex items-center gap-2 whitespace-nowrap [&>div[data-type=badge]]:cursor-pointer [&>div[data-type=badge]]:bg-input data-[state=active]:[&>div[data-type=badge]]:bg-button-primary',
        !disabled && 'hover:[&>div[data-type=badge]]:bg-button-primary-hovered!',
        error && 'text-red',
        className,
      )}
      disabled={disabled}
      ref={ref}
      {...props}
    >
      {children}
      {badge !== undefined && (
        <Badge data-type='badge' size='sm' variant='none'>
          {badge}
        </Badge>
      )}
    </Trigger>
  ),
);
TabsTrigger.displayName = Trigger.displayName;

const TabsMenuItem = forwardRef<HTMLButtonElement, Omit<TriggerProps, 'value'>>(
  ({badge, children, className, disabled, error, ...props}, ref) => (
    <button
      className={classes(
        triggerVariants({className, variant: 'menu'}),
        'mx-6 h-[72px] text-base font-medium',
        badge !== undefined &&
          'group flex items-center gap-2 whitespace-nowrap [&>div[data-type=badge]]:cursor-pointer [&>div[data-type=badge]]:bg-input data-[state=active]:[&>div[data-type=badge]]:bg-button-primary',
        !disabled && 'hover:[&>div[data-type=badge]]:bg-button-primary-hovered!',
        error && 'text-red',
        className,
      )}
      disabled={disabled}
      ref={ref}
      {...props}
    >
      {children}
      {badge !== undefined && (
        <Badge data-type='badge' size='sm' variant='none'>
          {badge}
        </Badge>
      )}
    </button>
  ),
);
TabsMenuItem.displayName = 'TabsMenuItem';

const TabsContent = forwardRef<ComponentRef<typeof Content>, ComponentPropsWithoutRef<typeof Content>>(
  ({className, ...props}, ref) => (
    <Content className={classes('mt-2 ring-0 focus-visible:outline-hidden', className)} ref={ref} {...props} />
  ),
);
TabsContent.displayName = Content.displayName;

export {Tabs, TabsContent, TabsList, TabsMenuItem, TabsTrigger};
