'use client';

import {ComponentPropsWithoutRef, ComponentRef, forwardRef} from 'react';

import {Root, Thumb} from '@radix-ui/react-switch';
import {CheckIcon, XIcon} from 'lucide-react';
import {Control, Controller} from 'react-hook-form';

import {classes} from '@/utils/common';

type SwitchProps = ComponentPropsWithoutRef<typeof Root> & {
  control?: Control<any>;
  controlName?: string;
};

const Switch = forwardRef<ComponentRef<typeof Root>, SwitchProps>(
  ({checked, className, control, controlName, onCheckedChange, ...props}, ref) => {
    const renderSwitch = (fieldChecked?: boolean, fieldOnCheckedChange?: (val?: boolean) => void) => (
      <Root
        checked={fieldChecked}
        className={classes(
          'peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent ring-0 transition-colors focus-visible:outline-hidden disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-button-primary data-[state=unchecked]:bg-input data-[state=checked]:hover:bg-button-primary-hovered data-[state=unchecked]:hover:bg-gray-200',
          className,
        )}
        onCheckedChange={(val) => {
          fieldOnCheckedChange?.(val);
          onCheckedChange?.(val);
        }}
        ref={ref}
        {...props}
      >
        <Thumb
          className={classes(
            'pointer-events-none flex size-5 items-center justify-center rounded-full bg-background shadow-lg ring-0 transition-transform focus-visible:outline-hidden data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0 data-[state=unchecked]:[&>svg:first-of-type]:hidden data-[state=checked]:[&>svg:nth-of-type(2)]:hidden',
          )}
        >
          <CheckIcon className='size-4 shrink-0' />
          <XIcon className='size-4 shrink-0' />
        </Thumb>
      </Root>
    );

    if (control && controlName)
      return (
        <Controller
          control={control}
          defaultValue={false}
          name={controlName}
          render={({field}) => renderSwitch(field.value, field.onChange)}
        />
      );

    return renderSwitch(checked);
  },
);
Switch.displayName = Root.displayName;

export {Switch};
