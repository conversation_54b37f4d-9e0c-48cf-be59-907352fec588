import {ButtonHTMLAttributes, forwardRef, ReactNode, useMemo, useState} from 'react';

import {PopoverContentProps} from '@radix-ui/react-popover';
import {PlusIcon, Trash2Icon} from 'lucide-react';

import {Badge} from '@/components/ui/Badge';
import {Button, IconButton} from '@/components/ui/Button';
import {Popover, PopoverContent, PopoverTrigger} from '@/components/ui/Popover';
import {MultiSelect} from '@/components/ui/special/Multiselect';
import {classes} from '@/utils/common';

type OptionType = {id: string; value: string};

type TableMultiSelectProps = ButtonHTMLAttributes<HTMLButtonElement> &
  Pick<PopoverContentProps, 'side'> & {
    children?: ReactNode;
    className?: string;
    defaultValue?: string[];
    notFoundClassName?: string;
    onOpenChange?: (open: boolean) => void;
    onValueChange: (value: string[]) => void;
    options: OptionType[];
    panelClassName?: string;
    renderElement?: (value: OptionType) => ReactNode;
    renderNotFound: (query: string) => ReactNode;
    searchPlaceholder: string;
    summaryLabel: string;
    summarySide?: PopoverContentProps['side'];
    withoutRemove?: boolean;
  };

const TableMultiSelect = forwardRef<HTMLButtonElement, TableMultiSelectProps>(
  (
    {
      children,
      className,
      defaultValue,
      disabled,
      notFoundClassName,
      onOpenChange,
      onValueChange,
      options,
      panelClassName,
      renderElement,
      renderNotFound,
      searchPlaceholder,
      side,
      summaryLabel,
      summarySide,
      withoutRemove,
      ...props
    },
    ref,
  ) => {
    const [selectedValues, setSelectedValues] = useState<string[]>(defaultValue || []);
    const [isEditPopoverOpen, setIsEditPopoverOpen] = useState(false);
    const [isListPopoverOpen, setIsListPopoverOpen] = useState(false);

    const selectedOptions = useMemo(
      () => options.filter((o) => selectedValues.includes(o.id)),
      [options, selectedValues],
    );

    const handleValueChange = (arr: string[]) => {
      setSelectedValues(arr);
      onValueChange(arr);
    };

    return (
      <div className={classes('flex items-center gap-2', className)}>
        <Popover
          onOpenChange={(open) => {
            setIsEditPopoverOpen(open);
            onOpenChange?.(open);
          }}
          open={isEditPopoverOpen}
        >
          <PopoverTrigger asChild>
            {children || <IconButton disabled={disabled} icon={<PlusIcon className='size-6' />} ref={ref} {...props} />}
          </PopoverTrigger>
          <PopoverContent
            className='max-w-none p-2'
            onEscapeKeyDown={() => setIsEditPopoverOpen(false)}
            onInteractOutside={(event) => {
              if (!event.defaultPrevented) setIsEditPopoverOpen(false);
            }}
            side={side}
          >
            <MultiSelect
              defaultValue={selectedValues}
              listClassName='max-h-[700px]'
              notFoundClassName={notFoundClassName}
              onValueChange={handleValueChange}
              options={options}
              panelClassName={panelClassName}
              renderNotFound={renderNotFound}
              searchPlaceholder={searchPlaceholder}
              selectedFirst
              withoutRemove={withoutRemove}
            />
          </PopoverContent>
        </Popover>

        {selectedValues.length > 0 && (
          <Popover onOpenChange={setIsListPopoverOpen} open={isListPopoverOpen}>
            <PopoverTrigger asChild>
              <Badge className='cursor-pointer' size='sm' variant='info'>
                {selectedValues.length} {summaryLabel}
              </Badge>
            </PopoverTrigger>
            <PopoverContent className='max-w-none p-2' side={summarySide}>
              <div className='flex flex-col gap-1'>
                {selectedOptions.map((option, index) => (
                  <div className='flex items-center justify-between' key={`${option.id}-${index}`}>
                    {renderElement ? renderElement(option) : <div className='text-sm px-2'>{option.value}</div>}
                    <Button
                      onClick={() => handleValueChange(selectedValues.filter((v) => v !== option.id))}
                      size='icon'
                      variant='none'
                    >
                      <Trash2Icon className='size-5 text-red' strokeWidth={1} />
                    </Button>
                  </div>
                ))}
              </div>
            </PopoverContent>
          </Popover>
        )}
      </div>
    );
  },
);
TableMultiSelect.displayName = 'TableMultiSelect';

export {TableMultiSelect};
