import {FC, KeyboardEvent, useCallback, useState} from 'react';

import {PencilRulerIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';

import {Button} from '@/components/ui/Button';
import {Input, InputLabel} from '@/components/ui/Input';
import {WithLabel} from '@/components/ui/Label';
import {Popover, PopoverContent, PopoverTrigger} from '@/components/ui/Popover';
import {Tooltip, TooltipContent, TooltipTrigger} from '@/components/ui/Tooltip';
import axios from '@/utils/axios';

type Props = {
  itemId: string;
  onChange?: (value: number, dimensions: {length: number; width?: number}) => void;
  shape: string | undefined;
  value?: null | {length: number; width?: null | number};
};

const SHAPES_WITH_WIDTH = ['PLATE', 'PLATE_BLACK', 'PLATE_GALVANIZED', 'STRIP_ROLL'];

const SetDimensionPopover: FC<Props> = ({itemId, onChange, shape, value}) => {
  const t = useTranslations();
  const [isOpen, setIsOpen] = useState(false);
  const [width, setWidth] = useState(value?.width?.toString() || '');
  const [length, setLength] = useState(value?.length?.toString() || '');

  const showWidth = SHAPES_WITH_WIDTH.includes(shape || '');

  const setChanges = useCallback(
    async ({length, width}: {length?: string; width?: string} = {}) => {
      try {
        if (length && (!showWidth || width)) {
          const value = await axios
            .post(`/api/goods/${itemId}/weight-in-kg`, {
              length: Number(length),
              ...(showWidth && {width: Number(width)}),
            })
            .then((res) => res.data?.weightInKg);
          onChange?.(value, {length: Number(length), ...(showWidth && {width: Number(width)})});
        }
      } catch (error) {
        console.error('Failed to set dimensions:', error);
      }
      setIsOpen(false);
    },
    [itemId, onChange, showWidth],
  );

  const handleKeyDown = useCallback(
    (event: KeyboardEvent<HTMLInputElement>) => {
      switch (event.key) {
        case 'Enter':
          setChanges({length, width});
          break;
        case 'Escape':
          event.preventDefault();
          setChanges();
          break;
      }
    },
    [length, setChanges, width],
  );

  return (
    <Popover
      onOpenChange={(open) => {
        if (open) {
          setIsOpen(true);
          setWidth(value?.width?.toString() || '');
          setLength(value?.length?.toString() || '');
        } else {
          setChanges({length, width});
        }
      }}
      open={isOpen}
    >
      <PopoverTrigger>
        <Tooltip>
          <TooltipTrigger>
            <Button className='px-0' variant='ghost'>
              <PencilRulerIcon className='size-5' strokeWidth={1} />
            </Button>
          </TooltipTrigger>
          <TooltipContent>{t('set dimensions')}</TooltipContent>
        </Tooltip>
      </PopoverTrigger>
      <PopoverContent
        className='flex max-w-none flex-col gap-4'
        onEscapeKeyDown={(event) => {
          event.preventDefault();
          setChanges();
        }}
        onInteractOutside={() => setChanges()}
      >
        {t('set dimensions')}
        <div className={`grid ${showWidth ? 'grid-cols-2' : 'grid-cols-1'} gap-4`}>
          <WithLabel>
            <Input
              onChange={({target: {value}}) => setLength(value)}
              onKeyDown={handleKeyDown}
              size='md'
              type='number'
              value={length}
            />
            <InputLabel>{t('length')}</InputLabel>
          </WithLabel>
          {showWidth && (
            <WithLabel>
              <Input
                onChange={({target: {value}}) => setWidth(value)}
                onKeyDown={handleKeyDown}
                size='md'
                type='number'
                value={width}
              />
              <InputLabel>{t('width')}</InputLabel>
            </WithLabel>
          )}
        </div>
      </PopoverContent>
    </Popover>
  );
};

export default SetDimensionPopover;
