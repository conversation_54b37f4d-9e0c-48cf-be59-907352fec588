import {forwardRef, HTMLAttributes, useEffect, useState} from 'react';

import {LabelProps} from '@radix-ui/react-label';
import {PrimitiveAtom, useAtom} from 'jotai';
import {debounce} from 'lodash';
import {SearchIcon, XIcon} from 'lucide-react';

import {Button} from '@/components/ui/Button';
import {Input, InputProps} from '@/components/ui/Input';
import {Label} from '@/components/ui/Label';
import {AtLeastOne} from '@/types/global';
import {classes} from 'utils/common';

type Props = AtLeastOne<Omit<InputProps, 'defaultValue' | 'key' | 'onChange'>, 'id' | 'placeholder' | 'value'> & {
  div?: HTMLAttributes<HTMLDivElement>;
  label?: Omit<LabelProps, 'htmlFor' | 'id'>;
  minLength?: number;
  store: PrimitiveAtom<string>;
};

const StoreSearchInput = forwardRef<HTMLInputElement, Props>(
  ({className, div, id, label, minLength = 3, placeholder, store, ...props}, ref) => {
    const [key, setKey] = useState(Math.random());
    const [value, setValue] = useAtom(store);

    const resetInput = () => {
      setKey(Math.random());
    };

    useEffect(() => {
      resetInput();
    }, [store]);

    const {className: divClassName, ...restDiv} = div || {};
    const {className: labelClassName, ...restLabel} = label || {};

    return (
      <div className={classes('relative flex max-w-[480px] items-center', divClassName)} {...restDiv}>
        <Label
          className={classes('absolute left-2 cursor-text', labelClassName)}
          htmlFor={id || placeholder}
          {...restLabel}
        >
          <SearchIcon className='size-5 shrink-0' />
        </Label>
        <Input
          className={classes('pl-8 pr-5', className)}
          defaultValue={value}
          id={id || placeholder}
          key={key}
          onChange={debounce(({target: {value}}) => {
            setValue(value.length >= minLength ? value : '');
          }, 500)}
          placeholder={placeholder}
          ref={ref}
          variant='outline'
          {...props}
        />
        {value && (
          <Button
            className='absolute right-1'
            onClick={() => {
              setValue('');
              resetInput();
            }}
            size='none'
            variant='none'
          >
            <XIcon className='size-4' />
          </Button>
        )}
      </div>
    );
  },
);

export {StoreSearchInput};
