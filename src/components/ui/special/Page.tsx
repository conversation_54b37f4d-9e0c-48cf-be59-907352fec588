import {FC, forwardRef, HTMLAttributes} from 'react';

import Head from 'next/head';

import {Config} from '@/constants/config';
import {classes} from '@/utils/common';

const Page = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(({className, ...props}, ref) => (
  <div className={classes('flex flex-col', className)} ref={ref} {...props} />
));
Page.displayName = 'Card';

type PageTitleProps = {
  children?: string;
};

const PageTitle: FC<PageTitleProps> = ({children}) => (
  <Head>
    <title>
      {children} - {Config.AppName}
    </title>
  </Head>
);
PageTitle.displayName = 'PageTitle';

const PageHeader = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(({className, ...props}, ref) => (
  <div
    className={classes('flex h-[72px] items-center gap-4 border-b border-border px-6', className)}
    ref={ref}
    {...props}
  />
));
PageHeader.displayName = 'PageHeader';

const PageHeaderTitle = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(({className, ...props}, ref) => (
  <div className={classes('shrink-0 whitespace-nowrap text-2xl font-semibold', className)} ref={ref} {...props} />
));
PageHeaderTitle.displayName = 'PageHeaderTitle';

const PageFilters = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(({className, ...props}, ref) => (
  <div
    // min-h-[65px]
    className={classes('flex min-h-[65px] gap-4 border-b border-border px-6 py-2', className)}
    ref={ref}
    {...props}
  />
));
PageFilters.displayName = 'PageFilters';

const PageContent = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(({className, ...props}, ref) => (
  <div className={className} ref={ref} {...props} />
));
PageContent.displayName = 'PageContent';

export {Page, PageContent, PageFilters, PageHeader, PageHeaderTitle, PageTitle};
