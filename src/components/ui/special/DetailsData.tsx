import {FC, ReactNode} from 'react';

import {HideableContent} from '@/components/ui/special/HideableContent';
import {classes} from '@/utils/common';

type Props = {
  children: ReactNode;
  className?: string;
  show?: boolean;
};

const DetailsData: FC<Props> = ({children, className = 'grid-cols-1', show = true}) => {
  return (
    <HideableContent show={show}>
      <div className={classes('mx-6 mt-6 grid gap-4', className)}>{children}</div>
    </HideableContent>
  );
};

type DetailsDataGridProps = {
  children: ReactNode;
  className?: string;
  title: ReactNode | string;
};

const DetailsDataGrid: FC<DetailsDataGridProps> = ({children, className, title}) => {
  return (
    <div className={classes('rounded-lg border border-border p-4', className)}>
      {typeof title === 'string' ? <div className='mb-4 text-base font-medium'>{title}</div> : title}
      {children}
    </div>
  );
};

type DetailsDataGridContentProps = {
  children: ReactNode;
  className?: string;
};

const DetailsDataGridContent: FC<DetailsDataGridContentProps> = ({children, className = 'grid-cols-4'}) => {
  return <div className={classes('grid gap-x-4 gap-y-6', className)}>{children}</div>;
};

export {DetailsData, DetailsDataGrid, DetailsDataGridContent};
