import {ButtonHTMLAttributes, FC, forwardRef, useEffect, useMemo, useState} from 'react';

import axios from 'axios';
import {PlusIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';
import {useSWRConfig} from 'swr';

import {Button} from '@/components/ui/Button';
import {Combobox} from '@/components/ui/Combobox';
import {NumberInput} from '@/components/ui/Input';
import {Popover, PopoverClose, PopoverContent, PopoverTrigger} from '@/components/ui/Popover';
import {toast} from '@/components/ui/Toast';
import {ToggleGroup, ToggleGroupItem} from '@/components/ui/ToggleGroup';
import {useHasPermission} from '@/hooks/helpers/useHasPermission';
import useManufacturingOrderActions from '@/hooks/useManufacturingOrderActions';
import useSuppliers from '@/hooks/useSuppliers';
import useWishlistActions from '@/hooks/useWishlistActions';
import {Id} from '@/types/global';
import {classes} from '@/utils/common';

enum OrderView {
  Buy = 'buy',
  Make = 'make',
  Order = 'order',
}

type BuyButtonProps = Pick<Props, 'item' | 'onExecute' | 'quantity'>;
type MakeButtonProps = BuyButtonProps & Pick<Props, 'isService' | 'saleId'>;
type Props = ButtonHTMLAttributes<HTMLButtonElement> & {
  className?: string;
  forTable?: boolean;
  isService?: boolean;
  item: {code: string; id: string; lastOrderedFrom?: Id | null; name: string; produced: boolean};
  onExecute?: () => void;
  quantity?: number;
  saleId?: string;
};

export const MakeButton: FC<MakeButtonProps> = ({isService, item, onExecute, quantity: initialQuantity, saleId}) => {
  const t = useTranslations();
  const [quantity, setQuantity] = useState(initialQuantity || 1);
  const {createLinkedManufacturingOrder, createManufacturingOrder} = useManufacturingOrderActions();

  useEffect(() => {
    setQuantity(initialQuantity || 1);
  }, [initialQuantity]);

  const handleMake = () => {
    (saleId
      ? createLinkedManufacturingOrder({...(isService ? {serviceId: item.id} : {productId: item.id}), quantity}, saleId)
      : createManufacturingOrder({product: {measurementUnit: '', ...item}, quantity})
    ).then(() => {
      onExecute?.();
    });
    setQuantity(1);
  };

  return (
    <>
      {t('select quantity')}
      <NumberInput onChange={setQuantity} value={quantity} />
      <PopoverClose asChild>
        <Button onClick={handleMake}>{t('make')}</Button>
      </PopoverClose>
    </>
  );
};

type SuppliersComboboxProps = Pick<Props, 'item'> & {
  onChange: (value: string) => void;
  value?: string;
};

const SuppliersCombobox: FC<SuppliersComboboxProps> = ({item, onChange, value}) => {
  const {suppliers} = useSuppliers();
  const [lastSuppliers, setLastSuppliers] = useState<Id[]>();
  const t = useTranslations();

  useEffect(() => {
    axios
      .get(`/api/suppliers/last-suppliers-for?materialGoodId=${item.id}`)
      .then((res) => setLastSuppliers(res.data))
      .catch(() => alert(t('an error occurred while loading name', {name: t('suffixed.supplier.end')})));
  }, [item.id, t]);

  return (
    <Combobox
      className='w-[250px]'
      onChange={onChange}
      open={!value}
      options={suppliers.map((supplier) => ({id: supplier.id, value: supplier.name}))}
      placeholder={t('supplier')}
      preferredValues={lastSuppliers?.map((supplier) => supplier.id)}
      renderNotFound={() => t('supplier not found')}
      searchPlaceholder={t('search supplier')}
      value={value}
    />
  );
};

export const BuyButton: FC<BuyButtonProps> = ({item, onExecute, quantity: initialQuantity}) => {
  const t = useTranslations();
  const [quantity, setQuantity] = useState(initialQuantity || 1);
  const [supplierId, setSupplierId] = useState<string | undefined>(item.lastOrderedFrom?.id);
  const {createWishlistItem} = useWishlistActions();
  const {mutate: globalMutate} = useSWRConfig();

  useEffect(() => {
    setQuantity(initialQuantity || 1);
  }, [initialQuantity]);

  const handleBuy = () => {
    createWishlistItem({
      materialGood: {...item, name: ''},
      quantity: Number(quantity),
      supplier: {id: supplierId || '', name: ''},
    })
      .then(() => {
        onExecute?.();
        toast.success(t('material name has been added to the wishlist', {name: item.name}));
        globalMutate((key) => Array.isArray(key) && key[0] === 'wishlists');
      })
      .catch(() => toast.error(t('material name has failed to add to the wishlist', {name: item.name})));
    setQuantity(1);
    setSupplierId(item.lastOrderedFrom?.id);
  };

  return (
    <>
      {t('select quantity')}
      <NumberInput onChange={setQuantity} value={quantity} />
      <SuppliersCombobox item={item} onChange={setSupplierId} value={supplierId} />
      <PopoverClose asChild>
        <Button disabled={!supplierId} onClick={handleBuy}>
          {t('buy')}
        </Button>
      </PopoverClose>
    </>
  );
};

const OrderButton = forwardRef<HTMLButtonElement, Props>(
  ({className, forTable, isService, item, quantity, saleId, ...props}, ref) => {
    const {hasPermission, isLoading} = useHasPermission();
    const t = useTranslations();
    const [view, setView] = useState<OrderView>(OrderView.Make);

    const canManufacture = useMemo(
      () => item.produced && !isLoading && hasPermission('read', 'manufacturing'),
      [hasPermission, isLoading, item.produced],
    );
    const canPurchase = useMemo(() => !isLoading && hasPermission('read', 'purchases'), [hasPermission, isLoading]);

    const allowedView = useMemo(
      () =>
        canManufacture && canPurchase
          ? OrderView.Order
          : canManufacture
            ? OrderView.Make
            : canPurchase
              ? OrderView.Buy
              : undefined,
      [canManufacture, canPurchase],
    );

    useEffect(() => {
      if (allowedView === OrderView.Buy) setView(OrderView.Buy);
    }, [allowedView]);

    if (!canManufacture && !canPurchase) return null;

    return (
      <Popover>
        <PopoverTrigger asChild>
          <Button
            className={classes(forTable && 'rounded-full', className)}
            ref={ref}
            size={forTable ? 'icon' : 'default'}
            variant='primary'
            {...props}
          >
            <PlusIcon />
            {!forTable && <>{t(allowedView as any)}</>}
          </Button>
        </PopoverTrigger>
        <PopoverContent align='end' className='flex max-w-none flex-col items-center gap-4'>
          {allowedView === OrderView.Order && (
            <ToggleGroup
              className='w-full'
              onValueChange={(value) => {
                setView(value as OrderView);
              }}
              type='single'
              value={view}
            >
              <ToggleGroupItem className='w-full' value={OrderView.Make}>
                {t(OrderView.Make)}
              </ToggleGroupItem>
              <ToggleGroupItem className='w-full' value={OrderView.Buy}>
                {t(OrderView.Buy)}
              </ToggleGroupItem>
            </ToggleGroup>
          )}
          {view === OrderView.Make && (
            <MakeButton isService={isService} item={item} quantity={quantity} saleId={saleId} />
          )}
          {view === OrderView.Buy && <BuyButton item={item} quantity={quantity} />}
        </PopoverContent>
      </Popover>
    );
  },
);

export {OrderButton};
