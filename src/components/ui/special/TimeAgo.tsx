import {FC} from 'react';

import {
  differenceInDays,
  differenceInHours,
  differenceInMinutes,
  differenceInSeconds,
  differenceInWeeks,
  format,
  isYesterday,
} from 'date-fns';
import {useTranslations} from 'next-intl';

import {formatDate} from 'utils/format';

type Props = {
  className?: string;
  date: Date | string | undefined;
  hide?: ('day' | 'days' | 'hours' | 'minutes' | 'seconds' | 'week' | 'weeks')[];
  useShort?: boolean;
  useYesterday?: boolean;
};

const TimeAgo: FC<Props> = ({className, date, hide, useShort, useYesterday}) => {
  const t = useTranslations();

  const now = new Date();
  let timeAgoText;
  if (!date || differenceInSeconds(now, new Date(date)) < 11) {
    timeAgoText = t('now');
  } else if (!hide?.includes('seconds') && differenceInSeconds(now, new Date(date)) < 60) {
    timeAgoText = t('value s ago', {value: differenceInSeconds(now, new Date(date))});
  } else if (!hide?.includes('minutes') && differenceInMinutes(now, new Date(date)) < 60) {
    timeAgoText = t('value min ago', {value: differenceInMinutes(now, new Date(date))});
  } else if (!hide?.includes('hours') && differenceInHours(now, new Date(date)) < 24) {
    timeAgoText =
      useYesterday && isYesterday(new Date(date))
        ? t('yesterday')
        : t('value h ago', {value: differenceInHours(now, new Date(date))});
  } else if (!hide?.includes('day') && differenceInDays(now, new Date(date)) === 1) {
    timeAgoText = useYesterday ? t('yesterday') : t('value day ago', {value: differenceInDays(now, new Date(date))});
  } else if (!hide?.includes('days') && differenceInDays(now, new Date(date)) < 7) {
    timeAgoText = t('value days ago', {value: differenceInDays(now, new Date(date))});
  } else if (!hide?.includes('week') && differenceInWeeks(now, new Date(date)) === 1) {
    timeAgoText = t('value week ago', {value: differenceInWeeks(now, new Date(date))});
  } else if (!hide?.includes('weeks') && differenceInWeeks(now, new Date(date)) <= 3) {
    timeAgoText = t('value weeks ago', {value: differenceInWeeks(now, new Date(date))});
  } else if (useShort) {
    timeAgoText = format(new Date(date), 'dd MMM');
  } else {
    timeAgoText = formatDate(new Date(date));
  }

  return <div className={className}>{timeAgoText}</div>;
};

export default TimeAgo;
