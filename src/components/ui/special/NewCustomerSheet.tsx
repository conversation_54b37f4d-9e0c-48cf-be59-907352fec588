import {FC, useEffect} from 'react';

import {joiResolver} from '@hookform/resolvers/joi';
import {isEmpty} from 'lodash';
import {PlusIcon, Trash2Icon} from 'lucide-react';
import {useTranslations} from 'next-intl';
import {useForm} from 'react-hook-form';

import {Button} from '@/components/ui/Button';
import {Checkbox} from '@/components/ui/Checkbox';
import {Input, InputLabel} from '@/components/ui/Input';
import {Label, WithLabel} from '@/components/ui/Label';
import {Sheet, SheetClose, SheetContent, SheetFooter, SheetHeader, SheetPage, SheetTitle} from '@/components/ui/Sheet';
import CountriesCombobox from '@/components/ui/special/CountriesCombobox';
import {SearchVATInput} from '@/components/ui/special/SearchVATInput';
import {ToggleGroup, ToggleGroupItem} from '@/components/ui/ToggleGroup';
import {customerSchema} from '@/hooks/useCustomer';
import useCustomerActions from '@/hooks/useCustomerActions';
import {AddressType} from '@/types/global';
import {Customer, CustomerType} from '@/types/sales';

type Props = {
  defaultValue?: string;
  onClose: () => void;
  onCreate: (customer: Customer) => void;
};

const NewCustomerSheet: FC<Props> = ({defaultValue, onClose, onCreate}) => {
  const t = useTranslations();
  const {createCustomer} = useCustomerActions();
  const {
    formState: {errors},
    register,
    reset,
    setValue,
    trigger,
    watch,
  } = useForm<Customer>({
    defaultValues: {
      type: CustomerType.LOCAL_LEGAL_ENTITY,
    },
    mode: 'onChange',
    resolver: joiResolver(customerSchema),
  });

  useEffect(() => {
    setTimeout(trigger, 50);
  }, [trigger]);

  return (
    <Sheet defaultOpen={true} onOpenChange={() => setTimeout(onClose, 500)}>
      <SheetPage side='right' size='lg'>
        <SheetTitle className='text-2xl'>{t('create new customer')}</SheetTitle>
        <SheetHeader>
          <SearchVATInput
            containerClassName='w-[433px]'
            defaultValue={defaultValue}
            disabled={watch('type') === CustomerType.INDIVIDUAL}
            onChange={(customer) => {
              reset({
                ...customer,
                addresses: [{...customer.addresses?.[0], types: [AddressType.BILLING]}],
                type: CustomerType.LOCAL_LEGAL_ENTITY,
              });
              setValue(
                'type',
                customer.addresses?.[0]?.state ? CustomerType.LOCAL_LEGAL_ENTITY : CustomerType.FOREIGN_LEGAL_ENTITY,
              );
              trigger();
            }}
          />
          <ToggleGroup
            className='w-full place-self-center overflow-x-auto'
            onValueChange={(value) => {
              setValue('type', value as CustomerType);
              if (value !== CustomerType.LOCAL_LEGAL_ENTITY) setValue('identificationNumber', '');
            }}
            type='single'
            value={watch('type')}
          >
            <ToggleGroupItem className='w-full' value={CustomerType.LOCAL_LEGAL_ENTITY}>
              {t(CustomerType.LOCAL_LEGAL_ENTITY)}
            </ToggleGroupItem>
            <ToggleGroupItem className='w-full' value={CustomerType.FOREIGN_LEGAL_ENTITY}>
              {t(CustomerType.FOREIGN_LEGAL_ENTITY)}
            </ToggleGroupItem>
            <ToggleGroupItem className='w-full' value={CustomerType.INDIVIDUAL}>
              {t(CustomerType.INDIVIDUAL)}
            </ToggleGroupItem>
          </ToggleGroup>
        </SheetHeader>
        <SheetContent className='flex flex-col gap-4 px-6'>
          <div className='flex flex-col gap-2 rounded-lg border p-4'>
            <WithLabel>
              <Input {...register('name')} error={!!errors.name} />
              <InputLabel>{t('name')}</InputLabel>
            </WithLabel>
            <WithLabel>
              <Input {...register('taxIdentificationNumber')} error={!!errors.taxIdentificationNumber} />
              <InputLabel>
                {t(
                  watch('type') === CustomerType.LOCAL_LEGAL_ENTITY
                    ? 'tax identification number'
                    : watch('type') === CustomerType.FOREIGN_LEGAL_ENTITY
                      ? 'foreign tax identification number'
                      : 'individual tax identification number',
                )}
              </InputLabel>
            </WithLabel>
            {watch('type') === CustomerType.LOCAL_LEGAL_ENTITY && (
              <WithLabel>
                <Input {...register('identificationNumber')} error={!!errors.identificationNumber} />
                <InputLabel>{t('identification number')}</InputLabel>
              </WithLabel>
            )}
          </div>
          {isEmpty(watch('addresses')) ? (
            <Button
              onClick={() =>
                setValue(
                  'addresses',
                  [
                    {
                      address1: '',
                      address2: '',
                      city: '',
                      country: '',
                      name: '',
                      state: '',
                      types: [AddressType.BILLING],
                      zip: '',
                    },
                  ],
                  {shouldValidate: true},
                )
              }
              variant='secondary'
            >
              {t('add address')}
            </Button>
          ) : (
            <div className='relative flex w-full flex-col gap-2 rounded-lg border p-4'>
              {!watch('addresses.1') && (
                <Button
                  className='absolute right-1 top-1'
                  onClick={() => {
                    setValue('addresses', []);
                    trigger();
                  }}
                  size='icon'
                  variant='ghost'
                >
                  <Trash2Icon className='size-5 text-red' strokeWidth={1} />
                </Button>
              )}
              <WithLabel>
                <Input {...register('addresses.0.address1')} error={!!errors.addresses?.[0]?.address1} />
                <InputLabel>{t('address (str no bl ap st fl)')}</InputLabel>
              </WithLabel>
              <div className='flex items-center gap-2'>
                <WithLabel className='w-full'>
                  <Input {...register('addresses.0.city')} error={!!errors.addresses?.[0]?.city} />
                  <InputLabel>{t('city')}</InputLabel>
                </WithLabel>
                <WithLabel className='w-full'>
                  <Input {...register('addresses.0.state')} error={!!errors.addresses?.[0]?.state} />
                  <InputLabel>{t('state')}</InputLabel>
                </WithLabel>
              </div>
              <div className='flex items-center gap-2'>
                <WithLabel className='w-full'>
                  <CountriesCombobox
                    error={!!errors.addresses?.[0]?.country}
                    onChange={(value) => setValue('addresses.0.country', value)}
                    value={watch('addresses.0.country')}
                  />
                  <InputLabel>{t('country')}</InputLabel>
                </WithLabel>
                <WithLabel>
                  <Input {...register('addresses.0.zip')} error={!!errors.addresses?.[0]?.zip} size='md' />
                  <InputLabel>{t('zip')}</InputLabel>
                </WithLabel>
              </div>
              <WithLabel>
                <Input {...register('addresses.0.name')} error={!!errors.addresses?.[0]?.name} />
                <InputLabel>{t('preferential name')}</InputLabel>
              </WithLabel>
              <div className='flex h-10 justify-between'>
                <div className='flex gap-8'>
                  <WithLabel direction='horizontal'>
                    <Checkbox defaultChecked={true} disabled />
                    <Label>{t('billing')}</Label>
                  </WithLabel>
                  {!watch('addresses.1') && (
                    <WithLabel direction='horizontal'>
                      <Checkbox
                        checked={watch('addresses.0.types')?.includes(AddressType.SHIPPING)}
                        id='shipping'
                        onCheckedChange={(value) =>
                          setValue(
                            'addresses.0.types',
                            value
                              ? [...(watch('addresses.0.types') || []), AddressType.SHIPPING]
                              : (watch('addresses.0.types') || []).filter((type) => type !== AddressType.SHIPPING),
                          )
                        }
                      />
                      <Label htmlFor='shipping'>{t('shipping')}</Label>
                    </WithLabel>
                  )}
                </div>
                {!watch('addresses.1') && (
                  <Button
                    onClick={() =>
                      setValue(
                        'addresses',
                        [
                          {...watch('addresses.0'), types: [AddressType.BILLING]},
                          {
                            address1: '',
                            address2: '',
                            city: '',
                            country: '',
                            name: '',
                            state: '',
                            types: [AddressType.SHIPPING],
                            zip: '',
                          },
                        ],
                        {shouldValidate: true},
                      )
                    }
                    variant='secondary'
                  >
                    <PlusIcon />
                    {t('shipping address')}
                  </Button>
                )}
              </div>
            </div>
          )}
          {watch('addresses.1') && (
            <div className='relative flex w-full flex-col gap-2 rounded-lg border p-4'>
              <Button
                className='absolute right-1 top-1'
                onClick={() => {
                  setValue('addresses', [watch('addresses.0')]);
                  trigger();
                }}
                size='icon'
                variant='ghost'
              >
                <Trash2Icon className='size-5 text-red' strokeWidth={1} />
              </Button>
              <WithLabel>
                <Input {...register('addresses.1.address1')} error={!!errors.addresses?.[1]?.address1} />
                <InputLabel>{t('address (str no bl ap st fl)')}</InputLabel>
              </WithLabel>
              <div className='flex items-center gap-2'>
                <WithLabel className='w-full'>
                  <Input {...register('addresses.1.city')} error={!!errors.addresses?.[1]?.city} />
                  <InputLabel>{t('city')}</InputLabel>
                </WithLabel>
                <WithLabel className='w-full'>
                  <Input {...register('addresses.1.state')} error={!!errors.addresses?.[1]?.state} />
                  <InputLabel>{t('state')}</InputLabel>
                </WithLabel>
              </div>
              <div className='flex items-center gap-2'>
                <WithLabel className='w-full'>
                  <CountriesCombobox
                    error={!!errors.addresses?.[1]?.country}
                    onChange={(value) => setValue('addresses.1.country', value)}
                    value={watch('addresses.1.country')}
                  />
                  <InputLabel>{t('country')}</InputLabel>
                </WithLabel>
                <WithLabel>
                  <Input {...register('addresses.1.zip')} error={!!errors.addresses?.[1]?.zip} size='md' />
                  <InputLabel>{t('zip')}</InputLabel>
                </WithLabel>
              </div>
              <WithLabel>
                <Input {...register('addresses.1.name')} error={!!errors.addresses?.[1]?.name} />
                <InputLabel>{t('preferential name')}</InputLabel>
              </WithLabel>
              <WithLabel className='h-10' direction='horizontal'>
                <Checkbox defaultChecked={true} disabled />
                <Label>{t('shipping')}</Label>
              </WithLabel>
            </div>
          )}
          {isEmpty(watch('bankAccounts')) ? (
            <Button
              onClick={() =>
                setValue('bankAccounts', [{bank: '', name: '', number: '', swiftNumber: ''}], {shouldValidate: true})
              }
              variant='secondary'
            >
              {t('add bank account')}
            </Button>
          ) : (
            <div className='relative flex w-full flex-col gap-2 rounded-lg border p-4'>
              <Button
                className='absolute right-1 top-1'
                onClick={() => {
                  setValue('bankAccounts', []);
                  trigger();
                }}
                size='icon'
                variant='ghost'
              >
                <Trash2Icon className='size-5 text-red' strokeWidth={1} />
              </Button>
              <WithLabel className='w-full'>
                <Input {...register('bankAccounts.0.bank')} error={!!errors.bankAccounts?.[0]?.bank} />
                <InputLabel>{t('bank')}</InputLabel>
              </WithLabel>
              <div className='flex items-center gap-2'>
                <WithLabel className='w-full'>
                  <Input {...register('bankAccounts.0.number')} error={!!errors.bankAccounts?.[0]?.number} />
                  <InputLabel>{t('number')}</InputLabel>
                </WithLabel>
                <WithLabel>
                  <Input {...register('bankAccounts.0.swiftNumber')} error={!!errors.bankAccounts?.[0]?.swiftNumber} />
                  <InputLabel>{t('swift')}</InputLabel>
                </WithLabel>
              </div>
              <WithLabel className='w-full'>
                <Input {...register('bankAccounts.0.name')} error={!!errors.bankAccounts?.[0]?.name} />
                <InputLabel>{t('preferential name')}</InputLabel>
              </WithLabel>
            </div>
          )}
          {isEmpty(watch('contacts')) ? (
            <Button
              onClick={() => setValue('contacts', [{email: '', name: '', phoneNumber: ''}], {shouldValidate: true})}
              variant='secondary'
            >
              {t('add contact')}
            </Button>
          ) : (
            <div className='relative flex w-full flex-col gap-2 rounded-lg border p-4'>
              <Button
                className='absolute right-1 top-1'
                onClick={() => {
                  setValue('contacts', []);
                  trigger();
                }}
                size='icon'
                variant='ghost'
              >
                <Trash2Icon className='size-5 text-red' strokeWidth={1} />
              </Button>
              <WithLabel>
                <Input {...register('contacts.0.name')} error={!!errors.contacts?.[0]?.name} />
                <InputLabel>{t('name')}</InputLabel>
              </WithLabel>
              <div className='flex items-center gap-2'>
                <WithLabel className='w-full'>
                  <Input {...register('contacts.0.phoneNumber')} error={!!errors.contacts?.[0]?.phoneNumber} />
                  <InputLabel>{t('phone number')}</InputLabel>
                </WithLabel>
                <WithLabel className='w-full'>
                  <Input {...register('contacts.0.email')} error={!!errors.contacts?.[0]?.email} />
                  <InputLabel>{t('email')}</InputLabel>
                </WithLabel>
              </div>
            </div>
          )}
        </SheetContent>
        <SheetFooter className='px-6'>
          <SheetClose asChild disabled={!isEmpty(errors)}>
            <Button
              onClick={() => {
                createCustomer(watch()).then((newCustomer) => {
                  if (newCustomer) {
                    onCreate(newCustomer);
                    onClose();
                  }
                });
              }}
            >
              {t('add customer')}
            </Button>
          </SheetClose>
        </SheetFooter>
      </SheetPage>
    </Sheet>
  );
};

export default NewCustomerSheet;
