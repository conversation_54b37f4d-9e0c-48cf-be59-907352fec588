import {forwardRef, ReactNode} from 'react';

import {FilterIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';

import {Button} from '@/components/ui/Button';
import {
  Sheet,
  SheetClose,
  SheetContent,
  SheetFooter,
  SheetOverlay,
  Sheet<PERSON><PERSON>,
  SheetTitle,
  SheetTrigger,
} from '@/components/ui/Sheet';

type Props = {
  children?: ReactNode;
  isApplyEnabled?: boolean;
  onApply?: () => void;
  onClear?: () => void;
  onClick?: () => void;
  onOpenChange?: (open: boolean) => void;
};

const FiltersSheet = forwardRef<HTMLButtonElement, Props>(
  ({children, isApplyEnabled, onApply, onClear, onClick, onOpenChange}, ref) => {
    const t = useTranslations();
    return (
      <Sheet onOpenChange={onOpenChange}>
        <SheetOverlay variant='light' />
        <SheetTrigger asChild>
          <Button onClick={onClick} ref={ref} variant='secondary'>
            <FilterIcon className='size-5' />
            {t('filter')}
          </Button>
        </SheetTrigger>
        <SheetPage side='right' size='ms'>
          <SheetTitle className='text-2xl'>{t('filter')}</SheetTitle>
          <SheetContent>{children}</SheetContent>
          <SheetFooter className='px-6'>
            <SheetClose asChild>
              <Button disabled={!isApplyEnabled} onClick={onApply}>
                {t('apply filters')}
              </Button>
            </SheetClose>
            <SheetClose asChild>
              <Button onClick={onClear} variant='secondary'>
                {t('clear all')}
              </Button>
            </SheetClose>
          </SheetFooter>
        </SheetPage>
      </Sheet>
    );
  },
);

export {FiltersSheet};
