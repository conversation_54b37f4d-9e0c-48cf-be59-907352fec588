import {FC} from 'react';

import {useAtomValue} from 'jotai';
import {useTranslations} from 'next-intl';

import {Combobox, type ComboboxProps} from '@/components/ui/Combobox';
import {countriesAtom} from '@/store/defaults';
import {Optional} from '@/types/global';

type Props = Optional<ComboboxProps, 'options'>;

const CountriesCombobox: FC<Props> = ({options, placeholder, renderNotFound, searchPlaceholder, ...rest}) => {
  const countries = useAtomValue(countriesAtom);
  const t = useTranslations();

  return (
    <Combobox
      options={options || countries.map((country) => ({id: country.code, value: country.name}))}
      placeholder={placeholder || t('select country')}
      renderNotFound={renderNotFound || (() => t('country not found'))}
      searchPlaceholder={searchPlaceholder || t('search country')}
      {...rest}
    />
  );
};

export default CountriesCombobox;
