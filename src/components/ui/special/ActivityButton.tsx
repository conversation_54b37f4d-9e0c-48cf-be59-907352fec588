import {forwardRef, useCallback, useEffect, useRef, useState} from 'react';

import {MessageCircleIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';

import {Badge} from '@/components/ui/Badge';
import {Button} from '@/components/ui/Button';
import {
  Sheet,
  SheetContent,
  SheetFooter,
  SheetOverlay,
  SheetPage,
  SheetTitle,
  SheetTrigger,
} from '@/components/ui/Sheet';
import MentionTextarea from '@/components/ui/special/MentionTextarea';
import {Tooltip, TooltipContent, TooltipTrigger} from '@/components/ui/Tooltip';
import {useRouter} from '@/hooks/helpers/useRouter';
import useEmployees from '@/hooks/useEmployees';
import {Activity} from '@/types/global';
import {formatDate, formatTime, formatTimeAgo} from '@/utils/format';
import {idsToNames} from '@/utils/mentions';
import {classes} from 'utils/common';

type Props = {
  activities: Activity[];
  createActivity: (note: string) => void;
  onClick?: () => void;
  onOpenChange?: (open: boolean) => void;
};

const ActivityButton = forwardRef<HTMLButtonElement, Props>(
  ({activities, createActivity, onClick, onOpenChange}, ref) => {
    const t = useTranslations();
    const [comment, setComment] = useState('');
    const {employees, isLoading} = useEmployees();

    const {
      pathname,
      query: {noteId, ...query},
      replace,
    } = useRouter();

    const [open, setOpen] = useState(false);
    const activityRefs = useRef<Record<string, HTMLDivElement | null>>({});

    useEffect(() => {
      if (noteId) setOpen(true);
    }, [noteId]);

    useEffect(() => {
      if (typeof noteId !== 'string' || !open) return;
      let cancelled = false;
      const tryScroll = () => {
        if (cancelled) return;
        const el = activityRefs.current[noteId];
        if (el) {
          el.scrollIntoView({behavior: 'smooth', block: 'center'});
          replace({pathname, query}, undefined, {shallow: true});
        } else {
          requestAnimationFrame(tryScroll);
        }
      };
      requestAnimationFrame(tryScroll);
      return () => {
        cancelled = true;
      };
    }, [noteId, open, query, pathname, replace]);

    const sendComment = useCallback(() => {
      createActivity(comment);
      setComment('');
    }, [comment, createActivity]);

    if (isLoading) return null;

    return (
      <Sheet
        onOpenChange={(isOpen) => {
          setOpen(isOpen);
          onOpenChange?.(isOpen);
        }}
        open={open}
      >
        <SheetOverlay variant='light' />
        <SheetTrigger asChild>
          <Button onClick={onClick} ref={ref} variant='secondary'>
            <MessageCircleIcon className='size-5' />
            {t('comments')}
            <Badge size='sm' variant='primary'>
              {activities.length}
            </Badge>
          </Button>
        </SheetTrigger>
        <SheetPage side='right' size='xl'>
          <SheetTitle className='text-2xl'>{t('comments')}</SheetTitle>
          <SheetContent>
            <div className='flex flex-col gap-4'>
              {activities.map((activity) => {
                const activityKey = String(activity.id);
                const {key, value} = formatTimeAgo(activity.createTime, {useYesterday: true});
                const activityRef =
                  activityKey === noteId
                    ? (el: HTMLDivElement | null) => {
                        activityRefs.current[activityKey] = el;
                      }
                    : undefined;

                return (
                  <div
                    className={classes('mx-6 flex flex-col gap-2 rounded-lg border p-4 shadow-sm')}
                    key={activityKey}
                    ref={activityRef}
                  >
                    <div className='whitespace-pre-line text-sm'>{idsToNames(activity.note, employees)?.display}</div>
                    <div className='flex items-center justify-between gap-2 text-sm font-light'>
                      {activity.addedBy.name}

                      <Tooltip>
                        <TooltipTrigger>
                          {key ? (
                            t(key as any, {value})
                          ) : (
                            <>
                              {formatDate(activity.createTime)} - {formatTime(activity.createTime)}
                            </>
                          )}
                        </TooltipTrigger>
                        <TooltipContent>
                          {formatDate(activity.createTime)} - {formatTime(activity.createTime)}
                        </TooltipContent>
                      </Tooltip>
                    </div>
                  </div>
                );
              })}
            </div>
          </SheetContent>
          <SheetFooter className='px-6'>
            <div className='flex items-end gap-2'>
              <MentionTextarea
                autoFocus
                maxRows={5}
                minRows={1}
                onChange={({target: {value}}) => setComment(value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && e.ctrlKey) {
                    e.preventDefault();
                    sendComment();
                  }
                }}
                placeholder={t('add your comment')}
                value={comment}
              />
              <Button disabled={!comment.trim()} onClick={sendComment}>
                {t('add comment')}
              </Button>
            </div>
          </SheetFooter>
        </SheetPage>
      </Sheet>
    );
  },
);

export default ActivityButton;
