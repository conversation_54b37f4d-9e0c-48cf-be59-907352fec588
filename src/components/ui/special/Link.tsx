import {forwardRef} from 'react';

import NextLink, {LinkProps} from 'next/link';

import {classes} from '@/utils/common';

const Link = forwardRef<
  HTMLAnchorElement,
  LinkProps &
    Omit<React.AnchorHTMLAttributes<HTMLAnchorElement>, keyof LinkProps> &
    React.RefAttributes<HTMLAnchorElement>
>(({className, ...props}, ref) => (
  <NextLink className={classes('ring-0 focus-visible:outline-hidden', className)} ref={ref} {...props} />
));
Link.displayName = 'Link';

export {Link};
