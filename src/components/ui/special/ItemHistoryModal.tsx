import {FC, ReactNode, useState} from 'react';

import {useTranslations} from 'next-intl';

import {DatePicker} from '@/components/ui/DatePicker';
import {Sheet, SheetContent, SheetOverlay, SheetPage, SheetTrigger} from '@/components/ui/Sheet';
import {Link} from '@/components/ui/special/Link';
import {WithoutEmpty} from '@/components/ui/special/WithoutEmpty';
import {Table, TableBody, TableCell, TableContainer, TableHead, TableHeader, TableRow} from '@/components/ui/Table';
import {Tabs, TabsList, TabsTrigger} from '@/components/ui/Tabs';
import {ToggleGroup, ToggleGroupItem} from '@/components/ui/ToggleGroup';
import {useHasPermission} from '@/hooks/helpers/useHasPermission';
import useItemHistory from '@/hooks/useItemHistory';
import {DateRange, Id, Unit} from '@/types/global';
import {
  InventoryHistoryDocumentType,
  InventoryItemHistoryCommitted,
  InventoryItemHistoryIncoming,
  InventoryItemHistoryStock,
  StockHistoryEntryType,
} from '@/types/inventory';
import {formatCurrency, formatDate, formatNumber} from '@/utils/format';

type ItemHistoryOrderCellProps = {
  item: Partial<
    Pick<
      InventoryItemHistoryStock,
      'inventoryAdjustmentOrder' | 'manufacturingOrder' | 'purchaseOrder' | 'salesOrder' | 'servicingOrder'
    >
  >;
};

const ItemHistoryOrderCell: FC<ItemHistoryOrderCellProps> = ({item}) => {
  const {hasPermission} = useHasPermission();
  const {inventoryAdjustmentOrder, manufacturingOrder, purchaseOrder, salesOrder, servicingOrder} = item;
  const order = salesOrder || manufacturingOrder || inventoryAdjustmentOrder || purchaseOrder || servicingOrder;

  let path = undefined;
  if (manufacturingOrder?.id === order?.id) {
    if (hasPermission('update', 'manufacturing')) path = 'manufacturing/orders';
  } else if (inventoryAdjustmentOrder?.id === order?.id) {
    if (hasPermission('financial', 'inventory')) path = 'inventory/adjustments';
  } else if (purchaseOrder?.id === order?.id) {
    if (hasPermission('update', 'purchases')) path = 'purchases/orders';
  } else if (salesOrder?.id === order?.id) {
    if (hasPermission('update', 'sales')) path = 'sales/orders';
  } else if (servicingOrder?.id === order?.id) {
    if (hasPermission('update', 'manufacturing')) path = 'manufacturing/services';
  }

  if (!path) return <WithoutEmpty value={order?.id}>{order?.name}</WithoutEmpty>;

  return (
    <WithoutEmpty value={order?.id}>
      <Link className='whitespace-nowrap hover:underline' href={`/${path}/${order?.id}`}>
        {order?.name}
      </Link>
    </WithoutEmpty>
  );
};

type ItemHistoryDocumentCellProps = Pick<InventoryItemHistoryStock, 'document'>;
// New component for rendering document links based on document type
const ItemHistoryDocumentCell: FC<ItemHistoryDocumentCellProps> = ({document}) => {
  const {hasPermission} = useHasPermission();
  let path;
  switch (document?.type) {
    case InventoryHistoryDocumentType.INVENTORY_MOVE_ADJUSTMENT:
      if (hasPermission('read', 'inventory') && hasPermission('financial', 'inventory')) path = 'inventory/adjustments';
      break;
    case InventoryHistoryDocumentType.MATERIAL_ISSUE_NOTE:
      if (
        hasPermission('read', 'inventory') &&
        hasPermission('read', 'manufacturing') &&
        hasPermission('financial', 'inventory')
      )
        path = 'inventory/consumptions';
      break;
    case InventoryHistoryDocumentType.RECEPTION_RECEIPT:
      if (hasPermission('read', 'inventory') && hasPermission('financial', 'inventory')) path = 'inventory/receptions';
      break;
    default:
      break;
  }
  if (!path) return <WithoutEmpty value={document?.id}>{document?.name}</WithoutEmpty>;
  return (
    <WithoutEmpty value={document?.id}>
      <Link className='whitespace-nowrap hover:underline' href={`/${path}/${document?.id}`}>
        {document?.name}
      </Link>
    </WithoutEmpty>
  );
};

type ItemHistoryStockTableProps = ItemHistoryTableProps & {
  hasFinancialPermission: boolean;
  items: InventoryItemHistoryStock[];
};

type ItemHistoryTableProps = {
  isValidating: boolean;
  measurementUnit: Unit;
};

export const ItemHistoryStockTable: FC<ItemHistoryStockTableProps> = ({
  hasFinancialPermission,
  isValidating,
  items,
  measurementUnit,
}) => {
  const t = useTranslations();

  return (
    <TableContainer className='h-[600px]'>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>{t('change date')}</TableHead>
            <TableHead>{t('order')}</TableHead>
            {hasFinancialPermission && <TableHead>{t('document')}</TableHead>}
            <TableHead className='text-right'>{t('quantity')}</TableHead>
            {hasFinancialPermission && <TableHead className='text-right'>{t('cost')}</TableHead>}
            <TableHead className='text-right'>{t('quantity after')}</TableHead>
            {hasFinancialPermission && <TableHead className='text-right'>{t('average cost after')}</TableHead>}
          </TableRow>
        </TableHeader>
        <TableBody className='h-full overflow-y-hidden' isValidating={isValidating}>
          {items.map((item, index) => (
            <TableRow key={`stock-row-${index}`}>
              <TableCell>{formatDate(item.createTime)}</TableCell>
              <TableCell>
                <ItemHistoryOrderCell item={item} />
              </TableCell>
              {hasFinancialPermission && (
                <TableCell>
                  <ItemHistoryDocumentCell document={item.document} />
                </TableCell>
              )}
              <TableCell className='text-right'>
                {formatNumber(item.quantity, true)} {t(`unit.name.${measurementUnit.name.toLowerCase()}` as any)}
              </TableCell>
              {hasFinancialPermission && <TableCell className='text-right'>{formatCurrency(item.price)}</TableCell>}
              <TableCell className='text-right'>
                {formatNumber(item.stockAfter)} {t(`unit.name.${measurementUnit.name.toLowerCase()}` as any)}
              </TableCell>
              {hasFinancialPermission && (
                <TableCell className='text-right'>{formatCurrency(item.averageCostAfter)}</TableCell>
              )}
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );
};

type ItemHistoryCommitedTableProps = ItemHistoryTableProps & {
  items: InventoryItemHistoryCommitted[];
};

export const ItemHistoryCommitedTable: FC<ItemHistoryCommitedTableProps> = ({isValidating, items, measurementUnit}) => {
  const t = useTranslations();

  return (
    <TableContainer className='h-[600px]'>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>{t('order')}</TableHead>
            <TableHead>{t('customer')}</TableHead>
            <TableHead className='text-right'>{t('quantity')}</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody className='h-full overflow-y-hidden' isValidating={isValidating}>
          {items.map((item, index) => (
            <TableRow key={`stock-row-${index}`}>
              <TableCell>
                <ItemHistoryOrderCell item={item} />
              </TableCell>
              <TableCell>
                <WithoutEmpty value={item.customer?.id}>{item.customer?.name}</WithoutEmpty>
              </TableCell>
              <TableCell className='text-right'>
                {formatNumber(item.quantity)} {t(`unit.name.${measurementUnit.name.toLowerCase()}` as any)}
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );
};

type ItemHistoryIncomingTableProps = ItemHistoryTableProps & {
  items: InventoryItemHistoryIncoming[];
};

export const ItemHistoryIncomingTable: FC<ItemHistoryIncomingTableProps> = ({isValidating, items, measurementUnit}) => {
  const t = useTranslations();

  return (
    <TableContainer className='h-[600px]'>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>{t('order')}</TableHead>
            <TableHead className='text-right'>{t('quantity')}</TableHead>
            <TableHead>{t('expected delivery')}</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody className='h-full overflow-y-hidden' isValidating={isValidating}>
          {items.map((item, index) => (
            <TableRow key={`stock-row-${index}`}>
              <TableCell>
                <ItemHistoryOrderCell item={item} />
              </TableCell>
              <TableCell className='text-right'>
                {formatNumber(item.quantity, true)} {t(`unit.name.${measurementUnit.name.toLowerCase()}` as any)}
              </TableCell>
              <TableCell>
                <WithoutEmpty value={item.expectedBy}>{formatDate(item.expectedBy)}</WithoutEmpty>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );
};

export enum ItemHistoryView {
  'committed' = 'committed',
  'incoming' = 'incoming',
  'stock' = 'stock',
}

type Props = {
  children?: ReactNode;
  initialView?: ItemHistoryView;
  item: Id & {code: string; measurementUnit: Unit};
  onClose?: () => void;
  open?: boolean;
};

const HistoryContent: FC<Pick<Props, 'initialView' | 'item'>> = ({initialView = 'stock', item}) => {
  const [dateRange, setDateRange] = useState<DateRange | undefined>(undefined);
  const [entryTypes, setEntryTypes] = useState<StockHistoryEntryType[]>([]);
  const {isValidating, itemHistory} = useItemHistory(item.id, {
    dateRange: dateRange?.to ? dateRange : {from: dateRange?.from, to: dateRange?.from},
    entryType: entryTypes?.length > 1 ? undefined : entryTypes[0],
  });
  const t = useTranslations();
  const [view, setView] = useState(initialView);
  const {hasPermission, isLoading} = useHasPermission();

  if (isLoading) return null;

  return (
    <div className='flex flex-col'>
      <div className='flex items-center justify-between gap-2 border-b pb-4 pl-6 pr-12 pt-0'>
        <div className='flex flex-col'>
          <div className='truncate text-lg font-semibold'>{item.name}</div>
          <div className='text-xs font-medium text-gray-400'>
            {t('sku')}: {item.code}
          </div>
        </div>
        <DatePicker modal onChange={setDateRange} value={dateRange} withRange withRemove />
      </div>
      <div className='flex h-[72px] items-center justify-between gap-4 border-b border-border px-6'>
        <Tabs defaultValue='stock' onValueChange={(value) => setView(value as ItemHistoryView)} variant='menu'>
          <TabsList variant='menu'>
            <TabsTrigger badge={formatNumber(itemHistory?.currentStock)} value={ItemHistoryView.stock} variant='menu'>
              {t('in stock')}
            </TabsTrigger>
            <TabsTrigger
              badge={formatNumber(itemHistory?.reservedStock)}
              value={ItemHistoryView.committed}
              variant='menu'
            >
              {t('committed')}
            </TabsTrigger>
            <TabsTrigger
              badge={formatNumber(itemHistory?.incomingStock)}
              value={ItemHistoryView.incoming}
              variant='menu'
            >
              {t('incoming')}
            </TabsTrigger>
            <TabsTrigger
              badge={formatNumber(itemHistory?.criticalOnHand)}
              disabled
              value='critical on hand'
              variant='menu'
            >
              {t('critical on hand')}
            </TabsTrigger>
            <TabsTrigger badge={formatNumber(itemHistory?.balance)} disabled value='balance' variant='menu'>
              {t('balance')}
            </TabsTrigger>
          </TabsList>
        </Tabs>
        <ToggleGroup
          allowEmpty
          className='mr-6'
          onValueChange={(value) => setEntryTypes(value as StockHistoryEntryType[])}
          type='multiple'
          value={entryTypes}
        >
          <ToggleGroupItem value={StockHistoryEntryType.INCOMING}>
            {t(StockHistoryEntryType.INCOMING as any)}
          </ToggleGroupItem>
          <ToggleGroupItem value={StockHistoryEntryType.OUTGOING}>
            {t(StockHistoryEntryType.OUTGOING as any)}
          </ToggleGroupItem>
        </ToggleGroup>
      </div>
      {view === ItemHistoryView.stock && (
        <ItemHistoryStockTable
          hasFinancialPermission={hasPermission('financial', 'inventory')}
          isValidating={isValidating}
          items={itemHistory?.stockHistory || []}
          measurementUnit={item.measurementUnit}
        />
      )}
      {view === ItemHistoryView.committed && (
        <ItemHistoryCommitedTable
          isValidating={isValidating}
          items={itemHistory?.committedTo || []}
          measurementUnit={item.measurementUnit}
        />
      )}
      {view === ItemHistoryView.incoming && (
        <ItemHistoryIncomingTable
          isValidating={isValidating}
          items={itemHistory?.incomingOrders || []}
          measurementUnit={item.measurementUnit}
        />
      )}
    </div>
  );
};

const ItemHistoryModal: FC<Props> = ({children, initialView, item, onClose, open}) => {
  return (
    <Sheet onOpenChange={onClose} open={open}>
      <SheetOverlay />
      <SheetTrigger asChild>{children}</SheetTrigger>
      <SheetPage size='4xl'>
        <SheetContent>
          <HistoryContent initialView={initialView} item={item} />
        </SheetContent>
      </SheetPage>
    </Sheet>
  );
};

export {ItemHistoryModal};
