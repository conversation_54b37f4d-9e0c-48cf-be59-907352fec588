import {FC} from 'react';

import {LoaderCircleIcon} from 'lucide-react';

type Props = {
  size?: 'lg' | 'md' | 'sm';
};

const sizeMap = {
  lg: 'size-40',
  md: 'size-24',
  sm: 'size-12',
};

const LoadingSpinner: FC<Props> = ({size = 'lg'}) => {
  return (
    <div className='relative flex size-full items-center justify-center text-blue-900'>
      <LoaderCircleIcon className={`${sizeMap[size]} animate-spin`} />
    </div>
  );
};

export {LoadingSpinner};
