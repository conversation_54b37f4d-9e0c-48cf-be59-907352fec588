import {FC, useEffect} from 'react';

import {useAtom} from 'jotai';
import {PrimitiveAtom} from 'jotai';
import {ChevronsDownIcon, ChevronsUpIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';

import {Button} from '@/components/ui/Button';

type Props = {
  store: PrimitiveAtom<boolean>;
};

const HideableContentToggle: FC<Props> = ({store}) => {
  const t = useTranslations();
  const [show, setShow] = useAtom(store);

  useEffect(() => {
    setShow(true);
  }, [setShow]);

  return (
    <div className='flex h-full items-center border-l'>
      <Button className='gap-2' onClick={() => setShow((prev) => !prev)} variant='text'>
        <div className='flex size-6 items-center justify-center rounded-full bg-menu text-white'>
          {show ? <ChevronsUpIcon className='size-5' /> : <ChevronsDownIcon className='size-5' />}
        </div>
        {t(show ? 'hide details' : 'show details')}
      </Button>
    </div>
  );
};

export {HideableContentToggle};
