import {ChangeEvent, forwardRef, useState} from 'react';

import {debounce} from 'lodash';
import {SearchIcon, XIcon} from 'lucide-react';

import {Button} from '@/components/ui/Button';
import {Input, InputProps} from '@/components/ui/Input';
import {AtLeastOne} from '@/types/global';
import {classes} from 'utils/common';

type Props = AtLeastOne<Omit<InputProps, 'key'>, 'id' | 'placeholder' | 'value'> & {
  minLength?: number;
};

const SearchInput = forwardRef<HTMLInputElement, Props>(
  ({className, id, minLength = 3, onChange, placeholder, ...props}, ref) => {
    const [key, setKey] = useState(Math.random());
    const [value, setValue] = useState('');

    const resetInput = () => {
      setKey(Math.random());
    };

    return (
      <div className='relative flex items-center'>
        <Input
          className={classes('pl-8! pr-5!', className)}
          defaultValue={value}
          id={id || placeholder}
          key={key}
          onChange={debounce((event: ChangeEvent<HTMLInputElement>) => {
            if (event.target?.value?.length >= minLength) {
              setValue(event.target?.value);
              onChange?.(event);
            } else {
              setValue('');
            }
          }, 500)}
          placeholder={placeholder}
          ref={ref}
          variant='outline'
          {...props}
        />
        <SearchIcon className='absolute left-2 size-5 shrink-0 cursor-text' />
        {value && (
          <Button
            className='absolute right-2'
            onClick={() => {
              setValue('');
              resetInput();
              onChange?.({target: {value: ''}} as ChangeEvent<HTMLInputElement>);
            }}
            size='none'
            variant='none'
          >
            <XIcon className='size-4' />
          </Button>
        )}
      </div>
    );
  },
);

export {SearchInput};
