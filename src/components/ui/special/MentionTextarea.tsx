import {forwardRef, useCallback, useEffect, useMemo, useRef, useState} from 'react';

import {AtSignIcon} from 'lucide-react';

import {TextareaAutosize, TextareaAutosizeProps} from '@/components/ui/Textarea';
import useEmployees from '@/hooks/useEmployees';
import {Employee} from '@/types/manufacturing';
import {classes} from '@/utils/common';
import {idsToNames} from '@/utils/mentions';

const escapeRegExp = (s: string) => s.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');

const useDisplayToInternal = (employees: Employee[]) =>
  useMemo(() => {
    if (employees.length === 0) return (txt: string) => txt;
    const nameToId = Object.fromEntries(employees.map((e) => [e.name, e.id]));
    const pattern = employees.map((e) => escapeRegExp(e.name)).join('|');
    // eslint-disable-next-line security/detect-non-literal-regexp
    const rx = new RegExp(`@(${pattern})(?=\\s|$)`, 'g');
    return (txt: string) => txt.replace(rx, (_m, name: string) => `@user:${nameToId[name] ?? name}`);
  }, [employees]);

const MentionTextarea = forwardRef<HTMLTextAreaElement, TextareaAutosizeProps>((props, ref) => {
  const {employees} = useEmployees({});
  const displayToInternal = useDisplayToInternal(employees);

  const [internal, setInternal] = useState(
    typeof props.value === 'string' ? props.value : typeof props.defaultValue === 'string' ? props.defaultValue : '',
  );

  useEffect(() => {
    if (typeof props.value === 'string' && props.value !== internal) setInternal(props.value);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [props.value]);

  const {display, mentions} = useMemo(() => idsToNames(internal, employees), [internal, employees]);

  const textareaRef = useRef<HTMLTextAreaElement>(null);
  useEffect(() => {
    if (!ref) return;
    if (typeof ref === 'function') ref(textareaRef.current);
    else ref.current = textareaRef.current;
  }, [ref]);

  const emitChange = useCallback(
    (val: string, native: React.ChangeEvent<HTMLTextAreaElement>) =>
      props.onChange?.({...native, target: {...native.target, value: val}}),
    [props],
  );

  const mentionAt = useCallback(
    (s: number, e: number) => mentions.find((m) => !(e <= m.start || s >= m.end)),
    [mentions],
  );

  const [menu, setMenu] = useState({
    index: 0,
    open: false,
    options: [] as Employee[],
    query: '',
  });

  const buildMenu = useCallback(
    (text: string, caret: number) => {
      const at = text.lastIndexOf('@', caret - 1);
      if (at === -1) return setMenu({index: 0, open: false, options: [], query: ''});
      const q = text.slice(at + 1, caret);
      if (/\s/.test(q)) return setMenu({index: 0, open: false, options: [], query: ''});
      const opts = employees.filter((e) => e.name.toLowerCase().includes(q.toLowerCase()));
      setMenu({index: 0, open: true, options: opts, query: q});
    },
    [employees],
  );

  useEffect(() => {
    const el = textareaRef.current;
    if (el) buildMenu(el.value, el.selectionStart);
  }, [display, buildMenu]);

  const insertEmployee = (emp: Employee) => {
    if (!textareaRef.current) return;
    const el = textareaRef.current;
    const before = el.value.slice(0, el.selectionStart - menu.query.length - 1);
    const after = el.value.slice(el.selectionStart);
    const newInt = displayToInternal(before) + `@user:${emp.id} ` + displayToInternal(after);
    setInternal(newInt);
    emitChange(newInt, {target: {value: newInt}} as any);
    requestAnimationFrame(() => {
      const caret = before.length + `@${emp.name} `.length;
      textareaRef.current?.setSelectionRange(caret, caret);
      textareaRef.current?.focus();
      buildMenu(el.value, caret);
    });
    setMenu({index: 0, open: false, options: [], query: ''});
  };

  const menuKeys = (event: React.KeyboardEvent) => {
    if (!menu.open) return;
    if (event.key === 'ArrowDown') {
      event.preventDefault();
      setMenu((m) => ({
        ...m,
        index: Math.min(m.index + 1, m.options.length - 1),
      }));
    } else if (event.key === 'ArrowUp') {
      event.preventDefault();
      setMenu((m) => ({...m, index: Math.max(m.index - 1, 0)}));
    } else if (event.key === 'Enter') {
      event.preventDefault();
      const emp = menu.options[menu.index];
      if (emp) insertEmployee(emp);
    } else if (event.key === 'Escape') {
      setMenu({index: 0, open: false, options: [], query: ''});
    }
  };

  const handleKeyDown = useCallback(
    (event: React.KeyboardEvent<HTMLTextAreaElement>) => {
      if (!textareaRef.current) return;
      const {selectionEnd: e0, selectionStart: s0} = textareaRef.current;

      if (event.key === '@') {
        const hit = mentionAt(s0, s0);
        if (hit) {
          event.preventDefault();
          const newDisplay = display.slice(0, hit.start) + '@' + display.slice(hit.end);
          const newInternal = displayToInternal(newDisplay);
          setInternal(newInternal);
          emitChange(newInternal, {target: {value: newInternal}} as any);
          requestAnimationFrame(() => {
            const caret = hit.start + 1;
            textareaRef.current?.setSelectionRange(caret, caret);
            buildMenu(newDisplay, caret);
          });
          return;
        }
        return;
      }

      const collapse = (event.key === 'Backspace' && s0 > 0) || event.key === 'Delete';
      if (!collapse) return;

      let [s, e] = [s0, e0];
      if (s === e) {
        if (event.key === 'Backspace') s -= 1;
        else e += 1;
      }
      mentions.forEach((m) => {
        if (m.end > s && m.start < e) {
          s = Math.min(s, m.start);
          e = Math.max(e, m.end);
        }
      });

      event.preventDefault();
      const newDisp = display.slice(0, s) + display.slice(e);
      const newInt = displayToInternal(newDisp);
      setInternal(newInt);
      emitChange(newInt, {target: {value: newInt}} as any);
      requestAnimationFrame(() => textareaRef.current?.setSelectionRange(s, s));
    },
    [display, displayToInternal, mentionAt, emitChange, buildMenu, mentions],
  );

  const handleChange = useCallback(
    (e: React.ChangeEvent<HTMLTextAreaElement>) => {
      const newInt = displayToInternal(e.target.value);
      setInternal(newInt);
      emitChange(newInt, e);
    },
    [displayToInternal, emitChange],
  );

  return (
    <div className='relative w-full'>
      <TextareaAutosize
        {...props}
        className={classes('w-full', props.className)}
        onChange={handleChange}
        onKeyDown={(e) => {
          handleKeyDown(e);
          menuKeys(e);
          props.onKeyDown?.(e);
        }}
        ref={textareaRef}
        value={display}
      />

      {menu.open && (
        <div className='mention-dropdown absolute bottom-full mb-2 left-0 z-50 w-full rounded-md border border-border bg-background shadow-lg'>
          <div className='max-h-[300px] overflow-y-auto p-1'>
            {menu.options.length === 0 ? (
              <div className='py-6 text-center text-sm'>no employee found</div>
            ) : (
              menu.options.map((emp, i) => (
                <button
                  className={`flex w-full items-center gap-0.5 rounded-md px-2 py-2 text-left text-sm hover:bg-gray-200 ${
                    i === menu.index ? 'bg-gray-200' : ''
                  }`}
                  key={emp.id}
                  onMouseDown={(e) => {
                    e.preventDefault();
                    insertEmployee(emp);
                  }}
                >
                  <AtSignIcon className='h-4 w-4' />
                  {emp.name}
                </button>
              ))
            )}
          </div>
        </div>
      )}
    </div>
  );
});

MentionTextarea.displayName = 'MentionTextarea';
export default MentionTextarea;
