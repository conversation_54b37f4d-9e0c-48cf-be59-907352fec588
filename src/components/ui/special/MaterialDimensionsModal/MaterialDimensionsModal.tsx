import {FC, ReactNode, useEffect, useMemo, useState} from 'react';

import {InspectionPanelIcon, ListCheckIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';

import {Button} from '@/components/ui/Button';
import {Combobox} from '@/components/ui/Combobox';
import {Input} from '@/components/ui/Input';
import {Label, WithLabel} from '@/components/ui/Label';
import {Select, SelectContent, SelectItem, SelectTrigger} from '@/components/ui/Select';
import {Sheet, SheetClose, SheetContent, SheetFooter, SheetOverlay, SheetPage, SheetTitle} from '@/components/ui/Sheet';
import {LoadingSpinner} from '@/components/ui/special/LoadingSpinner';
import useDimensions from '@/hooks/useDimensions';
import {MaterialDimension} from '@/types/inventory';
import {classes} from '@/utils/common';

const DEFAULT_SHAPE = 'PLATE';

const shapeIcons: {[key: string]: ReactNode} = {
  L_PROFILE: (
    <svg className='w-7 h-7 mb-1 text-gray-500' fill='none' stroke='currentColor' strokeWidth='2' viewBox='0 0 24 24'>
      <polyline points='5 5 5 19 19 19'></polyline>
    </svg>
  ),
  PLATE: (
    <svg className='w-7 h-7 mb-1 text-gray-500' fill='none' stroke='currentColor' strokeWidth='1.5' viewBox='0 0 24 24'>
      <rect height='14' rx='1' width='18' x='3' y='5'></rect>
    </svg>
  ),
  PLATE_BLACK: (
    <svg
      className='w-7 h-7 mb-1 text-black'
      fill='currentColor'
      stroke='currentColor'
      strokeWidth='0.1'
      viewBox='0 0 24 24'
    >
      <rect height='14' rx='1' width='18' x='3' y='5'></rect>
    </svg>
  ),
  PLATE_GALVANIZED: (
    <svg className='w-7 h-7 mb-1 text-gray-400' fill='none' stroke='currentColor' strokeWidth='1.5' viewBox='0 0 24 24'>
      <rect height='14' rx='1' width='18' x='3' y='5'></rect>
    </svg>
  ),
  RECTANGULAR_TUBE: (
    <svg className='w-7 h-7 mb-1 text-gray-500' fill='none' stroke='currentColor' strokeWidth='1.5' viewBox='0 0 24 24'>
      <rect height='14' rx='1' width='18' x='3' y='5'></rect>
      <rect fill='white' height='8' rx='0.5' width='12' x='6' y='8'></rect>
    </svg>
  ),
  ROUND_BAR: (
    <svg className='w-7 h-7 mb-1 text-gray-500' fill='currentColor' stroke='none' viewBox='0 0 24 24'>
      <circle cx='12' cy='12' r='9'></circle>
    </svg>
  ),
  ROUND_TUBE: (
    <svg className='w-7 h-7 mb-1 text-gray-500' fill='none' stroke='currentColor' strokeWidth='1.5' viewBox='0 0 24 24'>
      <circle cx='12' cy='12' r='9'></circle>
      <circle cx='12' cy='12' fill='white' r='5'></circle>
    </svg>
  ),
  SQUARE_BAR: (
    <svg className='w-7 h-7 mb-1 text-gray-500' fill='currentColor' stroke='none' viewBox='0 0 24 24'>
      <rect height='16' rx='1' width='16' x='4' y='4'></rect>
    </svg>
  ),
  SQUARE_TUBE: (
    <svg className='w-7 h-7 mb-1 text-gray-500' fill='none' stroke='currentColor' strokeWidth='1.5' viewBox='0 0 24 24'>
      <rect height='16' rx='1' width='16' x='4' y='4'></rect>
      <rect fill='white' height='10' rx='0.5' width='10' x='7' y='7'></rect>
    </svg>
  ),
  STRIP_ROLL: (
    <svg className='w-7 h-7 mb-1 text-gray-500' fill='none' stroke='currentColor' strokeWidth='1.5' viewBox='0 0 24 24'>
      <circle cx='12' cy='12' r='9'></circle>
      <circle cx='12' cy='12' r='6'></circle>
      <circle cx='12' cy='12' r='3'></circle>
    </svg>
  ),
  T_PROFILE: (
    <svg className='w-7 h-7 mb-1 text-gray-500' fill='none' stroke='currentColor' strokeWidth='2' viewBox='0 0 24 24'>
      <line x1='5' x2='19' y1='5' y2='5'></line>
      <line x1='12' x2='12' y1='5' y2='19'></line>
    </svg>
  ),
  U_PROFILE: (
    <svg className='w-7 h-7 mb-1 text-gray-500' fill='none' stroke='currentColor' strokeWidth='2' viewBox='0 0 24 24'>
      <polyline points='5 5 5 19 19 19 19 5'></polyline>
    </svg>
  ),
};

type Props = {
  onChange?: (dimensions: MaterialDimension | null) => void;
  onClose?: () => void;
  value?: MaterialDimension | null;
};

const MaterialDimensionsModal: FC<Props> = ({onChange, onClose, value}) => {
  const t = useTranslations();
  const [selectedMaterial, setSelectedMaterial] = useState<string>(value?.material?.key || '');
  const [selectedShape, setSelectedShape] = useState<string>(value?.shape?.shape || '');
  const [values, setValues] = useState<Record<string, any>>(
    value?.dimensions?.reduce((acc, d) => ({...acc, [d.dimensionKey]: d.value}), {}) || {},
  );

  const {dimensions, isLoading} = useDimensions();

  useEffect(() => {
    if (value) {
      setSelectedMaterial(value.material?.key || '');
      setSelectedShape(value.shape?.shape || '');
      setValues(value.dimensions?.reduce((acc, d) => ({...acc, [d.dimensionKey]: d.value}), {}) || {});
    }
  }, [value]);

  useEffect(() => {
    if (value || !dimensions?.length || selectedMaterial) return;

    const firstMat = dimensions[0];
    const firstPlate = firstMat.availableShapes[0].type ?? DEFAULT_SHAPE;

    setSelectedMaterial(firstMat.material.key);
    setSelectedShape(firstPlate);
    setValues({});
  }, [dimensions, value, selectedMaterial]);

  const selectedMaterialData = useMemo(() => {
    if (!selectedMaterial || !dimensions) return null;
    return dimensions.find((d) => d.material.key === selectedMaterial);
  }, [selectedMaterial, dimensions]);

  const isSteel = selectedMaterial?.toLowerCase().includes('steel') ?? false;

  const availableShapes = useMemo(() => {
    const materialData = selectedMaterialData || (dimensions?.length ? dimensions[0] : null);
    if (!materialData) return [];

    return materialData.availableShapes.map((shape) => ({
      ...shape,
      icon: shapeIcons[shape.type] || <InspectionPanelIcon className='size-7 mb-1 text-gray-500' />,
      id: shape.type,
    }));
  }, [selectedMaterialData, dimensions]);

  const visibleShapes = useMemo(
    () => availableShapes.filter((shape) => !(shape.isRestrictedToSteels && !isSteel)),
    [availableShapes, isSteel],
  );

  const getDimensionFields = () => {
    const shape = selectedMaterialData?.availableShapes.find((s) => s.type === selectedShape);
    if (!shape) {
      return <div className='text-sm'>{t('select item type to view dimensions')}</div>;
    }

    const count = shape.dimensions.length;
    const grid = count > 2 ? 'grid grid-cols-3' : count === 2 ? 'grid grid-cols-2' : 'flex flex-col';

    return (
      <div className={`${grid} gap-3`}>
        {shape.dimensions.map((dimension) => (
          <InputWithPresetSelect
            id={`${shape.type}-${dimension.key}`}
            key={`${shape.type}-${dimension.key}`}
            label={`${dimension.name} (mm):`}
            onChange={(val) => setValues({...values, [dimension.key]: val})}
            presetValues={dimension.standardValues || []}
            value={values[dimension.key] || ''}
          />
        ))}
      </div>
    );
  };

  return (
    <Sheet
      onOpenChange={(open) => {
        if (!open) onClose?.();
      }}
      open
    >
      <SheetOverlay />
      <SheetPage side='middle' size='lg'>
        <SheetTitle>{t('set material dimensions')}</SheetTitle>
        <SheetContent className='px-6 gap-6 flex flex-col'>
          {isLoading ? (
            <div className='flex justify-center items-center py-8'>
              <LoadingSpinner size='lg' />
            </div>
          ) : (
            <>
              <WithLabel>
                <Combobox
                  containerClassName='w-[var(--radix-popover-trigger-width)]'
                  onChange={(matKey) => {
                    setSelectedMaterial(matKey);
                    setSelectedShape(
                      dimensions?.find((dimension) => dimension.material.key === matKey)?.availableShapes?.[0]?.type ??
                        DEFAULT_SHAPE,
                    );
                    setValues({});
                  }}
                  options={
                    dimensions?.map((dimension) => ({
                      id: dimension.material.key,
                      value: `${dimension.material.name} (${dimension.material.density} kg/m³)`,
                    })) || []
                  }
                  placeholder={t('select material')}
                  searchPlaceholder={t('search material')}
                  value={selectedMaterial}
                />
                <Label>{t('select material label')}</Label>
              </WithLabel>
              <WithLabel className='gap-4'>
                <div className='grid grid-cols-5 gap-2'>
                  {visibleShapes.map((shape) => (
                    <div
                      className={classes(
                        'flex flex-col items-center justify-center p-2 border-2 rounded-md cursor-pointer hover:bg-gray-light text-center h-full',
                        selectedShape === shape.id ? 'border-primary bg-primary/10 shadow-sm' : 'border-gray-200',
                      )}
                      key={shape.id}
                      onClick={() => {
                        setSelectedShape(shape.id);
                        setValues({});
                      }}
                    >
                      {shape.icon}
                      <span className='text-xs font-medium text-gray-600'>{shape.name}</span>
                    </div>
                  ))}
                </div>
                <Label>{t('select item type')}</Label>
              </WithLabel>
              <WithLabel className='gap-2'>
                <div className='p-3 bg-gray-50 border rounded-md'>{getDimensionFields()}</div>
                <Label>{t('select dimensions')}</Label>
              </WithLabel>
            </>
          )}
          <SheetFooter>
            <div className='flex justify-end items-center gap-4'>
              <SheetClose asChild>
                {value && (
                  <Button onClick={() => onChange?.(null)} variant='secondary'>
                    {t('delete')}
                  </Button>
                )}
              </SheetClose>
              <SheetClose asChild>
                <Button
                  disabled={!selectedShape}
                  onClick={() =>
                    onChange?.({
                      dimensions: Object.entries(values).map(([key, value]) => ({
                        dimensionKey: key,
                        name: key,
                        value: Number(value),
                      })),
                      material: {
                        density: selectedMaterialData?.material.density || 0,
                        key: selectedMaterial,
                        name: selectedMaterialData?.material.name || '',
                      },
                      shape: {
                        name: selectedShape,
                        shape: selectedShape,
                      },
                    })
                  }
                >
                  {t('apply')}
                </Button>
              </SheetClose>
            </div>
          </SheetFooter>
        </SheetContent>
      </SheetPage>
    </Sheet>
  );
};

type InputWithPresetSelectProps = {
  id: string;
  label: string;
  onChange: (value: string) => void;
  presetValues: number[];
  value: string;
};

const InputWithPresetSelect: FC<InputWithPresetSelectProps> = ({id, label, onChange, presetValues, value}) => (
  <WithLabel>
    <div className='flex gap-2 border-b border-muted bg-white mt-2'>
      <Input
        className='border-0 pl-2 grow'
        id={id}
        onChange={(e) => onChange(e.target.value)}
        type='number'
        value={value}
      />
      {presetValues?.length > 0 && (
        <Select onValueChange={(val) => onChange(val)} value=''>
          <SelectTrigger className='w-fit pl-2 border-0 border-l' tabIndex={-1}>
            <ListCheckIcon className='size-4' />
          </SelectTrigger>
          <SelectContent>
            {presetValues.map((val) => (
              <SelectItem key={val} value={val.toString()}>
                {val}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      )}
    </div>
    <Label className='text-xs text-gray-600' htmlFor={id}>
      {label}
    </Label>
  </WithLabel>
);

export default MaterialDimensionsModal;
