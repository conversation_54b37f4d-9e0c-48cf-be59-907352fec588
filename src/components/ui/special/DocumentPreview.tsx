import {FC, useEffect, useRef} from 'react';

import {LoadingSpinner} from '@/components/ui/special/LoadingSpinner';

type Props = {
  content?: string;
};

const DocumentPreview: FC<Props> = ({content}) => {
  const iframeRef = useRef<HTMLIFrameElement>(null);

  useEffect(() => {
    const iframe = iframeRef.current;
    if (!iframe) return;

    const resize = () => {
      const doc = iframe.contentDocument?.documentElement;
      if (doc) iframe.style.height = `${doc.scrollHeight}px`;

      const winDoc = iframe.contentWindow?.document;
      if (winDoc) {
        winDoc.documentElement.style.overflow = 'hidden';
        winDoc.body.style.overflow = 'hidden';
      }
    };

    resize();
    iframe.addEventListener('load', resize);
    return () => iframe.removeEventListener('load', resize);
  }, [content]);

  if (!content) return <LoadingSpinner />;

  return (
    <div className='flex size-full max-w-[794px] items-center justify-center overflow-auto bg-background shadow-2xl'>
      <div className='size-full p-8'>
        <iframe className='size-full' ref={iframeRef} srcDoc={content} title='Document preview' />
      </div>
    </div>
  );
};

export {DocumentPreview};
