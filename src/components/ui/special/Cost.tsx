import {FC, useMemo} from 'react';

import {Dot} from '@/components/ui/special/Dot';
import {Tooltip, TooltipContent, TooltipTrigger} from '@/components/ui/Tooltip';
import {classes} from '@/utils/common';
import {formatNumber} from '@/utils/format';

const availableColors = {
  blue: 'bg-dot-blue',
  gray: 'bg-dot-gray',
  green: 'bg-dot-green',
  orange: 'bg-dot-orange',
  purple: 'bg-dot-purple',
  yellow: 'bg-dot-yellow',
} as const;

type CostBarProps = {
  color: keyof typeof availableColors;
  percentage: number;
};

const CostBar: FC<CostBarProps> = ({color, percentage}) => {
  return <div className={classes('h-full', availableColors[color])} style={{width: `${percentage}%`}} />;
};
CostBar.displayName = 'CostBar';

type CostItemData = {
  color: keyof typeof availableColors;
  cost: number;
  label: string;
};

type CostItemProps = CostItemData & {
  percentage: number;
};

const CostItem: FC<CostItemProps> = ({color, cost, label, percentage}) => {
  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <div className='flex h-[47px] cursor-pointer items-center space-x-2'>
          <Dot variant={color} />
          <div>
            <p className='text-xs font-medium opacity-60'>{label}</p>
            <p className='text-sm'>{formatNumber(cost)}</p>
          </div>
        </div>
      </TooltipTrigger>
      <TooltipContent>{percentage}%</TooltipContent>
    </Tooltip>
  );
};
CostItem.displayName = 'CostItem';

type Props = {
  cost: number;
  items: CostItemData[];
  label: string;
};

const Costs: FC<Props> = ({cost, items, label}) => {
  const computedItems = useMemo(
    () =>
      items.map((item) => ({
        ...item,
        percentage: Math.round((item.cost / cost) * 100),
      })),
    [cost, items],
  );

  return (
    <div className='rounded-lg border border-border'>
      <div className='flex w-full items-center justify-between px-4'>
        {computedItems.map((item) => (
          <CostItem key={item.label} {...item} />
        ))}
        <div className='h-full border-l border-border pl-4'>
          <p className='text-xs font-medium opacity-60'>{label}</p>
          <p className='text-base font-bold'>{formatNumber(cost)}</p>
        </div>
      </div>
      {cost > 0 && (
        <div className='flex h-1.5 w-full overflow-hidden rounded-full border-t border-border'>
          {computedItems.map(({color, percentage}) => (
            <CostBar color={color} key={color} percentage={percentage} />
          ))}
        </div>
      )}
    </div>
  );
};
Costs.displayName = 'Costs';

export {CostBar, CostItem, Costs};
