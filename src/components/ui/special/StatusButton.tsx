import {FC} from 'react';

import {ChevronDownIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';

import {Button} from '@/components/ui/Button';
import {DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger} from '@/components/ui/DropdownMenu';
import {classes} from '@/utils/common';

type Props = {
  className?: string;
  nextStatus: string | undefined;
  onChange: (status: string) => void;
  statuses?: string[];
};

const StatusButton: FC<Props> = ({className, nextStatus, onChange, statuses}) => {
  const t = useTranslations();

  if (!nextStatus) return null;

  return (
    <div className='flex items-center'>
      <Button
        className={classes(className, statuses && statuses.length > 0 && 'rounded-r-none border-r-0')}
        onClick={() => onChange(nextStatus)}
        variant='secondary'
      >
        {t('change status to')}: {t(nextStatus as any)}
      </Button>
      {statuses && statuses.length > 0 && (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button className='rounded-l-none px-2' variant='secondary'>
              <ChevronDownIcon />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            {statuses.map((status) => (
              <DropdownMenuItem asChild key={status} onClick={() => onChange(status)}>
                <Button className='w-full justify-start' variant='ghost'>
                  {t(status as any)}
                </Button>
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
      )}
    </div>
  );
};

export default StatusButton;
