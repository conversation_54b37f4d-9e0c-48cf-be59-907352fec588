import {FC} from 'react';

import {Accordion, AccordionContent, AccordionItem} from '@/components/ui/Accordion';

type Props = {
  children: React.ReactNode;
  show: boolean;
};

const HideableContent: FC<Props> = ({children, show}) => {
  return (
    <Accordion type='single' value={show ? 'content' : ''}>
      <AccordionItem value='content'>
        <AccordionContent className='p-0' quick>
          {children}
        </AccordionContent>
      </AccordionItem>
    </Accordion>
  );
};

export {HideableContent};
