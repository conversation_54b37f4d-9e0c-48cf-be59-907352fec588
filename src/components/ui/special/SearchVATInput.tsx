import {FC, ReactNode, useCallback, useEffect, useRef, useState} from 'react';

import axios from 'axios';
import {debounce} from 'lodash';
import {useTranslations} from 'next-intl';

import {Command, CommandEmpty, CommandGroup, CommandItem, CommandList} from '@/components/ui/Command';
import {Input} from '@/components/ui/Input';
import {Popover, PopoverContent, PopoverTrigger} from '@/components/ui/Popover';
import {toast} from '@/components/ui/Toast';
import useClickOutside from '@/hooks/helpers/useClickOutside';
import {Address, AddressType} from '@/types/global';
import {Customer} from '@/types/sales';
import {classes} from '@/utils/common';

type Props = {
  className?: string;
  containerClassName?: string;
  defaultValue?: string;
  disabled?: boolean;
  notFoundClassName?: string;
  notFoundElement?: ReactNode;
  onChange: (value: Customer) => void;
};

type SearchResults = {
  address: {
    address1: string;
    address2: null | string;
    city: string;
    country: string;
    name: null | string;
    state: null | string;
    types: AddressType[];
    zip: string;
  };
  identificationNumber: string;
  name: string;
  taxIdentificationNumber: string;
};

const SearchVATInput: FC<Props> = ({
  className,
  containerClassName,
  defaultValue,
  disabled,
  notFoundClassName,
  onChange,
}) => {
  const [searching, setSearching] = useState(false);
  const [options, setOptions] = useState<Customer[]>([]);
  const [open, setOpen] = useState(false);
  const t = useTranslations();
  const containerRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  const executeSearch = useCallback(
    (search: string) => {
      setOpen(true);
      setSearching(true);
      setOptions([]);
      axios
        .get(`/api/customers/search/${search}`)
        .then((res) => {
          setSearching(false);
          setOptions(
            res.data.map((customer: SearchResults) => ({
              ...customer,
              addresses: [customer.address as Address],
            })),
          );
        })
        .catch(() => {
          toast.error(t('an error occurred while loading name', {name: t('suffixed.customer.end')}));
          return [];
        });
    },
    [t],
  );

  useClickOutside([containerRef, inputRef], () => {
    setOpen(false);
  });

  useEffect(() => {
    if (defaultValue && defaultValue.length > 2) executeSearch(defaultValue);
  }, [defaultValue, executeSearch]);

  return (
    <Popover open={open}>
      <Command>
        <PopoverTrigger>
          <Input
            className={className}
            defaultValue={defaultValue}
            disabled={disabled}
            onChange={debounce(({target: {value}}) => {
              if (value.length > 2) {
                setOpen(true);
                setSearching(true);
                setOptions([]);
                axios
                  .get(`/api/customers/search/${value}`)
                  .then((res) => {
                    setSearching(false);
                    setOptions(
                      res.data.map((customer: SearchResults) => ({
                        ...customer,
                        addresses: [customer.address as Address],
                      })),
                    );
                  })
                  .catch(() => {
                    toast.error(t('an error occurred while loading name', {name: t('suffixed.customer.end')}));
                    return [];
                  });
              }
            }, 500)}
            onFocus={() => setOpen(searching || options.length > 0)}
            onKeyDown={({key}) => {
              if (key === 'Escape') {
                setOpen(false);
              }
            }}
            placeholder={`${t('search by tax identification number or name')}...`}
            ref={inputRef}
          />
        </PopoverTrigger>
        <PopoverContent className='p-2' onOpenAutoFocus={(e) => e.preventDefault()} ref={containerRef}>
          <CommandList className={containerClassName}>
            <CommandEmpty className={classes('mx-3 my-2.5', notFoundClassName)}>
              {searching && `${t('searching')}...`}
              {!searching && options.length === 0 && t('no results found')}
            </CommandEmpty>
            <CommandGroup>
              {options.map((option) => (
                <CommandItem
                  className='data-[selected=true]:bg-gray-200'
                  key={JSON.stringify(option)}
                  onSelect={() => {
                    onChange(option);
                    setOpen(false);
                    if (inputRef.current) inputRef.current.value = '';
                  }}
                  value={option.id || option.taxIdentificationNumber}
                >
                  {option.taxIdentificationNumber ? `${option.taxIdentificationNumber} - ` : ''}
                  {option.name}
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
        </PopoverContent>
      </Command>
    </Popover>
  );
};
SearchVATInput.displayName = 'VATSearch';

export {SearchVATInput};
