import {FC} from 'react';

import {classes} from 'utils/common';

type Props = {
  barClassName?: string;
  className?: string;
  progressClassName?: string;
};

const LoadingBar: FC<Props> = ({barClassName, className, progressClassName}) => {
  return (
    <div className={className}>
      <div className={classes('h-1 w-full overflow-hidden', barClassName)}>
        <div className={classes('loadingBar size-full', progressClassName)}></div>
      </div>
    </div>
  );
};

export {LoadingBar};
