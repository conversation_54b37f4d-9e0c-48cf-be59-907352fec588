import {CSSProperties, FC, memo} from 'react';

import {useTranslations} from 'next-intl';

import {Checkbox} from '@/components/ui/Checkbox';
import {CommandSimpleItem} from '@/components/ui/Command';
import {NumberInput} from '@/components/ui/Input';
import {Tooltip, TooltipContent, TooltipTrigger} from '@/components/ui/Tooltip';
import {LightInventoryItem} from '@/types/inventory';
import {classes} from '@/utils/common';

export type RowData = {category: string; type: 'header'} | {items: LightInventoryItem[]; type: 'itemRow'};

type Props = {
  index: number;
  row: RowData;
  selectedValues: {id: string; quantity: number}[];
  setQuantity: (id: string, quantity: number) => void;
  style: CSSProperties;
  withoutQuantity?: boolean;
};

const AddItemModalRow: FC<Props> = ({index, row, selectedValues, setQuantity, style, withoutQuantity}) => {
  const t = useTranslations();
  if (row.type === 'header') {
    return (
      <div
        className={classes('border-t pb-2 pt-3 text-muted', index === 0 ? 'border-transparent' : 'mt-3')}
        style={style}
      >
        {row.category}
      </div>
    );
  }

  return (
    <div className='my-1 grid grid-cols-4 gap-x-16' style={style}>
      {row.items.map((item) => {
        const selectedValue = selectedValues.find((selected) => selected.id === item.id);
        const isSelected = !!selectedValue && selectedValue.quantity > 0;

        return (
          <CommandSimpleItem className='flex items-center justify-between gap-2' key={item.id}>
            <div
              className='flex h-full cursor-pointer items-center gap-2'
              onClick={() => {
                setQuantity(item.id, isSelected ? 0 : 1);
              }}
            >
              <Checkbox checked={isSelected} />

              <Tooltip>
                <TooltipTrigger asChild>
                  <div className='line-clamp-1 select-none break-all'>{item.name}</div>
                </TooltipTrigger>
                <TooltipContent>{item.name}</TooltipContent>
              </Tooltip>
            </div>
            {!withoutQuantity && (
              <Tooltip>
                <TooltipTrigger>
                  <NumberInput
                    min={0}
                    onChange={(value) => setQuantity(item.id, value)}
                    small
                    value={selectedValue?.quantity || 0}
                  />
                </TooltipTrigger>
                <TooltipContent>{t(`unit.name.${item.measurementUnit.name}` as any)}</TooltipContent>
              </Tooltip>
            )}
          </CommandSimpleItem>
        );
      })}
    </div>
  );
};

export default memo(AddItemModalRow);
