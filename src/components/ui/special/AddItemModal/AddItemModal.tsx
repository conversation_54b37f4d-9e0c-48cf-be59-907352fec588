import {FC, useCallback, useEffect, useMemo, useRef, useState} from 'react';

import {sortBy} from 'lodash';
import {FilterIcon, PlusIcon, XIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';
import {VariableSizeList} from 'react-window';

import {Button} from '@/components/ui/Button';
import {Command, CommandEmpty, CommandInput, CommandList} from '@/components/ui/Command';
import {Popover, PopoverContent, PopoverTrigger} from '@/components/ui/Popover';
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from '@/components/ui/Select';
import {Sheet, SheetContent, SheetOverlay, SheetPage, SheetTitle} from '@/components/ui/Sheet';
import {Link} from '@/components/ui/special/Link';
import {MultiSelect} from '@/components/ui/special/Multiselect';
import useCategories from '@/hooks/useCategories';
import useEnabledUnits from '@/hooks/useEnabledUnits';
import useItemActions from '@/hooks/useItemActions';
import useItems from '@/hooks/useItems';
import {LightInventoryItem} from '@/types/inventory';

import AddItemModalRow, {RowData} from './AddItemModalRow';

type AddItemModalProps = {
  create?: 'full' | 'none' | 'quick';
  excludeIds?: (null | string | undefined)[];
  filteredByType?: boolean;
  onAdd: (values: {id: string; quantity: number}[]) => void;
  onClose: () => void;
  withoutQuantity?: boolean;
};

const HEADER_ROW_HEIGHT = 56.8;
const ITEM_ROW_HEIGHT = 40;

const groupItems = (
  filteredItems: LightInventoryItem[],
  categories: any[],
  categoriesFilter: string[],
  categoryIdToName: Map<string, string>,
  t: (key: any, options?: any) => string,
): Record<string, LightInventoryItem[]> => {
  const categoryMap = new Map<string, LightInventoryItem[]>();
  const withoutCategory: LightInventoryItem[] = [];

  if (categoriesFilter.includes('all')) {
    categories.forEach((category) => {
      categoryMap.set(category.details.name, []);
    });
  } else {
    categories
      .filter((category) => categoriesFilter.includes(category.id))
      .forEach((category) => {
        categoryMap.set(category.details.name, []);
      });
  }

  filteredItems.forEach((item) => {
    const categoryName = item.category?.id ? categoryIdToName.get(item.category.id) : null;
    if (categoryName) {
      if (categoryMap.has(categoryName)) {
        categoryMap.get(categoryName)!.push(item);
      } else if (categoriesFilter.includes(item.category.id)) {
        categoryMap.set(categoryName, [item]);
      }
    } else {
      withoutCategory.push(item);
    }
  });

  if (withoutCategory.length > 0 && categoriesFilter.includes('without category')) {
    categoryMap.set(t('without category'), withoutCategory);
  }

  return Object.fromEntries(categoryMap);
};

const flattenGroupedItems = (groupedItems: Record<string, LightInventoryItem[]>): RowData[] => {
  const rows: RowData[] = [];

  Object.entries(groupedItems).forEach(([category, items]) => {
    if (items.length > 0) {
      rows.push({category, type: 'header'});
      for (let i = 0; i < items.length; i += 4) {
        rows.push({items: items.slice(i, i + 4), type: 'itemRow'});
      }
    }
  });
  return rows;
};

const AddItemModal: FC<AddItemModalProps> = ({create = 'quick', excludeIds = [], onAdd, onClose, withoutQuantity}) => {
  const t = useTranslations();
  const {isLoading, items} = useItems({}, {revalidateOnFocus: true});
  const {categories, isLoading: categoryIsLoading} = useCategories();
  const {enabledUnits, units} = useEnabledUnits();
  const {createItem} = useItemActions();
  const [selectedValues, setSelectedValues] = useState<{id: string; quantity: number}[]>([]);
  const [categoriesFilter, setCategoriesFilter] = useState<string[]>([]);
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [selectedUnit, setSelectedUnit] = useState<string>('PIECE');
  const searchInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (!categoryIsLoading) {
      setCategoriesFilter(['all', 'without category', ...categories.map((category) => category.id)]);
    }
  }, [categoryIsLoading, categories]);

  const availableItems = useMemo(() => items.filter((item) => !excludeIds.includes(item.id)), [excludeIds, items]);

  const filteredItems = useMemo(() => {
    if (!searchQuery.trim()) return availableItems;
    const lowerQuery = searchQuery.trim().toLowerCase();
    return availableItems.filter((item) => item.name.toLowerCase().includes(lowerQuery));
  }, [availableItems, searchQuery]);

  const categoryIdToName = useMemo(
    () => new Map(categories.map((category) => [category.id, category.details.name])),
    [categories],
  );

  const groupedItems = useMemo(() => {
    return groupItems(filteredItems, categories, categoriesFilter, categoryIdToName, t);
  }, [filteredItems, categories, categoriesFilter, categoryIdToName, t]);

  const flattenedRows: RowData[] = useMemo(() => flattenGroupedItems(groupedItems), [groupedItems]);

  const handleAdd = useCallback(
    (values: {id: string; quantity: number}[]) => {
      const filteredValues = values.filter(({id}) => availableItems.some((item) => item.id === id));
      onAdd(filteredValues);
      onClose();
    },
    [availableItems, onAdd, onClose],
  );

  const setQuantity = useCallback((id: string, quantity: number) => {
    setSelectedValues((prev) => {
      const existing = prev.find((item) => item.id === id);
      if (existing) {
        if (quantity <= 0) {
          return prev.filter((item) => item.id !== id);
        } else {
          return prev.map((item) => (item.id === id ? {...item, quantity} : item));
        }
      } else if (quantity > 0) {
        return [...prev, {id, quantity}];
      }

      return prev;
    });
  }, []);

  const getItemSize = useCallback(
    (index: number) =>
      flattenedRows[index].type === 'header'
        ? index === 0
          ? HEADER_ROW_HEIGHT - 10
          : HEADER_ROW_HEIGHT
        : ITEM_ROW_HEIGHT,
    [flattenedRows],
  );

  const handleMultiSelectChange = useCallback(
    (values: string[]) => {
      setCategoriesFilter((prev) => {
        if (prev.includes('all') && !values.includes('all')) {
          return [];
        } else if (!prev.includes('all') && values.includes('all')) {
          return ['all', 'without category', ...categories.map((c) => c.id)];
        } else {
          return values;
        }
      });
    },
    [categories],
  );

  return (
    <Sheet onOpenChange={onClose} open>
      <SheetOverlay />
      <SheetPage size='10xl'>
        <SheetTitle>{t('add materials')}</SheetTitle>
        <SheetContent className='mx-4 mb-2 h-[800px]'>
          <Command className='h-full' shouldFilter={false}>
            <div className='flex items-center gap-4'>
              <CommandInput
                containerClassName='w-full'
                onValueChange={setSearchQuery}
                placeholder={t('materials')}
                ref={searchInputRef}
                value={searchQuery}
              />
              <Popover>
                <PopoverTrigger>
                  <Button variant='secondary'>
                    <FilterIcon className='size-5' /> {t('filter')}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className='max-w-none p-2'>
                  <MultiSelect
                    defaultValue={categoriesFilter}
                    onValueChange={handleMultiSelectChange}
                    options={[
                      {id: 'all', value: t('all', {isFemale: 'true'})},
                      ...sortBy(
                        categories.map((category) => ({
                          id: category.id,
                          value: category.details.name,
                        })),
                        'value',
                      ),
                      {id: 'without category', value: t('without category')},
                    ]}
                    renderNotFound={() => t('category not found')}
                    searchPlaceholder={t('search category')}
                  />
                </PopoverContent>
              </Popover>
              <Button disabled={!selectedValues.length} onClick={() => handleAdd(selectedValues)}>
                <PlusIcon className='size-5' /> {t('add number of materials', {number: selectedValues.length})}
              </Button>
              <Button onClick={onClose} variant='secondary'>
                <XIcon /> {t('cancel')}
              </Button>
            </div>
            <CommandList className='max-h-full p-2'>
              {isLoading ? (
                <CommandEmpty>{t('loading')}</CommandEmpty>
              ) : flattenedRows.length === 0 ? (
                <CommandEmpty className='py-0 -mx-1'>
                  {create === 'none' && t('no results found')}
                  {create !== 'none' && (
                    <div className='flex flex-col gap-4 max-w-56'>
                      {create === 'quick' && searchQuery.trim() && (
                        <div className='flex items-center gap-4'>
                          <Button
                            className='grow justify-start'
                            onClick={() => {
                              createItem({
                                criticalOnHand: 0,
                                measurementUnit: {id: selectedUnit, name: ''},
                                name: searchQuery,
                              }).then((item) => {
                                if (item) {
                                  setQuantity(item.id, 1);

                                  setTimeout(() => {
                                    if (searchInputRef.current) searchInputRef.current.focus();
                                  }, 100);
                                }
                              });
                            }}
                          >
                            {t('add item')}
                          </Button>
                          <Select onValueChange={setSelectedUnit} value={selectedUnit}>
                            <SelectTrigger size='sm'>
                              <SelectValue>
                                {t(`unit.name.${units.find(({id}) => id === selectedUnit)?.name || 'pcs'}` as any)}
                              </SelectValue>
                            </SelectTrigger>
                            <SelectContent>
                              {enabledUnits.map(({id, name}) => (
                                <SelectItem key={id} value={id}>
                                  {t(`unit.name.${name.toLowerCase()}` as any)}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                      )}
                      {create === 'full' && (
                        <Button asChild>
                          <Link href={`/inventory/items/new?name=${encodeURIComponent(searchQuery)}`} target='_blank'>
                            {t('add item')}
                          </Link>
                        </Button>
                      )}
                    </div>
                  )}
                </CommandEmpty>
              ) : (
                <VariableSizeList
                  height={646}
                  itemCount={flattenedRows.length}
                  itemSize={getItemSize}
                  overscanCount={5}
                  width='100%'
                >
                  {({index, style}) => (
                    <AddItemModalRow
                      index={index}
                      row={flattenedRows[index]}
                      selectedValues={selectedValues}
                      setQuantity={setQuantity}
                      style={style}
                      withoutQuantity={withoutQuantity}
                    />
                  )}
                </VariableSizeList>
              )}
            </CommandList>
          </Command>
        </SheetContent>
      </SheetPage>
    </Sheet>
  );
};

export default AddItemModal;
