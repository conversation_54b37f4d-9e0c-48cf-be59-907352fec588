import {forwardRef, HTMLAttributes} from 'react';

import {cva, VariantProps} from 'class-variance-authority';

import {classes} from '@/utils/common';

const dotVariants = cva('size-2 rounded-full', {
  defaultVariants: {
    variant: 'default',
  },
  variants: {
    variant: {
      blue: 'bg-dot-blue',
      default: '',
      gray: 'bg-dot-gray',
      green: 'bg-dot-green',
      orange: 'bg-dot-orange',
      purple: 'bg-dot-purple',
      yellow: 'bg-dot-yellow',
    },
  },
});

type Props = HTMLAttributes<HTMLDivElement> &
  VariantProps<typeof dotVariants> & {
    dotClassName?: string;
  };

const Dot = forwardRef<HTMLDivElement, Props>(({children, className, dotClassName, variant, ...props}, ref) => {
  return (
    <div className={classes('inline-flex items-center gap-1', className)} ref={ref} {...props}>
      <span className={classes(dotVariants({className: dotClassName, variant}), dotClassName)} />
      {children}
    </div>
  );
});
Dot.displayName = 'Dot';

export {Dot};
