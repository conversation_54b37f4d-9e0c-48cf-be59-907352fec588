import {FC, InputHTMLAttributes, ReactNode, useEffect, useRef, useState} from 'react';

import {useTranslations} from 'next-intl';

import {Checkbox} from '@/components/ui/Checkbox';
import {Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList} from '@/components/ui/Command';
import {RadioGroup, RadioGroupItem} from '@/components/ui/RadioGroup';
import {Tooltip, TooltipContent, TooltipTrigger} from '@/components/ui/Tooltip';
import {classes} from '@/utils/common';

type MultiSelectProps = InputHTMLAttributes<HTMLInputElement> & {
  autoFocus?: boolean;
  defaultValue?: string[];
  listClassName?: string;
  notFoundClassName?: string;
  onValueChange: (value: string[]) => void;
  options: OptionType[];
  panelClassName?: string;
  renderNotFound: (query: string) => ReactNode;
  searchPlaceholder: string;
  selectedFirst?: boolean;
  single?: boolean;
  withoutRemove?: boolean;
};

type OptionType = {id: string; value: string};

const MultiSelect: FC<MultiSelectProps> = ({
  autoFocus,
  defaultValue,
  listClassName,
  notFoundClassName,
  onValueChange,
  options,
  panelClassName,
  renderNotFound,
  searchPlaceholder,
  selectedFirst,
  single,
  ...props
}) => {
  const [selectedValues, setSelectedValues] = useState<string[]>(defaultValue || []);
  const selectedValuesSet = useRef(new Set(selectedValues));
  const [query, setQuery] = useState('');
  const t = useTranslations();

  useEffect(() => {
    setSelectedValues(defaultValue || []);
    selectedValuesSet.current = new Set(defaultValue);
  }, [defaultValue]);

  const toggleOption = (value: string) => {
    if (single) {
      selectedValuesSet.current.clear();
      selectedValuesSet.current.add(value);
    } else {
      if (selectedValuesSet.current.has(value)) {
        selectedValuesSet.current.delete(value);
      } else {
        selectedValuesSet.current.add(value);
      }
    }

    setSelectedValues(Array.from(selectedValuesSet.current));
    onValueChange(Array.from(selectedValuesSet.current));
  };

  // ---- Sort options so that selected ones come first, if selectedFirst is true ----
  const sortedOptions = selectedFirst
    ? [
        ...options.filter((opt) => selectedValuesSet.current.has(opt.id)),
        ...options.filter((opt) => !selectedValuesSet.current.has(opt.id)),
      ]
    : options;

  return (
    <Command className={classes(panelClassName)}>
      <CommandInput
        {...props}
        autoFocus={autoFocus}
        onValueChange={setQuery}
        placeholder={searchPlaceholder || `${t('search')}...`}
        value={query}
      />
      <CommandList className={classes('max-h-[332px]', listClassName)} onClick={() => setQuery('')}>
        <CommandEmpty className={classes('mx-3 my-2.5', notFoundClassName)}>{renderNotFound(query)}</CommandEmpty>
        <CommandGroup>
          <RadioGroup orientation='vertical'>
            {sortedOptions.map((option) => {
              const isSelected = selectedValuesSet.current.has(option.id);

              return (
                <CommandItem key={option.value} onSelect={() => toggleOption(option.id)}>
                  {!single && <Checkbox checked={isSelected} />}
                  {single && <RadioGroupItem checked={isSelected} value={''} />}

                  <Tooltip>
                    <TooltipTrigger>
                      <span className='line-clamp-1'>{option.value}</span>
                    </TooltipTrigger>
                    <TooltipContent>{option.value}</TooltipContent>
                  </Tooltip>
                </CommandItem>
              );
            })}
          </RadioGroup>
        </CommandGroup>
      </CommandList>
    </Command>
  );
};

MultiSelect.displayName = 'MultiSelect';

export {MultiSelect};
