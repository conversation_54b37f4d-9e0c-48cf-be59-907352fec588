import {FC, useState} from 'react';

import copy from 'clipboard-copy';
import {AlertCircleIcon, BrushIcon, CopyIcon, SlidersVerticalIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';

import {Button} from '@/components/ui/Button';
import {toast} from '@/components/ui/Toast';
import {Tooltip, TooltipContent, TooltipTrigger} from '@/components/ui/Tooltip';
import {Id} from '@/types/global';
import {classes} from '@/utils/common';
import InventoryItemPreviewDetailsSheet from 'components/ui/special/InventoryItemLink/InventoryItemPreviewDetailsSheet';

export type InventoryItemLinkSheetProps = {
  fullscreen?: boolean;
  id: string;
  onClose: () => void;
};

type Props = {
  className?: string;
  configured?: boolean;
  copyCode?: boolean;
  customized?: boolean;
  disabled?: boolean;
  fullscreen?: boolean;
  item: Id & {category?: Id | null; code?: string; variant?: null | string};
};

const InventoryItemLink: FC<Props> = ({className, configured, copyCode, customized, disabled, fullscreen, item}) => {
  const t = useTranslations();
  const [showDetails, setShowDetails] = useState(false);

  return (
    <>
      <div
        className={classes(
          'group flex w-fit select-none flex-col gap-1 whitespace-nowrap',
          !disabled && 'cursor-pointer',
          className,
        )}
        onMouseDown={(event) => {
          if (disabled) return;

          if (event.button === 1) {
            event.preventDefault();
            window.open(`/inventory/items/${item.id}`, '_blank');
          } else if (event.button === 0) {
            setShowDetails(true);
          }
        }}
      >
        <div className={classes('relative flex items-center gap-1', !disabled && 'group-hover:underline')}>
          <Tooltip>
            <TooltipTrigger asChild>
              <div className='max-w-60 overflow-x-hidden truncate'>{item.variant || item.name}</div>
            </TooltipTrigger>
            <TooltipContent>{item.variant || item.name}</TooltipContent>
          </Tooltip>

          {configured && (
            <Tooltip>
              <TooltipTrigger asChild>
                <SlidersVerticalIcon className='size-5 rounded-full bg-button-primary p-1' strokeWidth={2.5} />
              </TooltipTrigger>
              <TooltipContent>{t('configured')}</TooltipContent>
            </Tooltip>
          )}
          {customized && (
            <Tooltip>
              <TooltipTrigger asChild>
                <BrushIcon className='size-5 rounded-full bg-button-primary p-1' strokeWidth={2.5} />
              </TooltipTrigger>
              <TooltipContent>{t('customized')}</TooltipContent>
            </Tooltip>
          )}
          {item.category === null && (
            <Tooltip>
              <TooltipTrigger asChild>
                <AlertCircleIcon className='size-5 rounded-full text-red p-1' strokeWidth={2.5} />
              </TooltipTrigger>
              <TooltipContent>{t('incomplete item')}</TooltipContent>
            </Tooltip>
          )}
        </div>
        {item.code && (
          <div className='flex items-center gap-1 text-xs text-gray-400'>
            {`${t('sku')}: ${item.code}`}
            {copyCode && (
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    onClick={() => {
                      copy(item.code || '');
                      toast.success(t('name value has been copied', {name: t('sku'), value: item.code}));
                    }}
                    onMouseDown={(event) => event.stopPropagation()}
                    size='none'
                    variant='none'
                  >
                    <CopyIcon className='size-3' strokeWidth={2} />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>{t('copy')}</TooltipContent>
              </Tooltip>
            )}
          </div>
        )}
      </div>
      {showDetails && (
        <InventoryItemPreviewDetailsSheet fullscreen={fullscreen} id={item.id} onClose={() => setShowDetails(false)} />
      )}
    </>
  );
};

export default InventoryItemLink;
