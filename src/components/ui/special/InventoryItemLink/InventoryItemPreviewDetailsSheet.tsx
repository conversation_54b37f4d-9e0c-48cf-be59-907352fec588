import {FC} from 'react';

import {CheckIcon, PencilIcon, XIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';
import {FormProvider} from 'react-hook-form';

import InventoryItemsDetailsDataCosts from '@/components/pages/details/inventory/items/InventoryItemsDetailsData/InventoryItemsDetailsDataCosts';
import InventoryItemsDetailsDataDetails from '@/components/pages/details/inventory/items/InventoryItemsDetailsData/InventoryItemsDetailsDataDetails';
import InventoryItemsDetailsDataSale from '@/components/pages/details/inventory/items/InventoryItemsDetailsData/InventoryItemsDetailsDataSale';
import InventoryItemsDetailsDataSuppliers from '@/components/pages/details/inventory/items/InventoryItemsDetailsData/InventoryItemsDetailsDataSuppliers';
import {Badge} from '@/components/ui/Badge';
import {Sheet, SheetContent, SheetPage, SheetTitle} from '@/components/ui/Sheet';
import {Link} from '@/components/ui/special/Link';
import {Switch} from '@/components/ui/Switch';
import {Table, TableBody, TableCell, TableHead, TableHeader, TableRow} from '@/components/ui/Table';
import {useHasPermission} from '@/hooks/helpers/useHasPermission';
import useItem from '@/hooks/useItem';
import {minutesToWorkHoursTimeString} from '@/utils/common';
import {formatCurrency} from '@/utils/format';

import InventoryItemLink, {InventoryItemLinkSheetProps} from './InventoryItemLink';

const InventoryItemPreviewDetailsSheet: FC<InventoryItemLinkSheetProps> = ({fullscreen, id, onClose}) => {
  const {
    isLoading,
    item,
    useFormActions: {watch, ...restUseFormState},
  } = useItem(id);
  const {hasPermission, isLoading: permissionIsLoading} = useHasPermission();
  const t = useTranslations();

  if (!item || isLoading || permissionIsLoading) return null;

  return (
    <Sheet defaultOpen={true} onOpenChange={() => setTimeout(onClose, 500)}>
      <SheetPage side='right' size='2xl'>
        <SheetTitle className='text-2xl '>
          <Link
            className='flex w-fit items-center gap-2 hover:underline'
            href={`/inventory/items/${item.id}`}
            target={fullscreen ? '_self' : '_blank'}
          >
            <PencilIcon className='size-6' />
            {item.name}
          </Link>
        </SheetTitle>
        <SheetContent className='flex flex-col gap-4 px-6'>
          <FormProvider {...{watch, ...restUseFormState}}>
            <InventoryItemsDetailsDataDetails readonly />
            {hasPermission('financial', 'inventory') && (
              <InventoryItemsDetailsDataSale className='grid-cols-4' readonly />
            )}
            {watch('lastPurchase.supplierName') && <InventoryItemsDetailsDataSuppliers />}
            {hasPermission('financial', 'inventory') && watch('produced') && <InventoryItemsDetailsDataCosts />}
            {hasPermission('read', 'manufacturing') && watch('produced') && (
              <>
                {t('recipe for')} {item.unitOfProduction}{' '}
                {t(`unit.name.${item.measurementUnit.name.toLowerCase()}` as any)}
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>{t('material')}</TableHead>
                      <TableHead className='text-right'>{t('quantity')}</TableHead>
                      <TableHead className='text-right'>{t('waste')}</TableHead>
                      {hasPermission('financial', 'inventory') && (
                        <TableHead className='text-right'>{t('cost')}</TableHead>
                      )}
                      <TableHead className='text-right'>{t('available', {isPlural: 'true'})}</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody className='overflow-y-hidden' isValidating={!watch()}>
                    {watch('requiredMaterials')?.map((requiredMaterial, index) => {
                      const primaryOption = requiredMaterial.options?.[0];

                      if (!primaryOption) return null;

                      return (
                        <TableRow className='h-[61px]' key={`${primaryOption.id}-${index}`}>
                          <TableCell className='inline-flex w-full items-center justify-between gap-2'>
                            <InventoryItemLink disabled item={primaryOption} />
                          </TableCell>
                          <TableCell className='text-right'>{requiredMaterial.quantity}</TableCell>
                          <TableCell className='text-right'>{requiredMaterial.wastePercentage}%</TableCell>
                          {hasPermission('financial', 'inventory') && (
                            <TableCell className='text-right'>{formatCurrency(primaryOption.cost)}</TableCell>
                          )}
                          <TableCell className='text-right'>
                            <Badge className='gap-1' variant={primaryOption.available ? 'success' : 'error'}>
                              {primaryOption.available && <CheckIcon className='size-4 text-green' />}
                              {!primaryOption.available && <XIcon className='size-4 text-red' />}
                              {t(primaryOption.available ? 'available' : 'unavailable', {isPlural: 'true'})}
                            </Badge>
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>{t('operations')}</TableHead>
                      <TableHead className='text-right'>{t('duration')}</TableHead>
                      <TableHead className='text-right'>{t('parallelization')}</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody className='overflow-y-hidden' isValidating={!watch()}>
                    {watch('manufacturingOperations')?.map((operation, index) => (
                      <TableRow key={`${operation.id}-${index}`}>
                        <TableCell className='inline-flex items-center gap-2'>{operation.name}</TableCell>
                        <TableCell className='text-right'>
                          {minutesToWorkHoursTimeString(operation.durationInMinutes)}
                        </TableCell>
                        <TableCell className='text-right'>
                          <Switch checked={watch(`manufacturingOperations.${index}.parallelizable`)} disabled />
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </>
            )}
          </FormProvider>
        </SheetContent>
      </SheetPage>
    </Sheet>
  );
};

export default InventoryItemPreviewDetailsSheet;
