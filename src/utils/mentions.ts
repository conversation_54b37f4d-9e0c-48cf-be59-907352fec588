import {Mention} from '@/types/global';
import {Employee} from '@/types/manufacturing';

export const idsToNames = (raw: string, employees: Employee[]): {display: string; mentions: Mention[]} => {
  const idToName: Record<string, string> = Object.fromEntries(employees.map((e) => [e.id, e.name]));

  const mentions: Mention[] = [];
  let display = '';
  let i = 0;

  const rx = /@user:([^\s]+)(\s|$)/g;
  let m: null | RegExpExecArray;

  while ((m = rx.exec(raw))) {
    display += raw.slice(i, m.index);

    const id = m[1];
    const name = idToName[id] ?? id;

    const start = display.length;
    display += `@${name}`;
    const end = display.length;

    mentions.push({end, id, name, start});

    display += m[2];
    i = m.index + m[0].length;
  }

  display += raw.slice(i);
  return {display, mentions};
};
