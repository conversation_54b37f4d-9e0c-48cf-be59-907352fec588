import axios from 'axios';

import {toast} from '@/components/ui/Toast';
import {ErrorMessage} from '@/types/global';

const backendAxios = axios.create();

const redirectIfClient = (path: string) => {
  if (typeof window !== 'undefined') {
    window.location.replace(path);
  }
};

axios.interceptors.response.use(
  (response) => response,
  (error) => {
    const status = error?.response?.status;
    const pathname = typeof window !== 'undefined' ? window.location.pathname : '';

    if (status === 401) {
      redirectIfClient('/api/auth/logout');
      return Promise.reject();
    }

    if (status === 404 && pathname !== '/404' && !pathname.endsWith('404')) {
      redirectIfClient('/404');
      return Promise.reject();
    }

    if (status === 400) {
      const {message, userMessage} = error?.response?.data as ErrorMessage;
      if (userMessage) {
        toast.warning(userMessage);
      } else if (message) {
        toast.warning(message);
      }

      return Promise.reject(error);
    }

    if (status === 500 && pathname !== '/500' && !pathname.endsWith('500')) {
      redirectIfClient('/500');
      return Promise.reject();
    }

    return Promise.reject(error);
  },
);

export const handleError = (error: any, errorMessage: string): any => {
  const status = error?.response?.status;
  const hasUserMessage = !!error?.response?.data?.userMessage;
  const hasMessage = !!error?.response?.data?.message;

  if (status !== 400 || (status === 400 && !hasUserMessage && !hasMessage)) {
    toast.error(errorMessage);
  }

  return Promise.reject([]);
};

export {backendAxios};
export default axios;
