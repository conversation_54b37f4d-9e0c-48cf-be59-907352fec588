// @ts-ignore
import {theme} from '../../tailwind.config';

export type Screen = '2xl' | 'lg' | 'md' | 'sm' | 'xl';
export const screens = theme.screens as Record<Screen, string>;

/**
 * Given a pixel value, returns just below the next breakpoint.
 * See: https://www.w3.org/TR/mediaqueries-4/#mq-min-max
 */
const getMaxValue = (bp: string): string => `${parseInt(bp) - 0.02}px`;

const getBreakpointValue = (screen: Screen): string => {
  const value = screens[screen];
  if (!value) throw new Error(`Breakpoint "${screen}" is not defined in tailwind.config`);
  return String(value);
};

const getBreakpointKeys = (): Screen[] => Object.keys(screens) as Screen[];

/** Min-width media query */
export const up = (bp: Screen): string => {
  const min = getBreakpointValue(bp);
  return `@media only screen and (min-width: ${min})`;
};

/** Max-width media query */
export const down = (bp: Screen): string => {
  const max = getMaxValue(getBreakpointValue(bp));
  return `@media only screen and (max-width: ${max})`;
};

/** Between two breakpoints */
export const between = (minBp: Screen, maxBp: Screen): string => {
  const min = getBreakpointValue(minBp);
  const max = getMaxValue(getBreakpointValue(maxBp));
  return `@media only screen and (min-width: ${min}) and (max-width: ${max})`;
};

/** Media query for only one breakpoint range */
export const only = (bp: Screen): string => {
  const keys = getBreakpointKeys();
  const index = keys.indexOf(bp);
  const nextBp = keys[index + 1];

  return nextBp ? between(bp, nextBp) : up(bp);
};
