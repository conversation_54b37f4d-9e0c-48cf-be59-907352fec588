import {AbilityBuilder, createMongoAbility} from '@casl/ability';
import jwt from 'jsonwebtoken';

import {Actions, PermissionsAbility, Subjects} from '@/types/permissions';

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const testPermissions = {
  customers: ['read', 'update'],
  employees: ['read', 'update', 'financial'],
  inventory: ['read', 'update', 'financial'],
  invoices: ['read', 'update', 'financial'],
  manufacturing: ['read', 'update', 'financial'],
  purchases: ['read', 'update', 'financial'],
  reports: ['read', 'update', 'financial'],
  sales: ['read', 'update', 'financial'],
  settings: ['update'],
  suppliers: ['read', 'update'],
};

export const definePermissions = (permissions: Record<string, string[]>): PermissionsAbility => {
  const {can, rules} = new AbilityBuilder<PermissionsAbility>(createMongoAbility as any);

  Object.entries(permissions).forEach(([subject, actions]) => {
    actions.forEach((action) => {
      can(action as Actions, subject as Subjects);
    });
  });

  return createMongoAbility(rules) as PermissionsAbility;
};

export const getPermissionsFromToken = (token: string): Record<string, string[]> => {
  const decodedToken = jwt.decode(token);

  if (
    decodedToken &&
    typeof decodedToken === 'object' &&
    '/permissions' in decodedToken &&
    typeof decodedToken['/permissions'] === 'object'
  ) {
    return decodedToken['/permissions'] as Record<string, string[]>;
  }

  return {};
};

export const hasRequiredPermissions = (
  rawPermissions: null | Record<string, string[]> | undefined,
  action: Actions,
  subject: Subjects,
): boolean => {
  const actionsForSubject = rawPermissions?.[subject];
  if (!actionsForSubject) return false;

  if (action === 'read') {
    return actionsForSubject.includes('read') || actionsForSubject.includes('update');
  }

  return actionsForSubject.includes(action);
};
