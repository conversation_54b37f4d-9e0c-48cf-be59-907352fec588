const lightBgColors = [
  'bg-linear-to-t from-gray-200 to-gray-50',
  'bg-linear-to-t from-gray-500 to-gray-200',
  'bg-linear-to-t from-blue-200 to-blue-50',
  'bg-linear-to-t from-blue-500 to-blue-200',
  'bg-linear-to-t from-purple-200 to-purple-50',
  'bg-linear-to-t from-purple-500 to-purple-200',
  'bg-linear-to-t from-violet-200 to-violet-50',
  'bg-linear-to-t from-violet-500 to-violet-200',
  'bg-linear-to-t from-teal-200 to-teal-50',
  'bg-linear-to-t from-teal-500 to-teal-200',
  'bg-linear-to-t from-rose-200 to-rose-50',
  'bg-linear-to-t from-rose-500 to-rose-200',
  'bg-linear-to-t from-green-200 to-green-50',
  'bg-linear-to-t from-green-500 to-green-200',
  'bg-linear-to-t from-yellow-200 to-yellow-50',
  'bg-linear-to-t from-yellow-500 to-yellow-200',
  'bg-linear-to-t from-orange-200 to-orange-50',
  'bg-linear-to-t from-orange-500 to-orange-200',
  'bg-linear-to-t from-cyan-200 to-cyan-50',
  'bg-linear-to-t from-cyan-500 to-cyan-200',
  'bg-linear-to-t from-pink-200 to-pink-50',
  'bg-linear-to-t from-pink-500 to-pink-200',
  'bg-linear-to-t from-indigo-200 to-indigo-50',
  'bg-linear-to-t from-indigo-500 to-indigo-200',
  'bg-linear-to-t from-red-200 to-red-50',
  'bg-linear-to-t from-red-500 to-red-200',
  'bg-linear-to-t from-amber-200 to-amber-50',
  'bg-linear-to-t from-amber-500 to-amber-200',
  'bg-linear-to-t from-lime-200 to-lime-50',
  'bg-linear-to-t from-lime-500 to-lime-200',
  'bg-linear-to-t from-fuchsia-200 to-fuchsia-50',
  'bg-linear-to-t from-fuchsia-500 to-fuchsia-200',
  'bg-linear-to-t from-emerald-200 to-emerald-50',
  'bg-linear-to-t from-emerald-500 to-emerald-200',
] as const;

const darkBgColors = [
  'bg-linear-to-t from-gray-300 to-gray-400',
  'bg-linear-to-t from-gray-800 to-gray-500',
  'bg-linear-to-t from-blue-300 to-blue-400',
  'bg-linear-to-t from-blue-800 to-blue-500',
  'bg-linear-to-t from-purple-300 to-purple-400',
  'bg-linear-to-t from-purple-800 to-purple-500',
  'bg-linear-to-t from-violet-300 to-violet-400',
  'bg-linear-to-t from-violet-800 to-violet-500',
  'bg-linear-to-t from-teal-300 to-teal-400',
  'bg-linear-to-t from-teal-800 to-teal-500',
  'bg-linear-to-t from-rose-300 to-rose-400',
  'bg-linear-to-t from-rose-800 to-rose-500',
  'bg-linear-to-t from-green-300 to-green-400',
  'bg-linear-to-t from-green-800 to-green-500',
  'bg-linear-to-t from-yellow-300 to-yellow-400',
  'bg-linear-to-t from-yellow-800 to-yellow-500',
  'bg-linear-to-t from-orange-300 to-orange-400',
  'bg-linear-to-t from-orange-800 to-orange-500',
  'bg-linear-to-t from-cyan-300 to-cyan-400',
  'bg-linear-to-t from-cyan-800 to-cyan-500',
  'bg-linear-to-t from-pink-300 to-pink-400',
  'bg-linear-to-t from-pink-800 to-pink-500',
  'bg-linear-to-t from-indigo-300 to-indigo-400',
  'bg-linear-to-t from-indigo-800 to-indigo-500',
  'bg-linear-to-t from-red-300 to-red-400',
  'bg-linear-to-t from-red-800 to-red-500',
  'bg-linear-to-t from-amber-300 to-amber-400',
  'bg-linear-to-t from-amber-800 to-amber-500',
  'bg-linear-to-t from-lime-300 to-lime-400',
  'bg-linear-to-t from-lime-800 to-lime-500',
  'bg-linear-to-t from-fuchsia-300 to-fuchsia-400',
  'bg-linear-to-t from-fuchsia-800 to-fuchsia-500',
  'bg-linear-to-t from-emerald-300 to-emerald-400',
  'bg-linear-to-t from-emerald-800 to-emerald-500',
] as const;

export const bitwiseHash = (value: string): number => {
  let hash = 0;

  for (let i = 0; i < value.length; i++) {
    hash = ((hash << 5) - hash + value.charCodeAt(i)) | 0;
  }

  return hash;
};

const lightSolidColors = [
  // Primary colors (500 series) with text colors
  'bg-blue-500 text-blue-500',
  'bg-green-500 text-green-500',
  'bg-yellow-500 text-yellow-500',
  'bg-purple-500 text-purple-500',
  'bg-pink-500 text-pink-500',
  'bg-indigo-500 text-indigo-500',
  'bg-red-500 text-red-500',
  'bg-orange-500 text-orange-500',
  'bg-teal-500 text-teal-500',
  'bg-cyan-500 text-cyan-500',
  'bg-lime-500 text-lime-500',
  'bg-amber-500 text-amber-500',
  'bg-emerald-500 text-emerald-500',
  'bg-violet-500 text-violet-500',
  'bg-fuchsia-500 text-fuchsia-500',
  'bg-rose-500 text-rose-500',
  'bg-gray-500 text-gray-500',
  // Lighter variants (400 series) with text colors
  'bg-blue-400 text-blue-500',
  'bg-green-400 text-green-500',
  'bg-yellow-400 text-yellow-500',
  'bg-purple-400 text-purple-500',
  'bg-pink-400 text-pink-500',
  'bg-indigo-400 text-indigo-500',
  'bg-red-400 text-red-500',
  'bg-orange-400 text-orange-500',
  'bg-teal-400 text-teal-500',
  'bg-cyan-400 text-cyan-500',
  'bg-lime-400 text-lime-500',
  'bg-amber-400 text-amber-500',
  'bg-emerald-400 text-emerald-500',
  'bg-violet-400 text-violet-500',
  'bg-fuchsia-400 text-fuchsia-500',
  'bg-rose-400 text-rose-500',
  'bg-gray-400 text-gray-500',
] as const;

const darkSolidColors = [
  // Primary dark colors (700 series) with text colors
  'bg-blue-700 text-blue-700',
  'bg-green-700 text-green-700',
  'bg-yellow-700 text-yellow-700',
  'bg-purple-700 text-purple-700',
  'bg-pink-700 text-pink-700',
  'bg-indigo-700 text-indigo-700',
  'bg-red-700 text-red-700',
  'bg-orange-700 text-orange-700',
  'bg-teal-700 text-teal-700',
  'bg-cyan-700 text-cyan-700',
  'bg-lime-700 text-lime-700',
  'bg-amber-700 text-amber-700',
  'bg-emerald-700 text-emerald-700',
  'bg-violet-700 text-violet-700',
  'bg-fuchsia-700 text-fuchsia-700',
  'bg-rose-700 text-rose-700',
  'bg-gray-700 text-gray-700',
  // Darker variants (800 series) with text colors
  'bg-blue-800 text-blue-700',
  'bg-green-800 text-green-700',
  'bg-yellow-800 text-yellow-700',
  'bg-purple-800 text-purple-700',
  'bg-pink-800 text-pink-700',
  'bg-indigo-800 text-indigo-700',
  'bg-red-800 text-red-700',
  'bg-orange-800 text-orange-700',
  'bg-teal-800 text-teal-700',
  'bg-cyan-800 text-cyan-700',
  'bg-lime-800 text-lime-700',
  'bg-amber-800 text-amber-700',
  'bg-emerald-800 text-emerald-700',
  'bg-violet-800 text-violet-700',
  'bg-fuchsia-800 text-fuchsia-700',
  'bg-rose-800 text-rose-700',
  'bg-gray-800 text-gray-700',
] as const;

export const getBgColor = (
  value: null | string | undefined,
  {dark, solid}: {dark?: boolean; solid?: boolean} = {},
): string | undefined => {
  if (!value) return undefined;

  let colors;
  if (solid) {
    colors = dark ? darkSolidColors : lightSolidColors;
  } else {
    colors = dark ? darkBgColors : lightBgColors;
  }

  const index = Math.abs(bitwiseHash(value)) % colors.length;

  return colors[index];
};

export const javaStringHashCode = (value: string) => {
  let hash = 0;
  for (let i = 0; i < value.length; i++) {
    hash = value.charCodeAt(i) + 31 * hash;
  }
  return hash;
};

export const simpleAdditiveHash = (value: string) => {
  return value.split('').reduce((hash, char) => hash + char.charCodeAt(0), 0);
};

export const multiplicativeHash = (value: string) => {
  return value.split('').reduce((hash, char) => (hash * 31 + char.charCodeAt(0)) | 0, 0);
};

export const djb2Hash = (value: string) => {
  let hash = 5381;
  for (let i = 0; i < value.length; i++) {
    const char = value.charCodeAt(i);
    hash = (hash << 5) + hash + char; /* hash * 33 + char */
  }
  return hash;
};

export const sdbmHash = (value: string) => {
  let hash = 0;
  for (let i = 0; i < value.length; i++) {
    const char = value.charCodeAt(i);
    hash = char + (hash << 6) + (hash << 16) - hash;
  }
  return hash;
};

export const fnv1Hash = (value: string) => {
  let hash = 0x811c9dc5;
  for (let i = 0; i < value.length; i++) {
    hash = (hash ^ value.charCodeAt(i)) * 0x01000193;
  }
  return hash >>> 0;
};

export const oneAtATimeHash = (value: string) => {
  let hash = 0;
  for (let i = 0; i < value.length; i++) {
    hash += value.charCodeAt(i);
    hash += hash << 10;
    hash ^= hash >> 6;
  }
  hash += hash << 3;
  hash ^= hash >> 11;
  hash += hash << 15;
  return hash >>> 0;
};

export const bernsteinHash = (value: string) => {
  let hash = 0;
  for (let i = 0; i < value.length; i++) {
    const char = value.charCodeAt(i);
    hash = (33 * hash) ^ char;
  }
  return hash >>> 0;
};

export const shiftAndXORHash = (value: string) => {
  let hash = 0;
  for (let i = 0; i < value.length; i++) {
    hash ^= (hash << 5) + (hash >> 2) + value.charCodeAt(i);
  }
  return hash;
};

export const rotatingHash = (value: string) => {
  let hash = 0;
  for (let i = 0; i < value.length; i++) {
    hash = (hash << 4) ^ (hash >> 28) ^ value.charCodeAt(i);
  }
  return hash >>> 0;
};

export const xorShiftHash = (value: string) => {
  let hash = 0xdeadbeef;
  for (let i = 0; i < value.length; i++) {
    hash ^= value.charCodeAt(i);
    hash = (hash << 5) ^ (hash >> 2);
  }
  return hash;
};

export const elfHash = (value: string) => {
  let hash = 0;
  let x = 0;
  for (let i = 0; i < value.length; i++) {
    hash = (hash << 4) + value.charCodeAt(i);
    x = hash & 0xf0000000;
    if (x !== 0) {
      hash ^= x >> 24;
    }
    hash &= ~x;
  }
  return hash;
};

export const getHashColor = (hash: number, dark?: boolean) => {
  const colors = dark ? darkBgColors : lightBgColors;

  return colors.at(Math.abs(hash) % colors.length);
};
