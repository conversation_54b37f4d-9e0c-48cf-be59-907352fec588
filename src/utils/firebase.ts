import axios from 'axios';
import {getApp, getApps, initializeApp} from 'firebase/app';
import {getMessaging, getToken, isSupported, MessagePayload, onMessage} from 'firebase/messaging';

const firebaseConfig = {
  apiKey: 'AIzaSyAFt34HBS1zFzV1j3FBRlR354yjkeWmngI',
  appId: '1:977657342189:web:3eb9a950d722eaf9cf44e4',
  authDomain: 'fabriqon-app-prod.firebaseapp.com',
  measurementId: 'G-ZNM1E1ZYQX',
  messagingSenderId: '977657342189',
  projectId: 'fabriqon-app-prod',
  storageBucket: 'fabriqon-app-prod.appspot.com',
};

const app = !getApps().length ? initializeApp(firebaseConfig) : getApp();

const firebaseCloudMessagingInit = async (callback: (payload: MessagePayload) => void) => {
  try {
    return await isSupported().then((supported) => {
      if (!supported) return false;

      return Notification.requestPermission().then(async (permission) => {
        if (permission !== 'granted') return false;

        const messaging = getMessaging(app);

        try {
          await getToken(messaging, {
            vapidKey: 'BE3y1uh6h3cqJqqcI8MBWrl0OQzZzuJDehkD-MH43J7dlNWuMRouAUN8p1r-TzPKtuSfLMrGIal_70Qq-nmKQJI',
          })
            .then(async (token) => {
              if (token) {
                await axios.post(`/api/notifications/firebase/register-token/${token}`, {});
                return;
              } else {
                throw Error('NOTIFICATION: Failed to retrieve token.');
              }
            })
            .catch((err) => {
              throw err;
            });
        } catch (err) {
          throw err;
        }

        onMessage(messaging, (payload) => {
          callback(payload);
        });

        return true;
      });
    });
  } catch (err) {
    console.error(err);
    return false;
  }
};

export {firebaseCloudMessagingInit};
