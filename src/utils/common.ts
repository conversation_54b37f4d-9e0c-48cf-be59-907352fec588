import {ClassValue, clsx} from 'clsx';
import {addDays as dfnAddDays, isSaturday, isSunday, parse} from 'date-fns';
import {GetServerSidePropsContext} from 'next';
import {twMerge} from 'tailwind-merge';

import {Currency} from '@/types/global';

export const isClient = typeof window !== 'undefined';
export const isDev = process.env.NODE_ENV === 'development';

export const getReturnTo = ({defaultLocale, locale, resolvedUrl}: GetServerSidePropsContext) => ({
  returnTo: locale === defaultLocale ? resolvedUrl : `/${locale}${resolvedUrl}`,
});

export const classes = (...inputs: ClassValue[]) => twMerge(clsx(inputs));

export const getInitials = (username?: null | string): string | undefined =>
  username
    ?.replace(/[+-]/g, '')
    .split(' ')
    .map((w) => w[0])
    .join('')
    .slice(0, 2);

export const minutesToWorkHoursTimeString = (
  value: null | number | undefined,
  workingHoursPerDay: number = 24,
  daysText = 'd',
  hoursText = 'h',
  minutesText = 'min',
): string => {
  if (value === undefined || value === null) return '-';

  const totalHours = Math.floor(value / 60);
  const remainingMinutes = value % 60;

  const days = Math.floor(totalHours / workingHoursPerDay);
  const hours = totalHours % workingHoursPerDay;

  const parts = [
    days ? `${days}${daysText}` : '',
    hours ? `${hours}${hoursText}` : '',
    remainingMinutes ? `${Math.ceil(remainingMinutes)}${minutesText}` : '',
  ];

  return parts.filter(Boolean).join(' ') || `0${minutesText}`;
};

export const durationStringToDate = (value: string): Date => parse(value, 'HH:mm', new Date());

export const getQueryString = (
  params: Record<string, boolean | null | number | string | string[] | undefined>,
): string => {
  const searchParams = new URLSearchParams();

  Object.entries(params).forEach(([key, value]) => {
    if (value != null && value !== false) {
      if (Array.isArray(value)) {
        value.forEach((v) => v && searchParams.append(key, v.toString()));
      } else {
        searchParams.append(key, value.toString());
      }
    }
  });

  return searchParams.toString();
};

export const addDays = (
  daysToAdd: number,
  {date = new Date(), skipWeekends = true}: {date?: Date; skipWeekends?: boolean} = {},
): Date => {
  let resultDate = new Date(date);
  let daysAdded = 0;

  while (daysAdded < daysToAdd) {
    resultDate = dfnAddDays(resultDate, 1);
    if (!skipWeekends || (!isSaturday(resultDate) && !isSunday(resultDate))) {
      daysAdded++;
    }
  }

  return resultDate;
};

export const arrayOrObjectCheck = <T extends Record<string, unknown> | unknown[]>(
  trueValue: T,
  check: unknown,
  falseValue: T = (Array.isArray(trueValue) ? [] : {}) as T,
): T => (check ? trueValue : falseValue);

export const parseCurrency = (currency?: Currency, to = true): Currency | undefined => {
  if (!currency) return undefined;

  const amount = currency.amount || 0;
  const parsedAmount = to ? Number((amount * 100).toFixed(0)) : Number((amount / 100).toFixed(2));

  return {...currency, amount: parsedAmount};
};

export const newId = (): string => Math.random().toString(36).slice(2, 11);

export const withoutUnset = <T>(value: T): '-' | T => (value !== undefined && value !== null ? value : '-');

export const estimateTextWidth = (text: string): number => {
  const canvas = document.createElement('canvas');
  const context = canvas.getContext('2d');
  if (!context) return 0;

  context.font = '15px Arial';
  return Math.round(context.measureText(text).width) + 10;
};
