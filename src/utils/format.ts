import {
  differenceInDays,
  differenceInHours,
  differenceInMinutes,
  differenceInSeconds,
  differenceInWeeks,
  isValid,
  isYesterday,
  parse,
  parseISO,
} from 'date-fns';

import {defaultLocale} from '@/constants/config';
import {Address, Currency, ExchangeRates} from 'types/global';

const getLocale = (): string => (localStorage.getItem('locale') || defaultLocale).replace(/"/g, '');

const parseAnyDate = (input?: Date | null | string): Date | undefined => {
  if (!input) return undefined;

  if (typeof input === 'string') {
    let parsed = parse(input, 'dd.MM.yyyy', new Date());
    if (isValid(parsed)) return parsed;

    parsed = parseISO(input);
    if (isValid(parsed)) return parsed;

    parsed = new Date(input);
    if (isValid(parsed)) return parsed;

    return undefined;
  }

  const parsed = new Date(input);
  return isValid(parsed) ? parsed : undefined;
};

export const formatCurrency = (price?: Currency | null): string => {
  if (!price || isNaN(price.amount) || !price.currency) return '-';

  return new Intl.NumberFormat(getLocale(), {
    currency: price.currency,
    style: 'currency',
  }).format(price.amount);
};

export const formatDate = (date?: Date | null | string): string => {
  const parsedDate = parseAnyDate(date);
  if (!parsedDate) return '-';

  return new Intl.DateTimeFormat(getLocale()).format(parsedDate);
};

export const formatDay = (
  date?: Date | null | string,
  format: 'long' | 'narrow' | 'short' = 'long',
): string | undefined => {
  const parsedDate = parseAnyDate(date);
  if (!parsedDate) return undefined;

  return new Intl.DateTimeFormat(getLocale(), {weekday: format}).format(parsedDate);
};

export const formatTime = (date?: Date | null | string, hideMinutes?: boolean): string | undefined => {
  const parsedDate = parseAnyDate(date);
  if (!parsedDate) return undefined;

  return new Intl.DateTimeFormat(getLocale(), {
    hour: 'numeric',
    ...(hideMinutes ? {} : {minute: 'numeric'}),
  }).format(parsedDate);
};

export const formatNumber = (number?: number, sign?: boolean): string =>
  new Intl.NumberFormat(getLocale(), {
    ...(sign ? {signDisplay: 'exceptZero'} : {}),
  }).format(number ?? 0);

export const addressToString = (address?: Partial<Address>, partial?: boolean): string => {
  const parts = partial
    ? [address?.country, address?.state, address?.city]
    : [address?.country, address?.state, address?.city, address?.address1, address?.address2, address?.zip];

  return parts.filter(Boolean).join(', ');
};

export const formatVAT = (vatRate?: null | number, defaultVat: number = 0): string =>
  ((vatRate ?? defaultVat) * 100).toFixed(0);

export const convertCurrency = (
  amount: number | undefined,
  from: string,
  to: string,
  exchangeRates: ExchangeRates,
): number => {
  if (amount === undefined || !exchangeRates[from] || !exchangeRates[to]) return 0;

  const amountInRON = amount / exchangeRates[to];
  return amountInRON * exchangeRates[from];
};

export const formatTimeAgo = (
  date: Date | string | undefined,
  {
    hide = [],
    useYesterday = false,
  }: {
    hide?: ('day' | 'days' | 'hours' | 'minutes' | 'seconds' | 'week' | 'weeks')[];
    useYesterday?: boolean;
  },
) => {
  const now = new Date();

  if (!date) {
    return {
      key: 'now',
      value: now,
    };
  }

  const parsedDate = new Date(date);
  const diffSeconds = differenceInSeconds(now, parsedDate);

  if (diffSeconds < 11) {
    return {
      key: 'now',
      value: parsedDate,
    };
  }

  if (!hide.includes('seconds') && diffSeconds < 60) {
    return {
      key: 'value s ago',
      value: diffSeconds,
    };
  }

  const diffMinutes = differenceInMinutes(now, parsedDate);
  if (!hide.includes('minutes') && diffMinutes < 60) {
    return {
      key: 'value min ago',
      value: diffMinutes,
    };
  }

  const diffHours = differenceInHours(now, parsedDate);
  if (!hide.includes('hours') && diffHours < 24) {
    if (useYesterday && isYesterday(parsedDate)) {
      return {
        key: 'yesterday',
        value: parsedDate,
      };
    }
    return {
      key: 'value h ago',
      value: diffHours,
    };
  }

  const diffDays = differenceInDays(now, parsedDate);
  if (!hide.includes('day') && diffDays === 1) {
    if (useYesterday) {
      return {
        key: 'yesterday',
        value: parsedDate,
      };
    }
    return {
      key: 'value day ago',
      value: 1,
    };
  }

  if (!hide.includes('days') && diffDays < 7) {
    return {
      key: 'value days ago',
      value: diffDays,
    };
  }

  const diffWeeks = differenceInWeeks(now, parsedDate);
  if (!hide.includes('week') && diffWeeks === 1) {
    return {
      key: 'value week ago',
      value: 1,
    };
  }

  if (!hide.includes('weeks') && diffWeeks <= 3) {
    return {
      key: 'value weeks ago',
      value: diffWeeks,
    };
  }

  return {
    key: undefined,
    value: parsedDate,
  };
};
