{"ACCOMPANYING_LETTER": "Goods Accompanying Note", "account information": "Account Information", "actions": "Actions", "active": "Active", "activities": "Activities", "actual duration": "Actual Duration", "actual quantity": "Actual Quantity", "add": "Add", "add address": "Add Address", "add adjustment": "Add Adjustment", "add bank account": "Add Bank Account", "add category": "Add Category", "add comment": "Add Comment", "add consumable": "Add Consumable", "add consumables": "Add Consumables", "add contact": "Add Contact", "add customer": "Add Customer", "add customization note": "Add Customization Note", "add delegation": "Add Delegation", "add document": "Add Document", "add employee": "Add Employee", "add file": "Add file", "add files": "Add Files", "add goods accompanying note": "Add Accompanying Note", "add inventory unit": "Add Inventory Unit", "add item": "Add Item", "add leave": "Add Leave", "add list": "Add List", "add manufacturing order": "Add Manufacturing Order", "add material": "Add Material", "add materials": "Add Materials", "add new activity": "Add New Activity", "add new address": "Add New Address", "add new bank account": "Add New Bank Account", "add new contact": "Add New Contact", "add new option": "Add New Option", "add number of materials": "Add {number} of Tasks", "add number of tasks": "Add {number} of Tasks", "add operation": "Add Operation", "add operations": "Add Operations", "add option": "Add Option", "add order": "Add Order", "add product": "Add Product", "add quote": "Add Quote", "add reception": "Add Reception", "add sale order": "Add Sales Order", "add service": "Add Service", "add step": "Add Operation", "add subassembly": "Add Work In Progress", "add supplier": "Add Supplier", "add variant": "<PERSON><PERSON>", "add variant option": "Add Option", "add variants": "Add <PERSON>", "add widget": "Add Widget", "add workstation": "Add Workstation", "add your comment": "Add your comment", "address": "Address", "address (str no bl ap st fl)": "Address (Str. No. Bl. Ap. Sc. Et.)", "address not found": "Address not found", "addresses": "Addresses", "adjust": "Adjust", "adjust stock": "Adjust Stock", "adjusted on": "Adjusted On", "adjustment": "Stock Adjustment", "ADJUSTMENT": "Adjustment", "adjustment reason": "Adjustment Reason", "adjustment type": "Adjustment Type", "adjustment value": "Adjustment Value", "adjustments": "Stock Adjustments", "after stock": "After Stock", "align center": "Align Center", "align left": "Align Left", "align right": "Align Right", "all": "{is<PERSON><PERSON><PERSON>, select, true {All} false {All} other {All}}", "alternative": "Alternative", "amount": "Quantity", "an error occurred while loading name": "An error occurred while loading {name}. Please try again! If the problem persists, please contact us!", "apply": "Apply", "apply filters": "Apply Filters", "are you sure you want to leave this page?": "Are you sure you want to continue?", "assembling": "Manufacturing", "assigned to": "Assigned to", "availability": "Availability", "available": "{isPlural, select, true {Available} false {Available} other {Available}}", "available total": "Total Available", "average cost": "Average Cost", "average cost after": "Average Cost After", "awaiting customization": "Awaiting Customization", "back": "Back", "balance": "Balance", "bank": "Bank", "bank accounts": "Bank Accounts", "before stock": "Before Stock", "bill of materials": "Bill of Materials", "billing": "Billing", "ACCOUNTED": "Accounted", "BLOCKED": "Blocked", "bold": "Bold", "bought": "Purchased", "BROKEN_EQUIPMENT": "Broken Equipment", "bullet list": "Bullet List", "buy": "Buy", "BY_QUANTITY": "By Quantity", "BY_VALUE": "By Value", "cancel": "Cancel", "cancel date": "Cancellation Date", "cancel order": "Cancel Order", "CANCELED": "Canceled", "canceled": {"female": "canceled", "male": "canceled"}, "canceled orders": "Canceled", "categories": "Categories", "category": "Category", "category not found": "Category not found", "change date": "Change Date", "change photo": "Change Photo", "change status to": "Change Status To", "changes have been successfully applied": "The changes have been successfully applied.", "city": "City", "clear all": "Clear All", "client name": "Client Name", "client signature": "Client Signature", "clone consumable": "Clone Consumable", "clone item": "<PERSON><PERSON>", "clone material": "Clone Material", "clone order": "Clone Order", "clone product": "Clone This Product", "clone quote": "<PERSON><PERSON> Quote", "clone subassembly": "Clone Work In Progress", "close": "Close", "code": "Code", "command execution": "Command Execution", "comment box test": "Comment Box Test", "comments": "Comments", "committed": "Reserved", "company name": "Company Name", "complete the customization": "Complete the Customization", "complete the information below and select the data you want to be displayed on the commercial documents generated by the system": "Complete the information below and select the data you want to be displayed on the commercial documents generated by the system", "completed": "Completed", "configurable": "Configurable", "configure": "Configure", "configured": "Configured", "confirm": "Confirm", "confirm cancel": "Confirm Cancellation", "confirm delete": "Confirm Deletion", "confirm discard": "Confirm Discard", "connect mobile app": "Connect to Mobile App", "CONSUMABLE": "Consumable", "consumable details": "Consumable Details", "consumables": "Consumables", "consumption": "Consumption", "consumption date": "Consumption Date", "consumption details": "Consumption Details", "consumptions": "Consumptions", "contacts": "Contacts", "copied": "<PERSON>pied", "copy": "Copy", "copy from items": "Copy from Items", "copy link": "Copy Link", "cost": "Cost", "cost per hour": "Cost per Hour", "cost per unit": "Cost per Unit", "costs": "Costs", "country": "Country", "country not found": "Country not found", "create a new offer": "Create a New Offer", "create category": "Create Category", "create consumption": "Create Consumption", "create custom manufacturing order": "Create Custom Manufacturing Order", "create document": "Create Document", "create document and download": "Create Document and Download", "create employee": "Create Employee", "create invoice": "Issue Invoice", "create invoice and download": "Issue Invoice and Download", "create invoice and send": "Issue Invoice and Send", "create new address": "Create New Address", "create new customer": "Create New Customer", "create new supplier": "Create New Supplier", "create operation": "Create Operation", "create order": "Register Order", "create order and download": "Register Order and Download", "create order and send": "Register Order and Send", "create quote": "Issue Quote", "create quote and download": "Issue Quote and Download", "create quote and send": "Issue Quote and Send", "create service": "Create Service", "create supplier": "Create Supplier", "create workstation": "Create Workstation", "created": {"female": "created", "male": "created"}, "created on": "Created On", "critical on hand": "Minimum Required", "currency": "<PERSON><PERSON><PERSON><PERSON>", "current stock": "Current Stock", "CUSTOM": "Custom", "custom order": "Custom Order", "custom product": "Custom Product", "customer": "Customer", "customer name": "Customer Name", "customers": "Customers", "customization note": "Customization Note", "customization notes": "Customization Notes", "CUSTOMIZATION_NEEDED": "Product Requires Customization", "customize": "Customize", "customized": "Customized", "d": "d", "dashboard": "Dashboard", "date": "Date", "date completed": "Completion Date", "date range": "Date Range", "days": "Days", "default": "<PERSON><PERSON><PERSON>", "default delivery time for sales orders": "Default Delivery Time for Sales Orders", "default inventory unit": "Default Inventory Unit", "default supplier": "<PERSON><PERSON>ult Su<PERSON>lier", "default template": "<PERSON><PERSON><PERSON>", "default vat value": "Default VAT Rate", "delegate": "Delegate", "delegate id": "Delegate ID", "delegate name": "Delegate Name", "delete": "Delete", "delete customization note": "Delete Customization Note", "delete list": "Delete List", "delete selected table": "Delete Selected Table", "deleted": {"female": "deleted", "male": "deleted"}, "delivered": "Delivered", "DELIVERED": "Delivered", "delivered by": "Delivered By", "delivered date": "Delivered Date", "delivery date": "Delivery Date", "delivery deadline": "Delivery Deadline", "description": "Description", "destination unit": "Destination Unit", "details": "Details", "discard": "Discard", "DISCOUNT": "Discount", "discount": "Discount", "discount amount": "Discount Amount", "document": "Document", "document date": "Document Date", "document language": "Document Language", "document number": "Document Number", "document quantity": "Document Quantity", "document title": "Document Title", "documents": "Documents", "CLOSED": "Closed", "CONSUMPTION_RECORDED": "Consumption Recorded", "DONE": "Done", "done": "Done", "download": "Download", "download invoice": "Download Invoice", "DUE": "Due", "due date": "Due Date", "duration": "Duration", "edit": "Edit", "edit options": "Edit Options", "edit variants": "<PERSON>", "email": "Email", "email has failed to send": "The email failed to send", "employee": "Employee", "employee delegations": "Employee Delegations", "employee details": "Employee Details", "employee leaves": "Leaves", "employee mention example": "Employee Mention Example", "employee not found": "Employee not found", "employees": "Employees", "end time": "End Time", "english": "English", "EQUIPMENT_UNAVAILABLE": "Equipment Unavailable", "errors": {"createManufacturingOrder": {"no_manufacturing_operations_defined": "Cannot create manufacturing order without defined operations."}, "deleteInventoryUnit": {"invalid_state": "Cannot delete inventory unit if there is inventory on it."}, "finishManufacturingOrder": {"invalid_quantity": "Cannot output more materials than are available in stock."}, "goodsAccompanyingNote": {"not_enough_stock": "Not enough stock to reserve."}, "updateSalesOrderStatus": {"not_all_reserved": "Cannot mark the order as delivered if there are not enough materials in stock."}}, "execution": "Execution", "expected by": "Expected By", "expected delivery": "Expected Delivery", "expense distributed": "Expense Distributed", "export": "Export", "fifo": "FIFO", "files": "Files", "filter": "Filter", "finance": "Finance", "financial": "Financial", "financial view": "Financial View", "finish editing variants": "Finish Editing Variants", "finish work and create service report": "Finish Work and Create Service Report", "finish work and service report": "Finish Work and Service Report", "first name": "First Name", "fiscal": "Fiscal", "fiscal invoice": "Fiscal Invoice", "font size": "Font Size", "for materials": "For Materials", "for products": "For Products", "for subassemblies": "For Work In Process", "foreign tax identification number": "Tax Identification Number", "FOREIGN_LEGAL_ENTITY": "International Customer", "friday": "Friday", "from": "From", "general": "General", "generate document": "Generate Document", "generate goods accompanying note": "Generate Goods Accompanying Note", "generate invoice": "Generate Invoice", "generate variants": "Generate Variants", "generate wishlists": "Generate Wishlists", "german": "De<PERSON>ch", "go back": "Go Back", "go to home": "Go to Home", "goods accompanying note": "Goods Accompanying Note", "goods accompanying notes": "Goods Accompanying Note", "gross margin": "<PERSON>", "h": "h", "height": "Height", "help": "Help", "hide columns": "Hide Columns", "hide details": "Hide Details", "hide items with quantity zero": "Show Items with Quantity Zero", "history": "History", "hourly indirect administrative cost per worker": "Hourly Indirect Administrative Cost per Worker", "hourly indirect cost of production per worker": "Hourly Indirect Cost of Production per Worker", "hours": "Hours", "how many employees do you want to assign to this task?": "How many employees do you want to assign to this task?", "hungarian": "<PERSON><PERSON><PERSON>", "ia#": "Adjustment Number", "identification number": "Trade Register Number", "if you leave now, your changes will be lost": "If you leave this page now, your changes will be lost.", "import": "import", "imported": {"female": "imported", "male": "imported"}, "in stock": "In Stock", "EXECUTED": "Executed", "IN_PROGRESS": "In Progress", "in_quotation": "Quoting", "IN_QUOTATION": "Quoting", "include in documents": "Include in Documents", "INCOMING": "Incoming", "incoming": "Incoming", "incomplete item": "Incomplete Item", "indirect administrative": "Indirect Administrative", "indirect administrative cost": "Indirect Administrative Cost", "indirect production": "Indirect Production", "indirect production cost": "Indirect Cost of Production", "INDIVIDUAL": "Individual", "individual tax identification number": "Tax Identification Number", "information you’ve entered will be lost": "The information you've entered will be lost.", "insert table": "Insert Table", "insert template": "Insert Template", "inventory": "Inventory", "inventory adjustments": "Inventory Adjustments", "inventory description": "todo: inventory description", "inventory unit": "Inventory Unit", "inventory unit description": "todo: inventory unit description", "inventory unit used for": "Inventory Unit Used For", "inventory units": "Inventory Units", "invoice": "Invoice", "INVOICE": "Invoice", "invoices": "Invoices", "issue has been resolved": "The issue has been resolved.", "issue has failed to resolve": "The issue has not been successfully resolved. Please try again!", "italic": "Italic", "item": "<PERSON><PERSON>", "item details": "<PERSON><PERSON>", "item has incorrect dimension configuration": "The item has an incorrect dimension configuration.", "item history": "History", "item not found": "Item not found", "item requires at least one task": "Item requires at least one task", "item type": "Item Type", "items": "Items", "json": "JSON", "kanban view": "Ka<PERSON><PERSON>", "labor": "Labor", "labor cost": "Labor Cost", "language": "Language", "last 3 months": "Last 3 Months", "last 30 days": "Last 30 Days", "last delivery took": "Last Delivery Took", "last name": "Last Name", "last purchase price": "Last Purchase Price", "last week": "Last Week", "leave": "Leave", "length": "Length", "lifetime value": "Lifetime Value", "lifo": "LIFO", "list": "List", "list details": "List Details", "list view": "List", "lists": "Lists", "loading": "Loading", "loading point": "Loading Point", "LOCAL_LEGAL_ENTITY": "Legal Entity", "locales description": "todo: locales description", "locations": "Locations", "logout": "Logout", "m": "m", "made": "Made", "make": "Make", "managed by": "Assigned to", "manager": "Manager", "manufacture": "Manufacture", "MANUFACTURED": "Completed", "manufacturing": "Manufacturing", "MANUFACTURING": "In progress", "manufacturing details": "Manufacturing Details", "manufacturing in progress": "Manufacturing in Progress", "manufacturing order": "Manufacturing Order", "manufacturing orders with missing materials": "Manufacturing Orders with Missing Materials", "manufacturing planned": "Manufacturing Planned", "MANUFACTURING_TASK": "Manufacturing Task", "margin": "<PERSON><PERSON>", "mark as delivered": "Delivered", "mark as done": "<PERSON> as <PERSON>", "ON_HOLD": "On Hold", "mark as read": "<PERSON> <PERSON>", "markup": "<PERSON><PERSON>", "MATERIAL": "Material", "material": "Material", "material cost": "Material Cost", "material costs": "Material Costs", "material details": "Material Details", "material name has been added to the wishlist": "The material {name} has been added to the wishlist.", "material name has failed to add to the wishlist": "The material {name} has not been successfully added to the wishlist. Please try again!", "material not found": "Material not found", "materials": "Materials", "materials summary": "Materials Summary", "mention employee": "Mention Employee", "mention textarea test": "Mention Textarea Test", "mentioned employees": "Mentioned employees", "mentions": "Mentions", "message": "Message", "minutes": "Minutes", "missing materials": "Missing Materials", "MISSING_MATERIAL": "Missing Material", "monday": "Monday", "month": "Month", "monthly gross salary": "Monthly Gross Salary", "moved on": "Moved On", "my tasks": "My Tasks", "name": "Name", "name customization has completed": "Customization for {name} has been completed.", "name customization has failed to complete": "Customization for {name} has not been successfully completed. Please try again!", "name has been canceled successfully": "{name} has been successfully {canceled}.", "name has been created successfully": "{name} has been successfully {created}.", "name has been deleted successfully": "{name} has been successfully {deleted}.", "name has been sent successfully": "{name} has been sent successfully.", "name has been updated successfully": "{name} has been successfully {updated}.", "name has failed to cancel successfully": "{name} has not been successfully {canceled}. Please try again!", "name has failed to create successfully": "{name} has not been successfully {created}. Please try again!", "name has failed to delete successfully": "{name} has not been successfully {deleted}. Please try again!", "name has failed to import successfully": "{name} have not been successfully {imported}. Please try again!", "name has failed to send successfully": "{name} has not been successfully sent. Please try again!", "name has failed to update successfully": "{name} has not been successfully {updated}. Please try again!", "name has reported an issue": "{name} has reported an issue.", "name has resolved the issue": "{name} has resolved the issue.", "name have been imported successfully": "{name} have been {imported} successfully.", "name value has been copied": "{name} {value} has been copied.", "name: you have been tagged by triggeredBy in a comment": "{name}: You have been tagged by {triggered<PERSON><PERSON>} in a comment", "NEED_SUPPLY": "Needs Supply", "net value": "Net Value", "new": "New", "new address": "New Address", "new adjustment": "New Adjustment", "new consumable": "New Consumable", "new consumable name": "New Consumable Name", "new consumption": "New Consumption", "new custom manufacturing order": "New Custom Manufacturing Order", "new customer": "New Customer", "new customer name": "New Customer Name", "new employee": "New Employee", "new employee name": "New Employee Name", "new identification number": "New Identification Number", "new item": "New Item", "new item name": "New Item Name", "new list": "New List", "new manufacturing order": "New Manufacturing Order", "new material": "New Material", "new material name": "New Material Name", "new option": "New Option", "new order": "New Order", "new product": "New Product", "new product name": "New Product Name", "new purchase list": "New Purchase List", "new quote": "New Quote", "new sale order": "New Sales Order", "new sku": "New SKU", "new subassembly": "New Work In Process", "new subassembly name": "New Work In Process Name", "new supplier": "New Supplier", "new supplier name": "New Supplier Name", "new tax identification number": "New Tax Identification Number", "new value": "New Value", "new wishlist": "New Wishlist", "no available options": "No Available Options", "no employees found": "No employees found", "no filters applied": "No filters applied", "no messages": "No Messages", "no results found": "No results found", "NONE": "None", "notes": "Notes", "notification": "Notification", "notifications": "Notifications", "now": "Now", "nr materials in total": "{nr} materials in total", "number": "Number", "numbered list": "Numbered List", "observations": "Observations", "offer": "Offer", "ok": "OK", "on stock": "On Stock", "one of the workstations": "One of the Workstations", "open": "Open", "operation": "Operation Phase", "operation not found": "Operation not found", "operational steps": "Operational Phases", "operations": "Operational Phases", "option": "Option", "option values": "Option Values", "optional": "Optional", "optional items": "Optional Items", "order": "Order", "order date": "Order Date", "order details": "Order Details", "order for supplierName has failed to send successfully": "Order for {supplierName} has failed to send successfully. Please try again!", "order history": "Order History", "order nr": "Order Number", "order orderName for supplierName has been created successfully": "Order {orderName} for {supplierName} has been created successfully.", "order orderName for supplierName has been sent successfully": "Order {orderName} for {supplierName} has been sent successfully.", "order ranking": "Order Priority", "order status": "Order Status", "order_number: missing material": "{order_number}: Missing Material", "order_number: name has broken down or needs servicing": "{order_number}: {name} has broken down or needs servicing.", "order_number: name is not available": "{order_number}: {name} is not available.", "order#": "Order Number", "ordered": "Ordered", "orders": "Orders", "other supplier": "Other Supplier", "OUTGOING": "Outgoing", "OVERDUE": "Overdue", "PICKING_PACKING": "Picking & Packing", "overview": "Overview", "page not found": "Page Not Found", "PAID": "Paid", "paired": "Connected", "parallelization": "Parallelization", "PAUSED": "Paused", "pdf": "PDF", "people": "Employees", "percent distribution": "Percent Distribution", "phone nr": "Phone Number", "phone number": "Phone Number", "pick a date": "Pick a Date", "planned duration": "Planned Duration", "please confirm that you want to import": "Please confirm that you want to import!", "please contact an administrator if the problem persists": "Please contact an administrator if the problem persists.", "please fill in all mandatory fields before saving": "Please fill in all mandatory fields before saving", "please fill in all mandatory fields before updating": "Please fill in all mandatory fields before updating", "please review you adjustments to save all the changes": "Please review your adjustments to save all the changes.", "position": "Position", "potential margin": "Potential Margin", "preferential name": "Preferred Name", "preview": "Preview", "preview and create document": "Preview and Create Document", "preview and save document": "Preview and Save Document", "previous stock": "Previous Stock", "previous suppliers": "Previous Suppliers", "previously ordered": "Previously Ordered", "PRICE": "Price", "price": "Price", "print": "Print", "PROCESSING": "In Progress", "PRODUCT": "Product", "product": "Product", "product details": "Product Details", "product not found": "Product not found", "production": "Production", "production cost": "Production Cost", "production costs": "Production Costs", "production deadline": "Production Deadline", "production report": "Production Report", "production time": "Production Time", "products": "Products", "products / services": "Products / Services", "profit": "Profit", "proforma": "<PERSON><PERSON><PERSON>", "proforma invoice": "Proforma Invoice", "purchase": "Purchase Order", "purchase cost": "Purchase Cost", "purchase in progress": "Purchase in Progress", "purchase order": "Purchase Order", "purchase price": "Purchase Price", "purchases": "Purchases", "qr": "QR", "qualified employees": "Qualified Employees", "qualified for": "Qualified For", "QUANTITY": "Quantity", "quantity": "Quantity", "QUOTE_SENT": "Quote <PERSON>", "quantity after": "Stock After", "quick create": "Quick Create", "quote date": "Quote Date", "quote details": "Quote Det<PERSON>", "quote expiration": "Quote Expiration", "quotes": "Quotes", "READY": "Ready", "ready_to_ship": "Ready to Ship", "READY_TO_SHIP": "Ready to Ship", "realizable sales": "Realizable Sales", "reason for adjustment": "Reason for Adjustment", "receipt": "Receipt", "received by": "Received by", "received quantity": "Received Quantity", "reception": "Reception", "reception date": "Stock Entry Date", "reception details": "Reception Details", "reception price": "Reception Price", "reception value": "Reception Value", "receptions": "Receptions", "recipe": "Recipe", "recipe for": "Recipe for", "regionals": "Regionals", "remaining": "Remaining", "remove configuration": "Remove Configuration", "reports": "Reports", "required": "Required Per Unit", "required total": "Total Required", "resend": "Resend", "resend invoice": "Resend Invoice", "resend invoice and download": "Resend Invoice and Download", "resolve issue": "Resolve Issue", "resolved": "{is<PERSON><PERSON><PERSON>, select, true {Resolved} false {Resolved} other {Resolved}}", "review adjustments": "Review adjustments", "romanian": "Română", "saga": "Saga", "sale": "Sale", "sales": "Sales", "saturday": "Saturday", "save": "Save", "save and download": "Save and Download", "save and generate variants": "Save and Generate Variants", "save before leaving": "Save & leave", "save configuration": "Save Configuration", "search": "Search", "SENT": "<PERSON><PERSON>", "SENT_FOR_QUOTE": "Sent for Quote", "search address": "Search Address", "search by item name": "Search by Item Name", "search by name, email, phone number, city, tax identification number, identification number": "Search by Name, Email, Phone Number, City, Tax Identification Number, Registration Number", "search by name, position": "Search by Name, Position", "search by name, sku": "Search by Name, SKU", "search by order number": "Search by Order Number", "search by tax identification number or name": "Search by Tax Identification Number or Name", "search category": "Search Category", "search country": "Search Country", "search customer": "Search Customer", "search delegate": "Search Delegate", "search employee": "Search employee", "search inventory unit": "Search Inventory Unit", "search item": "Search Item", "search material": "Search Material", "search materials": "Search Materials", "search operation": "Search operation", "search product": "Search Product", "search service": "Search Service", "search supplier": "Search Supplier", "search user": "Search User", "search value": "Search Value", "search workstation": "Search workstation", "searching": "Searching", "select": "Select", "select country": "Select Country", "select dimensions": "Select Dimensions:", "select item type": "Select Item Type:", "select item type to view dimensions": "Select an item type to view available dimensions.", "select material": "Select Material", "select material label": "Select Material:", "select quantity": "Select Quantity", "select table size": "Select Table Size", "select the type of units used for your operations": "Select the Units of Measure Used for Your Operations", "selected": {"female": "selected", "male": "selected"}, "selling price": "Selling <PERSON>", "send": "Send", "send invoice": "Send Invoice", "send invoice and download": "Send Invoice and Download", "send order": "Send Order", "send order and download": "Send Order and Download", "send quote": "Send Quote", "send quote and download": "Send Quote and Download", "sent on": "<PERSON>t on", "server error": "Server Error", "SERVICE": "Service", "service": "Service", "service details": "Service Details", "service not found": "Service not found", "service report": "Service Report", "services": "Services", "services description": "todo: services description", "servicing in progress": "Servicing in Progress", "servicing planned": "Servicing Planned", "set dimensions": "Set Dimensions", "set material dimensions": "Set Material Dimensions", "settings": "Settings", "shipping": "Shipping", "SHIPPING": "In Shipping", "shipping address": "Shipping Address", "ships to": "Ships To", "show details": "Show Details", "show items below the preset min value": "Show Items Below the Preset Min. Value", "sign out": "Sign Out", "sku": "SKU", "social capital": "Social Capital", "someone": "Someone", "sorry, something went wrong on our end": "Sorry, something went wrong on our server.", "sorry, we couldn't find the page you're looking for": "Sorry, we couldn't find the page you're looking for.", "source unit": "Source Unit", "start time": "Start Time", "start work": "Start Work", "state": "State", "status": "Status", "stay": "Stay", "stock": "Stock", "stock adjustment required for count items": "Stock adjustment required for {count} items", "stock value": "Stock Value", "stock value calculation based on": "Stock Value Calculation Based on", "stock view": "Stock View", "STOPPED": "Stopped", "subassemblies": "Work In Process", "SUBASSEMBLY": "Work In Progress", "subassembly": "Work In Process", "subassembly details": "Work In Process Details", "subject": "Subject", "submitted": "Submitted", "SUBMITTED": "Not Started", "subtotal": "Subtotal", "suffixed": {"accompanyingNote": {"end": "of the goods accompanying note", "start": "The goods accompanying note"}, "accompanyingNotes": "of the goods accompanying notes", "account": "of the account", "activities": "of the activities", "activity": {"end": "of the activity", "start": "The activity"}, "adjustment": {"end": "of the adjustment", "start": "The adjustment"}, "adjustments": "of the adjustments", "categories": "of the categories", "category": "The category", "consumable": {"end": "of the consumable", "start": "The consumable"}, "consumables": "of the consumables", "consumption": {"end": "of the consumption", "start": "The consumption"}, "consumptions": "of the consumptions", "countries": "of the countries", "currencies": "of the currencies", "customer": {"end": "of the customer", "start": "The customer"}, "customers": "of the customers", "delegation": {"end": "of the delegation", "start": "The delegation"}, "dimensions": "of dimensions", "employee": {"end": "of the employee", "start": "The employee"}, "employees": "of the employees", "exchangeRates": "of the exchange rates", "inventories": "of the inventories", "inventory stats": "of the inventory statistics", "inventory unit": {"end": "of the inventory unit", "start": "The inventory unit"}, "inventory units": "of the inventory units", "invoice": {"end": "of the invoice", "start": "The invoice"}, "invoices": "of the invoices", "item": {"end": "of the item", "start": "The item"}, "item history": {"end": "of the history"}, "items": "of the items", "leave": {"end": "of the leave", "start": "The leave"}, "material": {"end": "of the material", "start": "The material"}, "materials": "of the materials", "myTasks": "my tasks", "offer": {"end": "of the offer", "start": "The offer"}, "order": {"end": "of the order", "start": "The order"}, "orders": "of the orders", "product": {"end": "of the product", "start": "The product"}, "products": "of the products", "quote": {"end": "of the quote", "start": "The quote"}, "quotes": "of the quotes", "reception": {"end": "of the reception", "start": "The reception"}, "receptions": "of the receptions", "report": {"end": "of the report", "start": "The report"}, "service": {"end": "of the service", "start": "The service"}, "serviceReport": {"end": "of the service report", "start": "The service report"}, "services": "of the services", "settings": "of the settings", "subassemblies": "of the work in process", "subassembly": {"end": "of the work in process", "start": "The work in process"}, "supplier": {"end": "of the supplier", "start": "The supplier"}, "suppliers": "of the suppliers", "task": {"end": "of the task", "start": "The task"}, "task allocation": {"end": "of the task allocation", "start": "The task allocation"}, "tasks": "of the tasks", "units": "of the units of measure", "users": "of the users", "variant": {"end": "of the variant", "start": "The variant"}, "variants": "of the variants", "wishlist": {"end": "of the wishlist", "start": "The wishlist for"}, "wishlists": "of the wishlists", "workstation": {"end": "of the workstation", "start": "The workstation"}, "workstations": "of the workstations"}, "summary": "Summary", "sunday": "Sunday", "supplier": "Supplier", "supplier not found": "Supplier not found", "suppliers": "Suppliers", "supporting document type": "Supporting Document Type", "swift": "SWIFT", "task": "Work Phase", "task management": "Task Management", "task not found": "Task not found", "tasks": "Work Phases", "tax": "VAT", "tax and order": "Tax and Order", "tax and order description": "todo: tax and order description", "tax identification number": "Tax ID Number", "tax rates": "VAT Rates", "taxes": "VAT", "templates": "Templates", "texts": {"administrativeCost": {"calculation": {"list": {"1": "Sum all the company's monthly/annual administrative and general costs", "2": "Divide by the total number of productive hours available in the same period", "3": "The result represents the indirect administrative cost per hour/worker"}, "title": "How to Calculate:"}, "description": "This field represents the value of all administrative and general costs of the company divided by the total number of productive hours available in a reference period. It includes:", "example": {"list": {"1": "Monthly administrative costs: 500,000 RON", "2": "Productive hours available: 4,000 hours", "3": "Hourly indirect administrative cost: 125 RON/h/worker"}, "title": "Example:"}, "list": {"1": "Administrative expenses (rent for administrative spaces, utilities, office supplies)", "2": "Salaries of administrative personnel (management, HR, accounting, IT)", "3": "Logistical and transportation costs (fleet, storage, fuel)", "4": "Marketing and sales expenses (salaries, commissions, promotions)", "5": "Depreciation of administrative equipment and software", "6": "External services (consulting, audit, legal)", "7": "Financial expenses (interest, insurance, bank fees)", "8": "Other general company expenses"}, "note": "This value will be automatically used in the calculation of total costs of finished products, being multiplied by the total time worked for each product.", "title": "Administrative Costs", "updateRecommendation": {"frequency": "quarterly or when there are significant changes in the structure of administrative costs.", "text": "Recommended Update:"}}, "indirectCost": {"calculation": {"list": {"1": "Sum all the factory's monthly/annual indirect costs (only those related to production activity)", "2": "Divide by the total number of productive hours available in the same period", "3": "The result represents the indirect cost per hour/worker"}, "title": "How to Calculate:"}, "description": "This field represents the value of all indirect production costs (general factory expenses) divided by the total number of productive hours available in a reference period. It includes:", "example": {"list": {"1": "Hourly indirect cost: 50 RON/h/worker", "2": "Monthly indirect costs: 100,000 RON", "3": "Productive hours available: 2,000 hours"}, "title": "Example:"}, "list": {"1": "Utilities (electricity, water, gas)", "2": "Rent and maintenance of production spaces", "3": "Depreciation of equipment and machinery", "4": "Salaries of indirect personnel (management, maintenance, quality)", "5": "Indirect consumables and materials", "6": "Other general production expenses"}, "note": "This value will be automatically used in the calculation of production costs, being multiplied by the total time worked for each finished product.", "title": "Indirect Costs", "updateRecommendation": {"frequency": "monthly or when there are significant changes in the cost structure.", "text": "Recommended Update:"}}, "signature": {"click again to confirm finishing the work and service report without a signature": "<PERSON>lick again to confirm finishing the work and service report without a signature", "document is not signed": "Document is not signed"}}, "the base currency used for all your operations": "The Base Currency Used for All Your Operations", "the categories can be assigned to items for better organizing and grouping of products and materials": "The Categories Can Be Assigned to Items for Better Organizing and Grouping of Products and Materials", "the changes have not been applied successfully": "The changes have not been applied successfully. Please try again!", "the item has no materials added": "The item has no materials added", "the item is a subassembly": "The item is a work in process", "the operation will be permanently deleted from the system": "Careful! The operation will be permanently deleted from the system.", "the types can be assigned for better organizing and grouping of products": "The Types Can Be Assigned for Better Organizing and Grouping of Products", "this change will perform a rescheduling!": "This change will perform a rescheduling!", "thursday": "Thursday", "to": "To", "to correctly allocate indirect costs to the manufactured products": "To correctly allocate indirect costs to the manufactured products", "today": "Today", "TODO": "To-Do", "total": "Total", "total achievable profit": "Total Achievable Profit", "total achievable sales": "Total Achievable Sales", "total average cost": "Total Average Cost", "total cost": "Total Cost", "total employees": "Total Employees", "total items": "Total Items", "total material cost": "Total Material Cost", "total materials": "Total Materials", "total monthly gross salary": "Total Monthly Gross Salary", "total percent distribution": "Total Percent Distribution", "total potential margin": "Total Potential Margin", "total price": "Total price", "total price with vat": "Total price with VAT", "total products": "Total Products", "total realizable sales": "Total Realizable Sales", "total revenue from the customer": "Total Revenue from the Customers", "total stock value": "Total Stock Value", "total total cost per hour": "Total Cost per Hour", "total value": "Total value", "total value distribution": "Total value distribution", "total vat": "Total VAT", "transfer between units": "Transfer Between Units", "transfer reason": "Transfer Reason", "transferred quantity": "Transferred Quantity", "transport": "Transport", "transport registration number": "Transport Registration Number", "TRANSPORTATION": "Transportation", "transported by": "Transported by", "transported with": "Transported with", "tuesday": "Tuesday", "type": "Type", "type @ to mention an employee": "Type @ to mention an employee", "type has an issue": "Type has an issue", "type has been updated": "Type has been updated", "TYPE_CHANGE": "Unit change", "um": "U.M.", "unassign task": "Unassign task", "unavailable": "Unavailable", "underline": "Underline", "unit": {"id": {"bag": "bags", "box": "boxes", "can": "cans", "cm": "centimeters", "cm2": "square centimeters", "cm3": "cubic centimeters", "day": "days", "g": "grams", "hour": "hours", "kg": "kilograms", "kwh": "kilowatt hours", "linear_meter": "linear meters", "liter": "liters", "m": "meters", "m2": "square meters", "m3": "cubic meters", "milli_liter": "milliliters", "minute": "minutes", "mm": "millimeters", "pack": "packs", "pair": "pairs", "pallet": "pallets", "piece": "pieces", "plate": "plates", "roll": "rolls", "sack": "sacks", "service": "services", "set": "sets", "tonne": "tonnes", "un": "units"}, "name": {"bag": "bags", "box": "boxes", "can": "cans", "cm": "cm", "cm2": "cm²", "cm3": "cm³", "day": "days", "g": "g", "h": "h", "kg": "kg", "kwh": "kWh", "l": "l", "linear_meter": "lm", "m": "m", "m2": "m²", "m3": "m³", "min": "min", "ml": "ml", "mm": "mm", "pac": "packs", "pair": "pairs", "pallet": "pallets", "pcs": "pcs", "plate": "plates", "rol": "rol", "sack": "sacks", "service": "serv", "set": "set", "t": "t", "un": "un"}}, "unit change": "Unit Change", "unit of measure": "Unit of measure", "unit price": "Unit price", "unit value distribution": "Unit value distribution", "units": "Units", "units of measure": "Units of measure", "unknown": "Unknown", "unloading point": "Unloading Point", "unresolved": "{is<PERSON><PERSON><PERSON>, select, true {Unresolved} false {Unresolved} other {Unresolved}}", "unsaved changes": "Unsaved changes", "update goods accompanying note": "Update Goods Accompanying Note", "updated": {"female": "modificată", "male": "modificat"}, "used": "Used", "user not found": "User not found", "value": "Value", "value day ago": "Value day ago", "value days ago": "Value days ago", "value distribution": "Value distribution", "value h ago": "Value h ago", "value min ago": "Value min ago", "value s ago": "Value s ago", "value week ago": "Value week ago", "value weeks ago": "Value weeks ago", "variant option": "Variant option", "variants": "Variants", "variants have been created successfully": "Variants have been created successfully", "variants have been updated successfully": "Variants have been updated successfully.", "variants have failed to create successfully": "Variants have failed to create successfully. Please try again!", "variants have failed to updated successfully": "Variants have not been successfully updated. Please try again!", "vat": "VAT", "vat rate": "VAT Rate", "vat value": "VAT Value", "view": "View", "view invoice": "View invoice", "view offer": "View Offer", "view proforma": "View proforma", "view purchase order": "View Purchase Order", "view reception": "View Reception", "viewed by count": "Viewed by count", "waste": "Waste", "wednesday": "Wednesday", "week": "Week", "weight": "Weight", "weighted average": "Weighted average", "width": "<PERSON><PERSON><PERSON>", "winmentor": "WinMENTOR", "wishlist": "Wishlist", "without category": "Without Category", "without vat": "Without VAT", "worker": "Worker", "worker signature": "Worker Signature", "workers": "Workers", "working days": "Working days", "working days description": "todo: working days description", "working hours": "Working hours", "working hours description": "todo: working hours description", "workmanship": "Workmanship", "workstation": "Workstation", "workstation not found": "Workstation not found", "workstations": "Workstations", "yesterday": "Yesterday", "you have unsaved changes": "You have unsaved changes.", "your notifications list is currently empty": "Your notifications list is currently empty", "zip": "Zip code"}