import {atom} from 'jotai';

import {defaultLocale} from '@/constants/config';
import {ConfigCountry, ConfigCurrency, ExchangeRates} from '@/types/global';
import {localeAtom} from 'store/locale';
import {UserSettings} from 'types/account';

export const defaultCurrencyAtom = atom<string>('');
export const defaultLocaleAtom = atom<string>(defaultLocale);
export const defaultVATAtom = atom<number>(0);
export const enabledUnitsAtom = atom<string[]>([]);
export const defaultWorkingHoursPerDayAtom = atom<number>(8);
export const currenciesAtom = atom<ConfigCurrency[]>([]);
export const countriesAtom = atom<ConfigCountry[]>([]);
export const companyNameAtom = atom<string>('');
export const exchangeRatesAtom = atom<ExchangeRates>({});

export const setDefaultsAtom = atom(null, async (get, set, settings: UserSettings) => {
  set(defaultLocaleAtom, settings?.general?.defaultLanguage);
  set(defaultCurrencyAtom, settings?.general?.defaultCurrency || '');
  set(enabledUnitsAtom, settings?.enabledMeasurementUnits);
  set(defaultVATAtom, settings?.taxRates?.defaultVAT);

  if (!get(localeAtom)) set(localeAtom, settings?.general?.defaultLanguage);

  if (settings?.manufacturing?.workDayStartTime && settings?.manufacturing?.workDayEndTime) {
    const startTime = new Date(`01/01/2000 ${settings?.manufacturing?.workDayStartTime}`);
    const endTime = new Date(`01/01/2000 ${settings?.manufacturing?.workDayEndTime}`);
    const diff = endTime.getTime() - startTime.getTime();
    set(defaultWorkingHoursPerDayAtom, diff / 1000 / 60 / 60);
  }
});

export const setCountriesAtom = atom(null, (_, set, countries: ConfigCountry[]) => {
  set(countriesAtom, countries);
});

export const setCurrenciesAtom = atom(null, (_, set, currencies: ConfigCurrency[]) => {
  set(currenciesAtom, currencies);
});

export const setCompanyNameAtom = atom(null, (_, set, companyName: string) => {
  set(companyNameAtom, companyName);
});

export const getCountryNameAtom = atom(null, (get) => (code: string) => {
  return get(countriesAtom).find((country) => country.code === code)?.name;
});

export const setExchangeRatesAtom = atom(null, (_, set, exchangeRates: ExchangeRates) => {
  set(exchangeRatesAtom, {...exchangeRates, RON: 1});
});
