import {atom} from 'jotai';
import {atomWithStorage} from 'jotai/utils';

import {StoredLayoutItem} from '@/types/dashboard';
import {NotificationMeta} from 'types/interface';

export const sidebarConfigAtom = atomWithStorage<{
  isOpened: boolean;
}>('sidebar', {isOpened: false});

export const tasksCurrentDateAtom = atom<Date>(new Date());

export const notificationsConfigAtom = atom<{
  count: number;
  currentFilter: string;
  isEnabled: boolean | undefined;
  isOpened: boolean;
  unread: boolean;
}>({count: 0, currentFilter: 'unresolved', isEnabled: undefined, isOpened: false, unread: false});

export const notificationsAtom = atom<NotificationMeta[]>([]);
export const selectedNotificationsAtom = atom<NotificationMeta | undefined>(undefined);

export const leaveConfirmModalOpenAtom = atom<boolean>(false);
export const saveCallbackAtom = atom<(() => Promise<void>) | null>(null);
export const pendingUrlAtom = atom<null | string>(null);
export const isNavigatingAtom = atom<boolean>(false);

export const dashboardLayoutAtom = atomWithStorage<StoredLayoutItem[]>('dashboard-layout', [], {
  getItem: (key, initialValue) => {
    const storedValue = localStorage.getItem(key);
    if (!storedValue) return initialValue;

    try {
      return JSON.parse(storedValue);
    } catch (_e) {
      return initialValue;
    }
  },
  removeItem: (key) => {
    localStorage.removeItem(key);
  },
  setItem: (key, value) => {
    localStorage.setItem(key, JSON.stringify(value));
  },
});
