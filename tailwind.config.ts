import type {Config} from 'tailwindcss';
import defaultTheme from 'tailwindcss/defaultTheme';
import plugin from 'tailwindcss/plugin';
import tailwindcssAnimate from 'tailwindcss-animate';

const config = {
  content: ['./pages/**/*.{ts,tsx}', './components/**/*.{ts,tsx}', './app/**/*.{ts,tsx}', './src/**/*.{ts,tsx}'],
  plugins: [
    tailwindcssAnimate,
    plugin(({addVariant}: {addVariant: (val1: string, val2: string) => void}) => {
      addVariant('not-first-of-type', '&:not(:first-of-type)');
      addVariant('not-last-of-type', '&:not(:last-of-type)');
      addVariant('odd', '&:nth-of-type(odd)');
      addVariant('even', '&:nth-of-type(even)');
      addVariant('not-disabled', '&:not([disabled])');
    }),
  ],
  prefix: '',

  theme: {
    container: {
      center: true,
      padding: '2rem',
      screens: {
        '2xl': '1400px',
      },
    },
    extend: {
      animation: {
        'accordion-down': 'accordion-down 0.2s ease-out',
        'accordion-down-quick': 'accordion-down 0.1s ease-out',
        'accordion-up': 'accordion-up 0.2s ease-out',
        'accordion-up-quick': 'accordion-up 0.1s ease-out',
        fadeIn: 'fadeIn 0.2s ease-out',
      },
      borderRadius: {
        lg: 'var(--radius)',
        md: 'calc(var(--radius) - 2px)',
        sm: 'calc(var(--radius) - 4px)',
      },
      colors: {
        accent: {
          DEFAULT: 'hsl(var(--accent) / <alpha-value>)',
          foreground: 'hsl(var(--accent-foreground) / <alpha-value>)',
        },
        alert: {
          blue: '#214766',
          gray: '#6b7280',
          green: '#235E45',
          'light-blue': '#E8F4FD',
          'light-gray': '#f3f4f6',
          'light-green': '#ECFDF3',
          'light-red': '#FEF3F2',
          'light-yellow': '#FFF6E2',
          red: '#AC1E13',
          yellow: '#BD7D00',
        },
        background: 'hsl(var(--background) / <alpha-value>)',
        blue: {
          dark: `hsl(var(--info-blue-dark) / <alpha-value>)`,
          DEFAULT: `hsl(var(--info-blue) / <alpha-value>)`,
          light: `hsl(var(--info-blue-light) / <alpha-value>)`,
        },
        border: {
          DEFAULT: 'hsl(var(--border) / <alpha-value>)',
          foreground: 'hsl(var(--border-foreground) / <alpha-value>)',
        },
        button: {
          label: '#000000',
          'label-hovered': '#6C6C7B',
          primary: 'hsl(var(--button-primary) / <alpha-value>)',
          'primary-hovered': '#DEAA28',
          secondary: '#FFFFFF',
          'secondary-hovered': '#D7D9DB',
        },
        destructive: {
          DEFAULT: 'hsl(var(--destructive) / <alpha-value>)',
          foreground: 'hsl(var(--destructive-foreground) / <alpha-value>)',
        },
        dot: {
          blue: 'hsl(var(--dot-blue) / <alpha-value>)',
          gray: 'hsl(var(--dot-gray) / <alpha-value>)',
          green: 'hsl(var(--dot-green) / <alpha-value>)',
          orange: 'hsl(var(--dot-orange) / <alpha-value>)',
          purple: 'hsl(var(--dot-purple) / <alpha-value>)',
          yellow: 'hsl(var(--dot-yellow) / <alpha-value>)',
        },
        foreground: 'hsl(var(--foreground) / <alpha-value>)',
        gray: {
          dark: 'hsl(var(--secondary-gray-dark) / <alpha-value>)',
          DEFAULT: 'hsl(var(--secondary-gray) / <alpha-value>)',
          light: 'hsl(var(--secondary-gray-light) / <alpha-value>)',
        },
        green: {
          dark: 'hsl(var(--success-green-dark) / <alpha-value>)',
          DEFAULT: 'hsl(var(--success-green) / <alpha-value>)',
          light: 'hsl(var(--success-green-light) / <alpha-value>)',
        },
        input: 'hsl(var(--input) / <alpha-value>)',
        menu: {
          DEFAULT: 'hsl(var(--menu) / <alpha-value>)',
          foreground: 'hsl(var(--menu-foreground) / <alpha-value>)',
        },
        muted: {
          DEFAULT: 'hsl(var(--muted) / <alpha-value>)',
          foreground: 'hsl(var(--muted-foreground) / <alpha-value>)',
        },
        oldprimary: '#FAC748',
        oldsecondary: '#ededed',
        primary: {
          DEFAULT: 'hsl(var(--primary) / <alpha-value>)',
          foreground: 'hsl(var(--primary-foreground) / <alpha-value>)',
        },
        red: {
          dark: 'hsl(var(--error-red-dark) / <alpha-value>)',
          DEFAULT: 'hsl(var(--error-red) / <alpha-value>)',
          light: 'hsl(var(--error-red-light) / <alpha-value>)',
        },
        yellow: {
          dark: 'hsl(var(--warning-yellow-dark) / <alpha-value>)',
          DEFAULT: 'hsl(var(--warning-yellow) / <alpha-value>)',
          light: 'hsl(var(--warning-yellow-light) / <alpha-value>)',
        },
      },
      keyframes: {
        'accordion-down': {
          from: {height: '0'},
          to: {height: 'var(--radix-accordion-content-height)'},
        },
        'accordion-up': {
          from: {height: 'var(--radix-accordion-content-height)'},
          to: {height: '0'},
        },
        fadeIn: {
          '0%': {opacity: '0', transform: 'translateY(5px)'},
          '100%': {opacity: '1', transform: 'translateY(0)'},
        },
      },
      transitionProperty: {
        bgColor: 'background-color',
        height: 'height',
        margin: 'margin',
        'max-height': 'max-height',
        padding: 'padding',
        width: 'width',
      },
    },
    fontFamily: {
      sans: ['var(--font-sans)', ...defaultTheme.fontFamily.sans],
    },
    screens: {...defaultTheme.screens, sm: '450px'},
  },
} satisfies Config;

export default config;
