// Global keys
"loading"
"notifications"
"your notifications list is currently empty"
"logout"
"name has reported an issue"
"name has resolved the issue"
"viewed by count"
"resolve issue"
"quick create"
"new quote"
"new sale order"
"new purchase list"
"new manufacturing order"
"edit"
"cancel"
"add"
"save"
"delete"
"name"
"city"
"state"
"country"
"zip"
"bank"
"number"
"swift"
"email"
"phone number"
"preferential name"
"shipping"
"billing"

// Layout components
"notifications"

// Customer related
"addresses"
"add address"
"add new address"
"address (str no bl ap st fl)"
"bank accounts"
"add bank account"
"add new bank account"
"contacts"
"add contact"
"add new contact"
"new customer name"
"customers"
"new tax identification number"
"tax identification number"
"foreign tax identification number"
"individual tax identification number"
"new identification number"
"identification number"
"total revenue from the customer"
"files"
"add file"

// Employee related
"new employee"
"employees"
"new employee name"
"employee details"
"position"
"monthly gross salary"
"operations"
"add operation"
"operation"
"operation not found"
"create operation"
"search operation"
"employee leaves"
"days"
"add leave"

// Inventory related
"new adjustment"
"adjustments"
"adjustment reason"
"items"
"source unit"
"destination unit"
"before stock"
"after stock"

// Manufacturing related
"manufacturing"

// Sales related
"orders"

// Enum values
CustomerType.LOCAL_LEGAL_ENTITY
CustomerType.FOREIGN_LEGAL_ENTITY
CustomerType.INDIVIDUAL
