{"extends": ["next/core-web-vitals", "prettier", "plugin:security/recommended-legacy", "plugin:react/recommended"], "parser": "@typescript-eslint/parser", "plugins": ["prettier", "@typescript-eslint", "import", "perfectionist"], "settings": {}, "rules": {"@typescript-eslint/no-unused-vars": ["warn", {"argsIgnorePattern": "^_", "caughtErrorsIgnorePattern": "^_", "varsIgnorePattern": "^_"}], "import/order": ["warn", {"groups": [["builtin", "external"], ["internal"], ["parent", "sibling", "index"], ["object"]], "pathGroups": [{"pattern": "react", "group": "external", "position": "before"}], "pathGroupsExcludedImportTypes": ["react"], "alphabetize": {"order": "asc", "caseInsensitive": true}, "newlines-between": "always"}], "sort-imports": ["warn", {"ignoreCase": true, "ignoreDeclarationSort": true, "memberSyntaxSortOrder": ["none", "all", "multiple", "single"]}], "no-unused-vars": "off", "prettier/prettier": "warn", "react/no-unused-state": "warn", "react/display-name": "off", "security/detect-object-injection": "off", "react/no-unknown-property": "warn", "react/react-in-jsx-scope": "off", "react/prop-types": "off", "perfectionist/sort-interfaces": ["warn", {"type": "natural", "order": "asc"}], "perfectionist/sort-array-includes": ["warn", {"type": "natural", "order": "asc"}], "perfectionist/sort-classes": ["warn", {"type": "natural", "order": "asc"}], "perfectionist/sort-decorators": ["warn", {"type": "natural", "order": "asc"}], "perfectionist/sort-enums": ["warn", {"type": "natural", "order": "asc"}], "perfectionist/sort-exports": ["warn", {"type": "natural", "order": "asc"}], "perfectionist/sort-heritage-clauses": ["warn", {"type": "natural", "order": "asc"}], "perfectionist/sort-intersection-types": ["warn", {"type": "natural", "order": "asc"}], "perfectionist/sort-jsx-props": ["warn", {"type": "natural", "order": "asc"}], "perfectionist/sort-maps": ["warn", {"type": "natural", "order": "asc"}], "perfectionist/sort-modules": ["warn", {"type": "natural", "order": "asc"}], "perfectionist/sort-named-exports": ["warn", {"type": "natural", "order": "asc"}], "perfectionist/sort-named-imports": ["warn", {"type": "natural", "order": "asc"}], "perfectionist/sort-object-types": ["warn", {"type": "natural", "order": "asc"}], "perfectionist/sort-objects": ["warn", {"type": "natural", "order": "asc"}], "perfectionist/sort-sets": ["warn", {"type": "natural", "order": "asc"}], "perfectionist/sort-switch-case": ["warn", {"type": "natural", "order": "asc"}], "perfectionist/sort-union-types": ["warn", {"type": "natural", "order": "asc"}], "perfectionist/sort-variable-declarations": ["warn", {"type": "natural", "order": "asc"}]}}