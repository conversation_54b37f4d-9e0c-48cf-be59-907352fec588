default:
  image: node:22-alpine

cache:
  key:
    files:
      - .pnpm-lock.yaml
  paths:
    - node_modules/

stages:
  - prepare
  - validate
  - build
  - deploy

dependencies:
  stage: prepare
  except:
    - develop
  before_script:
    - npm install -g pnpm
  script:
    - pnpm install -r --frozen-lockfile

sast:
  stage: prepare
  before_script:
    - echo "Do nothing"

lint:
  stage: validate
  except:
    - develop
  needs:
    - dependencies
  before_script:
    - npm install -g pnpm
  script:
    - pnpm lint --config .eslintrc.strict.json

typescript:
  stage: validate
  except:
    - develop
  needs:
    - dependencies
  before_script:
    - npm install -g pnpm typescript
  script:
    - pnpm tsc

test:
  stage: validate
  except:
    - develop
  needs:
    - dependencies
  before_script:
    - npm install -g pnpm
  script:
    - echo "Todo"

build-push:
  stage: build
  image: docker:latest
  services:
    - docker:dind
  cache:
    key:
      files:
        - .pnpm-lock.yaml
    paths:
      - node_modules/
  before_script:
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
  script:
    - docker build . -t $CI_REGISTRY_IMAGE:latest --progress=plain
    - docker push $CI_REGISTRY_IMAGE:latest
  only:
    - develop

include:
  - template: Security/SAST.gitlab-ci.yml


trigger_deploy:
  stage: deploy
  only:
    - develop
  trigger:
    project: ezbizproject/test-deployer
