{"rules": {"@typescript-eslint/no-unused-vars": ["error", {"argsIgnorePattern": "^_", "caughtErrorsIgnorePattern": "^_", "varsIgnorePattern": "^_"}], "import/order": ["error", {"groups": [["builtin", "external"], ["internal"], ["parent", "sibling", "index"], ["object"]], "pathGroups": [{"pattern": "react", "group": "external", "position": "before"}], "pathGroupsExcludedImportTypes": ["react"], "alphabetize": {"order": "asc", "caseInsensitive": true}, "newlines-between": "always"}], "sort-imports": ["error", {"ignoreCase": true, "ignoreDeclarationSort": true, "memberSyntaxSortOrder": ["none", "all", "multiple", "single"]}], "no-unused-vars": "off", "prettier/prettier": "error", "react/no-unused-state": "error", "security/detect-object-injection": "off", "react/no-unknown-property": "error", "@typescript-eslint/member-ordering": "error", "perfectionist/sort-interfaces": ["error", {"type": "natural", "order": "asc"}], "perfectionist/sort-array-includes": ["error", {"type": "natural", "order": "asc"}], "perfectionist/sort-classes": ["error", {"type": "natural", "order": "asc"}], "perfectionist/sort-decorators": ["error", {"type": "natural", "order": "asc"}], "perfectionist/sort-enums": ["error", {"type": "natural", "order": "asc"}], "perfectionist/sort-exports": ["error", {"type": "natural", "order": "asc"}], "perfectionist/sort-heritage-clauses": ["error", {"type": "natural", "order": "asc"}], "perfectionist/sort-intersection-types": ["error", {"type": "natural", "order": "asc"}], "perfectionist/sort-jsx-props": ["error", {"type": "natural", "order": "asc"}], "perfectionist/sort-maps": ["error", {"type": "natural", "order": "asc"}], "perfectionist/sort-modules": ["error", {"type": "natural", "order": "asc"}], "perfectionist/sort-named-exports": ["error", {"type": "natural", "order": "asc"}], "perfectionist/sort-named-imports": ["error", {"type": "natural", "order": "asc"}], "perfectionist/sort-object-types": ["error", {"type": "natural", "order": "asc"}], "perfectionist/sort-objects": ["error", {"type": "natural", "order": "asc"}], "perfectionist/sort-sets": ["error", {"type": "natural", "order": "asc"}], "perfectionist/sort-switch-case": ["error", {"type": "natural", "order": "asc"}], "perfectionist/sort-union-types": ["error", {"type": "natural", "order": "asc"}], "perfectionist/sort-variable-declarations": ["error", {"type": "natural", "order": "asc"}]}}